<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShopCheckLogMapper">

    <resultMap type="ShopCheckLog" id="ShopCheckLogResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="checkMask"    column="check_mask"    />
        <result property="checkNucleicAcid"    column="check_nucleic_acid"    />
        <result property="checkScanCode"    column="check_scan_code"    />
        <result property="checkDisinfection"    column="check_disinfection"    />
        <result property="checkOthers"    column="check_others"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="checkDate"  column="check_date" />
        <result property="shopStatus"    column="shop_status" />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="distanceStatus"    column="distance_status" />
        <result property="checkCrossDoorOperation"    column="check_cross_door_operation"    />
        <result property="checkFfhx"    column="check_ffhx"    />
        <result property="checkThreeOnePlace"    column="check_three_one_place"    />
        <result property="checkShopDecorate"    column="check_shop_decorate"    />
        <result property="checkHealthClean"    column="check_health_clean"    />
        <result property="checkFalsePropaganda"    column="check_false_propaganda"    />
        <result property="checkEnvironmentNeat"    column="check_environment_neat"    />
        <result property="checkCleanUpFood"    column="check_clean_up_food"    />
        <result property="checkUpdateByCg"    column="check_update_by_cg"    />
        <result property="checkOthersCg"    column="check_others_cg"    />
        <result property="checkPhoto"    column="check_photo"    />
        <result property="updateTimeCg" column="update_time_cg"/>
        <result property="jzCheckPhoto" column="jz_check_photo" />
        <association property="shop"    column="shop_id" javaType="Shop" resultMap="ShopResult" />
    </resultMap>

    <resultMap type="Shop" id="ShopResult">
        <result property="id"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopLicense"    column="shop_license"    />
        <result property="shopAddress"    column="shop_address"    />
        <result property="shopCreditCode"    column="shop_credit_code"    />
        <result property="shopRegisterAddress"    column="shop_register_address"    />
        <result property="shopOperatingAddress"    column="shop_operating_address"    />
        <result property="shopCategory"    column="shop_category"    />
        <result property="shopSubcategory"    column="shop_subcategory"    />
        <result property="shopLittlecategory" column="shop_littlecategory"/>
        <result property="shopContract"    column="shop_contract"    />
        <result property="shopContactPhone"    column="shop_contact_phone"    />
        <result property="shopHouseOwnership"    column="shop_house_ownership"    />
        <result property="isStateAssets"    column="is_state_assets"    />
        <result property="shopLandlordContract"    column="shop_landlord_contract"    />
        <result property="shoplAndlordPhone"    column="shopl_andlord_phone"    />
        <result property="shopGoverningProperty"    column="shop_governing_property"    />
        <result property="isSameCommunityProperty"    column="is_same_community_property"    />
        <result property="shopPropertyContact"    column="shop_property_contact"    />
        <result property="shopPropertyPhone"    column="shop_property_phone"    />
        <result property="isSiteCode"    column="is_site_code"    />
        <result property="shopEmployeeNum"    column="shop_employee_num"    />
        <result property="shopStatus"    column="shop_status"    />
        <result property="shopRemark"    column="shop_remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shopDeparmentId"    column="shop_deparment_id"    />
        <result property="isOpen"        column="is_open" />
        <result property="oddNumber"     column="odd_number"/>
        <result property="shopArea"      column="shop_area"    />
        <result property="supervisionStatus"    column="supervision_status"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="noticeType" column="notice_type"/>
        <result property="type" column="type"/>
        <result property="coordinate" column="coordinate"/>
        <result property="isAccommodation"    column="is_accommodation"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="legalPersonContact"    column="legal_person_contact"    />
        <result property="roadSection"    column="road_section"    />
        <result property="roadSectionStart"    column="road_section_start"    />
        <result property="roadSectionEnd"    column="road_section_end"    />
        <result property="informationStatus"    column="information_status"    />
        <result property="streetChief"    column="street_chief"    />
        <result property="chiefPhone"    column="chief_phone"    />
        <result property="insuranceLiabilityContact"    column="insurance_liability_contact"    />
        <result property="insuranceLiabilityPhone"    column="insurance_liability_phone"    />
        <result property="insuranceLiabilityFlag"    column="insurance_liability_flag"    />
        <result property="type" column="type"/>
        <result property="coordinate" column="coordinate"/>
    </resultMap>

    <sql id="selectShopCheckLogVo">
        select id, shop_id, dept_id, check_mask, check_nucleic_acid, check_scan_code, check_disinfection,
               check_others, create_time, update_time,check_date,shop_status, longitude, latitude,distance_status,
                       check_cross_door_operation, check_ffhx, check_three_one_place, check_shop_decorate,
               check_health_clean, check_false_propaganda, check_environment_neat, check_clean_up_food,
               check_update_by_cg, check_others_cg, check_photo, update_time_cg, jz_check_photo from shcy_shop_check_log
    </sql>

    <sql id ="selectCheckLogShopVo">
        SELECT
                scl.id as id,
                scl.shop_id as shopId,
                scl.check_mask as checkMask,
                scl.check_nucleic_acid as checkNucleicAcid,
                scl.check_scan_code as checkScanCode,
                scl.check_disinfection as checkDisinfection,
                scl.check_others as checkOthers,
                scl.create_time as create_time,
                scl.update_time as update_time,
                scl.check_date as check_date,
                scl.shop_status as shop_status,
                scl.distance_status as distance_status,
                scl.check_cross_door_operation as check_cross_door_operation,
                scl.check_ffhx as check_ffhx,
                scl.check_three_one_place as check_three_one_place,
                scl.check_health_clean as check_health_clean,
                scl.check_shop_decorate as check_shop_decorate,
                scl.check_false_propaganda as check_false_propaganda,
                scl.check_environment_neat as check_environment_neat,
                scl.check_clean_up_food as check_clean_up_food,
                scl.jz_check_photo as jzCheckPhoto,
                ss.id as shop_id,  ss.shop_name ,
                ss.shop_license,
                ss.shop_address,ss.shop_credit_code,ss.shop_register_address,ss.shop_operating_address,
                ss.shop_category,ss.shop_subcategory,ss.shop_littlecategory,ss.shop_contract,ss.shop_contact_phone,
                ss.shop_house_ownership,ss.is_state_assets,ss.shop_landlord_contract,ss.shopl_andlord_phone,
                ss.shop_governing_property,ss.is_same_community_property,ss.shop_property_contact,ss.shop_property_phone,
                ss.type as type ,ss.coordinate as coordinate
        FROM
                shcy_shop_check_log scl
                LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
    </sql>

    <sql id ="selectShopCheckLogShopVo">
        SELECT
                scl.id as id,
                scl.shop_id as shopId,
                ss.shop_name as shopName,
                scl.check_mask as checkMask,
                scl.check_nucleic_acid as checkNucleicAcid,
                scl.check_scan_code as checkScanCode,
                scl.check_disinfection as checkDisinfection,
                scl.check_others as checkOthers,
                scl.create_time as createTime,
                scl.update_time as updateTime,
                sd.dept_name as deptName,
                sd.dept_id as deptId,
                scl.check_date as checkDate,
                scl.shop_status as shopStatus,
                scl.distance_status as distanceStatus,
                scl.check_cross_door_operation as checkCrossDoorOperation,
                scl.check_ffhx as checkFfhx,
                scl.check_three_one_place as checkThreeOnePlace,
                scl.check_health_clean as checkHealthClean,
                scl.check_shop_decorate as checkShopDecorate,
                scl.check_false_propaganda as checkFalsePropaganda,
                scl.check_environment_neat as checkEnvironmentNeat,
                scl.check_clean_up_food as checkCleanUpFood,
                scl.check_others_cg as checkOthersCg,
                scl.check_photo as checkPhoto,
                scl.jz_check_photo as jzCheckPhoto
        FROM
                shcy_shop_check_log scl
                        LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
                        left join sys_dept sd on ss.shop_deparment_id  = sd.dept_id
    </sql>

    <select id="selectShopCheckLogList" parameterType="ShopCheckLog" resultType="com.ruoyi.shcy.domain.ShopCheckLog">
        <include refid="selectShopCheckLogShopVo"/>
        <where>
            <if test="shopId != null "> and scl.shop_id = #{shopId}</if>
            <if test="deptId != null"> and sd.dept_id = #{deptId}</if>
            <if test="deptName !=null and deptName !=''"> and sd.dept_name = #{deptName}</if>
            <if test="shopName !=null and shopName !=''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="checkMask != null  and checkMask != ''"> and scl.check_mask = #{checkMask}</if>
            <if test="checkNucleicAcid != null  and checkNucleicAcid != ''"> and scl.check_nucleic_acid = #{checkNucleicAcid}</if>
            <if test="checkScanCode != null  and checkScanCode != ''"> and scl.check_scan_code = #{checkScanCode}</if>
            <if test="checkDisinfection != null  and checkDisinfection != ''"> and check_disinfection = #{checkDisinfection}</if>
            <if test="checkOthers != null  and checkOthers != ''"> and scl.check_others = #{checkOthers}</if>
            <if test="checkDate != null">and scl.check_date = #{checkDate}</if>
            <if test="shopStatus != null  and shopStatus != ''"> and scl.shop_status = #{shopStatus}</if>
            <if test="distanceStatus != null  and distanceStatus != ''"> and scl.distance_status = #{distanceStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="checkCrossDoorOperation != null  and checkCrossDoorOperation != ''"> and scl.check_cross_door_operation = #{checkCrossDoorOperation}</if>
            <if test="checkFfhx != null  and checkFfhx != ''"> and scl.check_ffhx = #{checkFfhx}</if>
            <if test="checkThreeOnePlace != null  and checkThreeOnePlace != ''"> and scl.check_three_one_place = #{checkThreeOnePlace}</if>
            <if test="checkShopDecorate != null  and checkShopDecorate != ''"> and scl.check_shop_decorate = #{checkShopDecorate}</if>
            <if test="checkHealthClean != null  and checkHealthClean != ''"> and scl.check_health_clean = #{checkHealthClean}</if>
            <if test="checkFalsePropaganda != null  and checkFalsePropaganda != ''"> and scl.check_false_propaganda = #{checkFalsePropaganda}</if>
            <if test="checkEnvironmentNeat != null  and checkEnvironmentNeat != ''"> and scl.check_environment_neat = #{checkEnvironmentNeat}</if>
            <if test="checkCleanUpFood != null  and checkCleanUpFood != ''"> and scl.check_clean_up_food = #{checkCleanUpFood}</if>
            <if test="jzCheckPhoto != null  and jzCheckPhoto != ''"> and scl.jzCheckPhoto = #{jz_check_photo}</if>
        </where>
         order by scl.create_time desc
    </select>

    <select id="selectShopCheckLogListScreen" parameterType="ShopCheckLog" resultMap="ShopCheckLogResult">
        <include refid="selectShopCheckLogVo"/>
        <where>
            <if test="shopId != null">shop_id = #{shopId}</if>
        </where>
        order by check_date desc
    </select>

    <select id="selectShopCheckLogScreenList" parameterType="ShopCheckLog" resultMap="ShopCheckLogResult">
        <include refid="selectCheckLogShopVo"/>
        <where>
            <if test="shopId != null "> and scl.shop_id = #{shopId}</if>
            <if test="shopName !=null and shopName !=''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="checkMask != null  and checkMask != ''"> and scl.check_mask = #{checkMask}</if>
            <if test="checkNucleicAcid != null  and checkNucleicAcid != ''"> and scl.check_nucleic_acid = #{checkNucleicAcid}</if>
            <if test="checkScanCode != null  and checkScanCode != ''"> and scl.check_scan_code = #{checkScanCode}</if>
            <if test="checkDisinfection != null  and checkDisinfection != ''"> and check_disinfection = #{checkDisinfection}</if>
            <if test="checkOthers != null  and checkOthers != ''"> and scl.check_others = #{checkOthers}</if>
            <if test="checkDate != null">and scl.check_date = #{checkDate}</if>
            <if test="shopStatus != null  and shopStatus != ''"> and scl.shop_status = #{shopStatus}</if>
            <if test="distanceStatus != null  and distanceStatus != ''"> and scl.distance_status = #{distanceStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="checkCrossDoorOperation != null  and checkCrossDoorOperation != ''"> and scl.check_cross_door_operation = #{checkCrossDoorOperation}</if>
            <if test="checkFfhx != null  and checkFfhx != ''"> and scl.check_ffhx = #{checkFfhx}</if>
            <if test="checkThreeOnePlace != null  and checkThreeOnePlace != ''"> and scl.check_three_one_place = #{checkThreeOnePlace}</if>
            <if test="checkShopDecorate != null  and checkShopDecorate != ''"> and scl.check_shop_decorate = #{checkShopDecorate}</if>
            <if test="checkHealthClean != null  and checkHealthClean != ''"> and scl.check_health_clean = #{checkHealthClean}</if>
            <if test="checkFalsePropaganda != null  and checkFalsePropaganda != ''"> and scl.check_false_propaganda = #{checkFalsePropaganda}</if>
            <if test="checkEnvironmentNeat != null  and checkEnvironmentNeat != ''"> and scl.check_environment_neat = #{checkEnvironmentNeat}</if>
            <if test="checkCleanUpFood != null  and checkCleanUpFood != ''"> and scl.check_clean_up_food = #{checkCleanUpFood}</if>
            <if test="jzCheckPhoto != null  and jzCheckPhoto != ''"> and scl.jzCheckPhoto = #{jz_check_photo}</if>
            <if test="params.flag != null and params.flag != '' and params.flag== 1 ">
                AND DATE_FORMAT(scl.create_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
            </if>
        </where>
        order by scl.check_date desc
    </select>

    <select id="selectShopCheckLogById" parameterType="Long" resultMap="ShopCheckLogResult">
        <include refid="selectShopCheckLogVo"/>
        where id = #{id}
    </select>

    <select id="selectShopCheckLogByIdThree" parameterType="Long" resultType="com.ruoyi.shcy.domain.ShopCheckLog">
        SELECT
                scl.id as id,
                scl.shop_id as shopId,
                ss.shop_name as shopName,
                scl.check_mask as checkMask,
                scl.check_nucleic_acid as checkNucleicAcid,
                scl.check_scan_code as checkScanCode,
                scl.check_disinfection as checkDisinfection,
                scl.check_others as checkOthers,
                scl.create_time as createTime,
                scl.update_time as updateTime,
                sd.dept_name as deptName,
                sd.dept_id as deptId,
                scl.check_date as checkDate,
                scl.shop_status as shopStatus,
                scl.distance_status as distanceStatus,
                scl.check_cross_door_operation as checkCrossDoorOperation,
                scl.check_ffhx as checkFfhx,
                scl.check_mqsbls as checkMqsbls,
                scl.check_three_one_place as checkThreeOnePlace,
                scl.check_health_clean as checkHealthClean,
                scl.check_shop_decorate as checkShopDecorate,
                scl.check_false_propaganda as checkFalsePropaganda,
                scl.check_environment_neat as checkEnvironmentNeat,
                scl.check_clean_up_food as checkCleanUpFood,
                scl.jz_check_photo as jzCheckPhoto
        FROM
                shcy_shop_check_log scl
                        LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
                        left join sys_dept sd on ss.shop_deparment_id  = sd.dept_id
        where ss.id=#{id}  order by scl.check_date desc  limit 3
    </select>

    <select id="selectShopCheckLogByShopIdAndCheckDate" resultMap="ShopCheckLogResult">
        <include refid="selectShopCheckLogVo"/>
        where shop_id = #{shopId} AND DATE_FORMAT(check_date,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m') limit 1
    </select>

    <select id="selectShopCheckStatusByDeptId" parameterType="Long" resultType="com.ruoyi.shcy.domain.vo.ShopCommitteeCheckStatusVo">
        SELECT
                scl.create_time as checkDate,
                ss.shop_name as shopName,
                ss.community_street_chief as checkPerson,
                scl.distance_status as distanceStatus,
                scr.check_status as checkStatus,
                sd.dept_name as committee
        FROM
                shcy_shop ss
                        LEFT JOIN shcy_shop_check_log scl ON ss.id = scl.shop_id
                        left join shop_check_record scr on ss.id = scr.shop_id and  scl.check_date = scr.check_date
                        LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
          where scl.check_date is not null and scl.shop_status ="正常" and ss.shop_deparment_id = #{deptId}
          AND DATE_FORMAT(scr.create_time,'%Y') = DATE_FORMAT(CURDATE(),'%Y');
    </select>
    <select id="getDeptIdOfNewthCheckRecord" resultType="java.lang.Long">
        SELECT
            sd.dept_id
        FROM
            shcy_shop ss
                LEFT JOIN shcy_shop_check_log scl ON ss.id = scl.shop_id
                left join shop_check_record scr on ss.id = scr.shop_id and scl.check_date = scr.check_date
                LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
        where scl.check_date is not null and scl.shop_status ="正常"
        AND DATE_FORMAT(scr.create_time,'%Y') = DATE_FORMAT(CURDATE(),'%Y')
        order by scl.check_date desc
        limit 1
    </select>

    <insert id="insertShopCheckLog" parameterType="ShopCheckLog" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_shop_check_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="checkMask != null">check_mask,</if>
            <if test="checkNucleicAcid != null">check_nucleic_acid,</if>
            <if test="checkScanCode != null">check_scan_code,</if>
            <if test="checkDisinfection != null">check_disinfection,</if>
            <if test="checkOthers != null">check_others,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="checkDate != null">check_date,</if>
            <if test="shopStatus != null">shop_status,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="distanceStatus != null">distance_status,</if>
            <if test="checkCrossDoorOperation != null">check_cross_door_operation,</if>
            <if test="checkFfhx != null">check_ffhx,</if>
            <if test="checkThreeOnePlace != null">check_three_one_place,</if>
            <if test="checkShopDecorate != null">check_shop_decorate,</if>
            <if test="checkHealthClean != null">check_health_clean,</if>
            <if test="checkFalsePropaganda != null">check_false_propaganda,</if>
            <if test="checkEnvironmentNeat != null">check_environment_neat,</if>
            <if test="checkCleanUpFood != null">check_clean_up_food,</if>
            <if test="checkUpdateByCg != null">check_update_by_cg,</if>
            <if test="checkOthersCg != null">check_others_cg,</if>
            <if test="checkPhoto != null">check_photo,</if>
            <if test="updateTimeCg !=null">update_time_cg,</if>
            <if test="jzCheckPhoto != null">jz_check_photo,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="checkMask != null">#{checkMask},</if>
            <if test="checkNucleicAcid != null">#{checkNucleicAcid},</if>
            <if test="checkScanCode != null">#{checkScanCode},</if>
            <if test="checkDisinfection != null">#{checkDisinfection},</if>
            <if test="checkOthers != null">#{checkOthers},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="checkDate != null">#{checkDate},</if>
            <if test="shopStatus != null">#{shopStatus},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="distanceStatus != null">#{distanceStatus},</if>
            <if test="checkCrossDoorOperation != null">#{checkCrossDoorOperation},</if>
            <if test="checkFfhx != null">#{checkFfhx},</if>
            <if test="checkThreeOnePlace != null">#{checkThreeOnePlace},</if>
            <if test="checkShopDecorate != null">#{checkShopDecorate},</if>
            <if test="checkHealthClean != null">#{checkHealthClean},</if>
            <if test="checkFalsePropaganda != null">#{checkFalsePropaganda},</if>
            <if test="checkEnvironmentNeat != null">#{checkEnvironmentNeat},</if>
            <if test="checkCleanUpFood != null">#{checkCleanUpFood},</if>
            <if test="checkUpdateByCg != null">#{checkUpdateByCg},</if>
            <if test="checkOthersCg != null">#{checkOthersCg},</if>
            <if test="checkPhoto != null">#{checkPhoto},</if>
            <if test="updateTimeCg !=null">#{updateTimeCg},</if>
            <if test="jzCheckPhoto !=null">#{jzCheckPhoto},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateShopCheckLog" parameterType="ShopCheckLog">
        update shcy_shop_check_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="checkMask != null">check_mask = #{checkMask},</if>
            <if test="checkNucleicAcid != null">check_nucleic_acid = #{checkNucleicAcid},</if>
            <if test="checkScanCode != null">check_scan_code = #{checkScanCode},</if>
            <if test="checkDisinfection != null">check_disinfection = #{checkDisinfection},</if>
            <if test="checkOthers != null">check_others = #{checkOthers},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="checkDate !=null"> check_date = #{checkDate},</if>
            <if test="shopStatus != null">shop_status = #{shopStatus},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="distanceStatus != null">distance_status = #{distanceStatus},</if>
            <if test="checkCrossDoorOperation != null">check_cross_door_operation = #{checkCrossDoorOperation},</if>
            <if test="checkFfhx != null">check_ffhx = #{checkFfhx},</if>
            <if test="checkThreeOnePlace != null">check_three_one_place = #{checkThreeOnePlace},</if>
            <if test="checkShopDecorate != null">check_shop_decorate = #{checkShopDecorate},</if>
            <if test="checkHealthClean != null">check_health_clean = #{checkHealthClean},</if>
            <if test="checkFalsePropaganda != null">check_false_propaganda = #{checkFalsePropaganda},</if>
            <if test="checkEnvironmentNeat != null">check_environment_neat = #{checkEnvironmentNeat},</if>
            <if test="checkCleanUpFood != null">check_clean_up_food = #{checkCleanUpFood},</if>
            <if test="checkUpdateByCg != null">check_update_by_cg = #{checkUpdateByCg},</if>
            <if test="checkOthersCg != null">check_others_cg = #{checkOthersCg},</if>
            <if test="checkPhoto != null">check_photo = #{checkPhoto},</if>
            <if test="updateTimeCg  != null">update_time_cg  = #{updateTimeCg},</if>
            <if test="jzCheckPhoto  != null">jz_check_photo  = #{jzCheckPhoto},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShopCheckLogById" parameterType="Long">
        delete from shcy_shop_check_log where id = #{id}
    </delete>

    <delete id="deleteShopCheckLogByIds" parameterType="String">
        delete from shcy_shop_check_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectShopCheckLogList1" parameterType="ShopCheckLog" resultType="com.ruoyi.shcy.domain.ShopCheckLog">
        <include refid="selectShopCheckLogShopVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(scl.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>
</mapper>
