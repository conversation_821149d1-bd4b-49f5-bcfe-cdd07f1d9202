<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ControlMapper">
    
    <resultMap type="Control" id="ControlResult">
        <result property="controlId"    column="control_id"    />
        <result property="controlName"    column="control_name"    />
        <result property="controlCode"    column="control_code"    />
        <result property="controlLon"    column="control_lon"    />
        <result property="controlLat"    column="control_lat"    />
        <result property="controlType"    column="control_type"    />
    </resultMap>

    <sql id="selectControlVo">
        select control_id, control_name, control_code, control_lon, control_lat, control_type from shcy_control
    </sql>

    <select id="selectControlList" parameterType="Control" resultMap="ControlResult">
        <include refid="selectControlVo"/>
        <where>  
            <if test="controlName != null  and controlName != ''"> and control_name like concat('%', #{controlName}, '%')</if>
            <if test="controlCode != null  and controlCode != ''"> and control_code = #{controlCode}</if>
            <if test="controlType != null  and controlType != ''"> and control_type = #{controlType}</if>
        </where>
    </select>
    
    <select id="selectControlByControlId" parameterType="Long" resultMap="ControlResult">
        <include refid="selectControlVo"/>
        where control_id = #{controlId}
    </select>
        
    <insert id="insertControl" parameterType="Control" useGeneratedKeys="true" keyProperty="controlId">
        insert into shcy_control
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="controlName != null">control_name,</if>
            <if test="controlCode != null">control_code,</if>
            <if test="controlLon != null">control_lon,</if>
            <if test="controlLat != null">control_lat,</if>
            <if test="controlType != null">control_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="controlName != null">#{controlName},</if>
            <if test="controlCode != null">#{controlCode},</if>
            <if test="controlLon != null">#{controlLon},</if>
            <if test="controlLat != null">#{controlLat},</if>
            <if test="controlType != null">#{controlType},</if>
         </trim>
    </insert>

    <update id="updateControl" parameterType="Control">
        update shcy_control
        <trim prefix="SET" suffixOverrides=",">
            <if test="controlName != null">control_name = #{controlName},</if>
            <if test="controlCode != null">control_code = #{controlCode},</if>
            <if test="controlLon != null">control_lon = #{controlLon},</if>
            <if test="controlLat != null">control_lat = #{controlLat},</if>
            <if test="controlType != null">control_type = #{controlType},</if>
        </trim>
        where control_id = #{controlId}
    </update>

    <delete id="deleteControlByControlId" parameterType="Long">
        delete from shcy_control where control_id = #{controlId}
    </delete>

    <delete id="deleteControlByControlIds" parameterType="String">
        delete from shcy_control where control_id in 
        <foreach item="controlId" collection="array" open="(" separator="," close=")">
            #{controlId}
        </foreach>
    </delete>
</mapper>