<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.EmergencyAlertEventInfoMapper">

    <resultMap type="EmergencyAlertEventInfo" id="EmergencyAlertEventInfoResult">
        <result property="id"    column="id"    />
        <result property="eventNo"    column="event_no"    />
        <result property="alarmLocation"    column="alarm_location"    />
        <result property="disposalPerson"    column="disposal_person"    />
        <result property="createTime"    column="create_time"    />
        <result property="disposalDeadline"    column="disposal_deadline"    />
        <result property="floodMaterialPhoto"    column="flood_material_photo"    />
        <result property="floodPersonPhoto"    column="flood_person_photo"    />
        <result property="rescueOperationPhoto"    column="rescue_operation_photo"    />
        <result property="temporaryControlMeasure"    column="temporary_control_measure"    />
        <result property="otherHazard"    column="other_hazard"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
    </resultMap>

    <sql id="selectEmergencyAlertEventInfoVo">
        select id, event_no, alarm_location, disposal_person, create_time, disposal_deadline, flood_material_photo, flood_person_photo, rescue_operation_photo, temporary_control_measure, other_hazard,circulation_state, case_finish_time, deal_in_time_state  from shcy_emergency_alert_event_info
    </sql>

    <select id="selectEmergencyAlertEventInfoList" parameterType="EmergencyAlertEventInfo" resultMap="EmergencyAlertEventInfoResult">
        <include refid="selectEmergencyAlertEventInfoVo"/>
        <where>
            <if test="eventNo != null  and eventNo != ''"> and event_no like concat('%', #{eventNo}, '%')</if>
            <if test="alarmLocation != null  and alarmLocation != ''"> and alarm_location like concat('%', #{alarmLocation}, '%')</if>
            <if test="disposalPerson != null  and disposalPerson != ''"> and disposal_person like concat('%', #{disposalPerson}, '%')</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or alarm_location like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="selectEmergencyAlertEventInfoById" parameterType="Long" resultMap="EmergencyAlertEventInfoResult">
        <include refid="selectEmergencyAlertEventInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmergencyAlertEventInfo" parameterType="EmergencyAlertEventInfo" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_emergency_alert_event_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventNo != null">event_no,</if>
            <if test="alarmLocation != null">alarm_location,</if>
            <if test="disposalPerson != null">disposal_person,</if>
            <if test="createTime != null">create_time,</if>
            <if test="disposalDeadline != null">disposal_deadline,</if>
            <if test="floodMaterialPhoto != null">flood_material_photo,</if>
            <if test="floodPersonPhoto != null">flood_person_photo,</if>
            <if test="rescueOperationPhoto != null">rescue_operation_photo,</if>
            <if test="temporaryControlMeasure != null">temporary_control_measure,</if>
            <if test="otherHazard != null">other_hazard,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventNo != null">#{eventNo},</if>
            <if test="alarmLocation != null">#{alarmLocation},</if>
            <if test="disposalPerson != null">#{disposalPerson},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="disposalDeadline != null">#{disposalDeadline},</if>
            <if test="floodMaterialPhoto != null">#{floodMaterialPhoto},</if>
            <if test="floodPersonPhoto != null">#{floodPersonPhoto},</if>
            <if test="rescueOperationPhoto != null">#{rescueOperationPhoto},</if>
            <if test="temporaryControlMeasure != null">#{temporaryControlMeasure},</if>
            <if test="otherHazard != null">#{otherHazard},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
         </trim>
    </insert>

    <update id="updateEmergencyAlertEventInfo" parameterType="EmergencyAlertEventInfo">
        update shcy_emergency_alert_event_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventNo != null">event_no = #{eventNo},</if>
            <if test="alarmLocation != null">alarm_location = #{alarmLocation},</if>
            <if test="disposalPerson != null">disposal_person = #{disposalPerson},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="disposalDeadline != null">disposal_deadline = #{disposalDeadline},</if>
            <if test="floodMaterialPhoto != null">flood_material_photo = #{floodMaterialPhoto},</if>
            <if test="floodPersonPhoto != null">flood_person_photo = #{floodPersonPhoto},</if>
            <if test="rescueOperationPhoto != null">rescue_operation_photo = #{rescueOperationPhoto},</if>
            <if test="temporaryControlMeasure != null">temporary_control_measure = #{temporaryControlMeasure},</if>
            <if test="otherHazard != null">other_hazard = #{otherHazard},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmergencyAlertEventInfoById" parameterType="Long">
        delete from shcy_emergency_alert_event_info where id = #{id}
    </delete>

    <delete id="deleteEmergencyAlertEventInfoByIds" parameterType="String">
        delete from shcy_emergency_alert_event_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="EmergencyAlertEventInfo" resultType="long">
        select count(1) from shcy_emergency_alert_event_info
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or alarm_location like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

</mapper>
