<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyAfforestMapper">

    <resultMap type="ShcyAfforest" id="ShcyAfforestResult">
        <result property="id"    column="id"    />
        <result property="afforestId"    column="afforest_id"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="street"    column="street"    />
        <result property="managementDept"    column="management_dept"    />
        <result property="maintenanceDept"    column="maintenance_dept"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="responsibilityArea" column="responsibility_area" />
        <result property="streetArborist"    column="street_arborist"    />
        <result property="residentialArborist"    column="residential_arborist"    />
        <result property="greeneryType"    column="greenery_type"    />
        <result property="communityName"    column="community_name"    />
        <result property="managerName"    column="manager_name"    />
        <result property="officePhone"    column="office_phone"    />
        <result property="managerMobile"    column="manager_mobile"    />
    </resultMap>

    <sql id="selectShcyAfforestVo">
        select id, afforest_id, type, coordinate, street, management_dept, maintenance_dept, contact_phone, create_by, create_time, update_by, update_time, responsibility_area, street_arborist, residential_arborist, greenery_type, community_name, manager_name, office_phone, manager_mobile from shcy_afforest
    </sql>

    <select id="selectShcyAfforestList" parameterType="ShcyAfforest" resultMap="ShcyAfforestResult">
        <include refid="selectShcyAfforestVo"/>
        <where>
            <if test="afforestId != null  and afforestId != ''"> and afforest_id = #{afforestId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="street != null  and street != ''"> and street = #{street}</if>
            <if test="managementDept != null  and managementDept != ''"> and management_dept = #{managementDept}</if>
            <if test="maintenanceDept != null  and maintenanceDept != ''"> and maintenance_dept = #{maintenanceDept}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="responsibilityArea != null  and responsibilityArea != ''"> and responsibility_area = #{responsibilityArea}</if>
            <if test="streetArborist != null  and streetArborist != ''"> and street_arborist = #{streetArborist}</if>
            <if test="residentialArborist != null  and residentialArborist != ''"> and residential_arborist = #{residentialArborist}</if>
            <if test="greeneryType != null  and greeneryType != ''"> and greenery_type = #{greeneryType}</if>
            <if test="communityName != null  and communityName != ''"> and community_name like concat('%', #{communityName}, '%')</if>
            <if test="managerName != null  and managerName != ''"> and manager_name like concat('%', #{managerName}, '%')</if>
            <if test="officePhone != null  and officePhone != ''"> and office_phone = #{officePhone}</if>
            <if test="managerMobile != null  and managerMobile != ''"> and manager_mobile = #{managerMobile}</if>
        </where>
    </select>

    <select id="selectShcyAfforestById" parameterType="Long" resultMap="ShcyAfforestResult">
        <include refid="selectShcyAfforestVo"/>
        where id = #{id}
    </select>

    <insert id="insertShcyAfforest" parameterType="ShcyAfforest" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_afforest
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="afforestId != null">afforest_id,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="street != null">street,</if>
            <if test="managementDept != null">management_dept,</if>
            <if test="maintenanceDept != null">maintenance_dept,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="responsibilityArea != null">responsibility_area,</if>
            <if test="streetArborist != null">street_arborist,</if>
            <if test="residentialArborist != null">residential_arborist,</if>
            <if test="greeneryType != null">greenery_type,</if>
            <if test="communityName != null">community_name,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="officePhone != null">office_phone,</if>
            <if test="managerMobile != null">manager_mobile,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="afforestId != null">#{afforestId},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="street != null">#{street},</if>
            <if test="managementDept != null">#{managementDept},</if>
            <if test="maintenanceDept != null">#{maintenanceDept},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="responsibilityArea != null">#{responsibilityArea},</if>
            <if test="streetArborist != null">#{streetArborist},</if>
            <if test="residentialArborist != null">#{residentialArborist},</if>
            <if test="greeneryType != null">#{greeneryType},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="officePhone != null">#{officePhone},</if>
            <if test="managerMobile != null">#{managerMobile},</if>
         </trim>
    </insert>

    <update id="updateShcyAfforest" parameterType="ShcyAfforest">
        update shcy_afforest
        <trim prefix="SET" suffixOverrides=",">
            <if test="afforestId != null">afforest_id = #{afforestId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="street != null">street = #{street},</if>
            <if test="managementDept != null">management_dept = #{managementDept},</if>
            <if test="maintenanceDept != null">maintenance_dept = #{maintenanceDept},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="responsibilityArea != null">responsibility_area = #{responsibilityArea},</if>
            <if test="streetArborist != null">street_arborist = #{streetArborist},</if>
            <if test="residentialArborist != null">residential_arborist = #{residentialArborist},</if>
            <if test="greeneryType != null">greenery_type = #{greeneryType},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="officePhone != null">office_phone = #{officePhone},</if>
            <if test="managerMobile != null">manager_mobile = #{managerMobile},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyAfforestById" parameterType="Long">
        delete from shcy_afforest where id = #{id}
    </delete>

    <delete id="deleteShcyAfforestByIds" parameterType="String">
        delete from shcy_afforest where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
