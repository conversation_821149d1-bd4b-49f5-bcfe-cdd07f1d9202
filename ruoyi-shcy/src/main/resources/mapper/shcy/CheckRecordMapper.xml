<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.CheckRecordMapper">

    <resultMap type="CheckRecord" id="CheckRecordResult">
        <result property="id"    column="id"    />
        <result property="shopCheckId"    column="shop_check_id"    />
        <result property="shopId"    column="shop_id"    />
<!--        <result property="shopName"    column="shop_name"    />-->
        <result property="checkDate"    column="check_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="checkStatus"    column="check_status"    />
        <association property="shop"    column="shop_id" javaType="Shop" resultMap="ShopResult" />
    </resultMap>

    <resultMap type="Shop" id="ShopResult">
        <result property="id"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopLicense"    column="shop_license"    />
        <result property="shopAddress"    column="shop_address"    />
        <result property="shopCreditCode"    column="shop_credit_code"    />
        <result property="shopRegisterAddress"    column="shop_register_address"    />
        <result property="shopOperatingAddress"    column="shop_operating_address"    />
        <result property="shopCategory"    column="shop_category"    />
        <result property="shopSubcategory"    column="shop_subcategory"    />
        <result property="shopLittlecategory" column="shop_littlecategory"/>
        <result property="shopContract"    column="shop_contract"    />
        <result property="shopContactPhone"    column="shop_contact_phone"    />
        <result property="shopHouseOwnership"    column="shop_house_ownership"    />
        <result property="isStateAssets"    column="is_state_assets"    />
        <result property="shopLandlordContract"    column="shop_landlord_contract"    />

        <result property="shoplAndlordPhone"    column="shopl_andlord_phone"    />
        <result property="shopGoverningProperty"    column="shop_governing_property"    />
        <result property="isSameCommunityProperty"    column="is_same_community_property"    />
        <result property="shopPropertyContact"    column="shop_property_contact"    />
        <result property="shopPropertyPhone"    column="shop_property_phone"    />
        <result property="isSiteCode"    column="is_site_code"    />
        <result property="shopEmployeeNum"    column="shop_employee_num"    />
        <result property="shopStatus"    column="shop_status"    />
        <result property="shopRemark"    column="shop_remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shopDeparmentId"    column="shop_deparment_id"    />
        <result property="isOpen"        column="is_open" />
        <result property="oddNumber"     column="odd_number"/>
        <result property="shopArea"      column="shop_area"    />
        <result property="supervisionStatus"    column="supervision_status"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="noticeType" column="notice_type"/>
        <result property="type" column="type"/>
        <result property="coordinate" column="coordinate"/>
        <result property="isAccommodation"    column="is_accommodation"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="legalPersonContact"    column="legal_person_contact"    />
        <result property="roadSection"    column="road_section"    />
        <result property="roadSectionStart"    column="road_section_start"    />
        <result property="roadSectionEnd"    column="road_section_end"    />
        <result property="informationStatus"    column="information_status"    />
        <result property="streetChief"    column="street_chief"    />
        <result property="chiefPhone"    column="chief_phone"    />

        <result property="insuranceLiabilityContact"    column="insurance_liability_contact"    />
        <result property="insuranceLiabilityPhone"    column="insurance_liability_phone"    />
        <result property="insuranceLiabilityFlag"    column="insurance_liability_flag"    />

        <result property="type" column="type"/>
        <result property="coordinate" column="coordinate"/>
    </resultMap>



    <sql id="selectCheckRecordShopVo">
        select scr.id, scr.shop_check_id, scr.shop_id, scr.check_date, scr.create_by, scr.create_time,
               scr.update_time, scr.check_status, ss.id as shop_id,  ss.shop_name,ss.shop_license,
               ss.shop_address,ss.shop_credit_code,ss.shop_register_address,ss.shop_operating_address,
               ss.shop_category,ss.shop_subcategory,ss.shop_littlecategory,ss.shop_contract,ss.shop_contact_phone,
               ss.shop_house_ownership,ss.is_state_assets,ss.shop_landlord_contract,ss.shopl_andlord_phone,
               ss.shop_governing_property,ss.is_same_community_property,ss.shop_property_contact,ss.shop_property_phone,
               ss.type,ss.coordinate
        from shop_check_record as scr
        left join shcy_shop as ss   on  scr.shop_id = ss.id
    </sql>

    <sql id="selectCheckRecordVo">
        select id, shop_check_id, shop_id, shop_name, check_date, create_by, create_time, update_time, check_status from shop_check_record
    </sql>

    <select id="selectCheckRecordList" parameterType="CheckRecord" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordVo"/>
        <where>
            <if test="shopCheckId != null "> and shop_check_id = #{shopCheckId}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
<!--            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>-->
            <if test="checkDate != null "> and check_date = #{checkDate}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
        </where>
    </select>

    <select id="selectCheckRecordById" parameterType="Long" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectCheckRecordByShopIdAndCheckDate"  resultMap="CheckRecordResult">
        <include refid="selectCheckRecordShopVo"/>
        where shop_id = #{shopId } and check_date = #{checkDate} limit 1
    </select>

    <select id="selectCheckedShopNum" parameterType="CheckRecord" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordShopVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.flag != null and params.flag != '' and params.flag== 1 ">
                AND DATE_FORMAT(scr.create_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
            </if>
            <if test="1 == 1"> and check_status in ('检查通过','检查未通过')</if>
        </where>
    </select>

    <select id="selectCheckAndPassShop" parameterType="CheckRecord" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordShopVo"/>

        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.flag != null and params.flag != '' and params.flag== 1 ">
                AND DATE_FORMAT(scr.create_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
            </if>
            <if test="1 == 1"> and check_status = "检查通过"</if>
        </where>

    </select>


    <select id="selectCheckAndNoPassShop"  parameterType="CheckRecord" resultMap="CheckRecordResult" >
        <include refid="selectCheckRecordShopVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.flag != null and params.flag != '' and params.flag== 1 ">
                AND DATE_FORMAT(scr.create_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
            </if>
            <if test="1 == 1"> and check_status = "检查未通过"</if>
        </where>
    </select>

    <select id="selectNoCheckShop" parameterType="CheckRecord" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
           <if test="1 == 1"> and check_status = "未检查"</if>
        </where>
    </select>


    <insert id="insertCheckRecord" parameterType="CheckRecord" useGeneratedKeys="true" keyProperty="id">
        insert into shop_check_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopCheckId != null">shop_check_id,</if>
            <if test="shopId != null">shop_id,</if>
<!--            <if test="shopName != null">shop_name,</if>-->
            <if test="checkDate != null">check_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="checkStatus != null">check_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopCheckId != null">#{shopCheckId},</if>
            <if test="shopId != null">#{shopId},</if>
<!--            <if test="shopName != null">#{shopName},</if>-->
            <if test="checkDate != null">#{checkDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
         </trim>
    </insert>

    <update id="updateCheckRecord" parameterType="CheckRecord">
        update shop_check_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopCheckId != null">shop_check_id = #{shopCheckId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
<!--            <if test="shopName != null">shop_name = #{shopName},</if>-->
            <if test="checkDate != null">check_date = #{checkDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCheckRecordById" parameterType="Long">
        delete from shop_check_record where id = #{id}
    </delete>

    <delete id="deleteCheckRecordByIds" parameterType="String">
        delete from shop_check_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCheckRecordShopList" resultMap="CheckRecordResult">
        <include refid="selectCheckRecordShopVo"/>
    </select>
</mapper>
