<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.RoadStreetChiefMapper">

    <resultMap type="RoadStreetChief" id="RoadStreetChiefResult">
        <result property="id"    column="id"    />
        <result property="streetChief"    column="street_chief"    />
        <result property="chiefPhone"    column="chief_phone"    />
        <result property="roadName"    column="road_name"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="streetSubChief"    column="street_sub_chief"    />
        <result property="subChiefPhone"    column="sub_chief_phone"    />
        <result property="type2"    column="type2" />
        <result property="coordinate2"    column="coordinate2" />
        <result property="roadId" column="road_id" />
        <result property="isKeynoteRoad" column="is_keynote_road" />
        <result property="streetThirdChief"    column="street_third_chief"    />
        <result property="thirdChiefPhone"    column="third_chief_phone"    />
    </resultMap>

    <sql id="selectRoadStreetChiefVo">
        select id, street_chief, chief_phone, road_name, type, coordinate, create_time, create_by, update_time,street_sub_chief,sub_chief_phone,type2,coordinate2,road_id,is_keynote_road, street_third_chief, third_chief_phone from shcy_road_street_chief
    </sql>

    <select id="selectRoadStreetChiefList" parameterType="RoadStreetChief" resultMap="RoadStreetChiefResult">
        <include refid="selectRoadStreetChiefVo"/>
        <where>
            <if test="streetChief != null  and streetChief != ''"> and street_chief = #{streetChief}</if>
            <if test="chiefPhone != null  and chiefPhone != ''"> and chief_phone = #{chiefPhone}</if>
            <if test="roadName != null  and roadName != ''"> and road_name like concat('%', #{roadName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="streetSubChief != null  and streetSubChief != ''"> and street_sub_chief = #{streetSubChief}</if>
            <if test="subChiefPhone != null  and subChiefPhone != ''"> and sub_chief_phone = #{subChiefPhone}</if>
            <if test="type2 != null  and type2 != ''"> and type2 = #{type2}</if>
            <if test="coordinate2 != null  and coordinate2 != ''"> and coordinate2 = #{coordinate2}</if>
            <if test="roadId != null  and roadId != ''"> and road_id = #{roadId}</if>
            <if test="isKeynoteRoad != null  and isKeynoteRoad != ''"> and is_keynote_road = #{isKeynoteRoad}</if>
            <if test="streetThirdChief != null  and streetThirdChief != ''"> and street_third_chief = #{streetThirdChief}</if>
            <if test="thirdChiefPhone != null  and thirdChiefPhone != ''"> and third_chief_phone = #{thirdChiefPhone}</if>
        </where>
    </select>

    <select id="selectRoadStreetChiefById" parameterType="Long" resultMap="RoadStreetChiefResult">
        <include refid="selectRoadStreetChiefVo"/>
        where id = #{id}
    </select>

    <insert id="insertRoadStreetChief" parameterType="RoadStreetChief" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_road_street_chief
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="streetChief != null">street_chief,</if>
            <if test="chiefPhone != null">chief_phone,</if>
            <if test="roadName != null">road_name,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="streetSubChief != null">street_sub_chief,</if>
            <if test="subChiefPhone != null">sub_chief_phone,</if>
            <if test="type2 != null">type2,</if>
            <if test="coordinate2 != null">coordinate2,</if>
            <if test="roadId != null">road_id,</if>
            <if test="isKeynoteRoad != null">is_keynote_road,</if>
            <if test="streetThirdChief != null">street_third_chief,</if>
            <if test="thirdChiefPhone != null">third_chief_phone,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="streetChief != null">#{streetChief},</if>
            <if test="chiefPhone != null">#{chiefPhone},</if>
            <if test="roadName != null">#{roadName},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type2 != null">#{type2},</if>
            <if test="coordinate2 != null">#{coordinate2},</if>
            <if test="roadId != null">#{roadId},</if>
            <if test="isKeynoteRoad != null">#{isKeynoteRoad},</if>
            <if test="streetThirdChief != null">#{streetThirdChief},</if>
            <if test="thirdChiefPhone != null">#{thirdChiefPhone},</if>
         </trim>
    </insert>

    <update id="updateRoadStreetChief" parameterType="RoadStreetChief">
        update shcy_road_street_chief
        <trim prefix="SET" suffixOverrides=",">
            <if test="streetChief != null">street_chief = #{streetChief},</if>
            <if test="chiefPhone != null">chief_phone = #{chiefPhone},</if>
            <if test="roadName != null">road_name = #{roadName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="streetSubChief != null">street_sub_chief = #{streetSubChief},</if>
            <if test="subChiefPhone != null">sub_chief_phone = #{subChiefPhone},</if>
            <if test="type2 != null">type2 = #{type2},</if>
            <if test="coordinate2 != null">coordinate2 = #{coordinate2},</if>
            <if test="roadId != null">road_id = #{roadId},</if>
            <if test="isKeynoteRoad != null">is_keynote_road = #{isKeynoteRoad},</if>
            <if test="streetThirdChief != null">street_third_chief = #{streetThirdChief},</if>
            <if test="thirdChiefPhone != null">third_chief_phone = #{thirdChiefPhone},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRoadStreetChiefById" parameterType="Long">
        delete from shcy_road_street_chief where id = #{id}
    </delete>

    <delete id="deleteRoadStreetChiefByIds" parameterType="String">
        delete from shcy_road_street_chief where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
