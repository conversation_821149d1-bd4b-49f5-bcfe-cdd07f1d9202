<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.GridLeaderMapper">
    
    <resultMap type="GridLeader" id="GridLeaderResult">
        <result property="id"    column="id"    />
        <result property="gridId"    column="grid_id"    />
        <result property="name"    column="name"    />
        <result property="position"    column="position"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectGridLeaderVo">
        select id, grid_id, name, position, order_num from shcy_grid_leader
    </sql>

    <select id="selectGridLeaderList" parameterType="GridLeader" resultMap="GridLeaderResult">
        <include refid="selectGridLeaderVo"/>
        <where>  
            <if test="gridId != null "> and grid_id = #{gridId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectGridLeaderById" parameterType="Long" resultMap="GridLeaderResult">
        <include refid="selectGridLeaderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGridLeader" parameterType="GridLeader" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_grid_leader
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gridId != null">grid_id,</if>
            <if test="name != null">name,</if>
            <if test="position != null">position,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gridId != null">#{gridId},</if>
            <if test="name != null">#{name},</if>
            <if test="position != null">#{position},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateGridLeader" parameterType="GridLeader">
        update shcy_grid_leader
        <trim prefix="SET" suffixOverrides=",">
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="position != null">position = #{position},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGridLeaderById" parameterType="Long">
        delete from shcy_grid_leader where id = #{id}
    </delete>

    <delete id="deleteGridLeaderByIds" parameterType="String">
        delete from shcy_grid_leader where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>