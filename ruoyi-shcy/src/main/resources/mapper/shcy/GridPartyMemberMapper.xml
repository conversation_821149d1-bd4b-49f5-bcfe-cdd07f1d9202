<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.GridPartyMemberMapper">
    
    <resultMap type="GridPartyMember" id="GridPartyMemberResult">
        <result property="id"    column="id"    />
        <result property="gridId"    column="grid_id"    />
        <result property="name"    column="name"    />
        <result property="position"    column="position"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectGridPartyMemberVo">
        select id, grid_id, name, position, order_num from shcy_grid_party_member
    </sql>

    <select id="selectGridPartyMemberList" parameterType="GridPartyMember" resultMap="GridPartyMemberResult">
        <include refid="selectGridPartyMemberVo"/>
        <where>  
            <if test="gridId != null "> and grid_id = #{gridId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectGridPartyMemberById" parameterType="Long" resultMap="GridPartyMemberResult">
        <include refid="selectGridPartyMemberVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGridPartyMember" parameterType="GridPartyMember" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_grid_party_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gridId != null">grid_id,</if>
            <if test="name != null">name,</if>
            <if test="position != null">position,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gridId != null">#{gridId},</if>
            <if test="name != null">#{name},</if>
            <if test="position != null">#{position},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateGridPartyMember" parameterType="GridPartyMember">
        update shcy_grid_party_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="position != null">position = #{position},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGridPartyMemberById" parameterType="Long">
        delete from shcy_grid_party_member where id = #{id}
    </delete>

    <delete id="deleteGridPartyMemberByIds" parameterType="String">
        delete from shcy_grid_party_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>