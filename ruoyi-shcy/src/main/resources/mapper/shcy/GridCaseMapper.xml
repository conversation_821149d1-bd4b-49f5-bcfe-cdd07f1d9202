<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.GridCaseMapper">
    
    <resultMap type="GridCase" id="GridCaseResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="caseId"    column="case_id"    />
        <result property="problemSource"    column="problem_source"    />
        <result property="discoveryTime"    column="discovery_time"    />
        <result property="currentStatus"    column="current_status"    />
        <result property="hotlineRecording"    column="hotline_recording"    />
        <result property="mainDepartment"    column="main_department"    />
        <result property="caseCategory"    column="case_category"    />
        <result property="complainant"    column="complainant"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="street"    column="street"    />
        <result property="village"    column="village"    />
        <result property="grid"    column="grid"    />
        <result property="coordinates"    column="coordinates"    />
        <result property="caseEvaluation"    column="case_evaluation"    />
        <result property="occurrenceAddress"    column="occurrence_address"    />
        <result property="region"    column="region"    />
        <result property="problemDescription"    column="problem_description"    />
        <result property="acceptPerson"    column="accept_person"    />
        <result property="acceptTime"    column="accept_time"    />
        <result property="acceptOpinion"    column="accept_opinion"    />
        <result property="filingPerson"    column="filing_person"    />
        <result property="filingTime"    column="filing_time"    />
        <result property="filingOpinion"    column="filing_opinion"    />
    </resultMap>

    <sql id="selectGridCaseVo">
        select id, task_id, case_id, problem_source, discovery_time, current_status, hotline_recording, main_department, case_category, complainant, contact_person, contact_phone, street, village, grid, coordinates, case_evaluation, occurrence_address, region, problem_description, accept_person, accept_time, accept_opinion, filing_person, filing_time, filing_opinion from shcy_grid_case
    </sql>

    <select id="selectGridCaseList" parameterType="GridCase" resultMap="GridCaseResult">
        <include refid="selectGridCaseVo"/>
        <where>  
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="caseId != null  and caseId != ''"> and case_id = #{caseId}</if>
        </where>
    </select>
    
    <select id="selectGridCaseById" parameterType="Long" resultMap="GridCaseResult">
        <include refid="selectGridCaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGridCase" parameterType="GridCase" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_grid_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="caseId != null">case_id,</if>
            <if test="problemSource != null">problem_source,</if>
            <if test="discoveryTime != null">discovery_time,</if>
            <if test="currentStatus != null">current_status,</if>
            <if test="hotlineRecording != null">hotline_recording,</if>
            <if test="mainDepartment != null">main_department,</if>
            <if test="caseCategory != null">case_category,</if>
            <if test="complainant != null">complainant,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="street != null">street,</if>
            <if test="village != null">village,</if>
            <if test="grid != null">grid,</if>
            <if test="coordinates != null">coordinates,</if>
            <if test="caseEvaluation != null">case_evaluation,</if>
            <if test="occurrenceAddress != null">occurrence_address,</if>
            <if test="region != null">region,</if>
            <if test="problemDescription != null">problem_description,</if>
            <if test="acceptPerson != null">accept_person,</if>
            <if test="acceptTime != null">accept_time,</if>
            <if test="acceptOpinion != null">accept_opinion,</if>
            <if test="filingPerson != null">filing_person,</if>
            <if test="filingTime != null">filing_time,</if>
            <if test="filingOpinion != null">filing_opinion,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="caseId != null">#{caseId},</if>
            <if test="problemSource != null">#{problemSource},</if>
            <if test="discoveryTime != null">#{discoveryTime},</if>
            <if test="currentStatus != null">#{currentStatus},</if>
            <if test="hotlineRecording != null">#{hotlineRecording},</if>
            <if test="mainDepartment != null">#{mainDepartment},</if>
            <if test="caseCategory != null">#{caseCategory},</if>
            <if test="complainant != null">#{complainant},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="street != null">#{street},</if>
            <if test="village != null">#{village},</if>
            <if test="grid != null">#{grid},</if>
            <if test="coordinates != null">#{coordinates},</if>
            <if test="caseEvaluation != null">#{caseEvaluation},</if>
            <if test="occurrenceAddress != null">#{occurrenceAddress},</if>
            <if test="region != null">#{region},</if>
            <if test="problemDescription != null">#{problemDescription},</if>
            <if test="acceptPerson != null">#{acceptPerson},</if>
            <if test="acceptTime != null">#{acceptTime},</if>
            <if test="acceptOpinion != null">#{acceptOpinion},</if>
            <if test="filingPerson != null">#{filingPerson},</if>
            <if test="filingTime != null">#{filingTime},</if>
            <if test="filingOpinion != null">#{filingOpinion},</if>
         </trim>
    </insert>

    <update id="updateGridCase" parameterType="GridCase">
        update shcy_grid_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="caseId != null">case_id = #{caseId},</if>
            <if test="problemSource != null">problem_source = #{problemSource},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="currentStatus != null">current_status = #{currentStatus},</if>
            <if test="hotlineRecording != null">hotline_recording = #{hotlineRecording},</if>
            <if test="mainDepartment != null">main_department = #{mainDepartment},</if>
            <if test="caseCategory != null">case_category = #{caseCategory},</if>
            <if test="complainant != null">complainant = #{complainant},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="street != null">street = #{street},</if>
            <if test="village != null">village = #{village},</if>
            <if test="grid != null">grid = #{grid},</if>
            <if test="coordinates != null">coordinates = #{coordinates},</if>
            <if test="caseEvaluation != null">case_evaluation = #{caseEvaluation},</if>
            <if test="occurrenceAddress != null">occurrence_address = #{occurrenceAddress},</if>
            <if test="region != null">region = #{region},</if>
            <if test="problemDescription != null">problem_description = #{problemDescription},</if>
            <if test="acceptPerson != null">accept_person = #{acceptPerson},</if>
            <if test="acceptTime != null">accept_time = #{acceptTime},</if>
            <if test="acceptOpinion != null">accept_opinion = #{acceptOpinion},</if>
            <if test="filingPerson != null">filing_person = #{filingPerson},</if>
            <if test="filingTime != null">filing_time = #{filingTime},</if>
            <if test="filingOpinion != null">filing_opinion = #{filingOpinion},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGridCaseById" parameterType="Long">
        delete from shcy_grid_case where id = #{id}
    </delete>

    <delete id="deleteGridCaseByIds" parameterType="String">
        delete from shcy_grid_case where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>