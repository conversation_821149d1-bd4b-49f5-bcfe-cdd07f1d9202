<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.FloodControlKeyAreaMapper">
    
    <resultMap type="FloodControlKeyArea" id="FloodControlKeyAreaResult">
        <result property="id"    column="id"    />
        <result property="riskSite"    column="risk_site"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="riskType"    column="risk_type"    />
        <result property="vulnerableArea"    column="vulnerable_area"    />
        <result property="areaSize"    column="area_size"    />
        <result property="riskOverview"    column="risk_overview"    />
        <result property="affectedRange"    column="affected_range"    />
        <result property="controlPeriod"    column="control_period"    />
        <result property="riskRating"    column="risk_rating"    />
        <result property="countermeasure1"    column="countermeasure1"    />
        <result property="startCondition1"    column="start_condition1"    />
        <result property="responsibleDepartment1"    column="responsible_department1"    />
        <result property="contactPerson1"    column="contact_person1"    />
        <result property="contactPhone1"    column="contact_phone1"    />
        <result property="countermeasure2"    column="countermeasure2"    />
        <result property="startCondition2"    column="start_condition2"    />
        <result property="responsibleDepartment2"    column="responsible_department2"    />
        <result property="contactPerson2"    column="contact_person2"    />
        <result property="contactPhone2"    column="contact_phone2"    />
        <result property="remark"    column="remark"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="fillColor"    column="fill_color"    />
        <result property="outlineColor"    column="outline_color"    />
        <result property="cameras" column="cameras" />
    </resultMap>

    <sql id="selectFloodControlKeyAreaVo">
        select id, risk_site, risk_level, risk_type, vulnerable_area, area_size, risk_overview, affected_range, control_period, risk_rating, countermeasure1, start_condition1, responsible_department1, contact_person1, contact_phone1, countermeasure2, start_condition2, responsible_department2, contact_person2, contact_phone2, remark, type, coordinate, fill_color, outline_color, cameras from shcy_flood_control_key_area
    </sql>

    <select id="selectFloodControlKeyAreaList" parameterType="FloodControlKeyArea" resultMap="FloodControlKeyAreaResult">
        <include refid="selectFloodControlKeyAreaVo"/>
        <where>  
            <if test="riskSite != null  and riskSite != ''"> and risk_site like concat('%', #{riskSite}, '%')</if>
        </where>
    </select>
    
    <select id="selectFloodControlKeyAreaById" parameterType="Long" resultMap="FloodControlKeyAreaResult">
        <include refid="selectFloodControlKeyAreaVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFloodControlKeyArea" parameterType="FloodControlKeyArea" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_flood_control_key_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="riskSite != null">risk_site,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="riskType != null">risk_type,</if>
            <if test="vulnerableArea != null">vulnerable_area,</if>
            <if test="areaSize != null">area_size,</if>
            <if test="riskOverview != null">risk_overview,</if>
            <if test="affectedRange != null">affected_range,</if>
            <if test="controlPeriod != null">control_period,</if>
            <if test="riskRating != null">risk_rating,</if>
            <if test="countermeasure1 != null">countermeasure1,</if>
            <if test="startCondition1 != null">start_condition1,</if>
            <if test="responsibleDepartment1 != null">responsible_department1,</if>
            <if test="contactPerson1 != null">contact_person1,</if>
            <if test="contactPhone1 != null">contact_phone1,</if>
            <if test="countermeasure2 != null">countermeasure2,</if>
            <if test="startCondition2 != null">start_condition2,</if>
            <if test="responsibleDepartment2 != null">responsible_department2,</if>
            <if test="contactPerson2 != null">contact_person2,</if>
            <if test="contactPhone2 != null">contact_phone2,</if>
            <if test="remark != null">remark,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="fillColor != null">fill_color,</if>
            <if test="outlineColor != null">outline_color,</if>
            <if test="cameras != null">cameras,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="riskSite != null">#{riskSite},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="riskType != null">#{riskType},</if>
            <if test="vulnerableArea != null">#{vulnerableArea},</if>
            <if test="areaSize != null">#{areaSize},</if>
            <if test="riskOverview != null">#{riskOverview},</if>
            <if test="affectedRange != null">#{affectedRange},</if>
            <if test="controlPeriod != null">#{controlPeriod},</if>
            <if test="riskRating != null">#{riskRating},</if>
            <if test="countermeasure1 != null">#{countermeasure1},</if>
            <if test="startCondition1 != null">#{startCondition1},</if>
            <if test="responsibleDepartment1 != null">#{responsibleDepartment1},</if>
            <if test="contactPerson1 != null">#{contactPerson1},</if>
            <if test="contactPhone1 != null">#{contactPhone1},</if>
            <if test="countermeasure2 != null">#{countermeasure2},</if>
            <if test="startCondition2 != null">#{startCondition2},</if>
            <if test="responsibleDepartment2 != null">#{responsibleDepartment2},</if>
            <if test="contactPerson2 != null">#{contactPerson2},</if>
            <if test="contactPhone2 != null">#{contactPhone2},</if>
            <if test="remark != null">#{remark},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="fillColor != null">#{fillColor},</if>
            <if test="outlineColor != null">#{outlineColor},</if>
            <if test="cameras != null">#{cameras},</if>
         </trim>
    </insert>

    <update id="updateFloodControlKeyArea" parameterType="FloodControlKeyArea">
        update shcy_flood_control_key_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="riskSite != null">risk_site = #{riskSite},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="riskType != null">risk_type = #{riskType},</if>
            <if test="vulnerableArea != null">vulnerable_area = #{vulnerableArea},</if>
            <if test="areaSize != null">area_size = #{areaSize},</if>
            <if test="riskOverview != null">risk_overview = #{riskOverview},</if>
            <if test="affectedRange != null">affected_range = #{affectedRange},</if>
            <if test="controlPeriod != null">control_period = #{controlPeriod},</if>
            <if test="riskRating != null">risk_rating = #{riskRating},</if>
            <if test="countermeasure1 != null">countermeasure1 = #{countermeasure1},</if>
            <if test="startCondition1 != null">start_condition1 = #{startCondition1},</if>
            <if test="responsibleDepartment1 != null">responsible_department1 = #{responsibleDepartment1},</if>
            <if test="contactPerson1 != null">contact_person1 = #{contactPerson1},</if>
            <if test="contactPhone1 != null">contact_phone1 = #{contactPhone1},</if>
            <if test="countermeasure2 != null">countermeasure2 = #{countermeasure2},</if>
            <if test="startCondition2 != null">start_condition2 = #{startCondition2},</if>
            <if test="responsibleDepartment2 != null">responsible_department2 = #{responsibleDepartment2},</if>
            <if test="contactPerson2 != null">contact_person2 = #{contactPerson2},</if>
            <if test="contactPhone2 != null">contact_phone2 = #{contactPhone2},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="fillColor != null">fill_color = #{fillColor},</if>
            <if test="outlineColor != null">outline_color = #{outlineColor},</if>
            <if test="cameras != null">cameras = #{cameras},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFloodControlKeyAreaById" parameterType="Long">
        delete from shcy_flood_control_key_area where id = #{id}
    </delete>

    <delete id="deleteFloodControlKeyAreaByIds" parameterType="String">
        delete from shcy_flood_control_key_area where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>