<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SensorDataMapper">
    
    <resultMap type="SensorData" id="SensorDataResult">
        <result property="id"    column="id"    />
        <result property="imei"    column="imei"    />
        <result property="value"    column="value"    />
        <result property="unit"    column="unit"    />
        <result property="updateTime"    column="update_time"    />
        <association property="sensorDevice"    column="imei" javaType="SensorDevice" resultMap="SensorDeviceResult" />
    </resultMap>

    <resultMap id="SensorDeviceResult" type="SensorDevice">
        <result property="deviceName"    column="device_name"    />
        <result property="sensorimeiTypeName"    column="sensorimei_type_name"    />
        <result property="status"    column="status"    />
        <result property="imei"    column="imei"    />
    </resultMap>

    <sql id="selectSensorDataVo">
        select id, imei, value, unit, update_time from shcy_sensor_data
    </sql>

    <select id="selectSensorDataList1" parameterType="SensorData" resultMap="SensorDataResult">
        select s.id, s.imei, s.value, s.unit, s.update_time,
        d.device_name, d.sensorimei_type_name, d.status
        from shcy_sensor_data s
        left join shcy_sensor_device d on s.imei = d.imei
        <where>
            <if test="imei != null and imei != ''"> and s.imei = #{imei}</if>
        </where>
    </select>

    <select id="selectSensorDataList" parameterType="SensorData" resultMap="SensorDataResult">
        <include refid="selectSensorDataVo"/>
        <where>  
            <if test="imei != null  and imei != ''"> and imei = #{imei}</if>
        </where>
    </select>
    
    <select id="selectSensorDataById" parameterType="Long" resultMap="SensorDataResult">
        <include refid="selectSensorDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSensorData" parameterType="SensorData" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_sensor_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imei != null">imei,</if>
            <if test="value != null">value,</if>
            <if test="unit != null">unit,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imei != null">#{imei},</if>
            <if test="value != null">#{value},</if>
            <if test="unit != null">#{unit},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSensorData" parameterType="SensorData">
        update shcy_sensor_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="imei != null">imei = #{imei},</if>
            <if test="value != null">value = #{value},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSensorDataById" parameterType="Long">
        delete from shcy_sensor_data where id = #{id}
    </delete>

    <delete id="deleteSensorDataByIds" parameterType="String">
        delete from shcy_sensor_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>