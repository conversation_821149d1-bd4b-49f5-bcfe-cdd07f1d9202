<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyWghMapper">
    
    <resultMap type="ShcyWgh" id="ShcyWghResult">
        <result property="id"    column="id"    />
        <result property="wghId"    column="wgh_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="infobcName"    column="infobc_name"    />
        <result property="infoscName"    column="infosc_name"    />
        <result property="address"    column="address"    />
        <result property="description"    column="description"    />
        <result property="dealBy"    column="deal_by"    />
        <result property="dealPhoto"    column="deal_photo"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
        <result property="createTime"    column="create_time"    />
        <result property="caseEndTime"    column="case_end_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="yqReason"    column="yq_reason"    />
        <result property="tdReason"    column="td_reason"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="discoverTime"    column="discover_time"    />
        <result property="lianTime"    column="lian_time"    />
        <result property="reporter"    column="reporter"    />
        <result property="imagefilename"    column="imagefilename"    />
        <result property="memo"    column="memo"    />
    </resultMap>

    <sql id="selectShcyWghVo">
        select id, wgh_id,task_id,memo,lian_time,discover_time,imagefilename,reporter,yq_reason,td_reason, infobc_name, infosc_name, address, description, deal_by, deal_photo, circulation_state, create_by, update_by, deal_in_time_state, create_time, case_end_time, update_time, case_finish_time from shcy_wgh
    </sql>

    <select id="selectShcyWghList" parameterType="ShcyWgh" resultMap="ShcyWghResult">
        <include refid="selectShcyWghVo"/>
        <where>  
            <if test="wghId != null "> and wgh_id = #{wghId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="memo != null "> and memo = #{memo}</if>
            <if test="lianTime != null "> and lian_time = #{lianTime}</if>
            <if test="reporter != null "> and reporter = #{reporter}</if>
            <if test="imagefilename != null "> and imagefilename = #{imagefilename}</if>
            <if test="infobcName != null  and infobcName != ''"> and infobc_name  = #{infobcName}</if>
            <if test="infoscName != null  and infoscName != ''"> and infosc_name = #{infoscName}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="yqReason != null  and yqReason != ''"> and yq_reason = #{yqReason}</if>
            <if test="tdReason != null  and tdReason != ''"> and td_reason = #{tdReason}</if>
            <if test="dealBy != null  and dealBy != ''"> and deal_by = #{dealBy}</if>
            <if test="dealPhoto != null  and dealPhoto != ''"> and deal_photo = #{dealPhoto}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="dealInTimeState != null  and dealInTimeState != ''"> and deal_in_time_state = #{dealInTimeState}</if>
            <if test="caseEndTime != null "> and case_end_time = #{caseEndTime}</if>
            <if test="caseFinishTime != null "> and case_finish_time = #{caseFinishTime}</if>
            <if test="discoverTime != null "> and discover_time = #{discoverTime}</if>
            <if test="params.czFlag != null and  params.czFlag == 1 "> and circulation_state in ('1', '2')</if>
            <if test="params.czFlag != null and  params.czFlag == 2 "> and circulation_state = '0' </if>
            <if test="params.czFlag != null and  params.czFlag == 3 "> and circulation_state = '4' </if>
            <if test="params.czFlag != null and  params.czFlag == 4 "> and circulation_state = '3' </if>
            <if test="params.czFlag != null and  params.czFlag == 6 "> and circulation_state = '6' </if>
            <if test="params.allFlag != null and  params.allFlag == 1 "> and circulation_state in ('0','1','2','4','6') </if>
            <if test="params.allFlag != null and  params.allFlag == 6 "> and circulation_state in ('4','6') </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(lian_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(lian_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by lian_time desc
    </select>
    
    <select id="selectShcyWghById" parameterType="Long" resultMap="ShcyWghResult">
        <include refid="selectShcyWghVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShcyWgh" parameterType="ShcyWgh" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_wgh
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wghId != null">wgh_id,</if>
            <if test="taskId != null "> task_id,</if>
            <if test="imagefilename != null "> imagefilename,</if>
            <if test="memo != null "> memo,</if>
            <if test="lianTime != null "> lian_time,</if>
            <if test="reporter != null "> reporter,</if>
            <if test="yqReason != null "> yq_reason,</if>
            <if test="tdReason != null "> td_reason,</if>
            <if test="infobcName != null">infobc_name,</if>
            <if test="infoscName != null">infosc_name,</if>
            <if test="discoverTime != null">discover_time,</if>
            <if test="address != null">address,</if>
            <if test="description != null">description,</if>
            <if test="dealBy != null">deal_by,</if>
            <if test="dealPhoto != null">deal_photo,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="caseEndTime != null">case_end_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wghId != null">#{wghId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="imagefilename != null">#{imagefilename},</if>
            <if test="memo != null">#{memo},</if>
            <if test="lianTime != null">#{lianTime},</if>
            <if test="reporter != null">#{reporter},</if>
            <if test="yqReason != null">#{yqReason},</if>
            <if test="tdReason != null">#{tdReason},</if>
            <if test="infobcName != null">#{infobcName},</if>
            <if test="infoscName != null">#{infoscName},</if>
            <if test="discoverTime != null">#{discoverTime},</if>
            <if test="address != null">#{address},</if>
            <if test="description != null">#{description},</if>
            <if test="dealBy != null">#{dealBy},</if>
            <if test="dealPhoto != null">#{dealPhoto},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="caseEndTime != null">#{caseEndTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
         </trim>
    </insert>

    <update id="updateShcyWgh" parameterType="ShcyWgh">
        update shcy_wgh
        <trim prefix="SET" suffixOverrides=",">
            <if test="wghId != null">wgh_id = #{wghId},</if>
            <if test="imagefilename != null">imagefilename = #{imagefilename},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="lianTime != null">lian_time = #{lianTime},</if>
            <if test="reporter != null">reporter = #{reporter},</if>
            <if test="yqReason != null">yq_reason = #{yqReason},</if>
            <if test="tdReason != null">td_reason = #{tdReason},</if>
            <if test="infobcName != null">infobc_name = #{infobcName},</if>
            <if test="infoscName != null">infosc_name = #{infoscName},</if>
            <if test="discoverTime != null">discover_time = #{discoverTime},</if>
            <if test="address != null">address = #{address},</if>
            <if test="description != null">description = #{description},</if>
            <if test="dealBy != null">deal_by = #{dealBy},</if>
            <if test="dealPhoto != null">deal_photo = #{dealPhoto},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="caseEndTime != null">case_end_time = #{caseEndTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyWghById" parameterType="Long">
        delete from shcy_wgh where id = #{id}
    </delete>

    <delete id="deleteShcyWghByIds" parameterType="String">
        delete from shcy_wgh where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="ShcyWgh" resultType="long">
        select count(1) from shcy_wgh
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="dealBy != null"> and deal_by = #{dealBy}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (address like concat('%', #{params.keyword}, '%')
                or description like concat('%', #{params.keyword}, '%') or task_id like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="selectHandleList" parameterType="ShcyWgh" resultMap="ShcyWghResult">
        <include refid="selectShcyWghVo"/>
        <where>
            circulation_state in ('1', '2')
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="dealBy != null"> and deal_by = #{dealBy}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (address like concat('%', #{params.keyword}, '%')
                or description like concat('%', #{params.keyword}, '%') or task_id like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
        order by lian_time desc
    </select>

    <select id="selectHistoryList" parameterType="ShcyWgh" resultMap="ShcyWghResult">
        <include refid="selectShcyWghVo"/>
        <where>
            circulation_state = '4'
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="dealBy != null"> and deal_by = #{dealBy}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (address like concat('%', #{params.keyword}, '%')
                or description like concat('%', #{params.keyword}, '%') or task_id like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
        order by lian_time desc
    </select>

    <select id="selectShcyWghListByTaskDbcenterIds" resultMap="ShcyWghResult">
        SELECT *
        FROM shcy_wgh
        WHERE wgh_id IN
        <foreach item="taskDbcenterId" collection="taskDbcenterIds" open="(" separator="," close=")">
            #{taskDbcenterId}
        </foreach>
    </select>

    <select id="selectShcyWghByTaskDbcenterId" parameterType="String" resultMap="ShcyWghResult">
        <include refid="selectShcyWghVo"/>
        where task_id = #{taskId} limit 1
    </select>

    <update id="updateShcyWghByTaskIds" parameterType="String">
        update shcy_wgh set circulation_state = '6' where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

</mapper>