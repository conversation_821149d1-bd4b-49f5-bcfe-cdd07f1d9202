<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShopMapper">

    <resultMap type="Shop" id="ShopResult">
        <result property="id"    column="id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopAddress"    column="shop_address"    />
        <result property="shopLicense"    column="shop_license"    />
        <result property="shopCreditCode"    column="shop_credit_code"    />
        <result property="shopRegisterAddress"    column="shop_register_address"    />
        <result property="shopOperatingAddress"    column="shop_operating_address"    />
        <result property="shopCommittee"    column="shop_committee"    />
        <result property="shopCategory"    column="shop_category"    />
        <result property="shopSubcategory"    column="shop_subcategory"    />
        <result property="shopLittlecategory" column="shop_littlecategory"/>
        <result property="shopContract"    column="shop_contract"    />
        <result property="shopContactPhone"    column="shop_contact_phone"    />
        <result property="shopHouseOwnership"    column="shop_house_ownership"    />
        <result property="isStateAssets"    column="is_state_assets"    />
        <result property="shopLandlordContract"    column="shop_landlord_contract"    />
        <result property="shoplAndlordPhone"    column="shopl_andlord_phone"    />
        <result property="shopGoverningProperty"    column="shop_governing_property"    />
        <result property="isSameCommunityProperty"    column="is_same_community_property"    />
        <result property="shopPropertyContact"    column="shop_property_contact"    />
        <result property="shopPropertyPhone"    column="shop_property_phone"    />
        <result property="isSiteCode"    column="is_site_code"    />
        <result property="shopEmployeeNum"    column="shop_employee_num"    />
        <result property="shopStatus"    column="shop_status"    />
        <result property="shopRemark"    column="shop_remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shopDeparmentId"    column="shop_deparment_id"    />
        <result property="isOpen"        column="is_open" />
        <result property="oddNumber"     column="odd_number"/>
        <result property="shopArea"      column="shop_area"    />
        <result property="supervisionStatus"    column="supervision_status"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="noticeType" column="notice_type"/>
        <result property="type" column="type"/>
        <result property="coordinate" column="coordinate"/>
        <result property="isAccommodation"    column="is_accommodation"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="legalPersonContact"    column="legal_person_contact"    />
        <result property="roadSection"    column="road_section"    />
        <result property="roadSectionStart"    column="road_section_start"    />
        <result property="roadSectionEnd"    column="road_section_end"    />
        <result property="informationStatus"    column="information_status"    />
        <result property="streetChief"    column="street_chief"    />
        <result property="chiefPhone"    column="chief_phone"    />
        <result property="communityStreetChief"    column="community_street_chief"    />
        <result property="communityStreetChiefPhone"    column="community_street_chief_phone"    />
        <result property="merchantStreetChief"    column="merchant_street_chief"    />
        <result property="merchantStreetChiefPhone"    column="merchant_street_chief_phone"    />
        <result property="insuranceLiabilityContact"    column="insurance_liability_contact"    />
        <result property="insuranceLiabilityPhone"    column="insurance_liability_phone"    />
        <result property="insuranceLiabilityFlag"    column="insurance_liability_flag"    />
        <result property="chiefRoad" column="chief_road" />
        <result property="houseInvestigated" column="house_investigated" />
        <result property="propertyInvestigated" column="property_investigated" />
        <result property="dianmianPhoto" column="dianmian_photo" />
        <result property="yinyezhizhaoPhoto" column="yinyezhizhao_photo" />
        <result property="jingyingxukezhengPhoto" column="jingyingxukezheng_photo" />
    </resultMap>

    <sql id="selectShopVo">
        select id, shop_name, shop_address, shop_license, shop_credit_code, shop_register_address,
               shop_operating_address, shop_committee, shop_category, shop_subcategory,
               shop_contract, shop_contact_phone, shop_house_ownership, is_state_assets,
               shop_landlord_contract, shopl_andlord_phone, shop_governing_property,
               is_same_community_property, shop_property_contact, shop_property_phone,
               is_site_code, shop_employee_num, shop_status, shop_remark, create_time,
               update_time, shop_deparment_id,is_open,odd_number,shop_littlecategory,
               supervision_status,shop_area,longitude,latitude,notice_type, type, coordinate,
               is_accommodation, legal_person, legal_person_contact, road_section, road_section_start,
               road_section_end,information_status,street_chief,chief_phone,
               insurance_liability_contact,insurance_liability_phone,insurance_liability_flag,chief_road,
               house_investigated, property_investigated, community_street_chief, community_street_chief_phone,
               merchant_street_chief, merchant_street_chief_phone,
               dianmian_photo,yinyezhizhao_photo,jingyingxukezheng_photo from shcy_shop
    </sql>

    <sql id="selectShopDeptVo">
        SELECT
                ss.id as id,
                ss.shop_name AS shopName,
                ss.shop_address AS shopAddress,
                ss.shop_license AS shopLicense,
                ss.shop_credit_code as shopCreditCode,
                ss.shop_register_address AS shopRegisterAddress,
                ss.shop_operating_address AS shopOperatingAddress,
                ss.shop_category AS shopCategory,
                ss.shop_subcategory AS shopSubcategory,
                ss.shop_littlecategory as shopLittlecategory,
                ss.shop_contract AS shopContract,
                ss.shop_contact_phone AS shopContactPhone,
                ss.shop_house_ownership AS shopHouseOwnership,
                ss.is_state_assets AS isStateAssets,
                ss.shop_landlord_contract AS shopLandlordContract,
                ss.shopl_andlord_phone AS shoplAndlordPhone,
                ss.shop_governing_property AS shopGoverningProperty,
                ss.is_same_community_property AS isSameCommunityProperty,
                ss.shop_property_contact AS shopPropertyContact,
                ss.shop_property_phone as shopPropertyPhone,
                ss.is_site_code as isSiteCode,
                ss.shop_employee_num as shopEmployeeNum,
                ss.shop_status as shopStatus,
                ss.shop_remark as shopRemark,
                ss.create_time as createTime,
                ss.update_time as updateTime,
                ss.is_open as isOpen,
                sd.dept_id as shopDeparmentId,
                sd.dept_name AS shopCommittee,
                ss.odd_number as oddNumber,
                ss.shop_area as shopArea,
                ss.supervision_status as supervisionStatus,
                ss.longitude as longitude,
                ss.latitude as latitude,
                ss.notice_type as noticeType,
                ss.type as type,
                ss.coordinate as coordinate,
                ss.is_accommodation as  isAccommodation,
                ss.legal_person as legalPerson,
                ss.legal_person_contact as legalPersonContact ,
                ss.road_section as roadSection ,
                ss.road_section_start as roadSectionStart,
                ss.road_section_end as  roadSectionEnd,
                ss.information_status as informationStatus,
                ss.street_chief as streetChief,
                ss.chief_phone as  chiefPhone,
                ss.insurance_liability_contact as insuranceLiabilityContact,
                ss.insurance_liability_phone as insuranceLiabilityPhone,
                ss.insurance_liability_flag as insuranceLiabilityFlag,
                ss.dianmian_photo as  dianmianPhoto,
                ss.yinyezhizhao_photo as yinyezhizhaoPhoto,
                ss.jingyingxukezheng_photo as jingyingxukezhengPhoto
        FROM
                shcy_shop ss
                LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id;
    </sql>

    <select id="selectShopList" parameterType="Shop"  resultType="com.ruoyi.shcy.domain.Shop">
        SELECT
        ss.id as id,
        ss.shop_name AS shopName,
        ss.shop_address AS shopAddress,
        ss.shop_license AS shopLicense,
        ss.shop_credit_code as shopCreditCode,
        ss.shop_register_address AS shopRegisterAddress,
        ss.shop_operating_address AS shopOperatingAddress,
        ss.shop_category AS shopCategory,
        ss.shop_subcategory AS shopSubcategory,
        ss.shop_littlecategory as shopLittlecategory,
        ss.shop_contract AS shopContract,
        ss.shop_contact_phone AS shopContactPhone,
        ss.shop_house_ownership AS shopHouseOwnership,
        ss.is_state_assets AS isStateAssets,
        ss.shop_landlord_contract AS shopLandlordContract,
        ss.shopl_andlord_phone AS shoplAndlordPhone,
        ss.shop_governing_property AS shopGoverningProperty,
        ss.is_same_community_property AS isSameCommunityProperty,
        ss.shop_property_contact AS shopPropertyContact,
        ss.shop_property_phone as shopPropertyPhone,
        ss.is_site_code as isSiteCode,
        ss.shop_employee_num as shopEmployeeNum,
        ss.shop_status as shopStatus,
        ss.shop_remark as shopRemark,
        ss.create_time as createTime,
        ss.update_time as updateTime,
        ss.is_open as isOpen,
        sd.dept_id as shopDeparmentId,
        sd.dept_name AS shopCommittee,
        ss.odd_number as oddNumber,
        ss.shop_area as shopArea,
        ss.supervision_status as supervisionStatus,
        ss.longitude as longitude,
        ss.latitude as latitude,
        ss.notice_type as noticeType,
        ss.type as type,
        ss.coordinate as coordinate,
        ss.is_accommodation as  isAccommodation,
        ss.legal_person as legalPerson,
        ss.legal_person_contact as legalPersonContact ,
        ss.road_section as roadSection ,
        ss.road_section_start as roadSectionStart,
        ss.road_section_end as  roadSectionEnd,
        ss.information_status as informationStatus,
        ss.street_chief as streetChief,
        ss.chief_phone as  chiefPhone,
        ss.insurance_liability_contact as insuranceLiabilityContact,
        ss.insurance_liability_phone as insuranceLiabilityPhone,
        ss.insurance_liability_flag as insuranceLiabilityFlag,
        ss.chief_road as chiefRoad,
        ss.community_street_chief as communityStreetChief,
        ss.dianmian_photo as  dianmianPhoto,
        ss.yinyezhizhao_photo as yinyezhizhaoPhoto,
        ss.jingyingxukezheng_photo as jingyingxukezhengPhoto

        FROM
        shcy_shop ss
        LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
        <where>
            <if test="shopName != null  and shopName != ''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="shopAddress != null  and shopAddress != ''"> and ss.shop_address like  concat('%', #{shopAddress}, '%')</if>
            <if test="shopLicense != null  and shopLicense != ''"> and ss.shop_license = #{shopLicense}</if>
            <if test="shopCreditCode != null  and shopCreditCode != ''"> and ss.shop_credit_code = #{shopCreditCode}</if>
            <if test="shopRegisterAddress != null  and shopRegisterAddress != ''"> and ss.shop_register_address = #{shopRegisterAddress}</if>
            <if test="shopOperatingAddress != null  and shopOperatingAddress != ''"> and ss.shop_operating_address = #{shopOperatingAddress}</if>
            <if test="shopCommittee != null  and shopCommittee != ''"> and sd.shop_committee = #{shopCommittee}</if>
            <if test="shopCategory != null  and shopCategory != ''"> and ss.shop_category = #{shopCategory}</if>
            <if test="shopSubcategory != null  and shopSubcategory != ''"> and ss.shop_subcategory = #{shopSubcategory}</if>
            <if test="shopLittlecategory != null  and shopLittlecategory != ''"> and ss.shop_littlecategory = #{shopLittlecategory}</if>
            <if test="shopContract != null  and shopContract != ''"> and ss.shop_contract = #{shopContract}</if>
            <if test="shopContactPhone != null  and shopContactPhone != ''"> and ss.shop_contact_phone = #{shopContactPhone}</if>
            <if test="shopHouseOwnership != null  and shopHouseOwnership != ''"> and ss.shop_house_ownership = #{shopHouseOwnership}</if>
            <if test="isStateAssets != null  and isStateAssets != ''"> and ss.is_state_assets = #{isStateAssets}</if>
            <if test="shopLandlordContract != null  and shopLandlordContract != ''"> and ss.shop_landlord_contract = #{shopLandlordContract}</if>
            <if test="shoplAndlordPhone != null  and shoplAndlordPhone != ''"> and ss.shopl_andlord_phone = #{shoplAndlordPhone}</if>
            <if test="shopGoverningProperty != null  and shopGoverningProperty != ''"> and ss.shop_governing_property = #{shopGoverningProperty}</if>
            <if test="isSameCommunityProperty != null  and isSameCommunityProperty != ''"> and ss.is_same_community_property = #{isSameCommunityProperty}</if>
            <if test="shopPropertyContact != null  and shopPropertyContact != ''"> and ss.shop_property_contact = #{shopPropertyContact}</if>
            <if test="shopPropertyPhone != null  and shopPropertyPhone != ''"> and ss.shop_property_phone = #{shopPropertyPhone}</if>
            <if test="isSiteCode != null  and isSiteCode != ''"> and ss.is_site_code = #{isSiteCode}</if>
            <if test="shopEmployeeNum != null  and shopEmployeeNum != ''"> and ss.shop_employee_num = #{shopEmployeeNum}</if>
            <if test="shopStatus != null  and shopStatus != ''"> and ss.shop_status = #{shopStatus}</if>
            <if test="shopRemark != null  and shopRemark != ''"> and ss.shop_remark = #{shopRemark}</if>
            <if test="shopDeparmentId != null "> and sd.dept_id = #{shopDeparmentId}</if>
            <if test="oddNumber != null and oddNumber !=''">and ss.odd_number = #{oddNumber}</if>
            <if test="isOpen != null and isOpen !=''" > and ss.is_open = #{isOpen}</if>
            <if test="shopArea != null  and shopArea != ''"> and ss.shop_area = #{shopArea}</if>
            <if test="noticeType !=null and noticeType !=''">and ss.notice_type = #{noticeType}</if>
            <if test="supervisionStatus !=null and supervisionStatus !=''">and ss.supervision_status = #{supervisionStatus}</if>
            <if test="searchValue !=null and searchValue!='' "> and (ss.shop_operating_address like concat('%', #{searchValue}, '%')  or ss.shop_name like concat('%', #{searchValue}, '%'))</if>
            <if test="isAccommodation != null  and isAccommodation != ''"> and ss.is_accommodation = #{isAccommodation}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and ss.legal_person = #{legalPerson}</if>
            <if test="legalPersonContact != null  and legalPersonContact != ''"> and ss.legal_person_contact = #{legalPersonContact}</if>
            <if test="roadSection != null  and roadSection != ''"> and ss.road_section like concat('%', #{roadSection}, '%') </if>
           <if test=" roadSectionStart !=null and roadSectionStart != ''  and  roadFlag == 0 ">
               and  ss.road_section_start = #{roadSectionStart}  </if>
            <if test=" roadSectionEnd != null  and roadSectionEnd !='' and  roadFlag == 0 ">
                and ss.road_section_end = #{roadSectionEnd} </if>
            <if test="  roadFlag == 1 ">
                and (ss.road_section_start = #{roadSectionStart}  or  ss.road_section_end  = #{roadSectionEnd} )
            </if>
            <if test="informationStatus != null  and informationStatus != ''"> and ss.information_status = #{informationStatus}</if>
            <if test="streetChief != null  and streetChief != ''"> and ss.street_chief = #{streetChief}</if>
            <if test="chiefPhone != null  and chiefPhone != ''"> and ss.chief_phone = #{chiefPhone}</if>
            <if test="insuranceLiabilityContact != null  and insuranceLiabilityContact != ''"> and ss.insurance_liability_contact = #{insuranceLiabilityContact}</if>
            <if test="insuranceLiabilityPhone != null  and insuranceLiabilityPhone != ''"> and ss.insurance_liability_phone = #{insuranceLiabilityPhone}</if>
            <if test="insuranceLiabilityFlag != null  and insuranceLiabilityFlag != ''"> and ss.insurance_liability_flag = #{insuranceLiabilityFlag}</if>
            <if test="dianmianPhoto != null  and dianmianPhoto != ''"> and ss.dianmian_photo = #{dianmianPhoto}</if>
            <if test="yinyezhizhaoPhoto != null  and yinyezhizhaoPhoto != ''"> and ss.yinyezhizhao_photo = #{yinyezhizhaoPhoto}</if>
            <if test="jingyingxukezhengPhoto != null  and jingyingxukezhengPhoto != ''"> and ss.jingyingxukezheng_photo = #{jingyingxukezhengPhoto}</if>
        </where>

    </select>
    <select id="selectShopCount"  resultType="int">
        SELECT
        count(*) as id
        FROM
        shcy_shop

    </select>


    <select id="selectShopByCreditCode" parameterType="String" resultType="com.ruoyi.shcy.domain.Shop">
        SELECT
                ss.id as id,
                ss.shop_name AS shopName,
                ss.shop_address AS shopAddress,
                ss.shop_license AS shopLicense,
                ss.shop_credit_code as shopCreditCode,
                ss.shop_register_address AS shopRegisterAddress,
                ss.shop_operating_address AS shopOperatingAddress,
                ss.shop_category AS shopCategory,
                ss.shop_subcategory AS shopSubcategory,
                ss.shop_littlecategory as shopLittlecategory,
                ss.shop_contract AS shopContract,
                ss.shop_contact_phone AS shopContactPhone,
                ss.shop_house_ownership AS shopHouseOwnership,
                ss.is_state_assets AS isStateAssets,
                ss.shop_landlord_contract AS shopLandlordContract,
                ss.shopl_andlord_phone AS shoplAndlordPhone,
                ss.shop_governing_property AS shopGoverningProperty,
                ss.is_same_community_property AS isSameCommunityProperty,
                ss.shop_property_contact AS shopPropertyContact,
                ss.shop_property_phone as shopPropertyPhone,
                ss.is_site_code as isSiteCode,
                ss.shop_employee_num as shopEmployeeNum,
                ss.shop_status as shopStatus,
                ss.shop_remark as shopRemark,
                ss.create_time as createTime,
                ss.update_time as updateTime,
                ss.is_open as isOpen,
                sd.dept_id as shopDeparmentId,
                sd.dept_name AS shopCommittee,
                ss.odd_number as oddNumber,
                ss.shop_area as shopArea,
                ss.supervision_status as supervisionStatus,
                ss.longitude as longitude,
                ss.latitude as latitude,
                ss.notice_type as noticeType,
                ss.type as type,
                ss.coordinate as coordinate,
                ss.is_accommodation as  isAccommodation,
                ss.legal_person as legalPerson,
                ss.legal_person_contact as legalPersonContact ,
                ss.road_section as roadSection ,
                ss.road_section_start as roadSectionStart,
                ss.road_section_end as  roadSectionEnd,
                ss.information_status as informationStatus,
                ss.street_chief as streetChief,
                ss.chief_phone as  chiefPhone,
                ss.insurance_liability_contact as insuranceLiabilityContact,
                ss.insurance_liability_phone as insuranceLiabilityPhone,
                ss.insurance_liability_flag as insuranceLiabilityFlag,
                ss.chief_road as chiefRoad,
                ss.dianmian_photo as  dianmianPhoto,
                ss.yinyezhizhao_photo as yinyezhizhaoPhoto,
                ss.jingyingxukezheng_photo as jingyingxukezhengPhoto

        FROM
                shcy_shop ss
                LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
       where ss.shop_credit_code = #{creditCode}
    </select>

    <select id="selectShopListWithNoram" parameterType="Shop"  resultType="com.ruoyi.shcy.domain.Shop">
        SELECT
        ss.id as id,
        ss.shop_name AS shopName,
        ss.shop_address AS shopAddress,
        ss.shop_license AS shopLicense,
        ss.shop_credit_code as shopCreditCode,
        ss.shop_register_address AS shopRegisterAddress,
        ss.shop_operating_address AS shopOperatingAddress,
        ss.shop_category AS shopCategory,
        ss.shop_subcategory AS shopSubcategory,
        ss.shop_littlecategory as shopLittlecategory,
        ss.shop_contract AS shopContract,
        ss.shop_contact_phone AS shopContactPhone,
        ss.shop_house_ownership AS shopHouseOwnership,
        ss.is_state_assets AS isStateAssets,
        ss.shop_landlord_contract AS shopLandlordContract,
        ss.shopl_andlord_phone AS shoplAndlordPhone,
        ss.shop_governing_property AS shopGoverningProperty,
        ss.is_same_community_property AS isSameCommunityProperty,
        ss.shop_property_contact AS shopPropertyContact,
        ss.shop_property_phone as shopPropertyPhone,
        ss.is_site_code as isSiteCode,
        ss.shop_employee_num as shopEmployeeNum,
        ss.shop_status as shopStatus,
        ss.shop_remark as shopRemark,
        ss.create_time as createTime,
        ss.update_time as updateTime,
        ss.is_open as isOpen,
        sd.dept_id as shopDeparmentId,
        sd.dept_name AS shopCommittee,
        ss.odd_number as oddNumber,
        ss.shop_area as shopArea,
        ss.supervision_status as supervisionStatus,
        ss.longitude as longitude,
        ss.latitude as latitude,
        ss.notice_type  as noticeType,
        ss.type as type,
        ss.coordinate as coordinate,
        ss.is_accommodation as  isAccommodation,
        ss.legal_person as legalPerson,
        ss.legal_person_contact as legalPersonContact ,
        ss.road_section as roadSection ,
        ss.road_section_start as roadSectionStart,
        ss.road_section_end as  roadSectionEnd,
        ss.information_status as informationStatus,
        ss.street_chief as streetChief,
        ss.chief_phone as  chiefPhone,
        ss.insurance_liability_contact as insuranceLiabilityContact,
        ss.insurance_liability_phone as insuranceLiabilityPhone,
        ss.insurance_liability_flag as insuranceLiabilityFlag,
        ss.chief_road as chiefRoad,
        ss.dianmian_photo as  dianmianPhoto,
        ss.yinyezhizhao_photo as yinyezhizhaoPhoto,
        ss.jingyingxukezheng_photo as jingyingxukezhengPhoto
        FROM
        shcy_shop ss
        LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
        <where>
            <if test="shopName != null  and shopName != ''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="shopAddress != null  and shopAddress != ''"> and ss.shop_address like  concat('%', #{shopAddress}, '%')</if>
            <if test="shopLicense != null  and shopLicense != ''"> and ss.shop_license = #{shopLicense}</if>
            <if test="shopCreditCode != null  and shopCreditCode != ''"> and ss.shop_credit_code = #{shopCreditCode}</if>
            <if test="shopRegisterAddress != null  and shopRegisterAddress != ''"> and ss.shop_register_address = #{shopRegisterAddress}</if>
            <if test="shopOperatingAddress != null  and shopOperatingAddress != ''"> and ss.shop_operating_address = #{shopOperatingAddress}</if>
            <if test="shopCommittee != null  and shopCommittee != ''"> and sd.shop_committee = #{shopCommittee}</if>
            <if test="shopCategory != null  and shopCategory != ''"> and ss.shop_category = #{shopCategory}</if>
            <if test="shopSubcategory != null  and shopSubcategory != ''"> and ss.shop_subcategory = #{shopSubcategory}</if>
            <if test="shopLittlecategory != null  and shopLittlecategory != ''"> and ss.shop_littlecategory = #{shopLittlecategory}</if>
            <if test="shopContract != null  and shopContract != ''"> and ss.shop_contract = #{shopContract}</if>
            <if test="shopContactPhone != null  and shopContactPhone != ''"> and ss.shop_contact_phone = #{shopContactPhone}</if>
            <if test="shopHouseOwnership != null  and shopHouseOwnership != ''"> and ss.shop_house_ownership = #{shopHouseOwnership}</if>
            <if test="isStateAssets != null  and isStateAssets != ''"> and ss.is_state_assets = #{isStateAssets}</if>
            <if test="shopLandlordContract != null  and shopLandlordContract != ''"> and ss.shop_landlord_contract = #{shopLandlordContract}</if>
            <if test="shoplAndlordPhone != null  and shoplAndlordPhone != ''"> and ss.shopl_andlord_phone = #{shoplAndlordPhone}</if>
            <if test="shopGoverningProperty != null  and shopGoverningProperty != ''"> and ss.shop_governing_property = #{shopGoverningProperty}</if>
            <if test="isSameCommunityProperty != null  and isSameCommunityProperty != ''"> and ss.is_same_community_property = #{isSameCommunityProperty}</if>
            <if test="shopPropertyContact != null  and shopPropertyContact != ''"> and ss.shop_property_contact = #{shopPropertyContact}</if>
            <if test="shopPropertyPhone != null  and shopPropertyPhone != ''"> and ss.shop_property_phone = #{shopPropertyPhone}</if>
            <if test="isSiteCode != null  and isSiteCode != ''"> and ss.is_site_code = #{isSiteCode}</if>
            <if test="shopEmployeeNum != null  and shopEmployeeNum != ''"> and ss.shop_employee_num = #{shopEmployeeNum}</if>
            <if test="shopStatus != null  and shopStatus != ''"> and ss.shop_status = #{shopStatus}</if>
            <if test="shopRemark != null  and shopRemark != ''"> and ss.shop_remark = #{shopRemark}</if>
            <if test="shopDeparmentId != null "> and sd.dept_id = #{shopDeparmentId}</if>
            <if test="oddNumber != null and oddNumber !=''">and ss.odd_number = #{oddNumber}</if>
            <if test="isOpen != null"> and ss.is_open = #{isOpen}</if>
            <if test="shopArea != null  and shopArea != ''"> and ss.shop_area = #{shopArea}</if>
            <if test="noticeType != null and noticeType !=''">and ss.notice_type =#{noticeType}</if>
            <if test="supervisionStatus !=null and supervisionStatus !=''">and ss.supervision_status = #{supervisionStatus}</if>
            <if test="searchValue !=null and searchValue!='' "> and (ss.shop_operating_address like concat('%', #{searchValue}, '%')  or ss.shop_name like concat('%', #{searchValue}, '%'))</if>
            <if test="isAccommodation != null  and isAccommodation != ''"> and ss.is_accommodation = #{isAccommodation}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and ss.legal_person = #{legalPerson}</if>
            <if test="legalPersonContact != null  and legalPersonContact != ''"> and ss.legal_person_contact = #{legalPersonContact}</if>
            <if test="roadSection != null  and roadSection != ''"> and ss.road_section = #{roadSection}</if>
            <if test="roadSectionStart != null  and roadSectionStart != ''"> and ss.road_section_start = #{roadSectionStart}</if>
            <if test="roadSectionEnd != null  and roadSectionEnd != ''"> and ss.road_section_end = #{roadSectionEnd}</if>
            <if test="informationStatus != null  and informationStatus != ''"> and ss.information_status = #{informationStatus}</if>
            <if test="streetChief != null  and streetChief != ''"> and ss.street_chief = #{streetChief}</if>
            <if test="chiefPhone != null  and chiefPhone != ''"> and ss.chief_phone = #{chiefPhone}</if>
            <if test="insuranceLiabilityContact != null  and insuranceLiabilityContact != ''"> and ss.insurance_liability_contact = #{insuranceLiabilityContact}</if>
            <if test="insuranceLiabilityPhone != null  and insuranceLiabilityPhone != ''"> and ss.insurance_liability_phone = #{insuranceLiabilityPhone}</if>
            <if test="insuranceLiabilityFlag != null  and insuranceLiabilityFlag != ''"> and ss.insurance_liability_flag = #{insuranceLiabilityFlag}</if>
            <if test="dianmianPhoto != null  and dianmianPhoto != ''"> and ss.dianmian_photo = #{dianmianPhoto}</if>
            <if test="yinyezhizhaoPhoto != null  and yinyezhizhaoPhoto != ''"> and ss.yinyezhizhao_photo = #{yinyezhizhaoPhoto}</if>
            <if test="jingyingxukezhengPhoto != null  and jingyingxukezhengPhoto != ''"> and ss.jingyingxukezheng_photo = #{jingyingxukezhengPhoto}</if>
        </where>
    </select>

    <select id="selectCheckedShopListWithNormalCg" parameterType="com.ruoyi.shcy.domain.ShopCheckLog" resultType="com.ruoyi.shcy.domain.Shop">
        SELECT
                ss.id AS id,
                ss.shop_name AS shopName,
                ss.shop_address AS shopAddress,
                ss.shop_license AS shopLicense,
                ss.shop_credit_code AS shopCreditCode,
                ss.shop_register_address AS shopRegisterAddress,
                ss.shop_operating_address AS shopOperatingAddress,
                ss.shop_category AS shopCategory,
                ss.shop_subcategory AS shopSubcategory,
                ss.shop_littlecategory AS shopLittlecategory,
                ss.shop_contract AS shopContract,
                ss.shop_contact_phone AS shopContactPhone,
                ss.shop_house_ownership AS shopHouseOwnership,
                ss.is_state_assets AS isStateAssets,
                ss.shop_landlord_contract AS shopLandlordContract,
                ss.shopl_andlord_phone AS shoplAndlordPhone,
                ss.shop_governing_property AS shopGoverningProperty,
                ss.is_same_community_property AS isSameCommunityProperty,
                ss.shop_property_contact AS shopPropertyContact,
                ss.shop_property_phone AS shopPropertyPhone,
                ss.is_site_code AS isSiteCode,
                ss.shop_employee_num AS shopEmployeeNum,
                ss.shop_status AS shopStatus,
                ss.shop_remark AS shopRemark,
                ss.create_time AS createTime,
                ss.update_time AS updateTime,
                ss.is_open AS isOpen,
                sd.dept_id AS shopDeparmentId,
                sd.dept_name AS shopCommittee,
                ss.odd_number AS oddNumber,
                ss.shop_area AS shopArea,
                ss.supervision_status AS supervisionStatus,
                ss.longitude AS longitude,
                ss.latitude AS latitude,
                ss.notice_type AS noticeType,
                ss.type AS type,
                ss.coordinate AS coordinate,
                ss.is_accommodation AS isAccommodation,
                ss.legal_person AS legalPerson,
                ss.legal_person_contact AS legalPersonContact,
                ss.road_section AS roadSection,
                ss.road_section_start AS roadSectionStart,
                ss.road_section_end AS roadSectionEnd,
                ss.information_status AS informationStatus,
                ss.street_chief AS streetChief,
                ss.chief_phone AS chiefPhone,
                ss.insurance_liability_contact AS insuranceLiabilityContact,
                ss.insurance_liability_phone AS insuranceLiabilityPhone,
                ss.insurance_liability_flag AS insuranceLiabilityFlag,
                ss.chief_road AS chiefRoad,
                scl.check_update_by_cg AS checkUpdateByCg,
        ss.dianmian_photo as  dianmianPhoto,
        ss.yinyezhizhao_photo as yinyezhizhaoPhoto,
        ss.jingyingxukezheng_photo as jingyingxukezhengPhoto
        FROM
                shcy_shop ss
                LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
                LEFT JOIN shcy_shop_check_log scl ON ss.id = scl.shop_id
        <where>
            <if test="checkCrossDoorOperation != null  and checkCrossDoorOperation != ''"> and scl.check_cross_door_operation = #{checkCrossDoorOperation}</if>
            <if test="checkFfhx != null  and checkFfhx != ''"> and scl.check_ffhx = #{checkFfhx}</if>
        </where>
        and ss.shop_status = "已检查"  AND DATE_FORMAT(scl.check_date,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
    </select>

    <select id="selectAllShops" parameterType="Shop" resultMap="ShopResult">
        select id, shop_name, shop_address, shop_license, shop_credit_code, shop_register_address, shop_operating_address,
               shop_committee, shop_category, shop_subcategory,shop_littlecategory,shop_contract, shop_contact_phone, shop_house_ownership,
               is_state_assets, shop_landlord_contract, shopl_andlord_phone, shop_governing_property,
               is_same_community_property, shop_property_contact, shop_property_phone, is_site_code, shop_employee_num,
               shop_status, shop_remark, create_time, update_time, shop_deparment_id,is_open,odd_number,
               shop_area,supervision_status,longitude,latitude,notice_type, type, coordinate,is_accommodation, legal_person,
               legal_person_contact, road_section, road_section_start, road_section_end,information_status,street_chief,
               chief_phone,insurance_liability_contact,insurance_liability_phone,insurance_liability_flag,chief_road,
               dianmian_photo,yinyezhizhao_photo,jingyingxukezheng_photo, community_street_chief
        from shcy_shop
    </select>

    <select id="selectCommitteeShopNum" parameterType="Shop" resultType="com.ruoyi.shcy.domain.Shop">
        select count(*) as shopNum from shcy_shop where  shop_deparment_id=#{shopDeparmentId}
    </select>

    <select id ="selectShopTotal" parameterType="Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        select count(*) as ShopTotal from shcy_shop where  is_open="正常";
    </select>

    <select id ="selectCategoryShopNum" parameterType="String" resultType="com.ruoyi.shcy.domain.vo.CategoryDetailVo">
        select count(*) as categoryCheckShopTotal from shcy_shop
            where shop_category=#{shopCategory} and  is_open in ("正常")
    </select>

    <select id ="selectCategoryCheckedShopNum"   parameterType="Shop" resultType="com.ruoyi.shcy.domain.vo.CategoryDetailVo">
        select count(*) as categoryCheckedShopNum FROM `shcy_shop` ss
            LEFT JOIN shcy_shop_check_log scl ON ss.id = scl.shop_id
            <where>
                <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                    AND date_format(scl.check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
                </if>
                <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                    AND date_format(scl.check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
                </if>
            </where>
        and ss.shop_category = #{category}
          AND ss.is_open = "正常"
          AND scl.shop_status IN ("已检查")
    </select>

    <select id="selectTodayCheckShopTotal" parameterType="com.ruoyi.shcy.domain.ShopCheckLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*) as checkShopTotal
        FROM shcy_shop_check_log scl LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
        <where>
            <if test="checkDate != null">and scl.check_date = #{checkDate}</if>
        </where>
         and scl.shop_status in("已检查")
    </select>

    <select id="selectTodayCheckShopNomalNum" parameterType="com.ruoyi.shcy.domain.ShopCheckLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*) as shopNormalNum
        FROM shcy_shop_check_log scl LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
        <where>
            <if test="checkDate != null">and scl.check_date = #{checkDate}</if>
        </where>
        and scl.shop_status in("已检查")
    </select>

    <select id="selectTodayCheckShopRestNum" parameterType="com.ruoyi.shcy.domain.ShopCheckLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*) as shopRestNum
        FROM shcy_shop_check_log scl LEFT JOIN shcy_shop ss ON scl.shop_id = ss.id
        <where>
            <if test="checkDate != null">and scl.check_date = #{checkDate}</if>
        </where>
        and scl.shop_status in("歇业")
    </select>

    <select id="selectOpenNormalShopNum" parameterType="Shop" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where is_open = "营业"
    </select>

    <select id="selectOpenNomalShopCount" resultType="int">
        select count(*) as id from shcy_shop where is_open = "营业"
    </select>

    <select id="selectOpenRestShopNum" parameterType="Shop" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where  shop_status = "歇业"
    </select>

    <select id="selectOpenRestShopCount" resultType="int">
        select count(*) as id from shcy_shop where shop_status = "歇业"
    </select>

    <select id="selectOpenStopShopNum" parameterType="Shop" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where  is_open = "关停"
    </select>

    <select id="selectOpenStopShopCount" resultType="int">
        select count(*) as id from shcy_shop where is_open="关停"
    </select>

    <select id="selectTodayCheckShopCloseNum" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*) as shopCloseNum FROM shcy_shop
        <where>
            <if test="isOpen != null and isOpen !=''" > and is_open = #{isOpen}</if>
        </where>
    </select>

    <select id ="selectSupervisorShop" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*)  as shopSupervisorNum from  shcy_shop where  supervision_status ="重点监管";
    </select>

    <select id="selectTalkShop" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*)  as shopTalkNum  from  shcy_shop where  notice_type ="谈话";
    </select>

    <select id="selectHandleShop" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*)  as shopHandleNum  from  shcy_shop where  notice_type ="督办";
    </select>

    <select id="selectYellowCardShop" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT count(*)  as shopYellowCardNum  from  shcy_shop where  notice_type ="黄牌";
    </select>

    <select id="selectCommitteeCheckShopTotal" parameterType="com.ruoyi.common.core.domain.entity.SysDept" resultType="com.ruoyi.shcy.domain.vo.CommitteeDetailVo">
        SELECT
        count(*) as committeeCheckShopTotal,sd.dept_name as committee
        FROM shcy_shop ss
        LEFT JOIN sys_dept sd ON ss.shop_deparment_id = sd.dept_id
            <where>
                <if test="deptId != null "> and sd.dept_id = #{deptId}</if>
            </where>
    </select>

    <select id="selectCommitteeCheckedShopNum"  resultType="com.ruoyi.shcy.domain.vo.CommitteeDetailVo">
        select count(*) as committeeCheckedShopNum
        FROM shcy_shop_check_log scl
             left join shcy_shop ss on scl.shop_id = ss.id
             left join  sys_dept sd on sd.dept_id = ss.shop_deparment_id
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(scl.check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(scl.check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        and  scl.shop_status in("正常","歇业")  and sd.dept_id=#{deptId};
    </select>

    <select id="selectNoCheckedShopList" parameterType="com.ruoyi.shcy.domain.CheckRecord" resultMap="ShopResult">
        SELECT  id, shop_name, shop_address, shop_license, shop_credit_code, shop_register_address,
                shop_operating_address, shop_committee, shop_category, shop_subcategory,
                shop_contract, shop_contact_phone, shop_house_ownership, is_state_assets,
                shop_landlord_contract, shopl_andlord_phone, shop_governing_property,
                is_same_community_property, shop_property_contact, shop_property_phone,
                is_site_code, shop_employee_num, shop_status, shop_remark, create_time,
                update_time, shop_deparment_id,is_open,odd_number,shop_littlecategory,
                shop_area,longitude,latitude, type, coordinate,dianmian_photo,yinyezhizhao_photo,jingyingxukezheng_photo
                 from shcy_shop
        where shcy_shop.id  not in (
                select
                        scr.shop_id  as id
                FROM
                        shop_check_record  scr
                <where>
                    <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                        AND date_format(scr.check_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
                    </if>
                    <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                        AND date_format(scr.check_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
                    </if>
                    <if test="params.flag != null and params.flag != '' and params.flag== 1 ">
                        AND DATE_FORMAT(scr.create_time,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
                    </if>
                </where>
                       );

    </select>

    <select id="selectShopById" parameterType="Long" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where id = #{id}
    </select>

    <insert id="insertShop" parameterType="Shop" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopName != null">shop_name,</if>
            <if test="shopAddress != null">shop_address,</if>
            <if test="shopLicense != null">shop_license,</if>
            <if test="shopCreditCode != null">shop_credit_code,</if>
            <if test="shopRegisterAddress != null">shop_register_address,</if>
            <if test="shopOperatingAddress != null">shop_operating_address,</if>
            <if test="shopCommittee != null">shop_committee,</if>
            <if test="shopCategory != null">shop_category,</if>
            <if test="shopSubcategory != null">shop_subcategory,</if>
            <if test="shopLittlecategory != null">shop_littlecategory,</if>
            <if test="shopContract != null">shop_contract,</if>
            <if test="shopContactPhone != null">shop_contact_phone,</if>
            <if test="shopHouseOwnership != null">shop_house_ownership,</if>
            <if test="isStateAssets != null">is_state_assets,</if>
            <if test="shopLandlordContract != null">shop_landlord_contract,</if>
            <if test="shoplAndlordPhone != null">shopl_andlord_phone,</if>
            <if test="shopGoverningProperty != null">shop_governing_property,</if>
            <if test="isSameCommunityProperty != null">is_same_community_property,</if>
            <if test="shopPropertyContact != null">shop_property_contact,</if>
            <if test="shopPropertyPhone != null">shop_property_phone,</if>
            <if test="isSiteCode != null">is_site_code,</if>
            <if test="shopEmployeeNum != null">shop_employee_num,</if>
            <if test="shopStatus != null">shop_status,</if>
            <if test="shopRemark != null">shop_remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shopDeparmentId != null">shop_deparment_id,</if>
            <if test="isOpen !=null">is_open,</if>
            <if test="oddNumber !=null">odd_number,</if>
            <if test="shopArea != null">shop_area,</if>
            <if test="supervisionStatus != null">supervision_status,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="isAccommodation != null">is_accommodation,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="legalPersonContact != null">legal_person_contact,</if>
            <if test="roadSection != null">road_section,</if>
            <if test="roadSectionStart != null">road_section_start,</if>
            <if test="roadSectionEnd != null">road_section_end,</if>
            <if test="informationStatus != null">information_status,</if>
            <if test="streetChief != null">street_chief,</if>
            <if test="chiefPhone != null">chief_phone,</if>
            <if test="communityStreetChief != null">community_street_chief,</if>
            <if test="communityStreetChiefPhone != null">community_street_chief_phone,</if>
            <if test="merchantStreetChief != null">merchant_street_chief,</if>
            <if test="merchantStreetChiefPhone != null">merchant_street_chief_phone,</if>
            <if test="insuranceLiabilityContact != null">insurance_liability_contact,</if>
            <if test="insuranceLiabilityPhone != null">insurance_liability_phone,</if>
            <if test="insuranceLiabilityFlag != null">insurance_liability_flag,</if>
            <if test="chiefRoad != null">chief_road,</if>
            <if test="houseInvestigated != null">house_investigated,</if>
            <if test="propertyInvestigated != null">property_investigated,</if>
            <if test="dianmianPhoto != null">dianmian_photo,</if>
            <if test="yinyezhizhaoPhoto != null">yinyezhizhao_photo,</if>
            <if test="jingyingxukezhengPhoto != null">jingyingxukezheng_photo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopName != null">#{shopName},</if>
            <if test="shopAddress != null">#{shopAddress},</if>
            <if test="shopLicense != null">#{shopLicense},</if>
            <if test="shopCreditCode != null">#{shopCreditCode},</if>
            <if test="shopRegisterAddress != null">#{shopRegisterAddress},</if>
            <if test="shopOperatingAddress != null">#{shopOperatingAddress},</if>
            <if test="shopCommittee != null">#{shopCommittee},</if>
            <if test="shopCategory != null">#{shopCategory},</if>
            <if test="shopSubcategory != null">#{shopSubcategory},</if>
            <if test="shopLittlecategory != null">#{shopLittlecategory},</if>
            <if test="shopContract != null">#{shopContract},</if>
            <if test="shopContactPhone != null">#{shopContactPhone},</if>
            <if test="shopHouseOwnership != null">#{shopHouseOwnership},</if>
            <if test="isStateAssets != null">#{isStateAssets},</if>
            <if test="shopLandlordContract != null">#{shopLandlordContract},</if>
            <if test="shoplAndlordPhone != null">#{shoplAndlordPhone},</if>
            <if test="shopGoverningProperty != null">#{shopGoverningProperty},</if>
            <if test="isSameCommunityProperty != null">#{isSameCommunityProperty},</if>
            <if test="shopPropertyContact != null">#{shopPropertyContact},</if>
            <if test="shopPropertyPhone != null">#{shopPropertyPhone},</if>
            <if test="isSiteCode != null">#{isSiteCode},</if>
            <if test="shopEmployeeNum != null">#{shopEmployeeNum},</if>
            <if test="shopStatus != null">#{shopStatus},</if>
            <if test="shopRemark != null">#{shopRemark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shopDeparmentId != null">#{shopDeparmentId},</if>
            <if test="isOpen !=null"> #{isOpen},</if>
            <if test="oddNumber !=null"> #{oddNumber},</if>
            <if test="shopArea != null">#{shopArea},</if>
            <if test="supervisionStatus != null">#{supervisionStatus},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="isAccommodation != null">#{isAccommodation},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="legalPersonContact != null">#{legalPersonContact},</if>
            <if test="roadSection != null">#{roadSection},</if>
            <if test="roadSectionStart != null">#{roadSectionStart},</if>
            <if test="roadSectionEnd != null">#{roadSectionEnd},</if>
            <if test="informationStatus != null">#{informationStatus},</if>
            <if test="streetChief != null">#{streetChief},</if>
            <if test="chiefPhone != null">#{chiefPhone},</if>
            <if test="communityStreetChief != null">#{communityStreetChief},</if>
            <if test="communityStreetChiefPhone != null">#{communityStreetChiefPhone},</if>
            <if test="merchantStreetChief != null">#{merchantStreetChief},</if>
            <if test="merchantStreetChiefPhone != null">#{merchantStreetChiefPhone},</if>
            <if test="insuranceLiabilityContact != null">#{insuranceLiabilityContact},</if>
            <if test="insuranceLiabilityPhone != null">#{insuranceLiabilityPhone},</if>
            <if test="insuranceLiabilityFlag != null">#{insuranceLiabilityFlag},</if>
            <if test="chiefRoad != null">#{chiefRoad},</if>
            <if test="houseInvestigated != null">#{houseInvestigated},</if>
            <if test="propertyInvestigated != null">#{propertyInvestigated},</if>
            <if test="dianmianPhoto != null">#{dianmianPhoto},</if>
            <if test="yinyezhizhaoPhoto != null">#{yinyezhizhaoPhoto},</if>
            <if test="jingyingxukezhengPhoto != null">#{jingyingxukezhengPhoto},</if>
         </trim>
    </insert>

    <update id="updateShop" parameterType="Shop">
        update shcy_shop
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="shopAddress != null">shop_address = #{shopAddress},</if>
            <if test="shopLicense != null">shop_license = #{shopLicense},</if>
            <if test="shopCreditCode != null">shop_credit_code = #{shopCreditCode},</if>
            <if test="shopRegisterAddress != null">shop_register_address = #{shopRegisterAddress},</if>
            <if test="shopOperatingAddress != null">shop_operating_address = #{shopOperatingAddress},</if>
            <if test="shopCommittee != null">shop_committee = #{shopCommittee},</if>
            <if test="shopCategory != null">shop_category = #{shopCategory},</if>
            <if test="shopSubcategory != null">shop_subcategory = #{shopSubcategory},</if>
            <if test="shopLittlecategory != null">shop_littlecategory = #{shopLittlecategory},</if>
            <if test="shopContract != null">shop_contract = #{shopContract},</if>
            <if test="shopContactPhone != null">shop_contact_phone = #{shopContactPhone},</if>
            <if test="shopHouseOwnership != null">shop_house_ownership = #{shopHouseOwnership},</if>
            <if test="isStateAssets != null">is_state_assets = #{isStateAssets},</if>
            <if test="shopLandlordContract != null">shop_landlord_contract = #{shopLandlordContract},</if>
            <if test="shoplAndlordPhone != null">shopl_andlord_phone = #{shoplAndlordPhone},</if>
            <if test="shopGoverningProperty != null">shop_governing_property = #{shopGoverningProperty},</if>
            <if test="isSameCommunityProperty != null">is_same_community_property = #{isSameCommunityProperty},</if>
            <if test="shopPropertyContact != null">shop_property_contact = #{shopPropertyContact},</if>
            <if test="shopPropertyPhone != null">shop_property_phone = #{shopPropertyPhone},</if>
            <if test="isSiteCode != null">is_site_code = #{isSiteCode},</if>
            <if test="shopEmployeeNum != null">shop_employee_num = #{shopEmployeeNum},</if>
            <if test="shopStatus != null">shop_status = #{shopStatus},</if>
            <if test="shopRemark != null">shop_remark = #{shopRemark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shopDeparmentId != null">shop_deparment_id = #{shopDeparmentId},</if>
            <if test="isOpen !=null">is_open = #{isOpen},</if>
            <if test="oddNumber !=null">odd_number =#{oddNumber},</if>
            <if test="shopArea != null">shop_area = #{shopArea},</if>
            <if test="supervisionStatus != null">supervision_status = #{supervisionStatus},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="isAccommodation != null">is_accommodation = #{isAccommodation},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="legalPersonContact != null">legal_person_contact = #{legalPersonContact},</if>
            <if test="roadSection != null">road_section = #{roadSection},</if>
            <if test="roadSectionStart != null">road_section_start = #{roadSectionStart},</if>
            <if test="roadSectionEnd != null">road_section_end = #{roadSectionEnd},</if>
            <if test="informationStatus != null">information_status = #{informationStatus},</if>
            <if test="streetChief != null">street_chief = #{streetChief},</if>
            <if test="chiefPhone != null">chief_phone = #{chiefPhone},</if>
            <if test="communityStreetChief != null">community_street_chief = #{communityStreetChief},</if>
            <if test="communityStreetChiefPhone != null">community_street_chief_phone = #{communityStreetChiefPhone},</if>
            <if test="merchantStreetChief != null">merchant_street_chief = #{merchantStreetChief},</if>
            <if test="merchantStreetChiefPhone != null">merchant_street_chief_phone = #{merchantStreetChiefPhone},</if>
            <if test="insuranceLiabilityContact != null">insurance_liability_contact = #{insuranceLiabilityContact},</if>
            <if test="insuranceLiabilityPhone != null">insurance_liability_phone = #{insuranceLiabilityPhone},</if>
            <if test="insuranceLiabilityFlag != null">insurance_liability_flag = #{insuranceLiabilityFlag},</if>
            <if test="chiefRoad != null">chief_road = #{chiefRoad},</if>
            <if test="houseInvestigated != null">house_investigated = #{houseInvestigated},</if>
            <if test="propertyInvestigated != null">property_investigated = #{propertyInvestigated},</if>
            <if test="dianmianPhoto != null">dianmian_photo = #{dianmianPhoto},</if>
            <if test="yinyezhizhaoPhoto != null">yinyezhizhao_photo = #{yinyezhizhaoPhoto},</if>
            <if test="jingyingxukezhengPhoto != null">jingyingxukezheng_photo = #{jingyingxukezhengPhoto},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateShopSupervisiorStatus" parameterType="String">
        update shcy_shop set supervision_status="重点监管"
        where shop_credit_code = #{shopCreditCode}
    </update>

    <delete id="deleteShopById" parameterType="Long">
        delete from shcy_shop where id = #{id}
    </delete>

    <update id="updateShopCheckStatus" parameterType="Long">
        update  shcy_shop  set shop_status = "未检查"  where shop_deparment_id = #{shopDeparmentId}
    </update>

    <update id="updateCheckStatus"  parameterType="Long">
        update  shcy_shop  set shop_status = "未检查"  where id = #{id}
    </update>

    <delete id="deleteShopByIds" parameterType="String">
        delete from shcy_shop where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="selectCheckShopVo">
        select id, shop_name, is_open from shcy_shop
    </sql>

    <select id="selectCheckShopList"  resultMap="ShopResult">
        <include refid="selectCheckShopVo"/>
    </select>

    <select id="selectShopByChiefRoad" parameterType="String" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where chief_road = #{road}
    </select>

    <select id="selectAllYyShops" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where is_open &lt;&gt; '关停'
    </select>

    <select id="selectAllShutdownShops" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where is_open = '关停'
    </select>
</mapper>
