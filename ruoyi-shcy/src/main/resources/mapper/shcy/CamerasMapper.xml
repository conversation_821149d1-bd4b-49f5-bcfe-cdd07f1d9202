<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.CamerasMapper">

    <resultMap type="Cameras" id="CamerasResult">
        <result property="id"    column="id"    />
        <result property="cameraIndexCode"    column="camera_index_code"    />
        <result property="gbIndexCode"    column="gb_index_code"    />
        <result property="name"    column="name"    />
        <result property="deviceIndexCode"    column="device_index_code"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="altitude"    column="altitude"    />
        <result property="pixel"    column="pixel"    />
        <result property="cameraType"    column="camera_type"    />
        <result property="cameraTypeName"    column="camera_type_name"    />
        <result property="installPlace"    column="install_place"    />
        <result property="matrixCode"    column="matrix_code"    />
        <result property="chanNum"    column="chan_num"    />
        <result property="viewshed"    column="viewshed"    />
        <result property="capabilitySet"    column="capability_set"    />
        <result property="capabilitySetName"    column="capability_set_name"    />
        <result property="intelligentSet"    column="intelligent_set"    />
        <result property="intelligentSetName"    column="intelligent_set_name"    />
        <result property="recordLocation"    column="record_location"    />
        <result property="recordLocationName"    column="record_location_name"    />
        <result property="deviceResourceType"    column="device_resource_type"    />
        <result property="deviceResourceTypeName"    column="device_resource_type_name"    />
        <result property="channelType"    column="channel_type"    />
        <result property="channelTypeName"    column="channel_type_name"    />
        <result property="transType"    column="trans_type"    />
        <result property="transTypeName"    column="trans_type_name"    />
        <result property="unitIndexCode"    column="unit_index_code"    />
        <result property="treatyType"    column="treaty_type"    />
        <result property="treatyTypeName"    column="treaty_type_name"    />
        <result property="status"    column="status"    />
        <result property="statusName"    column="status_name"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />

        <result property="equipmentCode"    column="equipment_code"    />
        <result property="cameraName1"    column="camera_name1"    />
        <result property="cameraName2"    column="camera_name2"    />
        <result property="cameraName3"    column="camera_name3"    />
        <result property="cameraManufacturer"    column="camera_manufacturer"    />
        <result property="cameraModel"    column="camera_model"    />
        <result property="positionName"    column="position_name"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="macAddress"    column="mac_address"    />
        <result property="cameraUseType"    column="camera_use_type"    />
        <result property="fillLightAttribute"    column="fill_light_attribute"    />
        <result property="cameraEncodingFormat"    column="camera_encoding_format"    />
        <result property="storageEquipmentIp"    column="storage_equipment_ip"    />
        <result property="storageEquipmentChannel"    column="storage_equipment_channel"    />
        <result property="cameraPositionType"    column="camera_position_type"    />
        <result property="monitorDirection"    column="monitor_direction"    />
        <result property="networkProperty"    column="network_property"    />
        <result property="fixTime"    column="fix_time"    />
        <result property="managementUnit"    column="management_unit"    />
        <result property="managementPhone"    column="management_phone"    />
        <result property="equipmentState"    column="equipment_state"    />
        <result property="vedeoStorageDays"    column="vedeo_storage_days"    />
        <result property="vedeoResolution"    column="vedeo_resolution"    />
        <result property="vedeoSignalType"    column="vedeo_signal_type"    />


    </resultMap>

    <sql id="selectCamerasVo">
        select id, camera_index_code, gb_index_code, name, device_index_code, longitude, latitude, altitude, pixel,
               camera_type, camera_type_name, install_place, matrix_code, chan_num, viewshed, capability_set,
               capability_set_name, intelligent_set, intelligent_set_name, record_location, record_location_name,
               device_resource_type, device_resource_type_name, channel_type, channel_type_name, trans_type,
               trans_type_name, unit_index_code, treaty_type, treaty_type_name, status, status_name, type, coordinate,
                       equipment_code, camera_name1, camera_name2, camera_name3, camera_manufacturer, camera_model,
               position_name, ip_address, mac_address, camera_use_type, fill_light_attribute, camera_encoding_format,
               storage_equipment_ip, storage_equipment_channel, camera_position_type, monitor_direction, network_property,
               fix_time, management_unit, management_phone, equipment_state, vedeo_storage_days, vedeo_resolution,
               vedeo_signal_type from shcy_cameras
    </sql>

    <select id="selectCamerasList" parameterType="Cameras" resultMap="CamerasResult">
        <include refid="selectCamerasVo"/>
        <where>
            <if test="cameraIndexCode != null  and cameraIndexCode != ''"> and camera_index_code = #{cameraIndexCode}</if>
            <if test="gbIndexCode != null  and gbIndexCode != ''"> and gb_index_code = #{gbIndexCode}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="cameraTypeName != null  and cameraTypeName != ''"> and camera_type_name like concat('%', #{cameraTypeName}, '%')</if>
            <if test="positionName != null  and positionName != ''"> and position_name like concat('%', #{positionName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectCamerasById" parameterType="Long" resultMap="CamerasResult">
        <include refid="selectCamerasVo"/>
        where id = #{id}
    </select>

    <select id="selectCamerasByCameraIndexCode" parameterType="String" resultMap="CamerasResult">
        <include refid="selectCamerasVo"/>
        where camera_index_code = #{cameraIndexCode} limit 1
    </select>

    <insert id="insertCameras" parameterType="Cameras" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_cameras
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cameraIndexCode != null">camera_index_code,</if>
            <if test="gbIndexCode != null">gb_index_code,</if>
            <if test="name != null">name,</if>
            <if test="deviceIndexCode != null">device_index_code,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
            <if test="pixel != null">pixel,</if>
            <if test="cameraType != null">camera_type,</if>
            <if test="cameraTypeName != null">camera_type_name,</if>
            <if test="installPlace != null">install_place,</if>
            <if test="matrixCode != null">matrix_code,</if>
            <if test="chanNum != null">chan_num,</if>
            <if test="viewshed != null">viewshed,</if>
            <if test="capabilitySet != null">capability_set,</if>
            <if test="capabilitySetName != null">capability_set_name,</if>
            <if test="intelligentSet != null">intelligent_set,</if>
            <if test="intelligentSetName != null">intelligent_set_name,</if>
            <if test="recordLocation != null">record_location,</if>
            <if test="recordLocationName != null">record_location_name,</if>
            <if test="deviceResourceType != null">device_resource_type,</if>
            <if test="deviceResourceTypeName != null">device_resource_type_name,</if>
            <if test="channelType != null">channel_type,</if>
            <if test="channelTypeName != null">channel_type_name,</if>
            <if test="transType != null">trans_type,</if>
            <if test="transTypeName != null">trans_type_name,</if>
            <if test="unitIndexCode != null">unit_index_code,</if>
            <if test="treatyType != null">treaty_type,</if>
            <if test="treatyTypeName != null">treaty_type_name,</if>
            <if test="status != null">status,</if>
            <if test="statusName != null">status_name,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="equipmentCode != null">equipment_code,</if>
            <if test="cameraName1 != null">camera_name1,</if>
            <if test="cameraName2 != null">camera_name2,</if>
            <if test="cameraName3 != null">camera_name3,</if>
            <if test="cameraManufacturer != null">camera_manufacturer,</if>
            <if test="cameraModel != null">camera_model,</if>
            <if test="positionName != null">position_name,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="macAddress != null">mac_address,</if>
            <if test="cameraUseType != null">camera_use_type,</if>
            <if test="fillLightAttribute != null">fill_light_attribute,</if>
            <if test="cameraEncodingFormat != null">camera_encoding_format,</if>
            <if test="storageEquipmentIp != null">storage_equipment_ip,</if>
            <if test="storageEquipmentChannel != null">storage_equipment_channel,</if>
            <if test="cameraPositionType != null">camera_position_type,</if>
            <if test="monitorDirection != null">monitor_direction,</if>
            <if test="networkProperty != null">network_property,</if>
            <if test="fixTime != null">fix_time,</if>
            <if test="managementUnit != null">management_unit,</if>
            <if test="managementPhone != null">management_phone,</if>
            <if test="equipmentState != null">equipment_state,</if>
            <if test="vedeoStorageDays != null">vedeo_storage_days,</if>
            <if test="vedeoResolution != null">vedeo_resolution,</if>
            <if test="vedeoSignalType != null">vedeo_signal_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cameraIndexCode != null">#{cameraIndexCode},</if>
            <if test="gbIndexCode != null">#{gbIndexCode},</if>
            <if test="name != null">#{name},</if>
            <if test="deviceIndexCode != null">#{deviceIndexCode},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="pixel != null">#{pixel},</if>
            <if test="cameraType != null">#{cameraType},</if>
            <if test="cameraTypeName != null">#{cameraTypeName},</if>
            <if test="installPlace != null">#{installPlace},</if>
            <if test="matrixCode != null">#{matrixCode},</if>
            <if test="chanNum != null">#{chanNum},</if>
            <if test="viewshed != null">#{viewshed},</if>
            <if test="capabilitySet != null">#{capabilitySet},</if>
            <if test="capabilitySetName != null">#{capabilitySetName},</if>
            <if test="intelligentSet != null">#{intelligentSet},</if>
            <if test="intelligentSetName != null">#{intelligentSetName},</if>
            <if test="recordLocation != null">#{recordLocation},</if>
            <if test="recordLocationName != null">#{recordLocationName},</if>
            <if test="deviceResourceType != null">#{deviceResourceType},</if>
            <if test="deviceResourceTypeName != null">#{deviceResourceTypeName},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="channelTypeName != null">#{channelTypeName},</if>
            <if test="transType != null">#{transType},</if>
            <if test="transTypeName != null">#{transTypeName},</if>
            <if test="unitIndexCode != null">#{unitIndexCode},</if>
            <if test="treatyType != null">#{treatyType},</if>
            <if test="treatyTypeName != null">#{treatyTypeName},</if>
            <if test="status != null">#{status},</if>
            <if test="statusName != null">#{statusName},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="equipmentCode != null">#{equipmentCode},</if>
            <if test="cameraName1 != null">#{cameraName1},</if>
            <if test="cameraName2 != null">#{cameraName2},</if>
            <if test="cameraName3 != null">#{cameraName3},</if>
            <if test="cameraManufacturer != null">#{cameraManufacturer},</if>
            <if test="cameraModel != null">#{cameraModel},</if>
            <if test="positionName != null">#{positionName},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="macAddress != null">#{macAddress},</if>
            <if test="cameraUseType != null">#{cameraUseType},</if>
            <if test="fillLightAttribute != null">#{fillLightAttribute},</if>
            <if test="cameraEncodingFormat != null">#{cameraEncodingFormat},</if>
            <if test="storageEquipmentIp != null">#{storageEquipmentIp},</if>
            <if test="storageEquipmentChannel != null">#{storageEquipmentChannel},</if>
            <if test="cameraPositionType != null">#{cameraPositionType},</if>
            <if test="monitorDirection != null">#{monitorDirection},</if>
            <if test="networkProperty != null">#{networkProperty},</if>
            <if test="fixTime != null">#{fixTime},</if>
            <if test="managementUnit != null">#{managementUnit},</if>
            <if test="managementPhone != null">#{managementPhone},</if>
            <if test="equipmentState != null">#{equipmentState},</if>
            <if test="vedeoStorageDays != null">#{vedeoStorageDays},</if>
            <if test="vedeoResolution != null">#{vedeoResolution},</if>
            <if test="vedeoSignalType != null">#{vedeoSignalType},</if>
         </trim>
    </insert>

    <insert id="batchInsertCameras" parameterType="java.util.List">
        insert into shcy_cameras (camera_index_code, gb_index_code, name, device_index_code, longitude, latitude, altitude, pixel,
        camera_type, camera_type_name, install_place, matrix_code, chan_num, viewshed, capability_set,
        capability_set_name, intelligent_set, intelligent_set_name, record_location, record_location_name,
        device_resource_type, device_resource_type_name, channel_type, channel_type_name, trans_type,
        trans_type_name, unit_index_code, treaty_type, treaty_type_name, status, status_name, type, coordinate,
        equipment_code, camera_name1, camera_name2, camera_name3, camera_manufacturer, camera_model,
        position_name, ip_address, mac_address, camera_use_type, fill_light_attribute, camera_encoding_format,
        storage_equipment_ip, storage_equipment_channel, camera_position_type, monitor_direction, network_property,
        fix_time, management_unit, management_phone, equipment_state, vedeo_storage_days, vedeo_resolution,
        vedeo_signal_type) values
        <foreach collection="camerasList" item="item" separator=",">
            (#{item.cameraIndexCode}, #{item.gbIndexCode}, #{item.name}, #{item.deviceIndexCode}, #{item.longitude}, #{item.latitude}, #{item.altitude}, #{item.pixel},
            #{item.cameraType}, #{item.cameraTypeName}, #{item.installPlace}, #{item.matrixCode}, #{item.chanNum}, #{item.viewshed}, #{item.capabilitySet},
            #{item.capabilitySetName}, #{item.intelligentSet}, #{item.intelligentSetName}, #{item.recordLocation}, #{item.recordLocationName}, #{item.deviceResourceType},
            #{item.deviceResourceTypeName}, #{item.channelType}, #{item.channelTypeName}, #{item.transType},
            #{item.transTypeName}, #{item.unitIndexCode}, #{item.treatyType}, #{item.treatyTypeName}, #{item.status}, #{item.statusName}, #{item.type}, #{item.coordinate},
            #{item.equipmentCode}, #{item.cameraName1}, #{item.cameraName2}, #{item.cameraName3}, #{item.cameraManufacturer}, #{item.cameraModel},
            #{item.positionName}, #{item.ipAddress}, #{item.macAddress}, #{item.cameraUseType}, #{item.fillLightAttribute}, #{item.cameraEncodingFormat},
            #{item.storageEquipmentIp}, #{item.storageEquipmentChannel}, #{item.cameraPositionType}, #{item.monitorDirection}, #{item.networkProperty},
            #{item.fixTime}, #{item.managementUnit}, #{item.managementPhone}, #{item.equipmentState}, #{item.vedeoStorageDays}, #{item.vedeoResolution},
            #{item.vedeoSignalType})
        </foreach>
    </insert>

    <update id="updateCameras" parameterType="Cameras">
        update shcy_cameras
        <trim prefix="SET" suffixOverrides=",">
            <if test="cameraIndexCode != null">camera_index_code = #{cameraIndexCode},</if>
            <if test="gbIndexCode != null">gb_index_code = #{gbIndexCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="deviceIndexCode != null">device_index_code = #{deviceIndexCode},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
            <if test="pixel != null">pixel = #{pixel},</if>
            <if test="cameraType != null">camera_type = #{cameraType},</if>
            <if test="cameraTypeName != null">camera_type_name = #{cameraTypeName},</if>
            <if test="installPlace != null">install_place = #{installPlace},</if>
            <if test="matrixCode != null">matrix_code = #{matrixCode},</if>
            <if test="chanNum != null">chan_num = #{chanNum},</if>
            <if test="viewshed != null">viewshed = #{viewshed},</if>
            <if test="capabilitySet != null">capability_set = #{capabilitySet},</if>
            <if test="capabilitySetName != null">capability_set_name = #{capabilitySetName},</if>
            <if test="intelligentSet != null">intelligent_set = #{intelligentSet},</if>
            <if test="intelligentSetName != null">intelligent_set_name = #{intelligentSetName},</if>
            <if test="recordLocation != null">record_location = #{recordLocation},</if>
            <if test="recordLocationName != null">record_location_name = #{recordLocationName},</if>
            <if test="deviceResourceType != null">device_resource_type = #{deviceResourceType},</if>
            <if test="deviceResourceTypeName != null">device_resource_type_name = #{deviceResourceTypeName},</if>
            <if test="channelType != null">channel_type = #{channelType},</if>
            <if test="channelTypeName != null">channel_type_name = #{channelTypeName},</if>
            <if test="transType != null">trans_type = #{transType},</if>
            <if test="transTypeName != null">trans_type_name = #{transTypeName},</if>
            <if test="unitIndexCode != null">unit_index_code = #{unitIndexCode},</if>
            <if test="treatyType != null">treaty_type = #{treatyType},</if>
            <if test="treatyTypeName != null">treaty_type_name = #{treatyTypeName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="statusName != null">status_name = #{statusName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="equipmentCode != null">equipment_code = #{equipmentCode},</if>
            <if test="cameraName1 != null">camera_name1 = #{cameraName1},</if>
            <if test="cameraName2 != null">camera_name2 = #{cameraName2},</if>
            <if test="cameraName3 != null">camera_name3 = #{cameraName3},</if>
            <if test="cameraManufacturer != null">camera_manufacturer = #{cameraManufacturer},</if>
            <if test="cameraModel != null">camera_model = #{cameraModel},</if>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="macAddress != null">mac_address = #{macAddress},</if>
            <if test="cameraUseType != null">camera_use_type = #{cameraUseType},</if>
            <if test="fillLightAttribute != null">fill_light_attribute = #{fillLightAttribute},</if>
            <if test="cameraEncodingFormat != null">camera_encoding_format = #{cameraEncodingFormat},</if>
            <if test="storageEquipmentIp != null">storage_equipment_ip = #{storageEquipmentIp},</if>
            <if test="storageEquipmentChannel != null">storage_equipment_channel = #{storageEquipmentChannel},</if>
            <if test="cameraPositionType != null">camera_position_type = #{cameraPositionType},</if>
            <if test="monitorDirection != null">monitor_direction = #{monitorDirection},</if>
            <if test="networkProperty != null">network_property = #{networkProperty},</if>
            <if test="fixTime != null">fix_time = #{fixTime},</if>
            <if test="managementUnit != null">management_unit = #{managementUnit},</if>
            <if test="managementPhone != null">management_phone = #{managementPhone},</if>
            <if test="equipmentState != null">equipment_state = #{equipmentState},</if>
            <if test="vedeoStorageDays != null">vedeo_storage_days = #{vedeoStorageDays},</if>
            <if test="vedeoResolution != null">vedeo_resolution = #{vedeoResolution},</if>
            <if test="vedeoSignalType != null">vedeo_signal_type = #{vedeoSignalType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCamerasById" parameterType="Long">
        delete from shcy_cameras where id = #{id}
    </delete>

    <delete id="deleteCamerasByIds" parameterType="String">
        delete from shcy_cameras where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCameraListByIds" parameterType="String" resultMap="CamerasResult">
        <include refid="selectCamerasVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by FIELD(id,
            <foreach item="id" collection="array" separator="," close="">
                #{id}
            </foreach>
        )
    </select>
</mapper>
