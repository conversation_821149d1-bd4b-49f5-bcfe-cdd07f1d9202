<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.FloodReserveInfoMapper">
    
    <resultMap type="FloodReserveInfo" id="FloodReserveInfoResult">
        <result property="id"    column="id"    />
        <result property="propertyName"    column="property_name"    />
        <result property="reservePointAddress"    column="reserve_point_address"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactNumber"    column="contact_number"    />
        <result property="radiationArea"    column="radiation_area"    />
        <result property="wovenBag"    column="woven_bag"    />
        <result property="strawBag"    column="straw_bag"    />
        <result property="waterStopBoard"    column="water_stop_board"    />
        <result property="floodStopBag"    column="flood_stop_bag"    />
        <result property="yellowSand"    column="yellow_sand"    />
        <result property="cement"    column="cement"    />
        <result property="portableWorkLight"    column="portable_work_light"    />
        <result property="waterPump"    column="water_pump"    />
        <result property="raincoat"    column="raincoat"    />
        <result property="rainboots"    column="rainboots"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
    </resultMap>

    <sql id="selectFloodReserveInfoVo">
        select id, property_name, reserve_point_address, contact_person, contact_number, radiation_area, woven_bag, straw_bag, water_stop_board, flood_stop_bag, yellow_sand, cement, portable_work_light, water_pump, raincoat, rainboots, type, coordinate from shcy_flood_reserve_info
    </sql>

    <select id="selectFloodReserveInfoList" parameterType="FloodReserveInfo" resultMap="FloodReserveInfoResult">
        <include refid="selectFloodReserveInfoVo"/>
        <where>  
            <if test="propertyName != null  and propertyName != ''"> and property_name like concat('%', #{propertyName}, '%')</if>
        </where>
    </select>
    
    <select id="selectFloodReserveInfoById" parameterType="Long" resultMap="FloodReserveInfoResult">
        <include refid="selectFloodReserveInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFloodReserveInfo" parameterType="FloodReserveInfo" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_flood_reserve_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="propertyName != null">property_name,</if>
            <if test="reservePointAddress != null">reserve_point_address,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="radiationArea != null">radiation_area,</if>
            <if test="wovenBag != null">woven_bag,</if>
            <if test="strawBag != null">straw_bag,</if>
            <if test="waterStopBoard != null">water_stop_board,</if>
            <if test="floodStopBag != null">flood_stop_bag,</if>
            <if test="yellowSand != null">yellow_sand,</if>
            <if test="cement != null">cement,</if>
            <if test="portableWorkLight != null">portable_work_light,</if>
            <if test="waterPump != null">water_pump,</if>
            <if test="raincoat != null">raincoat,</if>
            <if test="rainboots != null">rainboots,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="propertyName != null">#{propertyName},</if>
            <if test="reservePointAddress != null">#{reservePointAddress},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="radiationArea != null">#{radiationArea},</if>
            <if test="wovenBag != null">#{wovenBag},</if>
            <if test="strawBag != null">#{strawBag},</if>
            <if test="waterStopBoard != null">#{waterStopBoard},</if>
            <if test="floodStopBag != null">#{floodStopBag},</if>
            <if test="yellowSand != null">#{yellowSand},</if>
            <if test="cement != null">#{cement},</if>
            <if test="portableWorkLight != null">#{portableWorkLight},</if>
            <if test="waterPump != null">#{waterPump},</if>
            <if test="raincoat != null">#{raincoat},</if>
            <if test="rainboots != null">#{rainboots},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
         </trim>
    </insert>

    <update id="updateFloodReserveInfo" parameterType="FloodReserveInfo">
        update shcy_flood_reserve_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="propertyName != null">property_name = #{propertyName},</if>
            <if test="reservePointAddress != null">reserve_point_address = #{reservePointAddress},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="radiationArea != null">radiation_area = #{radiationArea},</if>
            <if test="wovenBag != null">woven_bag = #{wovenBag},</if>
            <if test="strawBag != null">straw_bag = #{strawBag},</if>
            <if test="waterStopBoard != null">water_stop_board = #{waterStopBoard},</if>
            <if test="floodStopBag != null">flood_stop_bag = #{floodStopBag},</if>
            <if test="yellowSand != null">yellow_sand = #{yellowSand},</if>
            <if test="cement != null">cement = #{cement},</if>
            <if test="portableWorkLight != null">portable_work_light = #{portableWorkLight},</if>
            <if test="waterPump != null">water_pump = #{waterPump},</if>
            <if test="raincoat != null">raincoat = #{raincoat},</if>
            <if test="rainboots != null">rainboots = #{rainboots},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFloodReserveInfoById" parameterType="Long">
        delete from shcy_flood_reserve_info where id = #{id}
    </delete>

    <delete id="deleteFloodReserveInfoByIds" parameterType="String">
        delete from shcy_flood_reserve_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>