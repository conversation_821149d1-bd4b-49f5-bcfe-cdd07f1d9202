<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.NucleicAcidLogMapper">

    <resultMap type="NucleicAcidLog" id="NucleicAcidLogResult">
        <result property="id"    column="id"    />
        <result property="checkLogId"    column="check_log_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="nucleicAcid"    column="nucleic_acid"    />
        <result property="emplyeeId"    column="emplyee_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="checkDate"  column="check_date" />
    </resultMap>

    <sql id="selectNucleicAcidLogVo">
        select id, check_log_id, shop_id, nucleic_acid, emplyee_id,
               check_date,create_time, update_time from shcy_nucleic_acid_log
    </sql>

    <sql id="selectAcidLogShopEmployeeVo">
        SELECT
                nal.id as id,
                nal.check_log_id as checkLogId,
                ss.shop_name as shopName,
                nal.shop_id as shopId,
                nal.nucleic_acid as nucleicAcid,
                se.name as name,
                nal.emplyee_id as emplyeeId,
                nal.create_time as createTime,
                nal.update_time as updateTime,
                sd.dept_name as deptName,
                sd.dept_id as deptId,
                nal.check_date as checkDate

        FROM
                shcy_nucleic_acid_log nal
                left join shcy_shop ss on nal.shop_id = ss.id
                left join shcy_shop_employees  se on nal.emplyee_id = se.id
                left join shcy_shop_check_log scl  on nal.check_log_id = scl.id
                left join sys_dept sd on ss.shop_deparment_id  = sd.dept_id
    </sql>

    <select id="selectNucleicAcidLogList" parameterType="com.ruoyi.shcy.domain.NucleicAcidLog" resultType="com.ruoyi.shcy.domain.NucleicAcidLog">
        <include refid="selectAcidLogShopEmployeeVo"/>
        <where>
            <if test="checkLogId != null "> and check_log_id = #{checkLogId}</if>
            <if test="shopId != null "> and nal.shop_id = #{shopId}</if>
            <if test="deptId != null "> and sd.dept_id = #{deptId}</if>
            <if test="emplyeeId != null "> and nal.emplyee_id = #{emplyeeId}</if>
            <if test="shopName !=null and shopName !=''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="nucleicAcid != null  and nucleicAcid != ''"> and nal.nucleic_acid = #{nucleicAcid}</if>
            <if test="name != null and name !=''"> and se.name like concat('%', #{name}, '%')</if>
            <if test="deptName !=null and deptName !=''"> and sd.dept_name = #{deptName}</if>
            <if test="checkDate != null ">and nal.check_date = #{checkDate}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(nal.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(nal.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by nal.check_date desc
    </select>

    <select id="selectNucleicAcidLogById" parameterType="Long" resultMap="NucleicAcidLogResult">
        <include refid="selectNucleicAcidLogVo"/>
        where id = #{id}
    </select>
    <select id="selectNucleicAcidLogByEmployeeIdAndCheckDate" resultMap="NucleicAcidLogResult">
        <include refid="selectNucleicAcidLogVo"/>
        where emplyee_id = #{employeeId} and check_date = #{checkDate} limit 1
    </select>
    <select id="selectEmployeesAll" parameterType="com.ruoyi.shcy.domain.Shop" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        SELECT
                count(*) as employeeNum, ss.shop_name as shopName
        FROM  shcy_shop_employees sse
            LEFT JOIN shcy_shop ss ON sse.shop_id = ss.id
        where ss.is_open="正常"
        group by ss.shop_name
    </select>

    <select id="selectCheckEmployeesTotal" parameterType="com.ruoyi.shcy.domain.NucleicAcidLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        select count(*) as checkEmployeesTotal from shcy_nucleic_acid_log
        <where>
            <if test="checkDate != null ">and check_date = #{checkDate}</if>
        </where>
        and nucleic_acid in ("是","否")
    </select>
    <select id="selectCheckNormalEmployeesNum" parameterType="com.ruoyi.shcy.domain.NucleicAcidLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        select count(*) as checkNoramEmployeesNum from shcy_nucleic_acid_log
        <where>
            <if test="checkDate != null ">and check_date = #{checkDate}</if>
        </where>
        and nucleic_acid in ("是")
    </select>
    <select id="selectCheckAbnormalEmployeesNum" parameterType="com.ruoyi.shcy.domain.NucleicAcidLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        select count(*) as checkAbnormalEmployeesNum from shcy_nucleic_acid_log
        <where>
            <if test="checkDate != null ">and check_date = #{checkDate}</if>
        </where>
        and nucleic_acid in ("否")
    </select>
    <select id ="selectCheckRetiredEmployeesNum" parameterType="com.ruoyi.shcy.domain.NucleicAcidLog" resultType="com.ruoyi.shcy.domain.vo.ShopDetailVo">
        select count(*) as checkRetiredEmployeesNum from shcy_nucleic_acid_log
        <where>
            <if test="checkDate != null ">and check_date = #{checkDate}</if>
        </where>
        and nucleic_acid in ("离岗")
    </select>

    <insert id="insertNucleicAcidLog" parameterType="NucleicAcidLog">
        insert into shcy_nucleic_acid_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="checkLogId != null">check_log_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="nucleicAcid != null">nucleic_acid,</if>
            <if test="emplyeeId != null">emplyee_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="checkDate != null">check_date</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="checkLogId != null">#{checkLogId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="nucleicAcid != null">#{nucleicAcid},</if>
            <if test="emplyeeId != null">#{emplyeeId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="checkDate != null">#{checkDate}</if>
         </trim>
    </insert>

    <update id="updateNucleicAcidLog" parameterType="NucleicAcidLog">
        update shcy_nucleic_acid_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkLogId != null">check_log_id = #{checkLogId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="nucleicAcid != null">nucleic_acid = #{nucleicAcid},</if>
            <if test="emplyeeId != null">emplyee_id = #{emplyeeId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test ="checkDate !=null">check_date = #{checkDate}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNucleicAcidLogById" parameterType="Long">
        delete from shcy_nucleic_acid_log where id = #{id}
    </delete>

    <delete id="deleteNucleicAcidLogByIds" parameterType="String">
        delete from shcy_nucleic_acid_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
