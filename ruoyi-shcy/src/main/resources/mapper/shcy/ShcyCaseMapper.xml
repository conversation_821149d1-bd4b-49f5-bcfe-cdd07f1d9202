<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyCaseMapper">

    <resultMap type="ShcyCase" id="ShcyCaseResult">
        <result property="id"    column="id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="caseType"    column="case_type"    />
        <result property="caseTypeName"    column="case_type_name"    />
        <result property="caseDescription"    column="case_description"    />
        <result property="caseDealBy"    column="case_deal_by"    />
        <result property="caseDealPhoto"    column="case_deal_photo"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="caseEndTime" column ="case_end_time"/>
        <result property="dealInTimeState" column="deal_in_time_state" />
        <result property="shopAddress" column="shop_address" />
        <result property="shopCheckLogId" column="shop_check_log_id" />
        <result property="caseFinishTime" column="case_finish_time"/>
    </resultMap>

    <sql id="selectShcyCaseVo">
        select id, shop_name, case_type, case_type_name, case_description, case_deal_by, case_deal_photo, circulation_state, create_time, create_by, update_time, update_by,case_end_time,deal_in_time_state,shop_address,shop_check_log_id,case_finish_time from shcy_case
    </sql>

    <select id="selectShcyCaseList" parameterType="ShcyCase" resultMap="ShcyCaseResult">
        <include refid="selectShcyCaseVo"/>
        <where>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="caseTypeName != null  and caseTypeName != ''"> and case_type_name = #{caseTypeName}</if>
            <if test="caseDescription != null  and caseDescription != ''"> and case_description = #{caseDescription}</if>
            <if test="caseDealBy != null  and caseDealBy != ''"> and case_deal_by = #{caseDealBy}</if>
            <if test="caseDealPhoto != null  and caseDealPhoto != ''"> and case_deal_photo = #{caseDealPhoto}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="caseEndTime != null and caseEndTime != ''">and case_end_time = #{caseEndTime}</if>
            <if test="dealInTimeState != null and dealInTimeState != ''">and deal_in_time_state = #{dealInTimeState}</if>
            <if test="shopAddress != null and shopAddress != ''">and shop_address = #{shopAddress}</if>
            <if test="caseFinishTime != null and caseFinishTime != ''">and case_finish_time = #{caseFinishTime}</if>
            <if test="params.keyword != null and params.keyword != ''"> and (shop_name like concat('%', #{params.keyword}, '%')
                or shop_address like concat('%', #{params.keyword}, '%'))
            </if>
            <if test="params.permission != null and params.permission == 1"> and case_type in ('1', '2', '3', '4', '5') </if>
            <if test="params.permission != null and params.permission == 2"> and case_type in ('1', '4') </if>
            <if test="params.permission != null and params.permission == 3"> and case_type in ('2', '5') </if>
            <if test="params.permission != null and params.permission == 4"> and case_type = '3' </if>
            <if test="params.permission != null and params.permission == 5"> and case_type = '9' </if>
        </where>
        order by circulation_state desc, create_time desc
    </select>

    <select id="selectShcyCaseById" parameterType="Long" resultMap="ShcyCaseResult">
        <include refid="selectShcyCaseVo"/>
        where  id = #{id}
    </select>

    <insert id="insertShcyCase" parameterType="ShcyCase" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopName != null">shop_name,</if>
            <if test="caseType != null">case_type,</if>
            <if test="caseTypeName != null">case_type_name,</if>
            <if test="caseDescription != null">case_description,</if>
            <if test="caseDealBy != null">case_deal_by,</if>
            <if test="caseDealPhoto != null">case_deal_photo,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="caseEndTime != null">case_end_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="shopAddress != null">shop_address,</if>
            <if test="shopCheckLogId !=null">shop_check_log_id,</if>
            <if test="caseFinishTime !=null">case_finish_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopName != null">#{shopName},</if>
            <if test="caseType != null">#{caseType},</if>
            <if test="caseTypeName != null">#{caseTypeName},</if>
            <if test="caseDescription != null">#{caseDescription},</if>
            <if test="caseDealBy != null">#{caseDealBy},</if>
            <if test="caseDealPhoto != null">#{caseDealPhoto},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="caseEndTime != null">#{caseEndTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="shopAddress != null">#{shopAddress},</if>
            <if test="shopCheckLogId != null">#{shopCheckLogId},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
         </trim>
    </insert>

    <update id="updateShcyCase" parameterType="ShcyCase">
        update shcy_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="caseType != null">case_type = #{caseType},</if>
            <if test="caseTypeName != null">case_type_name = #{caseTypeName},</if>
            <if test="caseDescription != null">case_description = #{caseDescription},</if>
            <if test="caseDealBy != null">case_deal_by = #{caseDealBy},</if>
            <if test="caseDealPhoto != null">case_deal_photo = #{caseDealPhoto},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="caseEndTime != null">case_end_time = #{caseEndTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="shopAddress != null">shop_address = #{shopAddress},</if>
            <if test="shopCheckLogId != null">shop_check_log_id = #{shopCheckLogId},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
        </trim>
        where  id = #{id}
    </update>

    <update id="returnShcyCaseById" parameterType="Long">
        update shcy_case set circulation_state = '2' where id =#{id}
    </update>
    <delete id="deleteShcyCaseById" parameterType="Long">
        delete from shcy_case where  id = #{id}
    </delete>


    <delete id="deleteShcyCaseByIds" parameterType="String">
        delete from shcy_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectShcyCaseListByShopCheckLogIds" parameterType="String" resultMap="ShcyCaseResult">
        <include refid="selectShcyCaseVo"/>
        where shop_check_log_id in
        <foreach item="shopCheckLogId" collection="array" open="(" separator="," close=")">
            #{shopCheckLogId}
        </foreach>
    </select>
</mapper>
