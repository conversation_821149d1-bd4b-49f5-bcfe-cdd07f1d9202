<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ForestHeadManageMapper">
    
    <resultMap type="ForestHeadManage" id="ForestHeadManageResult">
        <result property="id"    column="id"    />
        <result property="forestHead"    column="forest_head"    />
        <result property="deputyForestHead"    column="deputy_forest_head"    />
        <result property="packageArea"    column="package_area"    />
        <result property="resourceType"    column="resource_type"    />
        <result property="name"    column="name"    />
        <result property="area"    column="area"    />
        <result property="managementUnit"    column="management_unit"    />
        <result property="managementUnitPerson"    column="management_unit_person"    />
        <result property="managementUnitTelphone"    column="management_unit_telphone"    />
        <result property="conservationUnit"    column="conservation_unit"    />
        <result property="conservationUnitPerson"    column="conservation_unit_person"    />
        <result property="conservationUnitTelphone"    column="conservation_unit_telphone"    />
        <result property="managementStyle"    column="management_style"    />
        <result property="jobDuty"    column="job_duty"    />
    </resultMap>

    <sql id="selectForestHeadManageVo">
        select id, forest_head, deputy_forest_head, package_area, resource_type, name, area, management_unit, management_unit_person, management_unit_telphone, conservation_unit, conservation_unit_person, conservation_unit_telphone, management_style, job_duty from shcy_forest_head_manage
    </sql>

    <select id="selectForestHeadManageList" parameterType="ForestHeadManage" resultMap="ForestHeadManageResult">
        <include refid="selectForestHeadManageVo"/>
        <where>  
            <if test="forestHead != null  and forestHead != ''"> and forest_head = #{forestHead}</if>
        </where>
    </select>
    
    <select id="selectForestHeadManageById" parameterType="Long" resultMap="ForestHeadManageResult">
        <include refid="selectForestHeadManageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertForestHeadManage" parameterType="ForestHeadManage" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_forest_head_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="forestHead != null">forest_head,</if>
            <if test="deputyForestHead != null">deputy_forest_head,</if>
            <if test="packageArea != null">package_area,</if>
            <if test="resourceType != null">resource_type,</if>
            <if test="name != null">name,</if>
            <if test="area != null">area,</if>
            <if test="managementUnit != null">management_unit,</if>
            <if test="managementUnitPerson != null">management_unit_person,</if>
            <if test="managementUnitTelphone != null">management_unit_telphone,</if>
            <if test="conservationUnit != null">conservation_unit,</if>
            <if test="conservationUnitPerson != null">conservation_unit_person,</if>
            <if test="conservationUnitTelphone != null">conservation_unit_telphone,</if>
            <if test="managementStyle != null">management_style,</if>
            <if test="jobDuty != null">job_duty,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="forestHead != null">#{forestHead},</if>
            <if test="deputyForestHead != null">#{deputyForestHead},</if>
            <if test="packageArea != null">#{packageArea},</if>
            <if test="resourceType != null">#{resourceType},</if>
            <if test="name != null">#{name},</if>
            <if test="area != null">#{area},</if>
            <if test="managementUnit != null">#{managementUnit},</if>
            <if test="managementUnitPerson != null">#{managementUnitPerson},</if>
            <if test="managementUnitTelphone != null">#{managementUnitTelphone},</if>
            <if test="conservationUnit != null">#{conservationUnit},</if>
            <if test="conservationUnitPerson != null">#{conservationUnitPerson},</if>
            <if test="conservationUnitTelphone != null">#{conservationUnitTelphone},</if>
            <if test="managementStyle != null">#{managementStyle},</if>
            <if test="jobDuty != null">#{jobDuty},</if>
         </trim>
    </insert>

    <update id="updateForestHeadManage" parameterType="ForestHeadManage">
        update shcy_forest_head_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="forestHead != null">forest_head = #{forestHead},</if>
            <if test="deputyForestHead != null">deputy_forest_head = #{deputyForestHead},</if>
            <if test="packageArea != null">package_area = #{packageArea},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="name != null">name = #{name},</if>
            <if test="area != null">area = #{area},</if>
            <if test="managementUnit != null">management_unit = #{managementUnit},</if>
            <if test="managementUnitPerson != null">management_unit_person = #{managementUnitPerson},</if>
            <if test="managementUnitTelphone != null">management_unit_telphone = #{managementUnitTelphone},</if>
            <if test="conservationUnit != null">conservation_unit = #{conservationUnit},</if>
            <if test="conservationUnitPerson != null">conservation_unit_person = #{conservationUnitPerson},</if>
            <if test="conservationUnitTelphone != null">conservation_unit_telphone = #{conservationUnitTelphone},</if>
            <if test="managementStyle != null">management_style = #{managementStyle},</if>
            <if test="jobDuty != null">job_duty = #{jobDuty},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteForestHeadManageById" parameterType="Long">
        delete from shcy_forest_head_manage where id = #{id}
    </delete>

    <delete id="deleteForestHeadManageByIds" parameterType="String">
        delete from shcy_forest_head_manage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>