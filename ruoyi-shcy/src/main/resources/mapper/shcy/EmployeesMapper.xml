<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.EmployeesMapper">

    <resultMap type="Employees" id="EmployeesResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="name"    column="name"    />
        <result property="idCard"    column="id_card"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="duty"  column="duty"/>
    </resultMap>

    <sql id="selectEmployeesVo">
        select id, shop_id, name, id_card,duty, contact_phone, create_time, update_time from shcy_shop_employees
    </sql>
    <sql id ="selectShoEmployeesVo">
        SELECT
                se.id as id,
                ss.shop_name as shopName,
                se.shop_id as shopId,
                se.name as name,
                se.id_card as idCard,
                se.duty as  duty,
                se.contact_phone as contactPhone,
                se.create_time as createTime,
                se.update_time as updateTime,
                sd.dept_id as deptId,
                sd.dept_name  as deptName

        FROM
                shcy_shop_employees se
                LEFT JOIN shcy_shop ss ON se.shop_id = ss.id
                left join sys_dept sd on ss.shop_deparment_id = sd.dept_id
    </sql>

    <select id="selectEmployeesList" parameterType="Employees" resultType="com.ruoyi.shcy.domain.Employees">
        <include refid="selectShoEmployeesVo"/>
        <where>
            <if test="deptId != null">and sd.dept_id = #{deptId}</if>
            <if test="deptName != null and deptName !=''">and sd.dept_name = #{deptName}</if>
            <if test="shopName !=null and shopName !=''"> and ss.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="shopId != null "> and se.shop_id = #{shopId}</if>
            <if test="name != null  and name != ''"> and se.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and se.id_card = #{idCard}</if>
            <if test="duty != null  and duty != ''"> and se.duty = #{duty}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and se.contact_phone = #{contactPhone}</if>
        </where>
    </select>

    <select id="selectEmployeesById" parameterType="Long" resultMap="EmployeesResult">
        <include refid="selectEmployeesVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployees" parameterType="Employees" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_shop_employees
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="name != null">name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="duty !=null">dety,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="name != null">#{name},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="duty !=null">duty,</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmployees" parameterType="Employees">
        update shcy_shop_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="duty !=null">duty = #{duty},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeesById" parameterType="Long">
        delete from shcy_shop_employees where id = #{id}
    </delete>

    <delete id="deleteEmployeesByIds" parameterType="String">
        delete from shcy_shop_employees where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
