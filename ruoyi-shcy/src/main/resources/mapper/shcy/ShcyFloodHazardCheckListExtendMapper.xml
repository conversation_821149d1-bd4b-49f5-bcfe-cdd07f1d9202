<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyFloodHazardCheckListExtendMapper">

    <resultMap type="ShcyFloodHazardCheckListExtend" id="ShcyFloodHazardCheckListExtendResult">
        <result property="id"    column="id"    />
        <result property="checkId"    column="check_id"    />
        <result property="checkItemParent"    column="check_item_parent"    />
        <result property="checkItemChild"    column="check_item_child"    />
        <result property="foundHazardCount"    column="found_hazard_count"    />
        <result property="rectifiedHazardCount"    column="rectified_hazard_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectShcyFloodHazardCheckListExtendVo">
        select id, check_id, check_item_parent, check_item_child, found_hazard_count, rectified_hazard_count, create_time, update_time, create_by, update_by from shcy_flood_hazard_check_list_extend
    </sql>

    <select id="selectShcyFloodHazardCheckListExtendList" parameterType="ShcyFloodHazardCheckListExtend" resultMap="ShcyFloodHazardCheckListExtendResult">
        <include refid="selectShcyFloodHazardCheckListExtendVo"/>
        <where>
            <if test="checkId != null "> and check_id = #{checkId}</if>
            <if test="checkItemParent != null "> and check_item_parent = #{checkItemParent}</if>
            <if test="checkItemChild != null "> and check_item_child = #{checkItemChild}</if>
            <if test="foundHazardCount != null "> and found_hazard_count = #{foundHazardCount}</if>
            <if test="rectifiedHazardCount != null "> and rectified_hazard_count = #{rectifiedHazardCount}</if>
        </where>
    </select>

    <select id="selectShcyFloodHazardCheckListExtendById" parameterType="Long" resultMap="ShcyFloodHazardCheckListExtendResult">
        <include refid="selectShcyFloodHazardCheckListExtendVo"/>
        where id = #{id}
    </select>

    <select id="sumCountByCheckId" parameterType="Long" resultMap="ShcyFloodHazardCheckListExtendResult">
        select sum(found_hazard_count) as found_hazard_count, sum(rectified_hazard_count) as rectified_hazard_count from shcy_flood_hazard_check_list_extend
        where check_id = #{id}
    </select>

    <insert id="insertShcyFloodHazardCheckListExtend" parameterType="ShcyFloodHazardCheckListExtend" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_flood_hazard_check_list_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkId != null">check_id,</if>
            <if test="checkItemParent != null">check_item_parent,</if>
            <if test="checkItemChild != null">check_item_child,</if>
            <if test="foundHazardCount != null">found_hazard_count,</if>
            <if test="rectifiedHazardCount != null">rectified_hazard_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkId != null">#{checkId},</if>
            <if test="checkItemParent != null">#{checkItemParent},</if>
            <if test="checkItemChild != null">#{checkItemChild},</if>
            <if test="foundHazardCount != null">#{foundHazardCount},</if>
            <if test="rectifiedHazardCount != null">#{rectifiedHazardCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateShcyFloodHazardCheckListExtend" parameterType="ShcyFloodHazardCheckListExtend">
        update shcy_flood_hazard_check_list_extend
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkId != null">check_id = #{checkId},</if>
            <if test="checkItemParent != null">check_item_parent = #{checkItemParent},</if>
            <if test="checkItemChild != null">check_item_child = #{checkItemChild},</if>
            <if test="foundHazardCount != null">found_hazard_count = #{foundHazardCount},</if>
            <if test="rectifiedHazardCount != null">rectified_hazard_count = #{rectifiedHazardCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyFloodHazardCheckListExtendById" parameterType="Long">
        delete from shcy_flood_hazard_check_list_extend where id = #{id}
    </delete>

    <delete id="deleteShcyFloodHazardCheckListExtendByIds" parameterType="String">
        delete from shcy_flood_hazard_check_list_extend where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
