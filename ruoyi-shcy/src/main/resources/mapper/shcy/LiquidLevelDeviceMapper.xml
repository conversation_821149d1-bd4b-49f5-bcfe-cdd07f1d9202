<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.LiquidLevelDeviceMapper">
    
    <resultMap type="LiquidLevelDevice" id="LiquidLevelDeviceResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceImei"    column="device_imei"    />
        <result property="address"    column="address"    />
        <result property="monitoredWaterBody"    column="monitored_water_body"    />
        <result property="pipelineType"    column="pipeline_type"    />
        <result property="drainageDirection"    column="drainage_direction"    />
        <result property="responsibleUnit"    column="responsible_unit"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactNumber"    column="contact_number"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="deviceState" column="device_state" />
        <result property="cameras" column="cameras" />
        <result property="images" column="images" />
        <result property="pumpStationArea" column="pump_station_area" />
        <result property="alarmStatus" column="alarm_status" />
    </resultMap>

    <sql id="selectLiquidLevelDeviceVo">
        select id, device_name, device_imei, address, monitored_water_body, pipeline_type, drainage_direction, responsible_unit, contact_person, contact_number, type, coordinate, device_state, cameras, images, pump_station_area, alarm_status from shcy_liquid_level_device
    </sql>

    <select id="selectLiquidLevelDeviceList" parameterType="LiquidLevelDevice" resultMap="LiquidLevelDeviceResult">
        <include refid="selectLiquidLevelDeviceVo"/>
        <where>  
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceImei != null  and deviceImei != ''"> and device_imei = #{deviceImei}</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
            <if test="deviceState != null and deviceState != ''"> and device_state = #{deviceState}</if>
            <if test="alarmStatus != null and alarmStatus != ''"> and alarm_status = #{alarmStatus}</if>
        </where>
    </select>
    
    <select id="selectLiquidLevelDeviceById" parameterType="Long" resultMap="LiquidLevelDeviceResult">
        <include refid="selectLiquidLevelDeviceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLiquidLevelDevice" parameterType="LiquidLevelDevice" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_liquid_level_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">device_name,</if>
            <if test="deviceImei != null">device_imei,</if>
            <if test="address != null">address,</if>
            <if test="monitoredWaterBody != null">monitored_water_body,</if>
            <if test="pipelineType != null">pipeline_type,</if>
            <if test="drainageDirection != null">drainage_direction,</if>
            <if test="responsibleUnit != null">responsible_unit,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="deviceState != null">device_state,</if>
            <if test="cameras != null">cameras,</if>
            <if test="images != null">images,</if>
            <if test="pumpStationArea != null">pump_station_area,</if>
            <if test="alarmStatus != null">alarm_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceImei != null">#{deviceImei},</if>
            <if test="address != null">#{address},</if>
            <if test="monitoredWaterBody != null">#{monitoredWaterBody},</if>
            <if test="pipelineType != null">#{pipelineType},</if>
            <if test="drainageDirection != null">#{drainageDirection},</if>
            <if test="responsibleUnit != null">#{responsibleUnit},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="deviceState != null">#{deviceState},</if>
            <if test="cameras != null">#{cameras},</if>
            <if test="images != null">#{images},</if>
            <if test="pumpStationArea != null">#{pumpStationArea},</if>
            <if test="alarmStatus != null">#{alarmStatus},</if>
         </trim>
    </insert>

    <update id="updateLiquidLevelDevice" parameterType="LiquidLevelDevice">
        update shcy_liquid_level_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceImei != null">device_imei = #{deviceImei},</if>
            <if test="address != null">address = #{address},</if>
            <if test="monitoredWaterBody != null">monitored_water_body = #{monitoredWaterBody},</if>
            <if test="pipelineType != null">pipeline_type = #{pipelineType},</if>
            <if test="drainageDirection != null">drainage_direction = #{drainageDirection},</if>
            <if test="responsibleUnit != null">responsible_unit = #{responsibleUnit},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="deviceState != null">device_state = #{deviceState},</if>
            <if test="cameras != null">cameras = #{cameras},</if>
            <if test="images != null">images = #{images},</if>
            <if test="pumpStationArea != null">pump_station_area = #{pumpStationArea},</if>
            <if test="alarmStatus != null">alarm_status = #{alarmStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLiquidLevelDeviceById" parameterType="Long">
        delete from shcy_liquid_level_device where id = #{id}
    </delete>

    <delete id="deleteLiquidLevelDeviceByIds" parameterType="String">
        delete from shcy_liquid_level_device where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="fxftAddress" resultType="java.lang.String">
        select address from shcy_liquid_level_device
    </select>

    <select id="selectImeiList" resultType="java.lang.String">
        select device_imei from shcy_liquid_level_device
    </select>

    <select id="selectLiquidLevelDeviceByDeviceImei" parameterType="String" resultMap="LiquidLevelDeviceResult">
        <include refid="selectLiquidLevelDeviceVo"/>
        where device_imei = #{deviceImei} limit 1
    </select>

    <update id="batchUpdateLiquidLevelDevice" parameterType="java.util.List">
        UPDATE shcy_liquid_level_device
        SET alarm_status = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.alarmStatus}
        </foreach>
        ELSE alarm_status
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateLiquidLevelDeviceState" parameterType="java.util.List">
        UPDATE shcy_liquid_level_device
        SET device_state = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.deviceState}
        </foreach>
        ELSE device_state
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>