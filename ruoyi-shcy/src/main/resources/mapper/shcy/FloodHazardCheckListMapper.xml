<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.FloodHazardCheckListMapper">

    <resultMap type="FloodHazardCheckList" id="FloodHazardCheckListResult">
        <result property="id"    column="id"    />
        <result property="checkLocation"    column="check_location"    />
        <result property="checkPerson"    column="check_person"    />
        <result property="contactNumber"    column="contact_number"    />
        <result property="checkPeople"    column="check_people"    />
        <result property="checkTime"    column="check_time"    />
        <result property="checkItem"    column="check_item"    />
        <result property="foundHazardCount"    column="found_hazard_count"    />
        <result property="rectifiedHazardCount"    column="rectified_hazard_count"    />
        <result property="remark"    column="remark"    />
        <result property="residentCommunityName"    column="resident_community_name"    />
        <result property="communityName"    column="community_name"    />
        <result property="communitySecretary"    column="community_secretary"    />
        <result property="communityHygieneDirector"    column="community_hygiene_director"    />
        <result property="solveRate"    column="solve_rate"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="caseEndTime"    column="case_end_time"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
        <result property="eventNo"    column="event_no"    />
    </resultMap>

    <sql id="selectFloodHazardCheckListVo">
        select id, check_location, event_no,check_person, contact_number, check_people, check_time, check_item, found_hazard_count, rectified_hazard_count, remark,
               resident_community_name, community_name, community_secretary, community_hygiene_director, solve_rate, publish_time, case_end_time, circulation_state, case_finish_time, deal_in_time_state from shcy_flood_hazard_check_list
    </sql>

    <select id="selectFloodHazardCheckListList" parameterType="FloodHazardCheckList" resultMap="FloodHazardCheckListResult">
        <include refid="selectFloodHazardCheckListVo"/>
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="checkLocation != null  and checkLocation != ''"> and check_location = #{checkLocation}</if>
            <if test="residentCommunityName != null  and residentCommunityName != ''"> and resident_community_name = #{residentCommunityName}</if>
            <if test="communityName != null  and communityName != ''"> and community_name = #{communityName}</if>
            <if test="eventNo != null  and eventNo != ''"> and event_no = #{eventNo}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or resident_community_name like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="selectFloodHazardCheckListById" parameterType="Long" resultMap="FloodHazardCheckListResult">
        <include refid="selectFloodHazardCheckListVo"/>
        where id = #{id}
    </select>

    <insert id="insertFloodHazardCheckList" parameterType="FloodHazardCheckList" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_flood_hazard_check_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkLocation != null">check_location,</if>
            <if test="checkPerson != null">check_person,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="checkPeople != null">check_people,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="checkItem != null">check_item,</if>
            <if test="foundHazardCount != null">found_hazard_count,</if>
            <if test="rectifiedHazardCount != null">rectified_hazard_count,</if>
            <if test="remark != null">remark,</if>
            <if test="residentCommunityName != null">resident_community_name,</if>
            <if test="communityName != null">community_name,</if>
            <if test="communitySecretary != null">community_secretary,</if>
            <if test="communityHygieneDirector != null">community_hygiene_director,</if>
            <if test="solveRate != null">solve_rate,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="caseEndTime != null">case_end_time,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="eventNo != null">event_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkLocation != null">#{checkLocation},</if>
            <if test="checkPerson != null">#{checkPerson},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="checkPeople != null">#{checkPeople},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="checkItem != null">#{checkItem},</if>
            <if test="foundHazardCount != null">#{foundHazardCount},</if>
            <if test="rectifiedHazardCount != null">#{rectifiedHazardCount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="residentCommunityName != null">#{residentCommunityName},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="communitySecretary != null">#{communitySecretary},</if>
            <if test="communityHygieneDirector != null">#{communityHygieneDirector},</if>
            <if test="solveRate != null">#{solveRate},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="caseEndTime != null">#{caseEndTime},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="eventNo != null">#{eventNo},</if>
         </trim>
    </insert>

    <update id="updateFloodHazardCheckList" parameterType="FloodHazardCheckList">
        update shcy_flood_hazard_check_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkLocation != null">check_location = #{checkLocation},</if>
            <if test="checkPerson != null">check_person = #{checkPerson},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="checkPeople != null">check_people = #{checkPeople},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="checkItem != null">check_item = #{checkItem},</if>
            <if test="foundHazardCount != null">found_hazard_count = #{foundHazardCount},</if>
            <if test="rectifiedHazardCount != null">rectified_hazard_count = #{rectifiedHazardCount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="residentCommunityName != null">resident_community_name = #{residentCommunityName},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="communitySecretary != null">community_secretary = #{communitySecretary},</if>
            <if test="communityHygieneDirector != null">community_hygiene_director = #{communityHygieneDirector},</if>
            <if test="solveRate != null">solve_rate = #{solveRate},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="caseEndTime != null">case_end_time = #{caseEndTime},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="eventNo != null">event_no = #{eventNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFloodHazardCheckListById" parameterType="Long">
        delete from shcy_flood_hazard_check_list where id = #{id}
    </delete>

    <delete id="deleteFloodHazardCheckListByIds" parameterType="String">
        delete from shcy_flood_hazard_check_list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="FloodHazardCheckList" resultType="long">
        select count(1) from shcy_flood_hazard_check_list
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or resident_community_name like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>
</mapper>
