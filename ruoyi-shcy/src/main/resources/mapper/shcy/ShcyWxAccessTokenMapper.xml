<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyWxAccessTokenMapper">
    
    <resultMap type="ShcyWxAccessToken" id="ShcyWxAccessTokenResult">
        <result property="id"    column="id"    />
        <result property="accessToken"    column="access_token"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectShcyWxAccessTokenVo">
        select id, access_token, expire_time, source from shcy_wx_access_token
    </sql>

    <select id="selectShcyWxAccessTokenList" parameterType="ShcyWxAccessToken" resultMap="ShcyWxAccessTokenResult">
        <include refid="selectShcyWxAccessTokenVo"/>
        <where>  
            <if test="accessToken != null  and accessToken != ''"> and access_token = #{accessToken}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
    </select>
    
    <select id="selectShcyWxAccessTokenById" parameterType="Long" resultMap="ShcyWxAccessTokenResult">
        <include refid="selectShcyWxAccessTokenVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShcyWxAccessToken" parameterType="ShcyWxAccessToken" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_wx_access_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accessToken != null">access_token,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="source != null">source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accessToken != null">#{accessToken},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="source != null">#{source},</if>
         </trim>
    </insert>

    <update id="updateShcyWxAccessToken" parameterType="ShcyWxAccessToken">
        update shcy_wx_access_token
        <trim prefix="SET" suffixOverrides=",">
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyWxAccessTokenById" parameterType="Long">
        delete from shcy_wx_access_token where id = #{id}
    </delete>

    <delete id="deleteShcyWxAccessTokenByIds" parameterType="String">
        delete from shcy_wx_access_token where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>