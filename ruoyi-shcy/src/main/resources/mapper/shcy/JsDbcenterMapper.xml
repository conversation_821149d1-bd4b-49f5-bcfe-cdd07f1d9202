<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.JsDbcenterMapper">
    
    <resultMap type="JsDbcenter" id="JsDbcenterResult">
        <result property="id"    column="ID"    />
        <result property="isstandard"    column="ISSTANDARD"    />
        <result property="checkimage"    column="CHECKIMAGE"    />
        <result property="servicetypename"    column="SERVICETYPENAME"    />
        <result property="wpType"    column="WP_TYPE"    />
        <result property="isfirstcontact"    column="ISFIRSTCONTACT"    />
        <result property="hyname"    column="HYNAME"    />
        <result property="upkeepername"    column="UPKEEPERNAME"    />
        <result property="deptname"    column="DEPTNAME"    />
        <result property="statusname"    column="STATUSNAME"    />
        <result property="synctime"    column="SYNCTIME"    />
        <result property="lastcontacttime"    column="LASTCONTACTTIME"    />
        <result property="accepttime"    column="ACCEPTTIME"    />
        <result property="cancletime"    column="CANCLETIME"    />
        <result property="lastsolvingtime"    column="LASTSOLVINGTIME"    />
        <result property="importantsolvingtime"    column="IMPORTANTSOLVINGTIME"    />
        <result property="middlesolvingtime"    column="MIDDLESOLVINGTIME"    />
        <result property="allendtime"    column="ALLENDTIME"    />
        <result property="allimportanttime"    column="ALLIMPORTANTTIME"    />
        <result property="allmiddletime"    column="ALLMIDDLETIME"    />
        <result property="endtime"    column="ENDTIME"    />
        <result property="telasktime"    column="TELASKTIME"    />
        <result property="solvingtime"    column="SOLVINGTIME"    />
        <result property="dispatchtime"    column="DISPATCHTIME"    />
        <result property="createtime"    column="CREATETIME"    />
        <result property="percreatetime"    column="PERCREATETIME"    />
        <result property="discovertime"    column="DISCOVERTIME"    />
        <result property="executedeptname"    column="EXECUTEDEPTNAME"    />
        <result property="workgridcode"    column="WORKGRIDCODE"    />
        <result property="communityname"    column="COMMUNITYNAME"    />
        <result property="streetname"    column="STREETNAME"    />
        <result property="workgrid"    column="WORKGRID"    />
        <result property="address"    column="ADDRESS"    />
        <result property="coordy"    column="COORDY"    />
        <result property="coordx"    column="COORDX"    />
        <result property="gridcode"    column="GRIDCODE"    />
        <result property="communitycode"    column="COMMUNITYCODE"    />
        <result property="streetcode"    column="STREETCODE"    />
        <result property="viewinfo"    column="VIEWINFO"    />
        <result property="casevaluation12345"    column="CASEVALUATION_12345"    />
        <result property="notReason"    column="NOT_REASON"    />
        <result property="description12345"    column="DESCRIPTION_12345"    />
        <result property="appealExplain"    column="APPEAL_EXPLAIN"    />
        <result property="banliresult12345"    column="BANLIRESULT_12345"    />
        <result property="wpSource"    column="WP_SOURCE"    />
        <result property="reportdeptname"    column="REPORTDEPTNAME"    />
        <result property="hotlinesn"    column="HOTLINESN"    />
        <result property="banliresult"    column="BANLIRESULT"    />
        <result property="duLimit"    column="DU_LIMIT"    />
        <result property="urgeCount"    column="URGE_COUNT"    />
        <result property="callbackFlag"    column="CALLBACK_FLAG"    />
        <result property="userevaluate"    column="USEREVALUATE"    />
        <result property="isanonymity"    column="ISANONYMITY"    />
        <result property="servicetype"    column="SERVICETYPE"    />
        <result property="similarcasesn"    column="SIMILARCASESN"    />
        <result property="approach"    column="APPROACH"    />
        <result property="urgentdegree"    column="URGENTDEGREE"    />
        <result property="partsn"    column="PARTSN"    />
        <result property="endnote"    column="ENDNOTE"    />
        <result property="dispatchnote"    column="DISPATCHNOTE"    />
        <result property="reporter"    column="REPORTER"    />
        <result property="infozcname"    column="INFOZCNAME"    />
        <result property="infoscname"    column="INFOSCNAME"    />
        <result property="infobcname"    column="INFOBCNAME"    />
        <result property="infotypename"    column="INFOTYPENAME"    />
        <result property="infosourcename"    column="INFOSOURCENAME"    />
        <result property="contactinfo"    column="CONTACTINFO"    />
        <result property="hastentypecount"    column="HASTENTYPECOUNT"    />
        <result property="hasleadtypecount"    column="HASLEADTYPECOUNT"    />
        <result property="huifangcount"    column="HUIFANGCOUNT"    />
        <result property="hechacount"    column="HECHACOUNT"    />
        <result property="heshicount"    column="HESHICOUNT"    />
        <result property="contactmode"    column="CONTACTMODE"    />
        <result property="callnumber"    column="CALLNUMBER"    />
        <result property="priorityarea"    column="PRIORITYAREA"    />
        <result property="checkresult"    column="CHECKRESULT"    />
        <result property="verifyresult"    column="VERIFYRESULT"    />
        <result property="endresult"    column="ENDRESULT"    />
        <result property="caseend"    column="CASEEND"    />
        <result property="insertuser"    column="INSERTUSER"    />
        <result property="keepersn"    column="KEEPERSN"    />
        <result property="insertdeptcode"    column="INSERTDEPTCODE"    />
        <result property="executedeptcode"    column="EXECUTEDEPTCODE"    />
        <result property="deptcode"    column="DEPTCODE"    />
        <result property="status"    column="STATUS"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="infoatcode"    column="INFOATCODE"    />
        <result property="infozccode"    column="INFOZCCODE"    />
        <result property="infosccode"    column="INFOSCCODE"    />
        <result property="infobccode"    column="INFOBCCODE"    />
        <result property="infotypeid"    column="INFOTYPEID"    />
        <result property="infosourceid"    column="INFOSOURCEID"    />
        <result property="casesn"    column="CASESN"    />
        <result property="taskid"    column="TASKID"    />
        <result property="subexecutedeptnameMh"    column="SUBEXECUTEDEPTNAME_MH"    />
        <result property="imagefilename"    column="IMAGEFILENAME"    />
    </resultMap>

    <sql id="selectJsDbcenterVo">
        select ID, ISSTANDARD, CHECKIMAGE, SERVICETYPENAME, WP_TYPE, ISFIRSTCONTACT, HYNAME, UPKEEPERNAME, DEPTNAME, STATUSNAME, SYNCTIME, LASTCONTACTTIME, ACCEPTTIME, CANCLETIME, LASTSOLVINGTIME, IMPORTANTSOLVINGTIME, MIDDLESOLVINGTIME, ALLENDTIME, ALLIMPORTANTTIME, ALLMIDDLETIME, ENDTIME, TELASKTIME, SOLVINGTIME, DISPATCHTIME, CREATETIME, PERCREATETIME, DISCOVERTIME, EXECUTEDEPTNAME, WORKGRIDCODE, COMMUNITYNAME, STREETNAME, WORKGRID, ADDRESS, COORDY, COORDX, GRIDCODE, COMMUNITYCODE, STREETCODE, VIEWINFO, CASEVALUATION_12345, NOT_REASON, DESCRIPTION_12345, APPEAL_EXPLAIN, BANLIRESULT_12345, WP_SOURCE, REPORTDEPTNAME, HOTLINESN, BANLIRESULT, DU_LIMIT, URGE_COUNT, CALLBACK_FLAG, USEREVALUATE, ISANONYMITY, SERVICETYPE, SIMILARCASESN, APPROACH, URGENTDEGREE, PARTSN, ENDNOTE, DISPATCHNOTE, REPORTER, INFOZCNAME, INFOSCNAME, INFOBCNAME, INFOTYPENAME, INFOSOURCENAME, CONTACTINFO, HASTENTYPECOUNT, HASLEADTYPECOUNT, HUIFANGCOUNT, HECHACOUNT, HESHICOUNT, CONTACTMODE, CALLNUMBER, PRIORITYAREA, CHECKRESULT, VERIFYRESULT, ENDRESULT, CASEEND, INSERTUSER, KEEPERSN, INSERTDEPTCODE, EXECUTEDEPTCODE, DEPTCODE, STATUS, DESCRIPTION, INFOATCODE, INFOZCCODE, INFOSCCODE, INFOBCCODE, INFOTYPEID, INFOSOURCEID, CASESN, TASKID, SUBEXECUTEDEPTNAME_MH, IMAGEFILENAME from JSDBCENTER.T_TASK_DBCENTER
    </sql>

    <select id="selectJsDbcenterById" parameterType="Long" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        where ID = #{id}
    </select>

    <select id="selectJsDbcenterByTaskid" parameterType="String" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        where TASKID = #{taskid}
    </select>

    <select id="selectJsDbcenterList" parameterType="JsDbcenter" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        <where>
            <if test="taskid != null  and taskid != ''"> and TASKID = #{taskid}</if>
        </where>
        ORDER BY DISCOVERTIME DESC
    </select>

    <select id="selectJsDbcenterSyncList" parameterType="String" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectJsDbcenterSyncListByTaskid" parameterType="String" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        where TASKID in
        <foreach item="taskid" collection="array" open="(" separator="," close=")">
            #{taskid}
        </foreach>
    </select>

    <select id="selectJsDbcenter12345List" parameterType="java.util.Map" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        WHERE INFOSOURCENAME = '12345上报'
        AND EXECUTEDEPTNAME = '石化街道'
        AND STATUSNAME != '已退回其他平台'
        AND DISCOVERTIME &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND DISCOVERTIME &lt; TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
    </select>

    <select id="selectJsDbcenterCaseList" parameterType="java.util.Map" resultMap="JsDbcenterResult">
        <include refid="selectJsDbcenterVo"/>
        WHERE INFOSOURCENAME IN ('专职监督员上报', '区级网格上报', '金山区专项调查', '兼职监督员上报')
        AND (EXECUTEDEPTNAME = '石化街道' OR DEPTNAME = '石化街道')
        <!--AND INFOBCNAME != '小区事务'-->
        AND CREATETIME &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
        AND CREATETIME &lt; TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
    </select>


</mapper>