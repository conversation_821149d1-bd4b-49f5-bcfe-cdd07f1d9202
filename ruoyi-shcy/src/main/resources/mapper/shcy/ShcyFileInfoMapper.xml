<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyFileInfoMapper">
    
    <resultMap type="ShcyFileInfo" id="ShcyFileInfoResult">
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="formId"    column="form_id"    />
    </resultMap>

    <sql id="selectShcyFileInfoVo">
        select file_id, file_name, file_path, form_id from shcy_file_info
    </sql>

    <select id="selectShcyFileInfoList" parameterType="ShcyFileInfo" resultMap="ShcyFileInfoResult">
        <include refid="selectShcyFileInfoVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="formId != null "> and form_id = #{formId}</if>
        </where>
    </select>
    
    <select id="selectShcyFileInfoByFileId" parameterType="Long" resultMap="ShcyFileInfoResult">
        <include refid="selectShcyFileInfoVo"/>
        where file_id = #{fileId}
    </select>
        
    <insert id="insertShcyFileInfo" parameterType="ShcyFileInfo" useGeneratedKeys="true" keyProperty="fileId">
        insert into shcy_file_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="formId != null">form_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="formId != null">#{formId},</if>
         </trim>
    </insert>

    <update id="updateShcyFileInfo" parameterType="ShcyFileInfo">
        update shcy_file_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="formId != null">form_id = #{formId},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteShcyFileInfoByFileId" parameterType="Long">
        delete from shcy_file_info where file_id = #{fileId}
    </delete>

    <delete id="deleteShcyFileInfoByFileIds" parameterType="String">
        delete from shcy_file_info where file_id in 
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

    <select id="selectShcyFileInfoByFileIds" resultMap="ShcyFileInfoResult">
        <include refid="selectShcyFileInfoVo"/>
        where file_id in
        <foreach collection="list" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </select>
</mapper>