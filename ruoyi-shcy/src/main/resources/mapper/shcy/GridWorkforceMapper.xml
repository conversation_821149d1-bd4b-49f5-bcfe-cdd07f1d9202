<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.GridWorkforceMapper">
    
    <resultMap type="GridWorkforce" id="GridWorkforceResult">
        <result property="id"    column="id"    />
        <result property="gridId"    column="grid_id"    />
        <result property="category"    column="category"    />
        <result property="identity"    column="identity"    />
        <result property="name"    column="name"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="politicalStatus"    column="political_status"    />
        <result property="position"    column="position"    />
        <result property="contact"    column="contact"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectGridWorkforceVo">
        select id, grid_id, category, identity, name, birth_date, political_status, position, contact, order_num from shcy_grid_workforce
    </sql>

    <select id="selectGridWorkforceList" parameterType="GridWorkforce" resultMap="GridWorkforceResult">
        <include refid="selectGridWorkforceVo"/>
        <where>  
            <if test="gridId != null "> and grid_id = #{gridId}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="identity != null  and identity != ''"> and identity = #{identity}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="birthDate != null "> and birth_date = #{birthDate}</if>
            <if test="politicalStatus != null  and politicalStatus != ''"> and political_status = #{politicalStatus}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by order_num asc
    </select>
    
    <select id="selectGridWorkforceById" parameterType="Long" resultMap="GridWorkforceResult">
        <include refid="selectGridWorkforceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGridWorkforce" parameterType="GridWorkforce" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_grid_workforce
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gridId != null">grid_id,</if>
            <if test="category != null">category,</if>
            <if test="identity != null">identity,</if>
            <if test="name != null">name,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="position != null">position,</if>
            <if test="contact != null">contact,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gridId != null">#{gridId},</if>
            <if test="category != null">#{category},</if>
            <if test="identity != null">#{identity},</if>
            <if test="name != null">#{name},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="position != null">#{position},</if>
            <if test="contact != null">#{contact},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateGridWorkforce" parameterType="GridWorkforce">
        update shcy_grid_workforce
        <trim prefix="SET" suffixOverrides=",">
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="identity != null">identity = #{identity},</if>
            <if test="name != null">name = #{name},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="position != null">position = #{position},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGridWorkforceById" parameterType="Long">
        delete from shcy_grid_workforce where id = #{id}
    </delete>

    <delete id="deleteGridWorkforceByIds" parameterType="String">
        delete from shcy_grid_workforce where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>