<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.TaskDbcenterMapper">
    
    <resultMap type="TaskDbcenter" id="TaskDbcenterResult">
        <result property="id"    column="ID"    />
        <result property="isstandard"    column="ISSTANDARD"    />
        <result property="checkimage"    column="CHECKIMAGE"    />
        <result property="servicetypename"    column="SERVICETYPENAME"    />
        <result property="wpType"    column="WP_TYPE"    />
        <result property="isfirstcontact"    column="ISFIRSTCONTACT"    />
        <result property="hyname"    column="HYNAME"    />
        <result property="upkeepername"    column="UPKEEPERNAME"    />
        <result property="deptname"    column="DEPTNAME"    />
        <result property="statusname"    column="STATUSNAME"    />
        <result property="synctime"    column="SYNCTIME"    />
        <result property="lastcontacttime"    column="LASTCONTACTTIME"    />
        <result property="accepttime"    column="ACCEPTTIME"    />
        <result property="cancletime"    column="CANCLETIME"    />
        <result property="lastsolvingtime"    column="LASTSOLVINGTIME"    />
        <result property="importantsolvingtime"    column="IMPORTANTSOLVINGTIME"    />
        <result property="middlesolvingtime"    column="MIDDLESOLVINGTIME"    />
        <result property="allendtime"    column="ALLENDTIME"    />
        <result property="allimportanttime"    column="ALLIMPORTANTTIME"    />
        <result property="allmiddletime"    column="ALLMIDDLETIME"    />
        <result property="endtime"    column="ENDTIME"    />
        <result property="telasktime"    column="TELASKTIME"    />
        <result property="solvingtime"    column="SOLVINGTIME"    />
        <result property="dispatchtime"    column="DISPATCHTIME"    />
        <result property="createtime"    column="CREATETIME"    />
        <result property="percreatetime"    column="PERCREATETIME"    />
        <result property="discovertime"    column="DISCOVERTIME"    />
        <result property="executedeptname"    column="EXECUTEDEPTNAME"    />
        <result property="workgridcode"    column="WORKGRIDCODE"    />
        <result property="communityname"    column="COMMUNITYNAME"    />
        <result property="streetname"    column="STREETNAME"    />
        <result property="workgrid"    column="WORKGRID"    />
        <result property="address"    column="ADDRESS"    />
        <result property="coordy"    column="COORDY"    />
        <result property="coordx"    column="COORDX"    />
        <result property="gridcode"    column="GRIDCODE"    />
        <result property="communitycode"    column="COMMUNITYCODE"    />
        <result property="streetcode"    column="STREETCODE"    />
        <result property="viewinfo"    column="VIEWINFO"    />
        <result property="casevaluation12345"    column="CASEVALUATION_12345"    />
        <result property="notReason"    column="NOT_REASON"    />
        <result property="description12345"    column="DESCRIPTION_12345"    />
        <result property="appealExplain"    column="APPEAL_EXPLAIN"    />
        <result property="banliresult12345"    column="BANLIRESULT_12345"    />
        <result property="wpSource"    column="WP_SOURCE"    />
        <result property="reportdeptname"    column="REPORTDEPTNAME"    />
        <result property="hotlinesn"    column="HOTLINESN"    />
        <result property="banliresult"    column="BANLIRESULT"    />
        <result property="duLimit"    column="DU_LIMIT"    />
        <result property="urgeCount"    column="URGE_COUNT"    />
        <result property="callbackFlag"    column="CALLBACK_FLAG"    />
        <result property="userevaluate"    column="USEREVALUATE"    />
        <result property="isanonymity"    column="ISANONYMITY"    />
        <result property="servicetype"    column="SERVICETYPE"    />
        <result property="similarcasesn"    column="SIMILARCASESN"    />
        <result property="approach"    column="APPROACH"    />
        <result property="urgentdegree"    column="URGENTDEGREE"    />
        <result property="partsn"    column="PARTSN"    />
        <result property="endnote"    column="ENDNOTE"    />
        <result property="dispatchnote"    column="DISPATCHNOTE"    />
        <result property="reporter"    column="REPORTER"    />
        <result property="infozcname"    column="INFOZCNAME"    />
        <result property="infoscname"    column="INFOSCNAME"    />
        <result property="infobcname"    column="INFOBCNAME"    />
        <result property="infotypename"    column="INFOTYPENAME"    />
        <result property="infosourcename"    column="INFOSOURCENAME"    />
        <result property="contactinfo"    column="CONTACTINFO"    />
        <result property="hastentypecount"    column="HASTENTYPECOUNT"    />
        <result property="hasleadtypecount"    column="HASLEADTYPECOUNT"    />
        <result property="huifangcount"    column="HUIFANGCOUNT"    />
        <result property="hechacount"    column="HECHACOUNT"    />
        <result property="heshicount"    column="HESHICOUNT"    />
        <result property="contactmode"    column="CONTACTMODE"    />
        <result property="callnumber"    column="CALLNUMBER"    />
        <result property="priorityarea"    column="PRIORITYAREA"    />
        <result property="checkresult"    column="CHECKRESULT"    />
        <result property="verifyresult"    column="VERIFYRESULT"    />
        <result property="endresult"    column="ENDRESULT"    />
        <result property="caseend"    column="CASEEND"    />
        <result property="insertuser"    column="INSERTUSER"    />
        <result property="keepersn"    column="KEEPERSN"    />
        <result property="insertdeptcode"    column="INSERTDEPTCODE"    />
        <result property="executedeptcode"    column="EXECUTEDEPTCODE"    />
        <result property="deptcode"    column="DEPTCODE"    />
        <result property="status"    column="STATUS"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="infoatcode"    column="INFOATCODE"    />
        <result property="infozccode"    column="INFOZCCODE"    />
        <result property="infosccode"    column="INFOSCCODE"    />
        <result property="infobccode"    column="INFOBCCODE"    />
        <result property="infotypeid"    column="INFOTYPEID"    />
        <result property="infosourceid"    column="INFOSOURCEID"    />
        <result property="casesn"    column="CASESN"    />
        <result property="taskid"    column="TASKID"    />
        <result property="primarydept"    column="PRIMARYDEPT"    />
        <result property="assistdept"    column="ASSISTDEPT"    />
        <result property="subassistdept"    column="SUBASSISTDEPT"    />
        <result property="subexecutedeptnameMh"    column="SUBEXECUTEDEPTNAME_MH"    />
        <result property="imagefilename"    column="IMAGEFILENAME"    />
        <result property="satisfaction"    column="SATISFACTION"    />
        <result property="residentialarea"    column="RESIDENTIALAREA"    />
        <result property="streetarea"    column="STREETAREA"    />
        <result property="community"    column="COMMUNITY"    />
        <result property="property"    column="PROPERTY"    />
        <result property="powerstorage"    column="POWERSTORAGE"    />
        <result property="trackingtype"    column="TRACKINGTYPE"    />
        <result property="isduplicate"    column="ISDUPLICATE"    />
        <result property="overdue"    column="OVERDUE"    />
        <result property="recenthotlinesn"    column="RECENTHOTLINESN"    />
        <result property="relatedhotlinesn"    column="RELATEDHOTLINESN"    />
        <result property="parentappealclassification"    column="PARENTAPPEALCLASSIFICATION"    />
        <result property="appealclassification"    column="APPEALCLASSIFICATION"    />
        <result property="parentduplicateclassification"    column="PARENTDUPLICATECLASSIFICATION"    />
        <result property="duplicateclassification"    column="DUPLICATECLASSIFICATION"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="evaluationmonth"    column="EVALUATIONMONTH"    />
        <result property="specialtopic"    column="SPECIALTOPIC"    />
        <result property="remark"    column="REMARK"    />
        <result property="isobjective"    column="ISOBJECTIVE"    />
        <result property="ischeck"    column="ISCHECK"    />
    </resultMap>

    <resultMap type="RxReportVO" id="RxReportVOResult">
        <result property="num"    column="num"    />
        <result property="parentappealclassification"    column="parentappealclassification"    />
        <result property="appealclassification"    column="appealclassification"    />
        <result property="residentialarea"    column="residentialarea"    />
        <result property="executedeptname"    column="executedeptname"    />
    </resultMap>

    <sql id="selectTaskDbcenterAllFieldsVo">
        select ID, ISSTANDARD, CHECKIMAGE, SERVICETYPENAME, WP_TYPE, ISFIRSTCONTACT, HYNAME, UPKEEPERNAME, DEPTNAME, STATUSNAME,
               SYNCTIME, LASTCONTACTTIME, ACCEPTTIME, CANCLETIME, LASTSOLVINGTIME, IMPORTANTSOLVINGTIME, MIDDLESOLVINGTIME,
               ALLENDTIME, ALLIMPORTANTTIME, ALLMIDDLETIME, ENDTIME, TELASKTIME, SOLVINGTIME, DISPATCHTIME, CREATETIME,
               PERCREATETIME, DISCOVERTIME, EXECUTEDEPTNAME, WORKGRIDCODE, COMMUNITYNAME, STREETNAME, WORKGRID, ADDRESS,
               COORDY, COORDX, GRIDCODE, COMMUNITYCODE, STREETCODE, VIEWINFO, CASEVALUATION_12345, NOT_REASON, DESCRIPTION_12345,
               APPEAL_EXPLAIN, BANLIRESULT_12345, WP_SOURCE, REPORTDEPTNAME, HOTLINESN, BANLIRESULT, DU_LIMIT, URGE_COUNT, CALLBACK_FLAG,
               USEREVALUATE, ISANONYMITY, SERVICETYPE, SIMILARCASESN, APPROACH, URGENTDEGREE, PARTSN, ENDNOTE, DISPATCHNOTE, REPORTER,
               INFOZCNAME, INFOSCNAME, INFOBCNAME, INFOTYPENAME, INFOSOURCENAME, CONTACTINFO, HASTENTYPECOUNT, HASLEADTYPECOUNT,
               HUIFANGCOUNT, HECHACOUNT, HESHICOUNT, CONTACTMODE, CALLNUMBER, PRIORITYAREA, CHECKRESULT, VERIFYRESULT, ENDRESULT,
               CASEEND, INSERTUSER, KEEPERSN, INSERTDEPTCODE, EXECUTEDEPTCODE, DEPTCODE, STATUS, DESCRIPTION, INFOATCODE, INFOZCCODE,
               INFOSCCODE, INFOBCCODE, INFOTYPEID, INFOSOURCEID, CASESN, TASKID, PRIMARYDEPT, ASSISTDEPT, SUBASSISTDEPT,
               SUBEXECUTEDEPTNAME_MH, IMAGEFILENAME, SATISFACTION, RESIDENTIALAREA, STREETAREA, POWERSTORAGE, TRACKINGTYPE, ISDUPLICATE, OVERDUE,
               RECENTHOTLINESN, RELATEDHOTLINESN, PARENTAPPEALCLASSIFICATION, APPEALCLASSIFICATION, PARENTDUPLICATECLASSIFICATION, DUPLICATECLASSIFICATION, circulation_state, EVALUATIONMONTH, SPECIALTOPIC, REMARK, ISOBJECTIVE, ISCHECK from t_task_dbcenter
    </sql>

    <sql id="selectTaskDbcenterVo">
        select ID, CHECKIMAGE, SERVICETYPENAME, WP_TYPE, HYNAME, DEPTNAME, STATUSNAME, SYNCTIME,
               ENDTIME, DISPATCHTIME, CREATETIME,
               DISCOVERTIME, EXECUTEDEPTNAME, COMMUNITYNAME, STREETNAME, ADDRESS,
               COORDY, COORDX, CASEVALUATION_12345, DESCRIPTION_12345,
               HOTLINESN,
               SERVICETYPE, APPROACH, URGENTDEGREE, PARTSN, REPORTER,
               INFOZCNAME, INFOSCNAME, INFOBCNAME, INFOTYPENAME, INFOSOURCENAME, CONTACTINFO,
               STATUS, DESCRIPTION,
               CASESN, TASKID,
               SUBEXECUTEDEPTNAME_MH, IMAGEFILENAME, SATISFACTION, RESIDENTIALAREA, STREETAREA, COMMUNITY, PROPERTY, POWERSTORAGE, TRACKINGTYPE, ISDUPLICATE, OVERDUE,
               RECENTHOTLINESN, RELATEDHOTLINESN, PARENTAPPEALCLASSIFICATION, APPEALCLASSIFICATION, PARENTDUPLICATECLASSIFICATION, DUPLICATECLASSIFICATION, circulation_state, EVALUATIONMONTH, SPECIALTOPIC, REMARK, ISOBJECTIVE, ISCHECK from t_task_dbcenter
    </sql>

    <sql id="selectSimpleTaskDbcenterVo">
        select ID, CHECKIMAGE, SERVICETYPENAME, HOTLINESN, STATUSNAME, DISCOVERTIME, ADDRESS, URGENTDEGREE, INFOSCNAME, INFOBCNAME, EXECUTEDEPTNAME, ENDTIME,
               INFOTYPENAME, INFOSOURCENAME, IMAGEFILENAME, SATISFACTION, REPORTER,
               DESCRIPTION, TASKID, SUBEXECUTEDEPTNAME_MH, ISDUPLICATE, RESIDENTIALAREA, RELATEDHOTLINESN, PARENTAPPEALCLASSIFICATION, APPEALCLASSIFICATION from t_task_dbcenter
    </sql>

    <select id="selectListByParentappealclassification" parameterType="TaskDbcenter" resultMap="RxReportVOResult">
        select count(*) as num,PARENTAPPEALCLASSIFICATION as parentappealclassification from  t_task_dbcenter
        <where>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>

            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.parentappealclassifications != null and params.parentappealclassifications != ''"><!-- 结束时间检索 -->
                AND PARENTAPPEALCLASSIFICATION in
                <foreach collection="params.parentappealclassifications" item="parentappealclassification" open="(" separator="," close=")">
                    #{parentappealclassification}
                </foreach>
            </if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
            and (PARENTAPPEALCLASSIFICATION is not null or PARENTAPPEALCLASSIFICATION != "")
        </where>
        GROUP BY PARENTAPPEALCLASSIFICATION ORDER BY num desc
    </select>

    <select id="selectXlByParentappealclassification" parameterType="TaskDbcenter" resultMap="RxReportVOResult">
        select count(*) as num,APPEALCLASSIFICATION as appealclassification from  t_task_dbcenter
        <where>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>

            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="parentappealclassification != null and parentappealclassification != ''"><!-- 结束时间检索 -->
                and PARENTAPPEALCLASSIFICATION = #{parentappealclassification}
            </if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
        </where>
        GROUP BY APPEALCLASSIFICATION ORDER BY num desc limit 6
    </select>

    <select id="selectJmqByParentappealclassification" parameterType="TaskDbcenter" resultMap="RxReportVOResult">
        select count(*) as num,RESIDENTIALAREA as residentialarea from  t_task_dbcenter
        <where>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>

            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="parentappealclassification != null and parentappealclassification != ''"><!-- 结束时间检索 -->
                and PARENTAPPEALCLASSIFICATION = #{parentappealclassification}
            </if>
            <if test="appealclassification != null and appealclassification != ''"><!-- 结束时间检索 -->
                and APPEALCLASSIFICATION = #{appealclassification}
            </if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
        </where>
        GROUP BY RESIDENTIALAREA ORDER BY num desc
    </select>

    <select id="selectTaskDbcenterListGroupByResidentialarea" parameterType="TaskDbcenter" resultMap="RxReportVOResult">
        select count(*) as num,RESIDENTIALAREA as residentialarea,PARENTAPPEALCLASSIFICATION as parentappealclassification,
        APPEALCLASSIFICATION as appealclassification,SUBEXECUTEDEPTNAME_MH as subexecutedeptnameMh   from  t_task_dbcenter
        <where>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>

            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="satisfaction != null  and satisfaction != ''"> and SATISFACTION = #{satisfaction}</if>
            <if test="evaluationmonth != null  and evaluationmonth != ''" > and EVALUATIONMONTH = #{evaluationmonth} </if>
            <if test="parentappealclassification != null and parentappealclassification != ''"><!-- 结束时间检索 -->
                and PARENTAPPEALCLASSIFICATION = #{parentappealclassification}
            </if>
            <if test="appealclassification != null and appealclassification != ''"><!-- 结束时间检索 -->
                and APPEALCLASSIFICATION = #{appealclassification}
            </if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
        </where>
        GROUP BY PARENTAPPEALCLASSIFICATION,APPEALCLASSIFICATION,RESIDENTIALAREA,SUBEXECUTEDEPTNAME_MH ORDER BY SUBEXECUTEDEPTNAME_MH desc
    </select>

    <select id="selectTaskDbcenterList" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        <where>  
            <if test="isstandard != null "> and ISSTANDARD = #{isstandard}</if>
            <if test="checkimage != null  and checkimage != ''"> and CHECKIMAGE = #{checkimage}</if>
            <if test="servicetypename != null  and servicetypename != ''"> and SERVICETYPENAME like concat('%', #{servicetypename}, '%')</if>
            <if test="wpType != null  and wpType != ''"> and WP_TYPE = #{wpType}</if>
            <if test="isfirstcontact != null "> and ISFIRSTCONTACT = #{isfirstcontact}</if>
            <if test="hyname != null  and hyname != ''"> and HYNAME like concat('%', #{hyname}, '%')</if>
            <if test="upkeepername != null  and upkeepername != ''"> and UPKEEPERNAME like concat('%', #{upkeepername}, '%')</if>
            <if test="deptname != null  and deptname != ''"> and DEPTNAME like concat('%', #{deptname}, '%')</if>
            <if test="statusname != null  and statusname != ''"> and STATUSNAME = #{statusname}</if>
            <if test="synctime != null "> and SYNCTIME = #{synctime}</if>
            <if test="lastcontacttime != null "> and LASTCONTACTTIME = #{lastcontacttime}</if>
            <if test="accepttime != null "> and ACCEPTTIME = #{accepttime}</if>
            <if test="cancletime != null "> and CANCLETIME = #{cancletime}</if>
            <if test="lastsolvingtime != null "> and LASTSOLVINGTIME = #{lastsolvingtime}</if>
            <if test="importantsolvingtime != null "> and IMPORTANTSOLVINGTIME = #{importantsolvingtime}</if>
            <if test="middlesolvingtime != null "> and MIDDLESOLVINGTIME = #{middlesolvingtime}</if>
            <if test="allendtime != null "> and ALLENDTIME = #{allendtime}</if>
            <if test="allimportanttime != null "> and ALLIMPORTANTTIME = #{allimportanttime}</if>
            <if test="allmiddletime != null "> and ALLMIDDLETIME = #{allmiddletime}</if>
            <if test="endtime != null "> and ENDTIME = #{endtime}</if>
            <if test="telasktime != null "> and TELASKTIME = #{telasktime}</if>
            <if test="solvingtime != null "> and SOLVINGTIME = #{solvingtime}</if>
            <if test="dispatchtime != null "> and DISPATCHTIME = #{dispatchtime}</if>
            <if test="createtime != null "> and CREATETIME = #{createtime}</if>
            <if test="percreatetime != null "> and PERCREATETIME = #{percreatetime}</if>
            <if test="discovertime != null "> and DISCOVERTIME = #{discovertime}</if>
            <if test="executedeptname != null  and executedeptname != ''"> and EXECUTEDEPTNAME like concat('%', #{executedeptname}, '%')</if>
            <if test="workgridcode != null  and workgridcode != ''"> and WORKGRIDCODE = #{workgridcode}</if>
            <if test="communityname != null  and communityname != ''"> and COMMUNITYNAME like concat('%', #{communityname}, '%')</if>
            <if test="streetname != null  and streetname != ''"> and STREETNAME like concat('%', #{streetname}, '%')</if>
            <if test="workgrid != null  and workgrid != ''"> and WORKGRID = #{workgrid}</if>
            <if test="address != null  and address != ''"> and ADDRESS like concat('%', #{address}, '%')</if>
            <if test="gridcode != null  and gridcode != ''"> and GRIDCODE = #{gridcode}</if>
            <if test="communitycode != null  and communitycode != ''"> and COMMUNITYCODE = #{communitycode}</if>
            <if test="streetcode != null  and streetcode != ''"> and STREETCODE = #{streetcode}</if>
            <if test="viewinfo != null  and viewinfo != ''"> and VIEWINFO = #{viewinfo}</if>
            <if test="casevaluation12345 != null "> and CASEVALUATION_12345 = #{casevaluation12345}</if>
            <if test="notReason != null  and notReason != ''"> and NOT_REASON = #{notReason}</if>
            <if test="description12345 != null  and description12345 != ''"> and DESCRIPTION_12345 = #{description12345}</if>
            <if test="appealExplain != null  and appealExplain != ''"> and APPEAL_EXPLAIN = #{appealExplain}</if>
            <if test="banliresult12345 != null "> and BANLIRESULT_12345 = #{banliresult12345}</if>
            <if test="wpSource != null  and wpSource != ''"> and WP_SOURCE = #{wpSource}</if>
            <if test="reportdeptname != null  and reportdeptname != ''"> and REPORTDEPTNAME like concat('%', #{reportdeptname}, '%')</if>
            <if test="hotlinesn != null  and hotlinesn != ''"> and HOTLINESN = #{hotlinesn}</if>
            <if test="banliresult != null "> and BANLIRESULT = #{banliresult}</if>
            <if test="duLimit != null "> and DU_LIMIT = #{duLimit}</if>
            <if test="urgeCount != null "> and URGE_COUNT = #{urgeCount}</if>
            <if test="callbackFlag != null "> and CALLBACK_FLAG = #{callbackFlag}</if>
            <if test="userevaluate != null "> and USEREVALUATE = #{userevaluate}</if>
            <if test="isanonymity != null "> and ISANONYMITY = #{isanonymity}</if>
            <if test="servicetype != null  and servicetype != ''"> and SERVICETYPE = #{servicetype}</if>
            <if test="similarcasesn != null  and similarcasesn != ''"> and SIMILARCASESN = #{similarcasesn}</if>
            <if test="approach != null "> and APPROACH = #{approach}</if>
            <if test="urgentdegree != null "> and URGENTDEGREE = #{urgentdegree}</if>
            <if test="partsn != null  and partsn != ''"> and PARTSN = #{partsn}</if>
            <if test="endnote != null  and endnote != ''"> and ENDNOTE = #{endnote}</if>
            <if test="dispatchnote != null  and dispatchnote != ''"> and DISPATCHNOTE = #{dispatchnote}</if>
            <if test="reporter != null  and reporter != ''"> and REPORTER = #{reporter}</if>
            <if test="infozcname != null  and infozcname != ''"> and INFOZCNAME = #{infozcname}</if>
            <if test="infoscname != null  and infoscname != ''"> and INFOSCNAME = #{infoscname}</if>
            <if test="infobcname != null  and infobcname != ''"> and INFOBCNAME = #{infobcname}</if>
            <if test="infotypename != null  and infotypename != ''"> and INFOTYPENAME = #{infotypename}</if>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>
            <if test="contactinfo != null  and contactinfo != ''"> and CONTACTINFO = #{contactinfo}</if>
            <if test="hastentypecount != null "> and HASTENTYPECOUNT = #{hastentypecount}</if>
            <if test="hasleadtypecount != null "> and HASLEADTYPECOUNT = #{hasleadtypecount}</if>
            <if test="huifangcount != null "> and HUIFANGCOUNT = #{huifangcount}</if>
            <if test="hechacount != null "> and HECHACOUNT = #{hechacount}</if>
            <if test="heshicount != null "> and HESHICOUNT = #{heshicount}</if>
            <if test="contactmode != null  and contactmode != ''"> and CONTACTMODE = #{contactmode}</if>
            <if test="callnumber != null  and callnumber != ''"> and CALLNUMBER = #{callnumber}</if>
            <if test="priorityarea != null  and priorityarea != ''"> and PRIORITYAREA = #{priorityarea}</if>
            <if test="checkresult != null "> and CHECKRESULT = #{checkresult}</if>
            <if test="verifyresult != null "> and VERIFYRESULT = #{verifyresult}</if>
            <if test="endresult != null "> and ENDRESULT = #{endresult}</if>
            <if test="caseend != null "> and CASEEND = #{caseend}</if>
            <if test="insertuser != null  and insertuser != ''"> and INSERTUSER = #{insertuser}</if>
            <if test="keepersn != null  and keepersn != ''"> and KEEPERSN = #{keepersn}</if>
            <if test="insertdeptcode != null  and insertdeptcode != ''"> and INSERTDEPTCODE = #{insertdeptcode}</if>
            <if test="executedeptcode != null  and executedeptcode != ''"> and EXECUTEDEPTCODE = #{executedeptcode}</if>
            <if test="deptcode != null  and deptcode != ''"> and DEPTCODE = #{deptcode}</if>
            <if test="status != null "> and STATUS = #{status}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION like concat('%', #{description}, '%')</if>
            <if test="infoatcode != null  and infoatcode != ''"> and INFOATCODE = #{infoatcode}</if>
            <if test="infozccode != null  and infozccode != ''"> and INFOZCCODE = #{infozccode}</if>
            <if test="infosccode != null  and infosccode != ''"> and INFOSCCODE = #{infosccode}</if>
            <if test="infobccode != null  and infobccode != ''"> and INFOBCCODE = #{infobccode}</if>
            <if test="infotypeid != null "> and INFOTYPEID = #{infotypeid}</if>
            <if test="infosourceid != null "> and INFOSOURCEID = #{infosourceid}</if>
            <if test="casesn != null  and casesn != ''"> and CASESN = #{casesn}</if>
            <if test="taskid != null  and taskid != ''"> and TASKID = #{taskid}</if>
            <if test="isduplicate != null  and isduplicate != ''"> and ISDUPLICATE = #{isduplicate}</if>
            <if test="overdue != null  and overdue != ''"> and OVERDUE = #{overdue}</if>
            <if test="satisfaction != null  and satisfaction != ''"> and SATISFACTION = #{satisfaction}</if>
            <if test="parentappealclassification != null  and parentappealclassification != ''"> and PARENTAPPEALCLASSIFICATION = #{parentappealclassification}</if>
            <if test="params.jmqflag != null  and params.jmqflag != ''"> and SUBEXECUTEDEPTNAME_MH like concat('%', #{params.jmqflag}, '%')</if>
            <if test="params.sourceFlag != null and params.sourceFlag == false"> and (INFOSOURCENAME ='区级网格上报' or INFOSOURCENAME ='专职监督员上报') </if>
            <if test="params.cqFlag != null and params.cqFlag == false"> and (OVERDUE is null or OVERDUE = '未超期') </if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
            <if test="params.jaFlag != null and params.jaFlag == true"> and (SUBEXECUTEDEPTNAME_MH != '区城运' or SUBEXECUTEDEPTNAME_MH !='石化街道移动处置') </if>
            <if test="subexecutedeptnameMh != null  and subexecutedeptnameMh != ''"> and SUBEXECUTEDEPTNAME_MH = #{subexecutedeptnameMh}</if>
            <if test="relatedhotlinesn != null  and relatedhotlinesn != ''"> and RELATEDHOTLINESN = #{relatedhotlinesn}</if>
            <if test="circulationState != null  and circulationState != '' and circulationState == '31'" > and circulation_state is null</if>
            <if test="circulationState != null  and circulationState != '' and circulationState != '31'" > and circulation_state = #{circulationState} </if>
            <if test="evaluationmonth != null  and evaluationmonth != ''" > and EVALUATIONMONTH = #{evaluationmonth} </if>
            <if test="params.evaBeginTime != null and params.evaBeginTime != ''and params.evaEndTime != null and params.evaEndTime != ''"><!-- 开始时间检索 -->
                AND (EVALUATIONMONTH BETWEEN #{params.evaBeginTime} AND #{params.evaEndTime})
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.jieanTime != null and params.jieanTime != ''"><!-- 结束时间检索 -->
                AND (date_format(ENDTIME,'%y%m%d') > date_format(#{params.jieanTime},'%y%m%d')  or STATUSNAME = '待督办' )
            </if>
            <if test="params.yjieanTime != null and params.yjieanTime != ''"><!-- 结束时间检索 -->
                AND date_format(ENDTIME,'%y%m%d') &lt;= date_format(#{params.yjieanTime},'%y%m%d')
            </if>
            <if test="params.typeName != null  and params.typeName != ''"> and INFOSOURCENAME != #{params.typeName}</if>
            <if test="params.typeName1 != null  and params.typeName1 != ''"> and INFOSOURCENAME != #{params.typeName1}</if>
            <if test="params.infobcname != null  and params.infobcname != ''"> and INFOBCNAME != #{params.infobcname}</if>
            <if test="params.dbcenterFlagParam != null and params.dbcenterFlagParam == true">
                AND STATUSNAME NOT IN ('已作废', '已结案', '已退回其他平台')
                AND CREATETIME &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
            </if>
            <if test="params.dbcenterFlagParam != null and params.dbcenterFlagParam == false">
                AND ID NOT IN (
                SELECT
                ID
                FROM
                t_task_dbcenter
                WHERE
                STATUSNAME NOT IN ('已作废', '已结案', '已退回其他平台')
                AND CREATETIME &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND INFOSOURCENAME != '12345上报')
            </if>
            <if test="params.dbcenterEmergentParam != null and params.dbcenterEmergentParam == true">
                AND (INFOBCNAME = '突发事件' and INFOSCNAME != '架空线坠落、乱设' OR DESCRIPTION LIKE '%安全隐患%' AND INFOSOURCENAME != '12345上报')
            </if>
            <if test="params.dbcenterEmergentParam != null and params.dbcenterEmergentParam == false">
                AND ID NOT IN (
                SELECT
                ID
                FROM
                t_task_dbcenter
                WHERE
                INFOBCNAME = '突发事件'
                AND INFOSCNAME != '架空线坠落、乱设'
                OR DESCRIPTION LIKE '%安全隐患%'
                AND INFOSOURCENAME != '12345上报')
            </if>
        </where>
        ORDER BY CREATETIME
    </select>

    <select id="selectTaskDbcenterListDesc" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        <where>
            <if test="isstandard != null "> and ISSTANDARD = #{isstandard}</if>
            <if test="isduplicate != null  and isduplicate != ''"> and ISDUPLICATE = #{isduplicate}</if>
            <if test="checkimage != null  and checkimage != ''"> and CHECKIMAGE = #{checkimage}</if>
            <if test="servicetypename != null  and servicetypename != ''"> and SERVICETYPENAME like concat('%', #{servicetypename}, '%')</if>
            <if test="wpType != null  and wpType != ''"> and WP_TYPE = #{wpType}</if>
            <if test="isfirstcontact != null "> and ISFIRSTCONTACT = #{isfirstcontact}</if>
            <if test="hyname != null  and hyname != ''"> and HYNAME like concat('%', #{hyname}, '%')</if>
            <if test="upkeepername != null  and upkeepername != ''"> and UPKEEPERNAME like concat('%', #{upkeepername}, '%')</if>
            <if test="deptname != null  and deptname != ''"> and DEPTNAME like concat('%', #{deptname}, '%')</if>
            <if test="statusname != null  and statusname != ''"> and STATUSNAME = #{statusname}</if>
            <if test="synctime != null "> and SYNCTIME = #{synctime}</if>
            <if test="lastcontacttime != null "> and LASTCONTACTTIME = #{lastcontacttime}</if>
            <if test="accepttime != null "> and ACCEPTTIME = #{accepttime}</if>
            <if test="cancletime != null "> and CANCLETIME = #{cancletime}</if>
            <if test="lastsolvingtime != null "> and LASTSOLVINGTIME = #{lastsolvingtime}</if>
            <if test="importantsolvingtime != null "> and IMPORTANTSOLVINGTIME = #{importantsolvingtime}</if>
            <if test="middlesolvingtime != null "> and MIDDLESOLVINGTIME = #{middlesolvingtime}</if>
            <if test="allendtime != null "> and ALLENDTIME = #{allendtime}</if>
            <if test="allimportanttime != null "> and ALLIMPORTANTTIME = #{allimportanttime}</if>
            <if test="allmiddletime != null "> and ALLMIDDLETIME = #{allmiddletime}</if>
            <if test="endtime != null "> and ENDTIME = #{endtime}</if>
            <if test="telasktime != null "> and TELASKTIME = #{telasktime}</if>
            <if test="solvingtime != null "> and SOLVINGTIME = #{solvingtime}</if>
            <if test="dispatchtime != null "> and DISPATCHTIME = #{dispatchtime}</if>
            <if test="createtime != null "> and CREATETIME = #{createtime}</if>
            <if test="percreatetime != null "> and PERCREATETIME = #{percreatetime}</if>
            <if test="discovertime != null "> and DISCOVERTIME = #{discovertime}</if>
            <if test="executedeptname != null  and executedeptname != ''"> and EXECUTEDEPTNAME like concat('%', #{executedeptname}, '%')</if>
            <if test="workgridcode != null  and workgridcode != ''"> and WORKGRIDCODE = #{workgridcode}</if>
            <if test="communityname != null  and communityname != ''"> and COMMUNITYNAME like concat('%', #{communityname}, '%')</if>
            <if test="streetname != null  and streetname != ''"> and STREETNAME like concat('%', #{streetname}, '%')</if>
            <if test="workgrid != null  and workgrid != ''"> and WORKGRID = #{workgrid}</if>
            <if test="address != null  and address != ''"> and ADDRESS like concat('%', #{address}, '%')</if>
            <if test="gridcode != null  and gridcode != ''"> and GRIDCODE = #{gridcode}</if>
            <if test="communitycode != null  and communitycode != ''"> and COMMUNITYCODE = #{communitycode}</if>
            <if test="streetcode != null  and streetcode != ''"> and STREETCODE = #{streetcode}</if>
            <if test="viewinfo != null  and viewinfo != ''"> and VIEWINFO = #{viewinfo}</if>
            <if test="casevaluation12345 != null "> and CASEVALUATION_12345 = #{casevaluation12345}</if>
            <if test="notReason != null  and notReason != ''"> and NOT_REASON = #{notReason}</if>
            <if test="description12345 != null  and description12345 != ''"> and DESCRIPTION_12345 = #{description12345}</if>
            <if test="appealExplain != null  and appealExplain != ''"> and APPEAL_EXPLAIN = #{appealExplain}</if>
            <if test="banliresult12345 != null "> and BANLIRESULT_12345 = #{banliresult12345}</if>
            <if test="wpSource != null  and wpSource != ''"> and WP_SOURCE = #{wpSource}</if>
            <if test="reportdeptname != null  and reportdeptname != ''"> and REPORTDEPTNAME like concat('%', #{reportdeptname}, '%')</if>
            <if test="hotlinesn != null  and hotlinesn != ''"> and HOTLINESN = #{hotlinesn}</if>
            <if test="banliresult != null "> and BANLIRESULT = #{banliresult}</if>
            <if test="duLimit != null "> and DU_LIMIT = #{duLimit}</if>
            <if test="urgeCount != null "> and URGE_COUNT = #{urgeCount}</if>
            <if test="callbackFlag != null "> and CALLBACK_FLAG = #{callbackFlag}</if>
            <if test="userevaluate != null "> and USEREVALUATE = #{userevaluate}</if>
            <if test="isanonymity != null "> and ISANONYMITY = #{isanonymity}</if>
            <if test="servicetype != null  and servicetype != ''"> and SERVICETYPE = #{servicetype}</if>
            <if test="similarcasesn != null  and similarcasesn != ''"> and SIMILARCASESN = #{similarcasesn}</if>
            <if test="approach != null "> and APPROACH = #{approach}</if>
            <if test="urgentdegree != null "> and URGENTDEGREE = #{urgentdegree}</if>
            <if test="partsn != null  and partsn != ''"> and PARTSN = #{partsn}</if>
            <if test="endnote != null  and endnote != ''"> and ENDNOTE = #{endnote}</if>
            <if test="dispatchnote != null  and dispatchnote != ''"> and DISPATCHNOTE = #{dispatchnote}</if>
            <if test="reporter != null  and reporter != ''"> and REPORTER = #{reporter}</if>
            <if test="infozcname != null  and infozcname != ''"> and INFOZCNAME = #{infozcname}</if>
            <if test="infoscname != null  and infoscname != ''"> and INFOSCNAME = #{infoscname}</if>
            <if test="infobcname != null  and infobcname != ''"> and INFOBCNAME = #{infobcname}</if>
            <if test="infotypename != null  and infotypename != ''"> and INFOTYPENAME = #{infotypename}</if>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>
            <if test="contactinfo != null  and contactinfo != ''"> and CONTACTINFO = #{contactinfo}</if>
            <if test="hastentypecount != null "> and HASTENTYPECOUNT = #{hastentypecount}</if>
            <if test="hasleadtypecount != null "> and HASLEADTYPECOUNT = #{hasleadtypecount}</if>
            <if test="huifangcount != null "> and HUIFANGCOUNT = #{huifangcount}</if>
            <if test="hechacount != null "> and HECHACOUNT = #{hechacount}</if>
            <if test="heshicount != null "> and HESHICOUNT = #{heshicount}</if>
            <if test="contactmode != null  and contactmode != ''"> and CONTACTMODE = #{contactmode}</if>
            <if test="callnumber != null  and callnumber != ''"> and CALLNUMBER = #{callnumber}</if>
            <if test="priorityarea != null  and priorityarea != ''"> and PRIORITYAREA = #{priorityarea}</if>
            <if test="checkresult != null "> and CHECKRESULT = #{checkresult}</if>
            <if test="verifyresult != null "> and VERIFYRESULT = #{verifyresult}</if>
            <if test="endresult != null "> and ENDRESULT = #{endresult}</if>
            <if test="caseend != null "> and CASEEND = #{caseend}</if>
            <if test="insertuser != null  and insertuser != ''"> and INSERTUSER = #{insertuser}</if>
            <if test="keepersn != null  and keepersn != ''"> and KEEPERSN = #{keepersn}</if>
            <if test="insertdeptcode != null  and insertdeptcode != ''"> and INSERTDEPTCODE = #{insertdeptcode}</if>
            <if test="executedeptcode != null  and executedeptcode != ''"> and EXECUTEDEPTCODE = #{executedeptcode}</if>
            <if test="deptcode != null  and deptcode != ''"> and DEPTCODE = #{deptcode}</if>
            <if test="status != null "> and STATUS = #{status}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION like concat('%', #{description}, '%')</if>
            <if test="infoatcode != null  and infoatcode != ''"> and INFOATCODE = #{infoatcode}</if>
            <if test="infozccode != null  and infozccode != ''"> and INFOZCCODE = #{infozccode}</if>
            <if test="infosccode != null  and infosccode != ''"> and INFOSCCODE = #{infosccode}</if>
            <if test="infobccode != null  and infobccode != ''"> and INFOBCCODE = #{infobccode}</if>
            <if test="infotypeid != null "> and INFOTYPEID = #{infotypeid}</if>
            <if test="infosourceid != null "> and INFOSOURCEID = #{infosourceid}</if>
            <if test="casesn != null  and casesn != ''"> and CASESN = #{casesn}</if>
            <if test="taskid != null  and taskid != ''"> and TASKID = #{taskid}</if>
            <if test="subexecutedeptnameMh != null  and subexecutedeptnameMh != ''"> and SUBEXECUTEDEPTNAME_MH = #{subexecutedeptnameMh}</if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
            <if test="relatedhotlinesn != null  and relatedhotlinesn != ''"> and RELATEDHOTLINESN = #{relatedhotlinesn}</if>
            <if test="circulationState != null  and circulationState != '' and circulationState == '31'" > and circulation_state is null</if>
            <if test="circulationState != null  and circulationState != '' and circulationState != '31'" > and circulation_state = #{circulationState} </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(CREATETIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.typeName != null  and params.typeName != ''"> and INFOSOURCENAME != #{params.typeName}</if>
            <if test="params.typeName1 != null  and params.typeName1 != ''"> and INFOSOURCENAME != #{params.typeName1}</if>
            <if test="params.infobcname != null  and params.infobcname != ''"> and INFOBCNAME != #{params.infobcname}</if>
            <if test="params.executedeptnames != null and params.executedeptnames != ''">
                AND EXECUTEDEPTNAME in
                <foreach collection="params.executedeptnames" item="executedeptname" open="(" separator="," close=")">
                    #{executedeptname}
                </foreach>
            </if>
            <if test="params.dbcenterFlagParam != null and params.dbcenterFlagParam == true">
                AND STATUSNAME NOT IN ('已作废', '已结案', '已退回其他平台')
                AND CREATETIME &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
            </if>
            <if test="params.dbcenterFlagParam != null and params.dbcenterFlagParam == false">
                AND ID NOT IN (
                SELECT
                ID
                FROM
                t_task_dbcenter
                WHERE
                STATUSNAME NOT IN ('已作废', '已结案', '已退回其他平台')
                AND CREATETIME &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND INFOSOURCENAME != '12345上报')
            </if>
            <if test="params.dbcenterEmergentParam != null and params.dbcenterEmergentParam == true">
                AND (INFOBCNAME = '突发事件' and INFOSCNAME != '架空线坠落、乱设' OR DESCRIPTION LIKE '%安全隐患%' AND INFOSOURCENAME != '12345上报')
            </if>
            <if test="params.dbcenterEmergentParam != null and params.dbcenterEmergentParam == false">
                AND ID NOT IN (
                SELECT
                ID
                FROM
                t_task_dbcenter
                WHERE
                INFOBCNAME = '突发事件'
                AND INFOSCNAME != '架空线坠落、乱设'
                OR DESCRIPTION LIKE '%安全隐患%'
                AND INFOSOURCENAME != '12345上报')
            </if>
            <if test="params.specialtopics != null and params.specialtopics != ''">
                AND (
                <foreach collection="params.specialtopics" item="specialtopic" separator=" OR ">
                    FIND_IN_SET(#{specialtopic}, SPECIALTOPIC)
                </foreach>
                )
            </if>
            <if test="params.satisfactions != null and params.satisfactions != ''">
                AND SATISFACTION in
                <foreach collection="params.satisfactions" item="satisfaction" open="(" separator="," close=")">
                    #{satisfaction}
                </foreach>
            </if>
        </where>
        ORDER BY CREATETIME desc
    </select>
    
    <select id="selectTaskDbcenterById" parameterType="Long" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTaskDbcenter" parameterType="TaskDbcenter">
        insert into t_task_dbcenter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="isstandard != null">ISSTANDARD,</if>
            <if test="checkimage != null">CHECKIMAGE,</if>
            <if test="servicetypename != null">SERVICETYPENAME,</if>
            <if test="wpType != null">WP_TYPE,</if>
            <if test="isfirstcontact != null">ISFIRSTCONTACT,</if>
            <if test="hyname != null">HYNAME,</if>
            <if test="upkeepername != null">UPKEEPERNAME,</if>
            <if test="deptname != null">DEPTNAME,</if>
            <if test="statusname != null">STATUSNAME,</if>
            <if test="synctime != null">SYNCTIME,</if>
            <if test="lastcontacttime != null">LASTCONTACTTIME,</if>
            <if test="accepttime != null">ACCEPTTIME,</if>
            <if test="cancletime != null">CANCLETIME,</if>
            <if test="lastsolvingtime != null">LASTSOLVINGTIME,</if>
            <if test="importantsolvingtime != null">IMPORTANTSOLVINGTIME,</if>
            <if test="middlesolvingtime != null">MIDDLESOLVINGTIME,</if>
            <if test="allendtime != null">ALLENDTIME,</if>
            <if test="allimportanttime != null">ALLIMPORTANTTIME,</if>
            <if test="allmiddletime != null">ALLMIDDLETIME,</if>
            <if test="endtime != null">ENDTIME,</if>
            <if test="telasktime != null">TELASKTIME,</if>
            <if test="solvingtime != null">SOLVINGTIME,</if>
            <if test="dispatchtime != null">DISPATCHTIME,</if>
            <if test="createtime != null">CREATETIME,</if>
            <if test="percreatetime != null">PERCREATETIME,</if>
            <if test="discovertime != null">DISCOVERTIME,</if>
            <if test="executedeptname != null">EXECUTEDEPTNAME,</if>
            <if test="workgridcode != null">WORKGRIDCODE,</if>
            <if test="communityname != null">COMMUNITYNAME,</if>
            <if test="streetname != null">STREETNAME,</if>
            <if test="workgrid != null">WORKGRID,</if>
            <if test="address != null">ADDRESS,</if>
            <if test="coordy != null">COORDY,</if>
            <if test="coordx != null">COORDX,</if>
            <if test="gridcode != null">GRIDCODE,</if>
            <if test="communitycode != null">COMMUNITYCODE,</if>
            <if test="streetcode != null">STREETCODE,</if>
            <if test="viewinfo != null">VIEWINFO,</if>
            <if test="casevaluation12345 != null">CASEVALUATION_12345,</if>
            <if test="notReason != null">NOT_REASON,</if>
            <if test="description12345 != null">DESCRIPTION_12345,</if>
            <if test="appealExplain != null">APPEAL_EXPLAIN,</if>
            <if test="banliresult12345 != null">BANLIRESULT_12345,</if>
            <if test="wpSource != null">WP_SOURCE,</if>
            <if test="reportdeptname != null">REPORTDEPTNAME,</if>
            <if test="hotlinesn != null">HOTLINESN,</if>
            <if test="banliresult != null">BANLIRESULT,</if>
            <if test="duLimit != null">DU_LIMIT,</if>
            <if test="urgeCount != null">URGE_COUNT,</if>
            <if test="callbackFlag != null">CALLBACK_FLAG,</if>
            <if test="userevaluate != null">USEREVALUATE,</if>
            <if test="isanonymity != null">ISANONYMITY,</if>
            <if test="servicetype != null">SERVICETYPE,</if>
            <if test="similarcasesn != null">SIMILARCASESN,</if>
            <if test="approach != null">APPROACH,</if>
            <if test="urgentdegree != null">URGENTDEGREE,</if>
            <if test="partsn != null">PARTSN,</if>
            <if test="endnote != null">ENDNOTE,</if>
            <if test="dispatchnote != null">DISPATCHNOTE,</if>
            <if test="reporter != null">REPORTER,</if>
            <if test="infozcname != null">INFOZCNAME,</if>
            <if test="infoscname != null">INFOSCNAME,</if>
            <if test="infobcname != null">INFOBCNAME,</if>
            <if test="infotypename != null">INFOTYPENAME,</if>
            <if test="infosourcename != null">INFOSOURCENAME,</if>
            <if test="contactinfo != null">CONTACTINFO,</if>
            <if test="hastentypecount != null">HASTENTYPECOUNT,</if>
            <if test="hasleadtypecount != null">HASLEADTYPECOUNT,</if>
            <if test="huifangcount != null">HUIFANGCOUNT,</if>
            <if test="hechacount != null">HECHACOUNT,</if>
            <if test="heshicount != null">HESHICOUNT,</if>
            <if test="contactmode != null">CONTACTMODE,</if>
            <if test="callnumber != null">CALLNUMBER,</if>
            <if test="priorityarea != null">PRIORITYAREA,</if>
            <if test="checkresult != null">CHECKRESULT,</if>
            <if test="verifyresult != null">VERIFYRESULT,</if>
            <if test="endresult != null">ENDRESULT,</if>
            <if test="caseend != null">CASEEND,</if>
            <if test="insertuser != null">INSERTUSER,</if>
            <if test="keepersn != null">KEEPERSN,</if>
            <if test="insertdeptcode != null">INSERTDEPTCODE,</if>
            <if test="executedeptcode != null">EXECUTEDEPTCODE,</if>
            <if test="deptcode != null">DEPTCODE,</if>
            <if test="status != null">STATUS,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="infoatcode != null">INFOATCODE,</if>
            <if test="infozccode != null">INFOZCCODE,</if>
            <if test="infosccode != null">INFOSCCODE,</if>
            <if test="infobccode != null">INFOBCCODE,</if>
            <if test="infotypeid != null">INFOTYPEID,</if>
            <if test="infosourceid != null">INFOSOURCEID,</if>
            <if test="casesn != null">CASESN,</if>
            <if test="taskid != null">TASKID,</if>
            <if test="primarydept != null">PRIMARYDEPT,</if>
            <if test="assistdept != null">ASSISTDEPT,</if>
            <if test="subassistdept != null">SUBASSISTDEPT,</if>
            <if test="subexecutedeptnameMh != null">SUBEXECUTEDEPTNAME_MH,</if>
            <if test="imagefilename != null">IMAGEFILENAME,</if>
            <if test="satisfaction != null">SATISFACTION,</if>
            <if test="residentialarea != null">RESIDENTIALAREA,</if>
            <if test="streetarea != null">STREETAREA,</if>
            <if test="community != null">COMMUNITY,</if>
            <if test="property != null">PROPERTY,</if>
            <if test="powerstorage != null">POWERSTORAGE,</if>
            <if test="trackingtype != null">TRACKINGTYPE,</if>
            <if test="isduplicate != null">ISDUPLICATE,</if>
            <if test="overdue != null">OVERDUE,</if>
            <if test="recenthotlinesn != null">RECENTHOTLINESN,</if>
            <if test="relatedhotlinesn != null">RELATEDHOTLINESN,</if>
            <if test="parentappealclassification != null">PARENTAPPEALCLASSIFICATION,</if>
            <if test="appealclassification != null">APPEALCLASSIFICATION,</if>
            <if test="parentduplicateclassification != null">PARENTDUPLICATECLASSIFICATION,</if>
            <if test="duplicateclassification != null">DUPLICATECLASSIFICATION,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="evaluationmonth != null">EVALUATIONMONTH,</if>
            <if test="specialtopic != null">SPECIALTOPIC,</if>
            <if test="remark != null">REMARK,</if>
            <if test="isobjective != null">ISOBJECTIVE,</if>
            <if test="ischeck != null">ISCHECK,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="isstandard != null">#{isstandard},</if>
            <if test="checkimage != null">#{checkimage},</if>
            <if test="servicetypename != null">#{servicetypename},</if>
            <if test="wpType != null">#{wpType},</if>
            <if test="isfirstcontact != null">#{isfirstcontact},</if>
            <if test="hyname != null">#{hyname},</if>
            <if test="upkeepername != null">#{upkeepername},</if>
            <if test="deptname != null">#{deptname},</if>
            <if test="statusname != null">#{statusname},</if>
            <if test="synctime != null">#{synctime},</if>
            <if test="lastcontacttime != null">#{lastcontacttime},</if>
            <if test="accepttime != null">#{accepttime},</if>
            <if test="cancletime != null">#{cancletime},</if>
            <if test="lastsolvingtime != null">#{lastsolvingtime},</if>
            <if test="importantsolvingtime != null">#{importantsolvingtime},</if>
            <if test="middlesolvingtime != null">#{middlesolvingtime},</if>
            <if test="allendtime != null">#{allendtime},</if>
            <if test="allimportanttime != null">#{allimportanttime},</if>
            <if test="allmiddletime != null">#{allmiddletime},</if>
            <if test="endtime != null">#{endtime},</if>
            <if test="telasktime != null">#{telasktime},</if>
            <if test="solvingtime != null">#{solvingtime},</if>
            <if test="dispatchtime != null">#{dispatchtime},</if>
            <if test="createtime != null">#{createtime},</if>
            <if test="percreatetime != null">#{percreatetime},</if>
            <if test="discovertime != null">#{discovertime},</if>
            <if test="executedeptname != null">#{executedeptname},</if>
            <if test="workgridcode != null">#{workgridcode},</if>
            <if test="communityname != null">#{communityname},</if>
            <if test="streetname != null">#{streetname},</if>
            <if test="workgrid != null">#{workgrid},</if>
            <if test="address != null">#{address},</if>
            <if test="coordy != null">#{coordy},</if>
            <if test="coordx != null">#{coordx},</if>
            <if test="gridcode != null">#{gridcode},</if>
            <if test="communitycode != null">#{communitycode},</if>
            <if test="streetcode != null">#{streetcode},</if>
            <if test="viewinfo != null">#{viewinfo},</if>
            <if test="casevaluation12345 != null">#{casevaluation12345},</if>
            <if test="notReason != null">#{notReason},</if>
            <if test="description12345 != null">#{description12345},</if>
            <if test="appealExplain != null">#{appealExplain},</if>
            <if test="banliresult12345 != null">#{banliresult12345},</if>
            <if test="wpSource != null">#{wpSource},</if>
            <if test="reportdeptname != null">#{reportdeptname},</if>
            <if test="hotlinesn != null">#{hotlinesn},</if>
            <if test="banliresult != null">#{banliresult},</if>
            <if test="duLimit != null">#{duLimit},</if>
            <if test="urgeCount != null">#{urgeCount},</if>
            <if test="callbackFlag != null">#{callbackFlag},</if>
            <if test="userevaluate != null">#{userevaluate},</if>
            <if test="isanonymity != null">#{isanonymity},</if>
            <if test="servicetype != null">#{servicetype},</if>
            <if test="similarcasesn != null">#{similarcasesn},</if>
            <if test="approach != null">#{approach},</if>
            <if test="urgentdegree != null">#{urgentdegree},</if>
            <if test="partsn != null">#{partsn},</if>
            <if test="endnote != null">#{endnote},</if>
            <if test="dispatchnote != null">#{dispatchnote},</if>
            <if test="reporter != null">#{reporter},</if>
            <if test="infozcname != null">#{infozcname},</if>
            <if test="infoscname != null">#{infoscname},</if>
            <if test="infobcname != null">#{infobcname},</if>
            <if test="infotypename != null">#{infotypename},</if>
            <if test="infosourcename != null">#{infosourcename},</if>
            <if test="contactinfo != null">#{contactinfo},</if>
            <if test="hastentypecount != null">#{hastentypecount},</if>
            <if test="hasleadtypecount != null">#{hasleadtypecount},</if>
            <if test="huifangcount != null">#{huifangcount},</if>
            <if test="hechacount != null">#{hechacount},</if>
            <if test="heshicount != null">#{heshicount},</if>
            <if test="contactmode != null">#{contactmode},</if>
            <if test="callnumber != null">#{callnumber},</if>
            <if test="priorityarea != null">#{priorityarea},</if>
            <if test="checkresult != null">#{checkresult},</if>
            <if test="verifyresult != null">#{verifyresult},</if>
            <if test="endresult != null">#{endresult},</if>
            <if test="caseend != null">#{caseend},</if>
            <if test="insertuser != null">#{insertuser},</if>
            <if test="keepersn != null">#{keepersn},</if>
            <if test="insertdeptcode != null">#{insertdeptcode},</if>
            <if test="executedeptcode != null">#{executedeptcode},</if>
            <if test="deptcode != null">#{deptcode},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="infoatcode != null">#{infoatcode},</if>
            <if test="infozccode != null">#{infozccode},</if>
            <if test="infosccode != null">#{infosccode},</if>
            <if test="infobccode != null">#{infobccode},</if>
            <if test="infotypeid != null">#{infotypeid},</if>
            <if test="infosourceid != null">#{infosourceid},</if>
            <if test="casesn != null">#{casesn},</if>
            <if test="taskid != null">#{taskid},</if>
            <if test="primarydept != null">#{primarydept},</if>
            <if test="assistdept != null">#{assistdept},</if>
            <if test="subassistdept != null">#{subassistdept},</if>
            <if test="subexecutedeptnameMh != null">#{subexecutedeptnameMh},</if>
            <if test="imagefilename != null">#{imagefilename},</if>
            <if test="satisfaction != null">#{satisfaction},</if>
            <if test="residentialarea != null">#{residentialarea},</if>
            <if test="streetarea != null">#{streetarea},</if>
            <if test="community != null">#{community},</if>
            <if test="property != null">#{property},</if>
            <if test="powerstorage != null">#{powerstorage},</if>
            <if test="trackingtype != null">#{trackingtype},</if>
            <if test="isduplicate != null">#{isduplicate},</if>
            <if test="overdue != null">#{overdue},</if>
            <if test="recenthotlinesn != null">#{recenthotlinesn},</if>
            <if test="relatedhotlinesn != null">#{relatedhotlinesn},</if>
            <if test="parentappealclassification != null">#{parentappealclassification},</if>
            <if test="appealclassification != null">#{appealclassification},</if>
            <if test="parentduplicateclassification != null">#{parentduplicateclassification},</if>
            <if test="duplicateclassification != null">#{duplicateclassification},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="evaluationmonth != null">#{evaluationmonth},</if>
            <if test="specialtopic != null">#{specialtopic},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isobjective != null">#{isobjective},</if>
            <if test="ischeck != null">#{ischeck},</if>
         </trim>
    </insert>

    <update id="updateTaskDbcenter" parameterType="TaskDbcenter">
        update t_task_dbcenter
        <trim prefix="SET" suffixOverrides=",">
            <if test="isstandard != null">ISSTANDARD = #{isstandard},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="checkimage != null">CHECKIMAGE = #{checkimage},</if>
            <if test="servicetypename != null">SERVICETYPENAME = #{servicetypename},</if>
            <if test="wpType != null">WP_TYPE = #{wpType},</if>
            <if test="isfirstcontact != null">ISFIRSTCONTACT = #{isfirstcontact},</if>
            <if test="hyname != null">HYNAME = #{hyname},</if>
            <if test="upkeepername != null">UPKEEPERNAME = #{upkeepername},</if>
            <if test="deptname != null">DEPTNAME = #{deptname},</if>
            <if test="statusname != null">STATUSNAME = #{statusname},</if>
            <if test="synctime != null">SYNCTIME = #{synctime},</if>
            <if test="lastcontacttime != null">LASTCONTACTTIME = #{lastcontacttime},</if>
            <if test="accepttime != null">ACCEPTTIME = #{accepttime},</if>
            <if test="cancletime != null">CANCLETIME = #{cancletime},</if>
            <if test="lastsolvingtime != null">LASTSOLVINGTIME = #{lastsolvingtime},</if>
            <if test="importantsolvingtime != null">IMPORTANTSOLVINGTIME = #{importantsolvingtime},</if>
            <if test="middlesolvingtime != null">MIDDLESOLVINGTIME = #{middlesolvingtime},</if>
            <if test="allendtime != null">ALLENDTIME = #{allendtime},</if>
            <if test="allimportanttime != null">ALLIMPORTANTTIME = #{allimportanttime},</if>
            <if test="allmiddletime != null">ALLMIDDLETIME = #{allmiddletime},</if>
            <if test="endtime != null">ENDTIME = #{endtime},</if>
            <if test="telasktime != null">TELASKTIME = #{telasktime},</if>
            <if test="solvingtime != null">SOLVINGTIME = #{solvingtime},</if>
            <if test="dispatchtime != null">DISPATCHTIME = #{dispatchtime},</if>
            <if test="createtime != null">CREATETIME = #{createtime},</if>
            <if test="percreatetime != null">PERCREATETIME = #{percreatetime},</if>
            <if test="discovertime != null">DISCOVERTIME = #{discovertime},</if>
            <if test="executedeptname != null">EXECUTEDEPTNAME = #{executedeptname},</if>
            <if test="workgridcode != null">WORKGRIDCODE = #{workgridcode},</if>
            <if test="communityname != null">COMMUNITYNAME = #{communityname},</if>
            <if test="streetname != null">STREETNAME = #{streetname},</if>
            <if test="workgrid != null">WORKGRID = #{workgrid},</if>
            <if test="address != null">ADDRESS = #{address},</if>
            <if test="coordy != null and coordy != 0.0">COORDY = #{coordy},</if>
            <if test="coordx != null and coordx != 0.0">COORDX = #{coordx},</if>
            <if test="gridcode != null">GRIDCODE = #{gridcode},</if>
            <if test="communitycode != null">COMMUNITYCODE = #{communitycode},</if>
            <if test="streetcode != null">STREETCODE = #{streetcode},</if>
            <if test="viewinfo != null">VIEWINFO = #{viewinfo},</if>
            <if test="casevaluation12345 != null">CASEVALUATION_12345 = #{casevaluation12345},</if>
            <if test="notReason != null">NOT_REASON = #{notReason},</if>
            <if test="description12345 != null">DESCRIPTION_12345 = #{description12345},</if>
            <if test="appealExplain != null">APPEAL_EXPLAIN = #{appealExplain},</if>
            <if test="banliresult12345 != null">BANLIRESULT_12345 = #{banliresult12345},</if>
            <if test="wpSource != null">WP_SOURCE = #{wpSource},</if>
            <if test="reportdeptname != null">REPORTDEPTNAME = #{reportdeptname},</if>
            <if test="hotlinesn != null">HOTLINESN = #{hotlinesn},</if>
            <if test="banliresult != null">BANLIRESULT = #{banliresult},</if>
            <if test="duLimit != null">DU_LIMIT = #{duLimit},</if>
            <if test="urgeCount != null">URGE_COUNT = #{urgeCount},</if>
            <if test="callbackFlag != null">CALLBACK_FLAG = #{callbackFlag},</if>
            <if test="userevaluate != null">USEREVALUATE = #{userevaluate},</if>
            <if test="isanonymity != null">ISANONYMITY = #{isanonymity},</if>
            <if test="servicetype != null">SERVICETYPE = #{servicetype},</if>
            <if test="similarcasesn != null">SIMILARCASESN = #{similarcasesn},</if>
            <if test="approach != null">APPROACH = #{approach},</if>
            <if test="urgentdegree != null">URGENTDEGREE = #{urgentdegree},</if>
            <if test="partsn != null">PARTSN = #{partsn},</if>
            <if test="endnote != null">ENDNOTE = #{endnote},</if>
            <if test="dispatchnote != null">DISPATCHNOTE = #{dispatchnote},</if>
            <if test="reporter != null">REPORTER = #{reporter},</if>
            <if test="infozcname != null">INFOZCNAME = #{infozcname},</if>
            <if test="infoscname != null">INFOSCNAME = #{infoscname},</if>
            <if test="infobcname != null">INFOBCNAME = #{infobcname},</if>
            <if test="infotypename != null">INFOTYPENAME = #{infotypename},</if>
            <if test="infosourcename != null">INFOSOURCENAME = #{infosourcename},</if>
            <if test="contactinfo != null">CONTACTINFO = #{contactinfo},</if>
            <if test="hastentypecount != null">HASTENTYPECOUNT = #{hastentypecount},</if>
            <if test="hasleadtypecount != null">HASLEADTYPECOUNT = #{hasleadtypecount},</if>
            <if test="huifangcount != null">HUIFANGCOUNT = #{huifangcount},</if>
            <if test="hechacount != null">HECHACOUNT = #{hechacount},</if>
            <if test="heshicount != null">HESHICOUNT = #{heshicount},</if>
            <if test="contactmode != null">CONTACTMODE = #{contactmode},</if>
            <if test="callnumber != null">CALLNUMBER = #{callnumber},</if>
            <if test="priorityarea != null">PRIORITYAREA = #{priorityarea},</if>
            <if test="checkresult != null">CHECKRESULT = #{checkresult},</if>
            <if test="verifyresult != null">VERIFYRESULT = #{verifyresult},</if>
            <if test="endresult != null">ENDRESULT = #{endresult},</if>
            <if test="caseend != null">CASEEND = #{caseend},</if>
            <if test="insertuser != null">INSERTUSER = #{insertuser},</if>
            <if test="keepersn != null">KEEPERSN = #{keepersn},</if>
            <if test="insertdeptcode != null">INSERTDEPTCODE = #{insertdeptcode},</if>
            <if test="executedeptcode != null">EXECUTEDEPTCODE = #{executedeptcode},</if>
            <if test="deptcode != null">DEPTCODE = #{deptcode},</if>
            <if test="status != null">STATUS = #{status},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="infoatcode != null">INFOATCODE = #{infoatcode},</if>
            <if test="infozccode != null">INFOZCCODE = #{infozccode},</if>
            <if test="infosccode != null">INFOSCCODE = #{infosccode},</if>
            <if test="infobccode != null">INFOBCCODE = #{infobccode},</if>
            <if test="infotypeid != null">INFOTYPEID = #{infotypeid},</if>
            <if test="infosourceid != null">INFOSOURCEID = #{infosourceid},</if>
            <if test="casesn != null">CASESN = #{casesn},</if>
            <if test="taskid != null">TASKID = #{taskid},</if>
            <if test="primarydept != null">PRIMARYDEPT = #{primarydept},</if>
            <if test="assistdept != null">ASSISTDEPT = #{assistdept},</if>
            <if test="subassistdept != null">SUBASSISTDEPT = #{subassistdept},</if>
            <if test="subexecutedeptnameMh != null">SUBEXECUTEDEPTNAME_MH = #{subexecutedeptnameMh},</if>
            <if test="imagefilename != null">IMAGEFILENAME = #{imagefilename},</if>
            <if test="satisfaction != null">SATISFACTION = #{satisfaction},</if>
            <if test="residentialarea != null">RESIDENTIALAREA = #{residentialarea},</if>
            <if test="streetarea != null">STREETAREA = #{streetarea},</if>
            <if test="community != null">COMMUNITY = #{community},</if>
            <if test="property != null">PROPERTY = #{property},</if>
            <if test="powerstorage != null">POWERSTORAGE = #{powerstorage},</if>
            <if test="trackingtype != null">TRACKINGTYPE = #{trackingtype},</if>
            <if test="isduplicate != null">ISDUPLICATE = #{isduplicate},</if>
            <if test="overdue != null">OVERDUE = #{overdue},</if>
            <if test="recenthotlinesn != null">RECENTHOTLINESN = #{recenthotlinesn},</if>
            <if test="relatedhotlinesn != null">RELATEDHOTLINESN = #{relatedhotlinesn},</if>
            <if test="parentappealclassification != null">PARENTAPPEALCLASSIFICATION = #{parentappealclassification},</if>
            <if test="appealclassification != null">APPEALCLASSIFICATION = #{appealclassification},</if>
            <if test="parentduplicateclassification != null">PARENTDUPLICATECLASSIFICATION = #{parentduplicateclassification},</if>
            <if test="duplicateclassification != null">DUPLICATECLASSIFICATION = #{duplicateclassification},</if>
            <if test="evaluationmonth != null">EVALUATIONMONTH = #{evaluationmonth},</if>
            <if test="specialtopic != null">SPECIALTOPIC = #{specialtopic},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="isobjective != null">ISOBJECTIVE = #{isobjective},</if>
            <if test="ischeck != null">ISCHECK = #{ischeck},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTaskDbcenterById" parameterType="Long">
        delete from t_task_dbcenter where ID = #{id}
    </delete>

    <delete id="deleteTaskDbcenterByIds" parameterType="String">
        delete from t_task_dbcenter where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTaskDbcenterIdListBySynctime" parameterType="String" resultType="Long">
        select ID from t_task_dbcenter
        <where>
            <if test="synctime != null and synctime != ''">
                AND date_format(SYNCTIME,'%y%m%d') &gt;= date_format(#{synctime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectTaskDbcenterIdList" resultType="Long">
        select ID from t_task_dbcenter
    </select>

    <select id="selectTaskDbcenterByHotlinesn" parameterType="String" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        where HOTLINESN = #{hotlinesn} limit 1
    </select>

    <select id="selectTaskDbcenterByRecenthotlinesn" parameterType="String" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        where RECENTHOTLINESN = #{recenthotlinesn} limit 1
    </select>

    <select id="selectTaskDbcenterSyncList" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        WHERE
        STATUSNAME NOT IN ( '已作废', '已结案', '已退回其他平台' )
    </select>

    <select id="selectTaskDbcenterSyncIdList" resultType="Long">
        select ID from t_task_dbcenter
        WHERE
            STATUSNAME NOT IN ( '已作废', '已结案', '已退回其他平台' )
    </select>

    <select id="selectTaskDbcenterSyncTaskidList" resultType="String">
        select TASKID from t_task_dbcenter
        WHERE
            STATUSNAME NOT IN ( '已作废', '已结案', '已退回其他平台' )
    </select>

    <delete id="deleteTaskDbcenterNotShjd">
        delete from t_task_dbcenter where EXECUTEDEPTNAME != '石化街道' AND DEPTNAME != '石化街道'
    </delete>

    <delete id="deleteTaskDbcenterReturnOther">
        delete from t_task_dbcenter where STATUSNAME = '已退回其他平台'
    </delete>

    <select id="selectTaskDbcenterRxList" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        <where>
            <if test="isstandard != null "> and ISSTANDARD = #{isstandard}</if>
            <if test="checkimage != null  and checkimage != ''"> and CHECKIMAGE = #{checkimage}</if>
            <if test="servicetypename != null  and servicetypename != ''"> and SERVICETYPENAME like concat('%', #{servicetypename}, '%')</if>
            <if test="wpType != null  and wpType != ''"> and WP_TYPE = #{wpType}</if>
            <if test="isfirstcontact != null "> and ISFIRSTCONTACT = #{isfirstcontact}</if>
            <if test="hyname != null  and hyname != ''"> and HYNAME like concat('%', #{hyname}, '%')</if>
            <if test="upkeepername != null  and upkeepername != ''"> and UPKEEPERNAME like concat('%', #{upkeepername}, '%')</if>
            <if test="deptname != null  and deptname != ''"> and DEPTNAME like concat('%', #{deptname}, '%')</if>
            <if test="statusname != null  and statusname != ''"> and STATUSNAME = #{statusname}</if>
            <if test="synctime != null "> and SYNCTIME = #{synctime}</if>
            <if test="lastcontacttime != null "> and LASTCONTACTTIME = #{lastcontacttime}</if>
            <if test="accepttime != null "> and ACCEPTTIME = #{accepttime}</if>
            <if test="cancletime != null "> and CANCLETIME = #{cancletime}</if>
            <if test="lastsolvingtime != null "> and LASTSOLVINGTIME = #{lastsolvingtime}</if>
            <if test="importantsolvingtime != null "> and IMPORTANTSOLVINGTIME = #{importantsolvingtime}</if>
            <if test="middlesolvingtime != null "> and MIDDLESOLVINGTIME = #{middlesolvingtime}</if>
            <if test="allendtime != null "> and ALLENDTIME = #{allendtime}</if>
            <if test="allimportanttime != null "> and ALLIMPORTANTTIME = #{allimportanttime}</if>
            <if test="allmiddletime != null "> and ALLMIDDLETIME = #{allmiddletime}</if>
            <if test="endtime != null "> and ENDTIME = #{endtime}</if>
            <if test="telasktime != null "> and TELASKTIME = #{telasktime}</if>
            <if test="solvingtime != null "> and SOLVINGTIME = #{solvingtime}</if>
            <if test="dispatchtime != null "> and DISPATCHTIME = #{dispatchtime}</if>
            <if test="createtime != null "> and CREATETIME = #{createtime}</if>
            <if test="percreatetime != null "> and PERCREATETIME = #{percreatetime}</if>
            <if test="discovertime != null "> and DISCOVERTIME = #{discovertime}</if>
            <if test="executedeptname != null  and executedeptname != ''"> and EXECUTEDEPTNAME like concat('%', #{executedeptname}, '%')</if>
            <if test="workgridcode != null  and workgridcode != ''"> and WORKGRIDCODE = #{workgridcode}</if>
            <if test="communityname != null  and communityname != ''"> and COMMUNITYNAME like concat('%', #{communityname}, '%')</if>
            <if test="streetname != null  and streetname != ''"> and STREETNAME like concat('%', #{streetname}, '%')</if>
            <if test="workgrid != null  and workgrid != ''"> and WORKGRID = #{workgrid}</if>
            <if test="address != null  and address != ''"> and ADDRESS like concat('%', #{address}, '%')</if>
            <if test="residentialarea != null  and residentialarea != ''"> and RESIDENTIALAREA = #{residentialarea}</if>
            <if test="streetarea != null  and streetarea != ''"> and STREETAREA = #{streetarea}</if>
            <if test="community != null  and community != ''"> and COMMUNITY = #{community}</if>
            <if test="property != null  and property != ''"> and PROPERTY = #{property}</if>
            <if test="communitycode != null  and communitycode != ''"> and COMMUNITYCODE = #{communitycode}</if>
            <if test="streetcode != null  and streetcode != ''"> and STREETCODE = #{streetcode}</if>
            <if test="viewinfo != null  and viewinfo != ''"> and VIEWINFO = #{viewinfo}</if>
            <if test="casevaluation12345 != null "> and CASEVALUATION_12345 = #{casevaluation12345}</if>
            <if test="notReason != null  and notReason != ''"> and NOT_REASON = #{notReason}</if>
            <if test="description12345 != null  and description12345 != ''"> and DESCRIPTION_12345 = #{description12345}</if>
            <if test="appealExplain != null  and appealExplain != ''"> and APPEAL_EXPLAIN = #{appealExplain}</if>
            <if test="banliresult12345 != null "> and BANLIRESULT_12345 = #{banliresult12345}</if>
            <if test="wpSource != null  and wpSource != ''"> and WP_SOURCE = #{wpSource}</if>
            <if test="reportdeptname != null  and reportdeptname != ''"> and REPORTDEPTNAME like concat('%', #{reportdeptname}, '%')</if>
            <if test="hotlinesn != null  and hotlinesn != ''"> and HOTLINESN = #{hotlinesn}</if>
            <if test="banliresult != null "> and BANLIRESULT = #{banliresult}</if>
            <if test="duLimit != null "> and DU_LIMIT = #{duLimit}</if>
            <if test="urgeCount != null "> and URGE_COUNT = #{urgeCount}</if>
            <if test="callbackFlag != null "> and CALLBACK_FLAG = #{callbackFlag}</if>
            <if test="userevaluate != null "> and USEREVALUATE = #{userevaluate}</if>
            <if test="isanonymity != null "> and ISANONYMITY = #{isanonymity}</if>
            <if test="servicetype != null  and servicetype != ''"> and SERVICETYPE = #{servicetype}</if>
            <if test="similarcasesn != null  and similarcasesn != ''"> and SIMILARCASESN = #{similarcasesn}</if>
            <if test="approach != null "> and APPROACH = #{approach}</if>
            <if test="urgentdegree != null "> and URGENTDEGREE = #{urgentdegree}</if>
            <if test="partsn != null  and partsn != ''"> and PARTSN = #{partsn}</if>
            <if test="endnote != null  and endnote != ''"> and ENDNOTE = #{endnote}</if>
            <if test="dispatchnote != null  and dispatchnote != ''"> and DISPATCHNOTE = #{dispatchnote}</if>
            <if test="reporter != null  and reporter != ''"> and REPORTER like concat('%', #{reporter}, '%')</if>
            <if test="infozcname != null  and infozcname != ''"> and INFOZCNAME = #{infozcname}</if>
            <if test="infoscname != null  and infoscname != ''"> and INFOSCNAME = #{infoscname}</if>
            <if test="infobcname != null  and infobcname != ''"> and INFOBCNAME = #{infobcname}</if>
            <if test="infotypename != null  and infotypename != ''"> and INFOTYPENAME = #{infotypename}</if>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>
            <if test="contactinfo != null  and contactinfo != ''"> and CONTACTINFO  like concat('%', #{contactinfo}, '%')</if>
            <if test="hastentypecount != null "> and HASTENTYPECOUNT = #{hastentypecount}</if>
            <if test="hasleadtypecount != null "> and HASLEADTYPECOUNT = #{hasleadtypecount}</if>
            <if test="huifangcount != null "> and HUIFANGCOUNT = #{huifangcount}</if>
            <if test="hechacount != null "> and HECHACOUNT = #{hechacount}</if>
            <if test="heshicount != null "> and HESHICOUNT = #{heshicount}</if>
            <if test="contactmode != null  and contactmode != ''"> and CONTACTMODE = #{contactmode}</if>
            <if test="callnumber != null  and callnumber != ''"> and CALLNUMBER = #{callnumber}</if>
            <if test="priorityarea != null  and priorityarea != ''"> and PRIORITYAREA = #{priorityarea}</if>
            <if test="checkresult != null "> and CHECKRESULT = #{checkresult}</if>
            <if test="verifyresult != null "> and VERIFYRESULT = #{verifyresult}</if>
            <if test="endresult != null "> and ENDRESULT = #{endresult}</if>
            <if test="caseend != null "> and CASEEND = #{caseend}</if>
            <if test="insertuser != null  and insertuser != ''"> and INSERTUSER = #{insertuser}</if>
            <if test="keepersn != null  and keepersn != ''"> and KEEPERSN = #{keepersn}</if>
            <if test="insertdeptcode != null  and insertdeptcode != ''"> and INSERTDEPTCODE = #{insertdeptcode}</if>
            <if test="executedeptcode != null  and executedeptcode != ''"> and EXECUTEDEPTCODE = #{executedeptcode}</if>
            <if test="deptcode != null  and deptcode != ''"> and DEPTCODE = #{deptcode}</if>
            <if test="status != null "> and STATUS = #{status}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION like concat('%', #{description}, '%')</if>
            <if test="infoatcode != null  and infoatcode != ''"> and INFOATCODE = #{infoatcode}</if>
            <if test="infozccode != null  and infozccode != ''"> and INFOZCCODE = #{infozccode}</if>
            <if test="infosccode != null  and infosccode != ''"> and INFOSCCODE = #{infosccode}</if>
            <if test="infobccode != null  and infobccode != ''"> and INFOBCCODE = #{infobccode}</if>
            <if test="infotypeid != null "> and INFOTYPEID = #{infotypeid}</if>
            <if test="infosourceid != null "> and INFOSOURCEID = #{infosourceid}</if>
            <if test="casesn != null  and casesn != ''"> and CASESN = #{casesn}</if>
            <if test="taskid != null  and taskid != ''"> and TASKID = #{taskid}</if>
            <if test="subexecutedeptnameMh != null  and subexecutedeptnameMh != ''"> and SUBEXECUTEDEPTNAME_MH = #{subexecutedeptnameMh}</if>
            <if test="relatedhotlinesn != null  and relatedhotlinesn != ''"> and RELATEDHOTLINESN = #{relatedhotlinesn}</if>
            <if test="isduplicate != null  and isduplicate != ''"> and ISDUPLICATE = #{isduplicate}</if>
            <if test="isobjective != null  and isobjective != ''"> and ISOBJECTIVE = #{isobjective}</if>
            <if test="powerstorage != null  and powerstorage != ''"> and POWERSTORAGE = #{powerstorage}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(DISCOVERTIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(DISCOVERTIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.typeName != null  and params.typeName != ''"> and INFOSOURCENAME != #{params.typeName}</if>
            <if test="params.infobcname != null  and params.infobcname != ''"> and INFOBCNAME != #{params.infobcname}</if>
            <if test="satisfaction != null  and satisfaction != ''"> and SATISFACTION = #{satisfaction}</if>
            <if test="appealclassification != null  and appealclassification != ''"> and APPEALCLASSIFICATION = #{appealclassification}</if>
            <if test="parentappealclassification != null  and parentappealclassification != ''"> and PARENTAPPEALCLASSIFICATION = #{parentappealclassification}</if>
            <if test="duplicateclassification != null  and duplicateclassification != ''"> and DUPLICATECLASSIFICATION = #{duplicateclassification}</if>
            <if test="parentduplicateclassification != null  and parentduplicateclassification != ''"> and PARENTDUPLICATECLASSIFICATION = #{parentduplicateclassification}</if>
            <if test="params.satisfactions != null and params.satisfactions != ''">
                AND SATISFACTION in
                <foreach collection="params.satisfactions" item="satisfaction" open="(" separator="," close=")">
                    #{satisfaction}
                </foreach>
            </if>
            <if test="params.appealclassifications != null and params.appealclassifications != ''">
                AND (
                <foreach collection="params.appealclassifications" item="appealclassification" separator=" OR ">
                    FIND_IN_SET(#{appealclassification}, APPEALCLASSIFICATION)
                </foreach>
                )
            </if>
            <if test="params.queryTrackingtypes != null and params.queryTrackingtypes != ''">
                AND (
                <foreach collection="params.queryTrackingtypes" item="queryTrackingtype" separator=" OR ">
                    FIND_IN_SET(#{queryTrackingtype}, TRACKINGTYPE)
                </foreach>
                )
            </if>
            <if test="params.specialtopics != null and params.specialtopics != ''">
                AND (
                <foreach collection="params.specialtopics" item="specialtopic" separator=" OR ">
                    FIND_IN_SET(#{specialtopic}, SPECIALTOPIC)
                </foreach>
                )
            </if>
            <if test="evaluationmonth != null  and evaluationmonth != ''"> and EVALUATIONMONTH = #{evaluationmonth}</if>
            <if test="params.vacancy == true"> and (RESIDENTIALAREA is null or RESIDENTIALAREA = '')</if>
            <if test="params.streetvacancy == true"> and (STREETAREA is null or STREETAREA = '')</if>
            <if test="params.communityvacancy == true"> and (COMMUNITY is null or COMMUNITY = '')</if>
            <if test="params.propertyvacancy == true"> and (PROPERTY is null or PROPERTY = '')</if>
            <if test="params.parentappealclassificationvacancy == true"> and (PARENTAPPEALCLASSIFICATION is null or PARENTAPPEALCLASSIFICATION = '')</if>
            <if test="params.appealclassificationsvacancy == true"> and (APPEALCLASSIFICATION is null or APPEALCLASSIFICATION = '')</if>
            <if test="params.parentduplicateclassificationvacancy == true"> and (PARENTDUPLICATECLASSIFICATION is null or PARENTDUPLICATECLASSIFICATION = '')</if>
            <if test="params.duplicateclassificationvacancy == true"> and (DUPLICATECLASSIFICATION is null or DUPLICATECLASSIFICATION = '')</if>
            <if test="params.cfFlag != null and params.cfFlag == true"> and (ISCHECK is NULL or ISCHECK !="是") </if>
            <if test="params.evaluationmonthStart != null and params.evaluationmonthEnd != null">
                AND (EVALUATIONMONTH BETWEEN #{params.evaluationmonthStart} AND #{params.evaluationmonthEnd})
            </if>
        </where>
        ORDER BY DISCOVERTIME DESC
    </select>


    <select id="selectDeptList" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        select * from t_task_dbcenter
        <where>
            <if test="params.evaBeginTime != null and params.evaBeginTime != ''and params.evaEndTime != null and params.evaEndTime != ''"><!-- 开始时间检索 -->
                AND (EVALUATIONMONTH BETWEEN #{params.evaBeginTime} AND #{params.evaEndTime})
            </if>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>
            and SATISFACTION is not  null
        </where>
    </select>

    <select id="selectTaskDbcenterHotlinesnList" resultType="String">
        select HOTLINESN from t_task_dbcenter where INFOSOURCENAME = '12345上报'
    </select>

    <select id="selectSimpleTaskDbcenterList" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        <include refid="selectSimpleTaskDbcenterVo"/>
        <where>
            <if test="infosourcename != null  and infosourcename != ''"> and INFOSOURCENAME = #{infosourcename}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(DISCOVERTIME,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(DISCOVERTIME,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.typeName != null  and params.typeName != ''"> and INFOSOURCENAME != #{params.typeName}</if>
            <if test="params.typeName1 != null  and params.typeName1 != ''"> and INFOSOURCENAME != #{params.typeName1}</if>
            <if test="params.infobcname != null  and params.infobcname != ''"> and INFOBCNAME != #{params.infobcname}</if>
        </where>
        ORDER BY CREATETIME
    </select>

    <select id="selectTaskDbcenterDbgdList" parameterType="TaskDbcenter" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(CREATETIME,'%Y-%m-%d') &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(CREATETIME,'%Y-%m-%d') &lt;= #{params.endTime}
            </if>
            <if test="params.executedeptnames != null and params.executedeptnames != ''">
                AND EXECUTEDEPTNAME in
                <foreach collection="params.executedeptnames" item="executedeptname" open="(" separator="," close=")">
                    #{executedeptname}
                </foreach>
            </if>
            <if test="statusname != null  and statusname != ''"> and STATUSNAME = #{statusname}</if>
            <if test="params.typeName != null  and params.typeName != ''"> and INFOSOURCENAME != #{params.typeName}</if>
            <if test="params.typeName1 != null  and params.typeName1 != ''"> and INFOSOURCENAME != #{params.typeName1}</if>
            <if test="params.infobcname != null  and params.infobcname != ''"> and INFOBCNAME != #{params.infobcname}</if>
            AND (
            (EXECUTEDEPTNAME = '金欣环卫') OR
            (EXECUTEDEPTNAME = '石化街道社区管理办') OR
            (EXECUTEDEPTNAME = '石化街道管违办' AND INFOBCNAME = '环卫市容' AND INFOSCNAME = '乱涂写、乱张贴、乱刻画') OR
            (EXECUTEDEPTNAME = '石化街道综合行政执法队' AND INFOBCNAME = '街面秩序' AND INFOSCNAME = '机动车乱停放、非机动车乱停放') OR
            (EXECUTEDEPTNAME = '锦石市政')
            )
        </where>
        ORDER BY CREATETIME
    </select>

    <select id="selectTaskDbcenterRxDuplicateFirstList" resultMap="TaskDbcenterResult">
        <include refid="selectTaskDbcenterVo"/>
        <where>
          RECENTHOTLINESN is null and RELATEDHOTLINESN is not null
        </where>
    </select>

</mapper>