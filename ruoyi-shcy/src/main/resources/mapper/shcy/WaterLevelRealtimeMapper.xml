<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.WaterLevelRealtimeMapper">
    
    <resultMap type="WaterLevelRealtime" id="WaterLevelRealtimeResult">
        <result property="waterLevelId"    column="water_level_id"    />
        <result property="waterLevel"    column="water_level"    />
        <result property="cjsj"    column="cjsj"    />
        <result property="location"    column="location"    />
        <result property="warn"    column="warn"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="xljId"    column="xlj_id"    />
        <result property="xljName"    column="xlj_name"    />
    </resultMap>

    <sql id="selectWaterLevelRealtimeVo">
        select water_level_id, water_level, cjsj, location, warn, latitude, longitude, xlj_id, xlj_name from jiyun.water_level_realtime
    </sql>

    <select id="selectWaterLevelRealtimeByWaterLevelId" parameterType="Integer" resultMap="WaterLevelRealtimeResult">
        <include refid="selectWaterLevelRealtimeVo"/>
        where water_level_id = #{waterLevelId}
    </select>

</mapper>