<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.CommitteeMapper">

    <resultMap type="Committee" id="CommitteeResult">
        <result property="id"    column="id"    />
        <result property="committeeName"    column="committee_name"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="siteRange"    column="site_range"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="fillColor" column="fill_color"/>
        <result property="outlineColor" column="outline_color" />
        <result property="streetFillColor"    column="street_fill_color"    />
        <result property="streetOutlineColor"    column="street_outline_color"    />
    </resultMap>

    <sql id="selectCommitteeVo">
        select id, committee_name, longitude, latitude, create_by, create_time, update_time, site_range, type,
               coordinate,fill_color,outline_color,street_fill_color, street_outline_color from shcy_committee
    </sql>

    <select id="selectCommitteeList" parameterType="Committee" resultMap="CommitteeResult">
        <include refid="selectCommitteeVo"/>
        <where>
            <if test="committeeName != null  and committeeName != ''"> and committee_name like concat('%', #{committeeName}, '%')</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="siteRange != null  and siteRange != ''"> and site_range = #{siteRange}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="fillColor != null  and fillColor != ''"> and fill_color = #{fillColor}</if>
            <if test="outlineColor != null  and outlineColor != ''"> and outline_color = #{outlineColor}</if>
            <if test="streetFillColor != null">street_fill_color,</if>
            <if test="streetOutlineColor != null">street_outline_color,</if>
            <if test="streetFillColor != null">#{streetFillColor},</if>
            <if test="streetOutlineColor != null">#{streetOutlineColor},</if>
        </where>
    </select>

    <select id="selectCommitteeById" parameterType="Long" resultMap="CommitteeResult">
        <include refid="selectCommitteeVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommittee" parameterType="Committee" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_committee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="committeeName != null">committee_name,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="siteRange != null">site_range,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="fillColor != null">fill_color,</if>
            <if test="outlineColor != null">outline_color,</if>
            <if test="streetFillColor != null">street_fill_color,</if>
            <if test="streetOutlineColor != null">street_outline_color,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="committeeName != null">#{committeeName},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="siteRange != null">#{siteRange},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="fillColor != null">#{fillColor},</if>
            <if test="outlineColor != null">#{outlineColor},</if>
            <if test="streetFillColor != null">#{streetFillColor},</if>
            <if test="streetOutlineColor != null">#{streetOutlineColor},</if>
        </trim>
    </insert>

    <update id="updateCommittee" parameterType="Committee">
        update shcy_committee
        <trim prefix="SET" suffixOverrides=",">
            <if test="committeeName != null">committee_name = #{committeeName},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="siteRange != null">site_range = #{siteRange},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="fillColor != null">fill_color = #{fillColor},</if>
            <if test="outlineColor != null">outline_color = #{outlineColor},</if>
            <if test="streetFillColor != null">street_fill_color = #{streetFillColor},</if>
            <if test="streetOutlineColor != null">street_outline_color = #{streetOutlineColor},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommitteeById" parameterType="Long">
        delete from shcy_committee where id = #{id}
    </delete>

    <delete id="deleteCommitteeByIds" parameterType="String">
        delete from shcy_committee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
