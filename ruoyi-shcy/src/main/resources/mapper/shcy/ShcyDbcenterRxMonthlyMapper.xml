<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyDbcenterRxMonthlyMapper">
    
    <resultMap type="ShcyDbcenterRxMonthly" id="ShcyDbcenterRxMonthlyResult">
        <result property="id"    column="id"    />
        <result property="year"    column="year"    />
        <result property="month"    column="month"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="yeartj"    column="yeartj"    />
        <result property="monthtj"    column="monthtj"    />
        <result property="qs"    column="qs"    />
        <result property="cqajs"    column="cqajs"    />
        <result property="sbjStartTime"    column="sbj_start_time"    />
        <result property="sbjEndTime"    column="sbj_end_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectShcyDbcenterRxMonthlyVo">
        select id, year, month, create_by,status, create_time, update_by, update_time, yeartj, monthtj, qs, cqajs, sbj_start_time, sbj_end_time from shcy_dbcenter_rx_monthly
    </sql>

    <select id="selectShcyDbcenterRxMonthlyList" parameterType="ShcyDbcenterRxMonthly" resultMap="ShcyDbcenterRxMonthlyResult">
        <include refid="selectShcyDbcenterRxMonthlyVo"/>
        <where>  
            <if test="year != null "> and year = #{year}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="month != null "> and month = #{month}</if>
            <if test="yeartj != null "> and yeartj = #{yeartj}</if>
            <if test="monthtj != null "> and monthtj = #{monthtj}</if>
            <if test="qs != null "> and qs = #{qs}</if>
            <if test="cqajs != null "> and cqajs = #{cqajs}</if>
            <if test="sbjStartTime != null "> and sbj_start_time = #{sbjStartTime}</if>
            <if test="sbjEndTime != null "> and sbj_end_time = #{sbjEndTime}</if>
        </where>
    </select>
    
    <select id="selectShcyDbcenterRxMonthlyById" parameterType="Long" resultMap="ShcyDbcenterRxMonthlyResult">
        <include refid="selectShcyDbcenterRxMonthlyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShcyDbcenterRxMonthly" parameterType="ShcyDbcenterRxMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_dbcenter_rx_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="year != null">year,</if>
            <if test="status != null">status,</if>
            <if test="month != null">month,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="yeartj != null">yeartj,</if>
            <if test="monthtj != null">monthtj,</if>
            <if test="qs != null">qs,</if>
            <if test="cqajs != null">cqajs,</if>
            <if test="sbjStartTime != null">sbj_start_time,</if>
            <if test="sbjEndTime != null">sbj_end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="year != null">#{year},</if>
            <if test="status != null">#{status},</if>
            <if test="month != null">#{month},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="yeartj != null">#{yeartj},</if>
            <if test="monthtj != null">#{monthtj},</if>
            <if test="qs != null">#{qs},</if>
            <if test="cqajs != null">#{cqajs},</if>
            <if test="sbjStartTime != null">#{sbjStartTime},</if>
            <if test="sbjEndTime != null">#{sbjEndTime},</if>
         </trim>
    </insert>

    <update id="updateShcyDbcenterRxMonthly" parameterType="ShcyDbcenterRxMonthly">
        update shcy_dbcenter_rx_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="status != null">status = #{status},</if>
            <if test="month != null">month = #{month},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="yeartj != null">yeartj = #{yeartj},</if>
            <if test="monthtj != null">monthtj = #{monthtj},</if>
            <if test="qs != null">qs = #{qs},</if>
            <if test="cqajs != null">cqajs = #{cqajs},</if>
            <if test="sbjStartTime != null">sbj_start_time = #{sbjStartTime},</if>
            <if test="sbjEndTime != null">sbj_end_time = #{sbjEndTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyDbcenterRxMonthlyById" parameterType="Long">
        delete from shcy_dbcenter_rx_monthly where id = #{id}
    </delete>

    <delete id="deleteShcyDbcenterRxMonthlyByIds" parameterType="String">
        delete from shcy_dbcenter_rx_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>