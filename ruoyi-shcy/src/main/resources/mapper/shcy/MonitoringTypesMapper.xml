<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.MonitoringTypesMapper">
    
    <resultMap type="MonitoringTypes" id="MonitoringTypesResult">
        <result property="monitoringTypeId"    column="monitoring_type_id"    />
        <result property="monitoringTypeCode"    column="monitoring_type_code"    />
        <result property="monitoringTypeName"    column="monitoring_type_name"    />
        <result property="monitoringTypeSort"    column="monitoring_type_sort"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMonitoringTypesVo">
        select monitoring_type_id, monitoring_type_code, monitoring_type_name, monitoring_type_sort, status, create_by, create_time, update_by, update_time, remark from shcy_monitoring_types
    </sql>

    <select id="selectMonitoringTypesList" parameterType="MonitoringTypes" resultMap="MonitoringTypesResult">
        <include refid="selectMonitoringTypesVo"/>
        <where>  
            <if test="monitoringTypeCode != null  and monitoringTypeCode != ''"> and monitoring_type_code = #{monitoringTypeCode}</if>
            <if test="monitoringTypeName != null  and monitoringTypeName != ''"> and monitoring_type_name like concat('%', #{monitoringTypeName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectMonitoringTypesByMonitoringTypeId" parameterType="Long" resultMap="MonitoringTypesResult">
        <include refid="selectMonitoringTypesVo"/>
        where monitoring_type_id = #{monitoringTypeId}
    </select>
        
    <insert id="insertMonitoringTypes" parameterType="MonitoringTypes" useGeneratedKeys="true" keyProperty="monitoringTypeId">
        insert into shcy_monitoring_types
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitoringTypeCode != null and monitoringTypeCode != ''">monitoring_type_code,</if>
            <if test="monitoringTypeName != null and monitoringTypeName != ''">monitoring_type_name,</if>
            <if test="monitoringTypeSort != null">monitoring_type_sort,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitoringTypeCode != null and monitoringTypeCode != ''">#{monitoringTypeCode},</if>
            <if test="monitoringTypeName != null and monitoringTypeName != ''">#{monitoringTypeName},</if>
            <if test="monitoringTypeSort != null">#{monitoringTypeSort},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMonitoringTypes" parameterType="MonitoringTypes">
        update shcy_monitoring_types
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitoringTypeCode != null and monitoringTypeCode != ''">monitoring_type_code = #{monitoringTypeCode},</if>
            <if test="monitoringTypeName != null and monitoringTypeName != ''">monitoring_type_name = #{monitoringTypeName},</if>
            <if test="monitoringTypeSort != null">monitoring_type_sort = #{monitoringTypeSort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where monitoring_type_id = #{monitoringTypeId}
    </update>

    <delete id="deleteMonitoringTypesByMonitoringTypeId" parameterType="Long">
        delete from shcy_monitoring_types where monitoring_type_id = #{monitoringTypeId}
    </delete>

    <delete id="deleteMonitoringTypesByMonitoringTypeIds" parameterType="String">
        delete from shcy_monitoring_types where monitoring_type_id in 
        <foreach item="monitoringTypeId" collection="array" open="(" separator="," close=")">
            #{monitoringTypeId}
        </foreach>
    </delete>

    <select id="selectAllMonitoringTypes" resultMap="MonitoringTypesResult">
        <include refid="selectMonitoringTypesVo"/>
    </select>

    <select id="getAllMonitoringTypeName" resultType="String">
        select monitoring_type_name from shcy_monitoring_types where status = '0' order by monitoring_type_sort asc
    </select>

</mapper>