<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.NonPropertyContactMapper">

    <resultMap type="NonPropertyContact" id="NonPropertyContactResult">
        <result property="id"    column="id"    />
        <result property="propertyServicePosition"    column="property_service_position"    />
        <result property="propertyCompany"    column="property_company"    />
        <result property="address" column="address" />
        <result property="contact"    column="contact"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
    </resultMap>

    <sql id="selectNonPropertyContactVo">
        select id, property_service_position, property_company, address, contact, contact_phone, create_by, create_time, update_time,type,coordinate from shcy_non_property_contact
    </sql>

    <select id="selectNonPropertyContactList" parameterType="NonPropertyContact" resultMap="NonPropertyContactResult">
        <include refid="selectNonPropertyContactVo"/>
        <where>
            <if test="propertyServicePosition != null  and propertyServicePosition != ''"> and property_service_position = #{propertyServicePosition}</if>
            <if test="propertyCompany != null  and propertyCompany != ''"> and property_company = #{propertyCompany}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
        </where>
    </select>

    <select id="selectNonPropertyContactById" parameterType="Long" resultMap="NonPropertyContactResult">
        <include refid="selectNonPropertyContactVo"/>
        where id = #{id}
    </select>

    <insert id="insertNonPropertyContact" parameterType="NonPropertyContact" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_non_property_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="propertyServicePosition != null">property_service_position,</if>
            <if test="propertyCompany != null">property_company,</if>
            <if test="contact != null">contact,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="address != null">address,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="propertyServicePosition != null">#{propertyServicePosition},</if>
            <if test="propertyCompany != null">#{propertyCompany},</if>
            <if test="contact != null">#{contact},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="address != null">#{address},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
         </trim>
    </insert>

    <update id="updateNonPropertyContact" parameterType="NonPropertyContact">
        update shcy_non_property_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="propertyServicePosition != null">property_service_position = #{propertyServicePosition},</if>
            <if test="propertyCompany != null">property_company = #{propertyCompany},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="address != null">address = #{address},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNonPropertyContactById" parameterType="Long">
        delete from shcy_non_property_contact where id = #{id}
    </delete>

    <delete id="deleteNonPropertyContactByIds" parameterType="String">
        delete from shcy_non_property_contact where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
