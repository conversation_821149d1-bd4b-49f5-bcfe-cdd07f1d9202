<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.RegionsMapper">
    
    <resultMap type="Regions" id="RegionsResult">
        <result property="id"    column="id"    />
        <result property="indexCode"    column="index_code"    />
        <result property="name"    column="name"    />
        <result property="parentIndexCode"    column="parent_index_code"    />
        <result property="treeCode"    column="tree_code"    />
        <result property="externalIndexCode"    column="external_index_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRegionsVo">
        select id, index_code, name, parent_index_code, tree_code, external_index_code, create_by, create_time, update_by, update_time from shcy_regions
    </sql>

    <select id="selectRegionsList" parameterType="Regions" resultMap="RegionsResult">
        <include refid="selectRegionsVo"/>
        <where>  
            <if test="indexCode != null  and indexCode != ''"> and index_code = #{indexCode}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="parentIndexCode != null  and parentIndexCode != ''"> and parent_index_code = #{parentIndexCode}</if>
            <if test="treeCode != null  and treeCode != ''"> and tree_code = #{treeCode}</if>
            <if test="externalIndexCode != null  and externalIndexCode != ''"> and external_index_code = #{externalIndexCode}</if>
        </where>
    </select>
    
    <select id="selectRegionsById" parameterType="Long" resultMap="RegionsResult">
        <include refid="selectRegionsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRegions" parameterType="Regions" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_regions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="indexCode != null">index_code,</if>
            <if test="name != null">name,</if>
            <if test="parentIndexCode != null">parent_index_code,</if>
            <if test="treeCode != null">tree_code,</if>
            <if test="externalIndexCode != null">external_index_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="indexCode != null">#{indexCode},</if>
            <if test="name != null">#{name},</if>
            <if test="parentIndexCode != null">#{parentIndexCode},</if>
            <if test="treeCode != null">#{treeCode},</if>
            <if test="externalIndexCode != null">#{externalIndexCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRegions" parameterType="Regions">
        update shcy_regions
        <trim prefix="SET" suffixOverrides=",">
            <if test="indexCode != null">index_code = #{indexCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="parentIndexCode != null">parent_index_code = #{parentIndexCode},</if>
            <if test="treeCode != null">tree_code = #{treeCode},</if>
            <if test="externalIndexCode != null">external_index_code = #{externalIndexCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRegionsById" parameterType="Long">
        delete from shcy_regions where id = #{id}
    </delete>

    <delete id="deleteRegionsByIds" parameterType="String">
        delete from shcy_regions where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>