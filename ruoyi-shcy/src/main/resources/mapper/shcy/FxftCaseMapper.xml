<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.FxftCaseMapper">

    <resultMap type="FxftCase" id="FxftCaseResult">
        <result property="id"    column="id"    />
        <result property="caseName"    column="case_name"    />
        <result property="caseType"    column="case_type"    />
        <result property="caseDescription"    column="case_description"    />
        <result property="caseDealBy"    column="case_deal_by"    />
        <result property="caseDealPhoto"    column="case_deal_photo"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
        <result property="address"    column="address"    />
        <result property="alarmRecordId"    column="alarm_record_id"    />
        <result property="caseEndTime"    column="case_end_time"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="checkOthers"    column="check_others"    />
        <result property="checkPhoto"    column="check_photo"    />
        <result property="checkUpdateBy"    column="check_update_by"    />
        <result property="checkUpdateTime"    column="check_update_time"    />
        <result property="deviceImei"    column="device_imei"    />
        <result property="caseNumber"    column="case_number"    />
        <result property="rescuePersonnelStatus"    column="rescue_personnel_status"    />
        <result property="rescueAction"    column="rescue_action"    />
    </resultMap>

    <sql id="selectFxftCaseVo">
        select id, case_name, case_type,rescue_action, case_description, case_deal_by, case_deal_photo, circulation_state, create_by, create_time, update_by, update_time, deal_in_time_state, address, alarm_record_id, case_end_time, case_finish_time, check_others, check_photo, check_update_by, check_update_time, device_imei, case_number, rescue_personnel_status from shcy_fxft_case
    </sql>

    <select id="selectFxftCaseList" parameterType="FxftCase" resultMap="FxftCaseResult">
        <include refid="selectFxftCaseVo"/>
        <where>
            <if test="caseName != null  and caseName != ''"> and case_name like concat('%', #{caseName}, '%')</if>
            <if test="caseNumber != null  and caseNumber != ''"> and case_number like concat('%', #{caseNumber}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="caseDealBy != null  and caseDealBy != ''"> and case_deal_by like concat('%', #{caseDealBy}, '%')</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="rescueAction != null  and rescueAction != ''"> and rescue_action = #{rescueAction}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.keyword != null  and params.keyword != ''"> and (case_number like concat('%', #{params.keyword}, '%')
                or address like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="selectFxftCaseById" parameterType="Long" resultMap="FxftCaseResult">
        <include refid="selectFxftCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertFxftCase" parameterType="FxftCase" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_fxft_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseName != null">case_name,</if>
            <if test="caseType != null">case_type,</if>
            <if test="rescueAction != null">rescue_action,</if>
            <if test="caseDescription != null">case_description,</if>
            <if test="caseDealBy != null">case_deal_by,</if>
            <if test="caseDealPhoto != null">case_deal_photo,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="address != null">address,</if>
            <if test="alarmRecordId != null">alarm_record_id,</if>
            <if test="caseEndTime != null">case_end_time,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
            <if test="checkOthers != null">check_others,</if>
            <if test="checkPhoto != null">check_photo,</if>
            <if test="checkUpdateBy != null">check_update_by,</if>
            <if test="checkUpdateTime != null">check_update_time,</if>
            <if test="deviceImei != null">device_imei,</if>
            <if test="caseNumber != null">case_number,</if>
            <if test="rescuePersonnelStatus != null">rescue_personnel_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseName != null">#{caseName},</if>
            <if test="caseType != null">#{caseType},</if>
            <if test="rescueAction != null">#{rescueAction},</if>
            <if test="caseDescription != null">#{caseDescription},</if>
            <if test="caseDealBy != null">#{caseDealBy},</if>
            <if test="caseDealPhoto != null">#{caseDealPhoto},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="address != null">#{address},</if>
            <if test="alarmRecordId != null">#{alarmRecordId},</if>
            <if test="caseEndTime != null">#{caseEndTime},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
            <if test="checkOthers != null">#{checkOthers},</if>
            <if test="checkPhoto != null">#{checkPhoto},</if>
            <if test="checkUpdateBy != null">#{checkUpdateBy},</if>
            <if test="checkUpdateTime != null">#{checkUpdateTime},</if>
            <if test="deviceImei != null">#{deviceImei},</if>
            <if test="caseNumber != null">#{caseNumber},</if>
            <if test="rescuePersonnelStatus != null">#{rescuePersonnelStatus},</if>
         </trim>
    </insert>

    <update id="updateFxftCase" parameterType="FxftCase">
        update shcy_fxft_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseName != null">case_name = #{caseName},</if>
            <if test="caseType != null">case_type = #{caseType},</if>
            <if test="rescueAction != null">rescue_action = #{rescueAction},</if>
            <if test="caseDescription != null">case_description = #{caseDescription},</if>
            <if test="caseDealBy != null">case_deal_by = #{caseDealBy},</if>
            <if test="caseDealPhoto != null">case_deal_photo = #{caseDealPhoto},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="address != null">address = #{address},</if>
            <if test="alarmRecordId != null">alarm_record_id = #{alarmRecordId},</if>
            <if test="caseEndTime != null">case_end_time = #{caseEndTime},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
            <if test="checkOthers != null">check_others = #{checkOthers},</if>
            <if test="checkPhoto != null">check_photo = #{checkPhoto},</if>
            <if test="checkUpdateBy != null">check_update_by = #{checkUpdateBy},</if>
            <if test="checkUpdateTime != null">check_update_time = #{checkUpdateTime},</if>
            <if test="deviceImei != null">device_imei = #{deviceImei},</if>
            <if test="caseNumber != null">case_number = #{caseNumber},</if>
            <if test="rescuePersonnelStatus != null">rescue_personnel_status = #{rescuePersonnelStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFxftCaseById" parameterType="Long">
        delete from shcy_fxft_case where id = #{id}
    </delete>

    <delete id="deleteFxftCaseByIds" parameterType="String">
        delete from shcy_fxft_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFxftCaseListByCaseNumber" parameterType="FxftCase" resultMap="FxftCaseResult">
        select  m.id, m.case_name, m.case_type,m.rescue_action, m.case_description, m.case_deal_by, m.case_deal_photo, m.circulation_state,
        m.create_by, m.create_time, m.update_by, m.update_time, m.deal_in_time_state, m.address, m.alarm_record_id, m.case_end_time,
        m.case_finish_time, m.check_others, m.check_photo, m.check_update_by, m.check_update_time, m.device_imei, m.case_number, m.rescue_personnel_status
        from shcy_fxft_case m
        LEFT JOIN nbiotyun_alarm_record n on m.alarm_record_id=n.id
        LEFT JOIN shcy_liquid_level_device o on o.device_imei=m.device_imei
        <where>
            <if test="caseNumber != null  and caseNumber != ''"> and m.case_number like concat('%', #{caseNumber}, '%')</if>
            <if test="circulationState != null  and circulationState != ''"> and m.circulation_state = #{circulationState}</if>
            <if test="caseDealBy != null  and caseDealBy != ''"> and m.case_deal_by = #{caseDealBy}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (m.case_number like concat('%', #{params.keyword}, '%')
                or m.address like concat('%', #{params.keyword}, '%'))
            </if>
            <if test="params.pipelineType != null  and params.pipelineType != ''"> and o.pipeline_type = #{params.pipelineType}
            </if>
        </where>
    </select>

    <select id="selectFxftCaseByAlarmRecordId" parameterType="Long" resultMap="FxftCaseResult">
        <include refid="selectFxftCaseVo"/>
        where alarm_record_id = #{alarmRecordId} limit 1
    </select>

    <select id="getCaseCount" parameterType="FxftCase" resultType="long">
        select count(1)
        from shcy_fxft_case m
        LEFT JOIN nbiotyun_alarm_record n on m.alarm_record_id=n.id
        LEFT JOIN shcy_liquid_level_device o on o.device_imei=m.device_imei
        <where>
            <if test="caseNumber != null  and caseNumber != ''"> and m.case_number like concat('%', #{caseNumber}, '%')</if>
            <if test="circulationState != null  and circulationState != ''"> and m.circulation_state = #{circulationState}</if>
            <if test="caseDealBy != null  and caseDealBy != ''"> and m.case_deal_by = #{caseDealBy}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (m.case_number like concat('%', #{params.keyword}, '%')
                or m.address like concat('%', #{params.keyword}, '%'))
            </if>
            <if test="params.pipelineType != null  and params.pipelineType != ''"> and o.pipeline_type = #{params.pipelineType}
            </if>
        </where>
    </select>


</mapper>
