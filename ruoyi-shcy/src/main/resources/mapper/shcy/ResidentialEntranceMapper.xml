<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ResidentialEntranceMapper">

    <resultMap type="ResidentialEntrance" id="ResidentialEntranceResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="committee"    column="committee"    />
        <result property="committeeId"    column="committee_id"    />
        <result property="residential"    column="residential"    />
        <result property="detailSite"    column="detail_site"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="withGuard"    column="with_guard"    />
        <result property="motorVehiclePassing"    column="motor_vehicle_passing"    />
        <result property="entranceType"    column="entrance_type"    />
        <result property="nonMotorVehicleTraffic"    column="non_motor_vehicle_traffic"    />
        <result property="passenger"    column="passenger"    />
        <result property="openTime"    column="open_time"    />
        <result property="remark"    column="remark"    />
        <result property="entranceProperty" column="entrance_property" />
    </resultMap>

    <sql id="selectResidentialEntranceVo">
        select id, name, residential_id, committee, committee_id, residential, detail_site, type, coordinate, create_time, create_by, update_time, with_guard, motor_vehicle_passing, entrance_type, non_motor_vehicle_traffic, passenger, open_time, remark,entrance_property  from shcy_residential_entrance
    </sql>

<!--    <select id="selectResidentialEntranceList" parameterType="ResidentialEntrance" resultMap="ResidentialEntranceResult">-->
    <select id="selectResidentialEntranceList" parameterType="ResidentialEntrance" resultType="com.ruoyi.shcy.domain.ResidentialEntrance">
        SELECT
        sre.id as id,
        sc.committee_name as committee,
        sc.id as committeeId,
        sr.residential as residential,
        sr.id as residentialId,
        sre.detail_site as detailSite,
        sre.type as type,
        sre.coordinate as coordinate,
        sre.create_by as createBy,
        sre.create_time as createTime,
        sre.update_time as updateTime,
        sre.name as name,
        sre.with_guard as withGuard,
        sre.motor_vehicle_passing as motorVehiclePassing,
        sre.entrance_type as entranceType,
        sre.non_motor_vehicle_traffic as nonMotorVehicleTraffic,
        sre.passenger as passenger,
        sre.open_time as openTime,
        sre.remark as remark,
        sre.entrance_property as  entranceProperty
        FROM
        shcy_residential_entrance sre left join  shcy_committee sc on sre.committee_id =sc.id
        left join shcy_residentials  sr on  sre.residential_id = sr.id
        <where>
            <if test="residentialId != null "> and sr.id = #{residentialId}</if>
            <if test="committee != null  and committee != ''"> and c.committee_name = #{committee}</if>
            <if test="committeeId != null "> and sc.id = #{committeeId}</if>
            <if test="residential != null  and residential != ''"> and sr.residential = #{residential}</if>
            <if test="detailSite != null  and detailSite != ''"> and sre.detail_site = #{detailSite}</if>

            <if test="type != null  and type != ''"> and sre.type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and sre.coordinate = #{coordinate}</if>
            <if test="name != null  and name != ''"> and sre.name like concat('%', #{name}, '%')</if>
            <if test="withGuard != null  and withGuard != ''"> and sre.with_guard = #{withGuard}</if>
            <if test="motorVehiclePassing != null  and motorVehiclePassing != ''"> and sre.motor_vehicle_passing = #{motorVehiclePassing}</if>
            <if test="entranceType != null  and entranceType != ''"> and sre.entrance_type = #{entranceType}</if>
            <if test="nonMotorVehicleTraffic != null  and nonMotorVehicleTraffic != ''"> and sre.non_motor_vehicle_traffic = #{nonMotorVehicleTraffic}</if>
            <if test="passenger != null  and passenger != ''"> and sre.passenger = #{passenger}</if>
            <if test="openTime != null  and openTime != ''"> and sre.open_time = #{openTime}</if>
        </where>
    </select>

    <select id="selectResidentialEntranceById" parameterType="Long" resultMap="ResidentialEntranceResult">
        <include refid="selectResidentialEntranceVo"/>
        where id = #{id}
    </select>

    <insert id="insertResidentialEntrance" parameterType="ResidentialEntrance" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_residential_entrance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="residentialId != null">residential_id,</if>
            <if test="committee != null">committee,</if>
            <if test="committeeId != null">committee_id,</if>
            <if test="residential != null">residential,</if>
            <if test="detailSite != null">detail_site,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="name != null">name,</if>
            <if test="withGuard != null">with_guard,</if>
            <if test="motorVehiclePassing != null">motor_vehicle_passing,</if>
            <if test="entranceType != null">entrance_type,</if>
            <if test="nonMotorVehicleTraffic != null">non_motor_vehicle_traffic,</if>
            <if test="passenger != null">passenger,</if>
            <if test="openTime != null">open_time,</if>
            <if test="remark != null">remark,</if>
            <if test="entranceProperty != null">entrance_property,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="residentialId != null">#{residentialId},</if>
            <if test="committee != null">#{committee},</if>
            <if test="committeeId != null">#{committeeId},</if>
            <if test="residential != null">#{residential},</if>
            <if test="detailSite != null">#{detailSite},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="name != null">#{name},</if>
            <if test="withGuard != null">#{withGuard},</if>
            <if test="motorVehiclePassing != null">#{motorVehiclePassing},</if>
            <if test="entranceType != null">#{entranceType},</if>
            <if test="nonMotorVehicleTraffic != null">#{nonMotorVehicleTraffic},</if>
            <if test="passenger != null">#{passenger},</if>
            <if test="openTime != null">#{openTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="entranceProperty != null"> #{entranceProperty},</if>
        </trim>
    </insert>

    <update id="updateResidentialEntrance" parameterType="ResidentialEntrance">
        update shcy_residential_entrance
        <trim prefix="SET" suffixOverrides=",">
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="committee != null">committee = #{committee},</if>
            <if test="committeeId != null">committee_id = #{committeeId},</if>
            <if test="residential != null">residential = #{residential},</if>
            <if test="detailSite != null">detail_site = #{detailSite},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="name != null">name = #{name},</if>
            <if test="withGuard != null">with_guard = #{withGuard},</if>
            <if test="motorVehiclePassing != null">motor_vehicle_passing = #{motorVehiclePassing},</if>
            <if test="entranceType != null">entrance_type = #{entranceType},</if>
            <if test="nonMotorVehicleTraffic != null">non_motor_vehicle_traffic = #{nonMotorVehicleTraffic},</if>
            <if test="passenger != null">passenger = #{passenger},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="entranceProperty != null"> entrance_property = #{entranceProperty}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResidentialEntranceById" parameterType="Long">
        delete from shcy_residential_entrance where id = #{id}
    </delete>

    <delete id="deleteResidentialEntranceByIds" parameterType="String">
        delete from shcy_residential_entrance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
