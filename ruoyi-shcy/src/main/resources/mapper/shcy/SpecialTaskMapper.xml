<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SpecialTaskMapper">
    
    <resultMap type="SpecialTask" id="SpecialTaskResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="gridArea"    column="grid_area"    />
        <result property="inspectionDate"    column="inspection_date"    />
        <result property="issueType"    column="issue_type"    />
        <result property="taskName"    column="task_name"    />
        <result property="inspectionResult"    column="inspection_result"    />
        <result property="scenePhoto"    column="scene_photo"    />
        <result property="reviewDate"    column="review_date"    />
        <result property="reviewResult"    column="review_result"    />
        <result property="disposalPhoto"    column="disposal_photo"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSpecialTaskVo">
        select id, company_name, company_address, grid_area, inspection_date, issue_type, task_name, inspection_result, scene_photo, review_date, review_result, disposal_photo, circulation_state, create_by, create_time, update_by, update_time from shcy_special_task
    </sql>

    <select id="selectSpecialTaskList" parameterType="SpecialTask" resultMap="SpecialTaskResult">
        <include refid="selectSpecialTaskVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="gridArea != null  and gridArea != ''"> and grid_area = #{gridArea}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (company_name like concat('%', #{params.keyword}, '%')
                or task_name like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>
    
    <select id="selectSpecialTaskById" parameterType="Long" resultMap="SpecialTaskResult">
        <include refid="selectSpecialTaskVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSpecialTask" parameterType="SpecialTask" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_special_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="gridArea != null">grid_area,</if>
            <if test="inspectionDate != null">inspection_date,</if>
            <if test="issueType != null">issue_type,</if>
            <if test="taskName != null">task_name,</if>
            <if test="inspectionResult != null">inspection_result,</if>
            <if test="scenePhoto != null">scene_photo,</if>
            <if test="reviewDate != null">review_date,</if>
            <if test="reviewResult != null">review_result,</if>
            <if test="disposalPhoto != null">disposal_photo,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="gridArea != null">#{gridArea},</if>
            <if test="inspectionDate != null">#{inspectionDate},</if>
            <if test="issueType != null">#{issueType},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="inspectionResult != null">#{inspectionResult},</if>
            <if test="scenePhoto != null">#{scenePhoto},</if>
            <if test="reviewDate != null">#{reviewDate},</if>
            <if test="reviewResult != null">#{reviewResult},</if>
            <if test="disposalPhoto != null">#{disposalPhoto},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSpecialTask" parameterType="SpecialTask">
        update shcy_special_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="gridArea != null">grid_area = #{gridArea},</if>
            <if test="inspectionDate != null">inspection_date = #{inspectionDate},</if>
            <if test="issueType != null">issue_type = #{issueType},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="inspectionResult != null">inspection_result = #{inspectionResult},</if>
            <if test="scenePhoto != null">scene_photo = #{scenePhoto},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="reviewResult != null">review_result = #{reviewResult},</if>
            <if test="disposalPhoto != null">disposal_photo = #{disposalPhoto},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSpecialTaskById" parameterType="Long">
        delete from shcy_special_task where id = #{id}
    </delete>

    <delete id="deleteSpecialTaskByIds" parameterType="String">
        delete from shcy_special_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="SpecialTask" resultType="long">
        select count(1) from shcy_special_task
        <where>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="gridArea != null  and gridArea != ''"> and grid_area = #{gridArea}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (company_name like concat('%', #{params.keyword}, '%')
                or task_name like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>
</mapper>