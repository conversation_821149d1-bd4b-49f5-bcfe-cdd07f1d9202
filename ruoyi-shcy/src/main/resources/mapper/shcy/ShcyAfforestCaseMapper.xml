<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyAfforestCaseMapper">

    <resultMap type="ShcyAfforestCase" id="ShcyAfforestCaseResult">
        <result property="id"    column="id"    />
        <result property="problemName"    column="problem_name"    />
        <result property="problemDescription"    column="problem_description"    />
        <result property="problemAddress"    column="problem_address"    />
        <result property="dealTimeLimited"    column="deal_time_limited"    />
        <result property="dealDept"    column="deal_dept"    />
        <result property="appendix"    column="appendix"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="fillDate"    column="fill_date"    />
        <result property="forestId"    column="forest_id"    />
        <result property="dealStatus" column="deal_status" />
        <result property="dealDay" column="deal_day" />
        <result property="problemType" column="problem_type" />
        <result property="dealFinishTime" column="deal_finish_time" />
        <result property="dealInTimeState" column="deal_in_time_state" />
        <result property="dealPhoto" column="deal_photo" />
        <result property="dealDescription" column="deal_description" />

    </resultMap>

    <sql id="selectShcyAfforestCaseVo">
        select id, problem_name, problem_description, problem_address, deal_time_limited, deal_dept, appendix, create_by, create_time, update_by, update_time,
               fill_date, forest_id,deal_status,deal_day,problem_type,deal_finish_time,deal_in_time_state,deal_photo,deal_description from shcy_afforest_case
    </sql>

    <select id="selectShcyAfforestCaseList" parameterType="ShcyAfforestCase" resultMap="ShcyAfforestCaseResult">
        <include refid="selectShcyAfforestCaseVo"/>
        <where>
            <if test="problemName != null  and problemName != ''"> and  problem_name = #{problemName}</if>
            <if test="problemDescription != null  and problemDescription != ''"> and problem_description = #{problemDescription}</if>
            <if test="problemAddress != null  and problemAddress != ''"> and problem_address = #{problemAddress}</if>
            <if test="dealTimeLimited != null "> and deal_time_limited = #{dealTimeLimited}</if>
            <if test="dealDept != null  and dealDept != ''"> and deal_dept = #{dealDept}</if>
            <if test="appendix != null  and appendix != ''"> and appendix = #{appendix}</if>
            <if test="fillDate != null "> and fill_date = #{fillDate}</if>
            <if test="forestId != null "> and forest_id = #{forestId}</if>
            <if test="dealStatus != null and dealStatus != ''"> and deal_status = #{dealStatus}</if>
            <if test="dealDay != null and dealDay != ''"> and deal_day = #{dealDay}</if>
            <if test="problemType != null and problemType != ''"> and problem_type = #{problemType}</if>
            <if test="dealFinishTime != null and dealFinishTime != ''"> and deal_finish_time = #{dealFinishTime}</if>
            <if test="dealInTimeState != null and dealInTimeState != ''"> and deal_in_time_state = #{dealInTimeState}</if>
            <if test="dealPhoto != null and dealPhoto != ''"> and deal_photo = #{dealPhoto}</if>
            <if test="dealDescription != null and dealDescription != ''"> and deal_description = #{dealDescription}</if>
        </where>
        order by deal_status desc,create_time desc
    </select>

    <select id="selectShcyAfforestCaseById" parameterType="Long" resultMap="ShcyAfforestCaseResult">
        <include refid="selectShcyAfforestCaseVo"/>
        where id = #{id}
    </select>

    <select id="selectShcyAfforestCaseByForestId" parameterType="Long" resultMap="ShcyAfforestCaseResult">
        <include refid="selectShcyAfforestCaseVo"/>
        where forest_id = #{forestId}
    </select>

    <insert id="insertShcyAfforestCase" parameterType="ShcyAfforestCase" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_afforest_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="problemName != null">problem_name,</if>
            <if test="problemDescription != null">problem_description,</if>
            <if test="problemAddress != null">problem_address,</if>
            <if test="dealTimeLimited != null">deal_time_limited,</if>
            <if test="dealDept != null">deal_dept,</if>
            <if test="appendix != null">appendix,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fillDate != null">fill_date,</if>
            <if test="forestId != null">forest_id,</if>
            <if test="dealStatus != null">deal_status,</if>
            <if test="dealDay != null">deal_day,</if>
            <if test="problemType != null">problem_type,</if>
            <if test="dealFinishTime != null">deal_finish_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="dealPhoto != null">deal_photo,</if>
            <if test="dealDescription != null">deal_description,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="problemName != null">#{problemName},</if>
            <if test="problemDescription != null">#{problemDescription},</if>
            <if test="problemAddress != null">#{problemAddress},</if>
            <if test="dealTimeLimited != null">#{dealTimeLimited},</if>
            <if test="dealDept != null">#{dealDept},</if>
            <if test="appendix != null">#{appendix},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fillDate != null">#{fillDate},</if>
            <if test="forestId != null">#{forestId},</if>
            <if test="dealStatus != null">#{dealStatus},</if>
            <if test="dealDay != null">#{dealDay},</if>
            <if test="problemType != null">#{problemType},</if>
            <if test="dealFinishTime != null">#{dealFinishTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="dealPhoto != null">#{dealPhoto},</if>
            <if test="dealDescription != null">#{dealDescription},</if>
         </trim>
    </insert>

    <update id="updateShcyAfforestCase" parameterType="ShcyAfforestCase">
        update shcy_afforest_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="problemName != null">problem_name = #{problemName},</if>
            <if test="problemDescription != null">problem_description = #{problemDescription},</if>
            <if test="problemAddress != null">problem_address = #{problemAddress},</if>
            <if test="dealTimeLimited != null">deal_time_limited = #{dealTimeLimited},</if>
            <if test="dealDept != null">deal_dept = #{dealDept},</if>
            <if test="appendix != null">appendix = #{appendix},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="fillDate != null">fill_date = #{fillDate},</if>
            <if test="forestId != null">forest_id = #{forestId},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="dealDay != null">deal_day = #{dealDay},</if>
            <if test="problemType != null">problem_type = #{problemType},</if>
            <if test="dealFinishTime != null">deal_finish_time = #{dealFinishTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="dealPhoto != null">deal_photo = #{dealPhoto},</if>
            <if test="dealDescription != null">deal_description = #{dealDescription},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyAfforestCaseById" parameterType="Long">
        delete from shcy_afforest_case where id = #{id}
    </delete>
    <update id ="returnShcyAffroestCaseById" parameterType="Long">
        update shcy_afforest_case  set deal_status = '2' where id = #{id}
    </update>

    <delete id="deleteShcyAfforestCaseByIds" parameterType="String">
        delete from shcy_afforest_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
