<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.CategoryMapper">
    
    <resultMap type="Category" id="CategoryResult">
        <result property="id"    column="id"    />
        <result property="category"    column="category"    />
    </resultMap>

    <sql id="selectCategoryVo">
        select id, category from shcy_shop_category
    </sql>

    <select id="selectCategoryList" parameterType="Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>  
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
    </select>
    
    <select id="selectCategoryById" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCategory" parameterType="Category" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_shop_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="category != null">category,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="category != null">#{category},</if>
         </trim>
    </insert>

    <update id="updateCategory" parameterType="Category">
        update shcy_shop_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="category != null">category = #{category},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCategoryById" parameterType="Long">
        delete from shcy_shop_category where id = #{id}
    </delete>

    <delete id="deleteCategoryByIds" parameterType="String">
        delete from shcy_shop_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>