<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.RiverLakeMapper">
    
    <resultMap type="RiverLake" id="RiverLakeResult">
        <result property="id"    column="id"    />
        <result property="classification"    column="classification"    />
        <result property="level"    column="level"    />
        <result property="isCrossTown"    column="is_cross_town"    />
        <result property="flowTown"    column="flow_town"    />
        <result property="administrativeDivision"    column="administrative_division"    />
        <result property="streetTown"    column="street_town"    />
        <result property="length"    column="length"    />
        <result property="square"    column="square"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRiverLakeVo">
        select id, classification, level, is_cross_town, flow_town, administrative_division, street_town, length, square, remark, create_time, create_by, update_time from shcy_river_lake
    </sql>

    <select id="selectRiverLakeList" parameterType="RiverLake" resultMap="RiverLakeResult">
        <include refid="selectRiverLakeVo"/>
        <where>  
            <if test="classification != null  and classification != ''"> and classification = #{classification}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="isCrossTown != null  and isCrossTown != ''"> and is_cross_town = #{isCrossTown}</if>
            <if test="flowTown != null  and flowTown != ''"> and flow_town = #{flowTown}</if>
            <if test="administrativeDivision != null  and administrativeDivision != ''"> and administrative_division = #{administrativeDivision}</if>
            <if test="streetTown != null  and streetTown != ''"> and street_town = #{streetTown}</if>
            <if test="length != null  and length != ''"> and length = #{length}</if>
            <if test="square != null  and square != ''"> and square = #{square}</if>
        </where>
    </select>
    
    <select id="selectRiverLakeById" parameterType="Long" resultMap="RiverLakeResult">
        <include refid="selectRiverLakeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRiverLake" parameterType="RiverLake" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_river_lake
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classification != null">classification,</if>
            <if test="level != null">level,</if>
            <if test="isCrossTown != null">is_cross_town,</if>
            <if test="flowTown != null">flow_town,</if>
            <if test="administrativeDivision != null">administrative_division,</if>
            <if test="streetTown != null">street_town,</if>
            <if test="length != null">length,</if>
            <if test="square != null">square,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classification != null">#{classification},</if>
            <if test="level != null">#{level},</if>
            <if test="isCrossTown != null">#{isCrossTown},</if>
            <if test="flowTown != null">#{flowTown},</if>
            <if test="administrativeDivision != null">#{administrativeDivision},</if>
            <if test="streetTown != null">#{streetTown},</if>
            <if test="length != null">#{length},</if>
            <if test="square != null">#{square},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRiverLake" parameterType="RiverLake">
        update shcy_river_lake
        <trim prefix="SET" suffixOverrides=",">
            <if test="classification != null">classification = #{classification},</if>
            <if test="level != null">level = #{level},</if>
            <if test="isCrossTown != null">is_cross_town = #{isCrossTown},</if>
            <if test="flowTown != null">flow_town = #{flowTown},</if>
            <if test="administrativeDivision != null">administrative_division = #{administrativeDivision},</if>
            <if test="streetTown != null">street_town = #{streetTown},</if>
            <if test="length != null">length = #{length},</if>
            <if test="square != null">square = #{square},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRiverLakeById" parameterType="Long">
        delete from shcy_river_lake where id = #{id}
    </delete>

    <delete id="deleteRiverLakeByIds" parameterType="String">
        delete from shcy_river_lake where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>