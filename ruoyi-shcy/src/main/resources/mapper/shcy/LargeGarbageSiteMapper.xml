<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.LargeGarbageSiteMapper">

    <resultMap type="LargeGarbageSite" id="LargeGarbageSiteResult">
        <result property="id"    column="id"    />
        <result property="residential"    column="residential"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="committee"    column="committee"    />
        <result property="committeeId"    column="committee_id"    />
        <result property="longtitude"    column="longtitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="name"    column="name"    />
    </resultMap>

    <sql id="selectLargeGarbageSiteVo">
        select id, residential, residential_id, committee, committee_id, longtitude, latitude, create_time, create_by, update_time, type, coordinate, name from shcy_large_garbage_site
    </sql>

<!--    <select id="selectLargeGarbageSiteList" parameterType="LargeGarbageSite" resultMap="LargeGarbageSiteResult">-->
    <select id="selectLargeGarbageSiteList" parameterType="LargeGarbageSite" resultType="com.ruoyi.shcy.domain.LargeGarbageSite">
        SELECT
        lgs.id as id,
        sc.committee_name as committee,
        sc.id as committeeId,
        sr.residential as residential,
        sr.id as residentialId,
        lgs.longtitude as longtitude,
        lgs.latitude as latitude,
        lgs.create_time as createTime,
        lgs.create_by as createBy,
        lgs.update_time as updateTime,
        lgs.type as type,
        lgs.coordinate as coordinate,
        lgs.name as name
        FROM
        shcy_large_garbage_site  lgs left join  shcy_committee sc on lgs.committee_id =sc.id
        left join shcy_residentials  sr on  lgs.residential_id = sr.id

        <where>
            <if test="residential != null  and residential != ''"> and sr.residential = #{residential}</if>
            <if test="residentialId != null  and residentialId != ''"> and sr.id = #{residentialId}</if>
            <if test="committee != null  and committee != ''"> and sc.committee_name = #{committee}</if>
            <if test="committeeId != null "> and sc.id = #{committeeId}</if>
            <if test="longtitude != null  and longtitude != ''"> and lgs.longtitude = #{longtitude}</if>
            <if test="latitude != null  and latitude != ''"> and lgs.latitude = #{latitude}</if>
            <if test="type != null  and type != ''"> and lgs.type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and lgs.coordinate = #{coordinate}</if>
            <if test="name != null  and name != ''"> and lgs.name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectLargeGarbageSiteById" parameterType="Long" resultMap="LargeGarbageSiteResult">
        <include refid="selectLargeGarbageSiteVo"/>
        where id = #{id}
    </select>

    <insert id="insertLargeGarbageSite" parameterType="LargeGarbageSite" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_large_garbage_site
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="residential != null">residential,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="committee != null">committee,</if>
            <if test="committeeId != null">committee_id,</if>
            <if test="longtitude != null">longtitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="name != null">name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="residential != null">#{residential},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="committee != null">#{committee},</if>
            <if test="committeeId != null">#{committeeId},</if>
            <if test="longtitude != null">#{longtitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>

    <update id="updateLargeGarbageSite" parameterType="LargeGarbageSite">
        update shcy_large_garbage_site
        <trim prefix="SET" suffixOverrides=",">
            <if test="residential != null">residential = #{residential},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="committee != null">committee = #{committee},</if>
            <if test="committeeId != null">committee_id = #{committeeId},</if>
            <if test="longtitude != null">longtitude = #{longtitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="name != null">name = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLargeGarbageSiteById" parameterType="Long">
        delete from shcy_large_garbage_site where id = #{id}
    </delete>

    <delete id="deleteLargeGarbageSiteByIds" parameterType="String">
        delete from shcy_large_garbage_site where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
