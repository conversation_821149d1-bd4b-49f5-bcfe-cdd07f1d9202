<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.NbiotyunDeviceMapper">
    
    <resultMap type="NbiotyunDevice" id="NbiotyunDeviceResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceImei"    column="device_imei"    />
        <result property="deviceImsi"    column="device_imsi"    />
        <result property="iccid"    column="iccid"    />
        <result property="deviceTypeName"    column="device_type_name"    />
        <result property="deviceModelName"    column="device_model_name"    />
        <result property="registerSign"    column="register_sign"    />
        <result property="state"    column="state"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="installAddress"    column="install_address"    />
        <result property="contact"    column="contact"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="companyName"    column="company_name"    />
        <result property="roomName"    column="room_name"    />
        <result property="region"    column="region"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="responsiblePerson"    column="responsible_person"    />
    </resultMap>

    <sql id="selectNbiotyunDeviceVo">
        select device_id, device_imei, device_imsi, iccid, device_type_name, device_model_name, register_sign, state, latitude, longitude, install_address, contact, phonenumber, company_name, room_name, region, create_time, update_time, responsible_person from nbiotyun_device
    </sql>

    <select id="selectNbiotyunDeviceList" parameterType="NbiotyunDevice" resultMap="NbiotyunDeviceResult">
        <include refid="selectNbiotyunDeviceVo"/>
        <where>  
            <if test="deviceTypeName != null  and deviceTypeName != ''"> and device_type_name like concat('%', #{deviceTypeName}, '%')</if>
            <if test="deviceModelName != null  and deviceModelName != ''"> and device_model_name like concat('%', #{deviceModelName}, '%')</if>
            <if test="state != null "> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectNbiotyunDeviceByDeviceId" parameterType="Long" resultMap="NbiotyunDeviceResult">
        <include refid="selectNbiotyunDeviceVo"/>
        where device_id = #{deviceId}
    </select>
        
    <insert id="insertNbiotyunDevice" parameterType="NbiotyunDevice">
        insert into nbiotyun_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceImei != null">device_imei,</if>
            <if test="deviceImsi != null">device_imsi,</if>
            <if test="iccid != null">iccid,</if>
            <if test="deviceTypeName != null">device_type_name,</if>
            <if test="deviceModelName != null">device_model_name,</if>
            <if test="registerSign != null">register_sign,</if>
            <if test="state != null">state,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="installAddress != null">install_address,</if>
            <if test="contact != null">contact,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="companyName != null">company_name,</if>
            <if test="roomName != null">room_name,</if>
            <if test="region != null">region,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceImei != null">#{deviceImei},</if>
            <if test="deviceImsi != null">#{deviceImsi},</if>
            <if test="iccid != null">#{iccid},</if>
            <if test="deviceTypeName != null">#{deviceTypeName},</if>
            <if test="deviceModelName != null">#{deviceModelName},</if>
            <if test="registerSign != null">#{registerSign},</if>
            <if test="state != null">#{state},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="installAddress != null">#{installAddress},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="roomName != null">#{roomName},</if>
            <if test="region != null">#{region},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
         </trim>
    </insert>

    <update id="updateNbiotyunDevice" parameterType="NbiotyunDevice">
        update nbiotyun_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceImei != null">device_imei = #{deviceImei},</if>
            <if test="deviceImsi != null">device_imsi = #{deviceImsi},</if>
            <if test="iccid != null">iccid = #{iccid},</if>
            <if test="deviceTypeName != null">device_type_name = #{deviceTypeName},</if>
            <if test="deviceModelName != null">device_model_name = #{deviceModelName},</if>
            <if test="registerSign != null">register_sign = #{registerSign},</if>
            <if test="state != null">state = #{state},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="installAddress != null">install_address = #{installAddress},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="roomName != null">room_name = #{roomName},</if>
            <if test="region != null">region = #{region},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteNbiotyunDeviceByDeviceId" parameterType="Long">
        delete from nbiotyun_device where device_id = #{deviceId}
    </delete>

    <delete id="deleteNbiotyunDeviceByDeviceIds" parameterType="String">
        delete from nbiotyun_device where device_id in 
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>

    <select id="selectResponsiblePerson" parameterType="String" resultType="String">
        select responsible_person from nbiotyun_device where device_imei = #{deviceImei} limit 1
    </select>
</mapper>