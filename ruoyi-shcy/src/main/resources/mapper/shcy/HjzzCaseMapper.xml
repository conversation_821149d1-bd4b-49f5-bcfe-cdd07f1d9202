<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.HjzzCaseMapper">

    <resultMap type="HjzzCase" id="HjzzCaseResult">
        <result property="id"    column="id"    />
        <result property="caseName"    column="case_name"    />
        <result property="caseType"    column="case_type"    />
        <result property="caseDescription"    column="case_description"    />
        <result property="caseDealBy"    column="case_deal_by"    />
        <result property="caseDealPhoto"    column="case_deal_photo"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
        <result property="address"    column="address"    />
        <result property="alarmRecordId"    column="alarm_record_id"    />
        <result property="caseEndTime"    column="case_end_time"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="checkOthers"    column="check_others"    />
        <result property="checkPhoto"    column="check_photo"    />
        <result property="checkUpdateBy"    column="check_update_by"    />
        <result property="checkUpdateTime"    column="check_update_time"    />
        <result property="caseNumber"    column="case_number"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="dumpingGarbageType"    column="dumping_garbage_type"    />
        <result property="isPersonalBehavior"    column="is_personal_behavior"    />
        <result property="isFiling"    column="is_filing"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="legalName"    column="legal_name"    />
        <result property="personnelInfo"    column="personnel_info"    />
        <result property="idCardNo"    column="id_card_no"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="penaltyCaseNo"    column="penalty_case_no"    />
        <result property="penaltyAmount"    column="penalty_amount"    />
        <result property="alarmDate"    column="alarm_date"    />
        <result property="investigationDeadline"    column="investigation_deadline"    />
        <result property="investigationCompleteTime"    column="investigation_complete_time"    />
        <result property="investigationCompleteState"    column="investigation_complete_state"    />
        <result property="attachment"    column="attachment"    />
    </resultMap>

    <sql id="selectHjzzCaseVo">
        select id, case_name, case_type, case_description, case_deal_by, case_deal_photo, circulation_state, create_by, create_time, update_by, update_time, deal_in_time_state, address, alarm_record_id, case_end_time, case_finish_time, check_others, check_photo, check_update_by, check_update_time, case_number, license_plate, vehicle_type, responsible_person, dumping_garbage_type, is_personal_behavior, is_filing, company_name, company_address, legal_name, personnel_info, id_card_no, contact_info, penalty_case_no, penalty_amount, alarm_date,  investigation_deadline, investigation_complete_time, investigation_complete_state, attachment from shcy_hjzz_case
    </sql>

    <select id="selectHjzzCaseList" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="caseNumber != null  and caseNumber != ''"> and case_number = #{caseNumber}</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="checkOthers != null  and checkOthers != ''"> and check_others = #{checkOthers}</if>
        </where>
    </select>

    <select id="selectHjzzCaseCount" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
        </where>
        order by alarm_date asc
    </select>

    <select id="selectHjzzCaseById" parameterType="Long" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertHjzzCase" parameterType="HjzzCase" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_hjzz_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseName != null">case_name,</if>
            <if test="caseType != null">case_type,</if>
            <if test="caseDescription != null">case_description,</if>
            <if test="caseDealBy != null">case_deal_by,</if>
            <if test="caseDealPhoto != null">case_deal_photo,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="address != null">address,</if>
            <if test="alarmRecordId != null">alarm_record_id,</if>
            <if test="caseEndTime != null">case_end_time,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
            <if test="checkOthers != null">check_others,</if>
            <if test="checkPhoto != null">check_photo,</if>
            <if test="checkUpdateBy != null">check_update_by,</if>
            <if test="checkUpdateTime != null">check_update_time,</if>
            <if test="caseNumber != null">case_number,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="dumpingGarbageType != null">dumping_garbage_type,</if>
            <if test="isPersonalBehavior != null">is_personal_behavior,</if>
            <if test="isFiling != null">is_filing,</if>
            <if test="companyName != null">company_name,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="legalName != null">legal_name,</if>
            <if test="personnelInfo != null">personnel_info,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="penaltyCaseNo != null">penalty_case_no,</if>
            <if test="penaltyAmount != null">penalty_amount,</if>
            <if test="alarmDate != null">alarm_date,</if>
            <if test="investigationDeadline != null">investigation_deadline,</if>
            <if test="investigationCompleteTime != null">investigation_complete_time,</if>
            <if test="investigationCompleteState != null">investigation_complete_state,</if>
            <if test="attachment != null">attachment,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseName != null">#{caseName},</if>
            <if test="caseType != null">#{caseType},</if>
            <if test="caseDescription != null">#{caseDescription},</if>
            <if test="caseDealBy != null">#{caseDealBy},</if>
            <if test="caseDealPhoto != null">#{caseDealPhoto},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="address != null">#{address},</if>
            <if test="alarmRecordId != null">#{alarmRecordId},</if>
            <if test="caseEndTime != null">#{caseEndTime},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
            <if test="checkOthers != null">#{checkOthers},</if>
            <if test="checkPhoto != null">#{checkPhoto},</if>
            <if test="checkUpdateBy != null">#{checkUpdateBy},</if>
            <if test="checkUpdateTime != null">#{checkUpdateTime},</if>
            <if test="caseNumber != null">#{caseNumber},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="dumpingGarbageType != null">#{dumpingGarbageType},</if>
            <if test="isPersonalBehavior != null">#{isPersonalBehavior},</if>
            <if test="isFiling != null">#{isFiling},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="legalName != null">#{legalName},</if>
            <if test="personnelInfo != null">#{personnelInfo},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="penaltyCaseNo != null">#{penaltyCaseNo},</if>
            <if test="penaltyAmount != null">#{penaltyAmount},</if>
            <if test="alarmDate != null">#{alarmDate},</if>
            <if test="investigationDeadline != null">#{investigationDeadline},</if>
            <if test="investigationCompleteTime != null">#{investigationCompleteTime},</if>
            <if test="investigationCompleteState != null">#{investigationCompleteState},</if>
            <if test="attachment != null">#{attachment},</if>
         </trim>
    </insert>

    <update id="updateHjzzCase" parameterType="HjzzCase">
        update shcy_hjzz_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseName != null">case_name = #{caseName},</if>
            <if test="caseType != null">case_type = #{caseType},</if>
            <if test="caseDescription != null">case_description = #{caseDescription},</if>
            <if test="caseDealBy != null">case_deal_by = #{caseDealBy},</if>
            <if test="caseDealPhoto != null">case_deal_photo = #{caseDealPhoto},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="address != null">address = #{address},</if>
            <if test="alarmRecordId != null">alarm_record_id = #{alarmRecordId},</if>
            <if test="caseEndTime != null">case_end_time = #{caseEndTime},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
            <if test="checkOthers != null">check_others = #{checkOthers},</if>
            <if test="checkPhoto != null">check_photo = #{checkPhoto},</if>
            <if test="checkUpdateBy != null">check_update_by = #{checkUpdateBy},</if>
            <if test="checkUpdateTime != null">check_update_time = #{checkUpdateTime},</if>
            <if test="caseNumber != null">case_number = #{caseNumber},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="dumpingGarbageType != null">dumping_garbage_type = #{dumpingGarbageType},</if>
            <if test="isPersonalBehavior != null">is_personal_behavior = #{isPersonalBehavior},</if>
            <if test="isFiling != null">is_filing = #{isFiling},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="legalName != null">legal_name = #{legalName},</if>
            <if test="personnelInfo != null">personnel_info = #{personnelInfo},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="penaltyCaseNo != null">penalty_case_no = #{penaltyCaseNo},</if>
            <if test="penaltyAmount != null">penalty_amount = #{penaltyAmount},</if>
            <if test="alarmDate != null">alarm_date = #{alarmDate},</if>
            <if test="investigationDeadline != null">investigation_deadline = #{investigationDeadline},</if>
            <if test="investigationCompleteTime != null">investigation_complete_time = #{investigationCompleteTime},</if>
            <if test="investigationCompleteState != null">investigation_complete_state = #{investigationCompleteState},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHjzzCaseById" parameterType="Long">
        delete from shcy_hjzz_case where id = #{id}
    </delete>

    <delete id="deleteHjzzCaseByIds" parameterType="String">
        delete from shcy_hjzz_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHistoryList" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            circulation_state in ('0', '3')
            <if test="caseNumber != null  and caseNumber != ''"> and case_number = #{caseNumber}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (case_number like concat('%', #{params.keyword}, '%')
                or penalty_case_no like concat('%', #{params.keyword}, '%')
                or address like concat('%', #{params.keyword}, '%')
                or license_plate like concat('%', #{params.keyword}, '%'))
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(alarm_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(alarm_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectHandleList" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            circulation_state in ('1', '2')
            <if test="caseNumber != null  and caseNumber != ''"> and case_number like concat('%', #{caseNumber}, '%') </if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="dumpingGarbageType != null  and dumpingGarbageType != ''"> and dumping_garbage_type = #{dumpingGarbageType}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (case_number like concat('%', #{params.keyword}, '%')
                or penalty_case_no like concat('%', #{params.keyword}, '%')
                or address like concat('%', #{params.keyword}, '%')
                or license_plate like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="getPendingCaseCount" resultType="long">
        select count(1) from shcy_hjzz_case where circulation_state = '1'
    </select>

    <select id="getProcessingCaseCount" resultType="long">
        select count(1) from shcy_hjzz_case where circulation_state = '2'
    </select>

    <select id="getCaseCount" parameterType="HjzzCase" resultType="long">
        select count(1) from shcy_hjzz_case
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="dumpingGarbageType != null  and dumpingGarbageType != ''"> and dumping_garbage_type = #{dumpingGarbageType}</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (case_number like concat('%', #{params.keyword}, '%')
                or penalty_case_no like concat('%', #{params.keyword}, '%')
                or address like concat('%', #{params.keyword}, '%')
                or license_plate like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="getViolations" parameterType="HjzzCase" resultType="com.ruoyi.shcy.domain.vo.ViolationDataVO">
        SELECT license_plate AS licensePlate, COUNT(*) AS violationsCount
        FROM shcy_hjzz_case
        <where>
            circulation_state = 0 and case_type = '1'
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        GROUP BY license_plate
        ORDER BY violationsCount DESC;
    </select>

    <select id="selectHjzzCaseByCaseNumber" parameterType="String" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        where case_number = #{caseNumber} limit 1
    </select>

    <select id="selectHjzzCaseWgList" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="caseNumber != null  and caseNumber != ''"> and case_number = #{caseNumber}</if>
            <if test="caseType != null  and caseType != ''"> and case_type = #{caseType}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(alarm_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(alarm_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectCompletedList" parameterType="HjzzCase" resultMap="HjzzCaseResult">
        <include refid="selectHjzzCaseVo"/>
        <where>
            circulation_state = '0'
        </where>
    </select>

</mapper>
