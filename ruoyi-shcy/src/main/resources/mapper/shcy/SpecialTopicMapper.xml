<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SpecialTopicMapper">
    
    <resultMap type="SpecialTopic" id="SpecialTopicResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectSpecialTopicVo">
        select id, name, order_num from shcy_special_topic
    </sql>

    <select id="selectSpecialTopicList" parameterType="SpecialTopic" resultMap="SpecialTopicResult">
        <include refid="selectSpecialTopicVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
    </select>
    
    <select id="selectSpecialTopicById" parameterType="Long" resultMap="SpecialTopicResult">
        <include refid="selectSpecialTopicVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSpecialTopic" parameterType="SpecialTopic" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_special_topic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateSpecialTopic" parameterType="SpecialTopic">
        update shcy_special_topic
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSpecialTopicById" parameterType="Long">
        delete from shcy_special_topic where id = #{id}
    </delete>

    <delete id="deleteSpecialTopicByIds" parameterType="String">
        delete from shcy_special_topic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>