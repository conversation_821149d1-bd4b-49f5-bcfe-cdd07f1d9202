<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyDbcenterRxMonthlyExtendMapper">
    
    <resultMap type="ShcyDbcenterRxMonthlyExtend" id="ShcyDbcenterRxMonthlyExtendResult">
        <result property="id"    column="id"    />
        <result property="dept"    column="dept"    />
        <result property="las"    column="las"    />
        <result property="czrks"    column="czrks"    />
        <result property="bzllas"    column="bzllas"    />
        <result property="bzlczrklas"    column="bzlczrklas"    />
        <result property="tzllas"    column="tzllas"    />
        <result property="tzlczrklas"    column="tzlczrklas"    />
        <result property="jylclas"    column="jylclas"    />
        <result property="jylcczrklas"    column="jylcczrklas"    />
        <result property="cjlas"    column="cjlas"    />
        <result property="cjczrklas"    column="cjczrklas"    />
        <result property="bzlas"    column="bzlas"    />
        <result property="jas"    column="jas"    />
        <result property="jal"    column="jal"    />
        <result property="wjas"    column="wjas"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="pid"    column="pid"    />
    </resultMap>

    <sql id="selectShcyDbcenterRxMonthlyExtendVo">
        select id, dept, las, czrks, bzllas, bzlczrklas, tzllas, tzlczrklas, jylclas, jylcczrklas, cjlas, cjczrklas, bzlas, jas, jal, wjas, create_time, create_by, update_by, update_time, pid from shcy_dbcenter_rx_monthly_extend
    </sql>

    <select id="selectShcyDbcenterRxMonthlyExtendList" parameterType="ShcyDbcenterRxMonthlyExtend" resultMap="ShcyDbcenterRxMonthlyExtendResult">
        <include refid="selectShcyDbcenterRxMonthlyExtendVo"/>
        <where>  
            <if test="dept != null  and dept != ''"> and dept = #{dept}</if>
            <if test="las != null  and las != ''"> and las = #{las}</if>
            <if test="czrks != null  and czrks != ''"> and czrks = #{czrks}</if>
            <if test="bzllas != null  and bzllas != ''"> and bzllas = #{bzllas}</if>
            <if test="bzlczrklas != null  and bzlczrklas != ''"> and bzlczrklas = #{bzlczrklas}</if>
            <if test="tzllas != null  and tzllas != ''"> and tzllas = #{tzllas}</if>
            <if test="tzlczrklas != null  and tzlczrklas != ''"> and tzlczrklas = #{tzlczrklas}</if>
            <if test="jylclas != null  and jylclas != ''"> and jylclas = #{jylclas}</if>
            <if test="jylcczrklas != null  and jylcczrklas != ''"> and jylcczrklas = #{jylcczrklas}</if>
            <if test="cjlas != null  and cjlas != ''"> and cjlas = #{cjlas}</if>
            <if test="cjczrklas != null  and cjczrklas != ''"> and cjczrklas = #{cjczrklas}</if>
            <if test="bzlas != null  and bzlas != ''"> and bzlas = #{bzlas}</if>
            <if test="jas != null  and jas != ''"> and jas = #{jas}</if>
            <if test="jal != null  and jal != ''"> and jal = #{jal}</if>
            <if test="wjas != null  and wjas != ''"> and wjas = #{wjas}</if>
            <if test="pid != null "> and pid = #{pid}</if>
        </where>
    </select>
    
    <select id="selectShcyDbcenterRxMonthlyExtendById" parameterType="Long" resultMap="ShcyDbcenterRxMonthlyExtendResult">
        <include refid="selectShcyDbcenterRxMonthlyExtendVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShcyDbcenterRxMonthlyExtend" parameterType="ShcyDbcenterRxMonthlyExtend" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_dbcenter_rx_monthly_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dept != null">dept,</if>
            <if test="las != null">las,</if>
            <if test="czrks != null">czrks,</if>
            <if test="bzllas != null">bzllas,</if>
            <if test="bzlczrklas != null">bzlczrklas,</if>
            <if test="tzllas != null">tzllas,</if>
            <if test="tzlczrklas != null">tzlczrklas,</if>
            <if test="jylclas != null">jylclas,</if>
            <if test="jylcczrklas != null">jylcczrklas,</if>
            <if test="cjlas != null">cjlas,</if>
            <if test="cjczrklas != null">cjczrklas,</if>
            <if test="bzlas != null">bzlas,</if>
            <if test="jas != null">jas,</if>
            <if test="jal != null">jal,</if>
            <if test="wjas != null">wjas,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="pid != null">pid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dept != null">#{dept},</if>
            <if test="las != null">#{las},</if>
            <if test="czrks != null">#{czrks},</if>
            <if test="bzllas != null">#{bzllas},</if>
            <if test="bzlczrklas != null">#{bzlczrklas},</if>
            <if test="tzllas != null">#{tzllas},</if>
            <if test="tzlczrklas != null">#{tzlczrklas},</if>
            <if test="jylclas != null">#{jylclas},</if>
            <if test="jylcczrklas != null">#{jylcczrklas},</if>
            <if test="cjlas != null">#{cjlas},</if>
            <if test="cjczrklas != null">#{cjczrklas},</if>
            <if test="bzlas != null">#{bzlas},</if>
            <if test="jas != null">#{jas},</if>
            <if test="jal != null">#{jal},</if>
            <if test="wjas != null">#{wjas},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="pid != null">#{pid},</if>
         </trim>
    </insert>

    <update id="updateShcyDbcenterRxMonthlyExtend" parameterType="ShcyDbcenterRxMonthlyExtend">
        update shcy_dbcenter_rx_monthly_extend
        <trim prefix="SET" suffixOverrides=",">
            <if test="dept != null">dept = #{dept},</if>
            <if test="las != null">las = #{las},</if>
            <if test="czrks != null">czrks = #{czrks},</if>
            <if test="bzllas != null">bzllas = #{bzllas},</if>
            <if test="bzlczrklas != null">bzlczrklas = #{bzlczrklas},</if>
            <if test="tzllas != null">tzllas = #{tzllas},</if>
            <if test="tzlczrklas != null">tzlczrklas = #{tzlczrklas},</if>
            <if test="jylclas != null">jylclas = #{jylclas},</if>
            <if test="jylcczrklas != null">jylcczrklas = #{jylcczrklas},</if>
            <if test="cjlas != null">cjlas = #{cjlas},</if>
            <if test="cjczrklas != null">cjczrklas = #{cjczrklas},</if>
            <if test="bzlas != null">bzlas = #{bzlas},</if>
            <if test="jas != null">jas = #{jas},</if>
            <if test="jal != null">jal = #{jal},</if>
            <if test="wjas != null">wjas = #{wjas},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="pid != null">pid = #{pid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyDbcenterRxMonthlyExtendById" parameterType="Long">
        delete from shcy_dbcenter_rx_monthly_extend where id = #{id}
    </delete>

    <delete id="deleteShcyDbcenterRxMonthlyExtendByIds" parameterType="String">
        delete from shcy_dbcenter_rx_monthly_extend where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>