<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ParcelInformationMapper">

    <resultMap type="ParcelInformation" id="ParcelInformationResult">
        <result property="id"    column="id"    />
        <result property="parcelNo"    column="parcel_no"    />
        <result property="lotNumber"    column="lot_number"    />
        <result property="landRights"    column="land_rights"    />
        <result property="landUseTrial"    column="land_use_trial"    />
        <result property="belongCommittee"    column="belong_committee"    />
        <result property="ownership"    column="ownership"    />
        <result property="isGyzc"    column="is_gyzc"    />
        <result property="ownershipContact"    column="ownership_contact"    />
        <result property="ownershipPhone"    column="ownership_phone"    />
        <result property="propertyCompany"    column="property_company"    />
        <result property="propertyContact"    column="property_contact"    />
        <result property="propertyPhone"    column="property_phone"    />
        <result property="rightToUse"    column="right_to_use"    />
        <result property="unitType"    column="unit_type"    />
        <result property="unitAddress"    column="unit_address"    />
        <result property="unitContact"    column="unit_contact"    />
        <result property="unitPhone"    column="unit_phone"    />
        <result property="rightToUse1"    column="right_to_use1"    />
        <result property="unitType1"    column="unit_type1"    />
        <result property="unitAddress1"    column="unit_address1"    />
        <result property="unitContact1"    column="unit_contact1"    />
        <result property="unitPhone1"    column="unit_phone1"    />
        <result property="rightToUse2"    column="right_to_use2"    />
        <result property="unitType2"    column="unit_type2"    />
        <result property="unitAddress2"    column="unit_address2"    />
        <result property="unitContact2"    column="unit_contact2"    />
        <result property="unitPhone2"    column="unit_phone2"    />
        <result property="rightToUse3"    column="right_to_use3"    />
        <result property="unitType3"    column="unit_type3"    />
        <result property="unitAddress3"    column="unit_address3"    />
        <result property="unitContact3"    column="unit_contact3"    />
        <result property="unitPhone3"    column="unit_phone3"    />
        <result property="rightToUse4"    column="right_to_use4"    />
        <result property="unitType4"    column="unit_type4"    />
        <result property="unitAddress4"    column="unit_address4"    />
        <result property="unitContact4"    column="unit_contact4"    />
        <result property="unitPhone4"    column="unit_phone4"    />
        <result property="rightToUse5"    column="right_to_use5"    />
        <result property="unitType5"    column="unit_type5"    />
        <result property="unitAddress5"    column="unit_address5"    />
        <result property="unitContact5"    column="unit_contact5"    />
        <result property="unitPhone5"    column="unit_phone5"    />
        <result property="shapeArea"    column="shape_area"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="attribute"    column="attribute"    />
        <result property="projectType" column="project_type"/>

    </resultMap>

    <sql id="selectParcelInformationVo">
        select id, parcel_no, lot_number, land_rights, land_use_trial, belong_committee, ownership, is_gyzc, ownership_contact,
               ownership_phone, property_company, property_contact, property_phone, right_to_use, unit_type, unit_address,
               unit_contact, unit_phone, right_to_use1, unit_type1, unit_address1, unit_contact1, unit_phone1, right_to_use2,
               unit_type2, unit_address2, unit_contact2, unit_phone2, right_to_use3, unit_type3, unit_address3, unit_contact3,
               unit_phone3, right_to_use4, unit_type4, unit_address4, unit_contact4, unit_phone4, right_to_use5, unit_type5,
               unit_address5, unit_contact5, unit_phone5, shape_area, create_time, create_by, update_time,type, coordinate,
               attribute, project_type from shcy_parcel_information
    </sql>

    <select id="selectParcelInformationList" parameterType="ParcelInformation" resultMap="ParcelInformationResult">
        <include refid="selectParcelInformationVo"/>
        <where>
            <if test="parcelNo != null  and parcelNo != ''"> and parcel_no = #{parcelNo}</if>
            <if test="lotNumber != null  and lotNumber != ''"> and lot_number = #{lotNumber}</if>
            <if test="landRights != null  and landRights != ''"> and land_rights = #{landRights}</if>
            <if test="landUseTrial != null  and landUseTrial != ''"> and land_use_trial = #{landUseTrial}</if>
            <if test="belongCommittee != null  and belongCommittee != ''"> and belong_committee = #{belongCommittee}</if>
            <if test="ownership != null  and ownership != ''"> and ownership = #{ownership}</if>
            <if test="isGyzc != null  and isGyzc != ''"> and is_gyzc = #{isGyzc}</if>
            <if test="ownershipContact != null  and ownershipContact != ''"> and ownership_contact = #{ownershipContact}</if>
            <if test="ownershipPhone != null  and ownershipPhone != ''"> and ownership_phone = #{ownershipPhone}</if>
            <if test="propertyCompany != null  and propertyCompany != ''"> and property_company = #{propertyCompany}</if>
            <if test="propertyContact != null  and propertyContact != ''"> and property_contact = #{propertyContact}</if>
            <if test="propertyPhone != null  and propertyPhone != ''"> and property_phone = #{propertyPhone}</if>
            <if test="rightToUse != null  and rightToUse != ''"> and right_to_use = #{rightToUse}</if>
            <if test="unitType != null  and unitType != ''"> and unit_type = #{unitType}</if>
            <if test="unitAddress != null  and unitAddress != ''"> and unit_address = #{unitAddress}</if>
            <if test="unitContact != null  and unitContact != ''"> and unit_contact = #{unitContact}</if>
            <if test="unitPhone != null  and unitPhone != ''"> and unit_phone = #{unitPhone}</if>
            <if test="rightToUse1 != null  and rightToUse1 != ''"> and right_to_use1 = #{rightToUse1}</if>
            <if test="unitType1 != null  and unitType1 != ''"> and unit_type1 = #{unitType1}</if>
            <if test="unitAddress1 != null  and unitAddress1 != ''"> and unit_address1 = #{unitAddress1}</if>
            <if test="unitContact1 != null  and unitContact1 != ''"> and unit_contact1 = #{unitContact1}</if>
            <if test="unitPhone1 != null  and unitPhone1 != ''"> and unit_phone1 = #{unitPhone1}</if>
            <if test="rightToUse2 != null  and rightToUse2 != ''"> and right_to_use2 = #{rightToUse2}</if>
            <if test="unitType2 != null  and unitType2 != ''"> and unit_type2 = #{unitType2}</if>
            <if test="unitAddress2 != null  and unitAddress2 != ''"> and unit_address2 = #{unitAddress2}</if>
            <if test="unitContact2 != null  and unitContact2 != ''"> and unit_contact2 = #{unitContact2}</if>
            <if test="unitPhone2 != null  and unitPhone2 != ''"> and unit_phone2 = #{unitPhone2}</if>
            <if test="rightToUse3 != null  and rightToUse3 != ''"> and right_to_use3 = #{rightToUse3}</if>
            <if test="unitType3 != null  and unitType3 != ''"> and unit_type3 = #{unitType3}</if>
            <if test="unitAddress3 != null  and unitAddress3 != ''"> and unit_address3 = #{unitAddress3}</if>
            <if test="unitContact3 != null  and unitContact3 != ''"> and unit_contact3 = #{unitContact3}</if>
            <if test="unitPhone3 != null  and unitPhone3 != ''"> and unit_phone3 = #{unitPhone3}</if>
            <if test="rightToUse4 != null  and rightToUse4 != ''"> and right_to_use4 = #{rightToUse4}</if>
            <if test="unitType4 != null  and unitType4 != ''"> and unit_type4 = #{unitType4}</if>
            <if test="unitAddress4 != null  and unitAddress4 != ''"> and unit_address4 = #{unitAddress4}</if>
            <if test="unitContact4 != null  and unitContact4 != ''"> and unit_contact4 = #{unitContact4}</if>
            <if test="unitPhone4 != null  and unitPhone4 != ''"> and unit_phone4 = #{unitPhone4}</if>
            <if test="rightToUse5 != null  and rightToUse5 != ''"> and right_to_use5 = #{rightToUse5}</if>
            <if test="unitType5 != null  and unitType5 != ''"> and unit_type5 = #{unitType5}</if>
            <if test="unitAddress5 != null  and unitAddress5 != ''"> and unit_address5 = #{unitAddress5}</if>
            <if test="unitContact5 != null  and unitContact5 != ''"> and unit_contact5 = #{unitContact5}</if>
            <if test="unitPhone5 != null  and unitPhone5 != ''"> and unit_phone5 = #{unitPhone5}</if>
            <if test="shapeArea != null  and shapeArea != ''"> and shape_area = #{shapeArea}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="attribute != null  and attribute != ''"> and attribute = #{attribute}</if>
            <if test="projectType != null  and projectType != ''"> and  project_type = #{projectType}</if>
        </where>
    </select>

    <select id="selectParcelInformationById" parameterType="Long" resultMap="ParcelInformationResult">
        <include refid="selectParcelInformationVo"/>
        where id = #{id}
    </select>

    <insert id="insertParcelInformation" parameterType="ParcelInformation" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_parcel_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parcelNo != null">parcel_no,</if>
            <if test="lotNumber != null">lot_number,</if>
            <if test="landRights != null">land_rights,</if>
            <if test="landUseTrial != null">land_use_trial,</if>
            <if test="belongCommittee != null">belong_committee,</if>
            <if test="ownership != null">ownership,</if>
            <if test="isGyzc != null">is_gyzc,</if>
            <if test="ownershipContact != null">ownership_contact,</if>
            <if test="ownershipPhone != null">ownership_phone,</if>
            <if test="propertyCompany != null">property_company,</if>
            <if test="propertyContact != null">property_contact,</if>
            <if test="propertyPhone != null">property_phone,</if>
            <if test="rightToUse != null">right_to_use,</if>
            <if test="unitType != null">unit_type,</if>
            <if test="unitAddress != null">unit_address,</if>
            <if test="unitContact != null">unit_contact,</if>
            <if test="unitPhone != null">unit_phone,</if>
            <if test="rightToUse1 != null">right_to_use1,</if>
            <if test="unitType1 != null">unit_type1,</if>
            <if test="unitAddress1 != null">unit_address1,</if>
            <if test="unitContact1 != null">unit_contact1,</if>
            <if test="unitPhone1 != null">unit_phone1,</if>
            <if test="rightToUse2 != null">right_to_use2,</if>
            <if test="unitType2 != null">unit_type2,</if>
            <if test="unitAddress2 != null">unit_address2,</if>
            <if test="unitContact2 != null">unit_contact2,</if>
            <if test="unitPhone2 != null">unit_phone2,</if>
            <if test="rightToUse3 != null">right_to_use3,</if>
            <if test="unitType3 != null">unit_type3,</if>
            <if test="unitAddress3 != null">unit_address3,</if>
            <if test="unitContact3 != null">unit_contact3,</if>
            <if test="unitPhone3 != null">unit_phone3,</if>
            <if test="rightToUse4 != null">right_to_use4,</if>
            <if test="unitType4 != null">unit_type4,</if>
            <if test="unitAddress4 != null">unit_address4,</if>
            <if test="unitContact4 != null">unit_contact4,</if>
            <if test="unitPhone4 != null">unit_phone4,</if>
            <if test="rightToUse5 != null">right_to_use5,</if>
            <if test="unitType5 != null">unit_type5,</if>
            <if test="unitAddress5 != null">unit_address5,</if>
            <if test="unitContact5 != null">unit_contact5,</if>
            <if test="unitPhone5 != null">unit_phone5,</if>
            <if test="shapeArea != null">shape_area,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="attribute != null">attribute,</if>
            <if test="projectType != null">project_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parcelNo != null">#{parcelNo},</if>
            <if test="lotNumber != null">#{lotNumber},</if>
            <if test="landRights != null">#{landRights},</if>
            <if test="landUseTrial != null">#{landUseTrial},</if>
            <if test="belongCommittee != null">#{belongCommittee},</if>
            <if test="ownership != null">#{ownership},</if>
            <if test="isGyzc != null">#{isGyzc},</if>
            <if test="ownershipContact != null">#{ownershipContact},</if>
            <if test="ownershipPhone != null">#{ownershipPhone},</if>
            <if test="propertyCompany != null">#{propertyCompany},</if>
            <if test="propertyContact != null">#{propertyContact},</if>
            <if test="propertyPhone != null">#{propertyPhone},</if>
            <if test="rightToUse != null">#{rightToUse},</if>
            <if test="unitType != null">#{unitType},</if>
            <if test="unitAddress != null">#{unitAddress},</if>
            <if test="unitContact != null">#{unitContact},</if>
            <if test="unitPhone != null">#{unitPhone},</if>
            <if test="rightToUse1 != null">#{rightToUse1},</if>
            <if test="unitType1 != null">#{unitType1},</if>
            <if test="unitAddress1 != null">#{unitAddress1},</if>
            <if test="unitContact1 != null">#{unitContact1},</if>
            <if test="unitPhone1 != null">#{unitPhone1},</if>
            <if test="rightToUse2 != null">#{rightToUse2},</if>
            <if test="unitType2 != null">#{unitType2},</if>
            <if test="unitAddress2 != null">#{unitAddress2},</if>
            <if test="unitContact2 != null">#{unitContact2},</if>
            <if test="unitPhone2 != null">#{unitPhone2},</if>
            <if test="rightToUse3 != null">#{rightToUse3},</if>
            <if test="unitType3 != null">#{unitType3},</if>
            <if test="unitAddress3 != null">#{unitAddress3},</if>
            <if test="unitContact3 != null">#{unitContact3},</if>
            <if test="unitPhone3 != null">#{unitPhone3},</if>
            <if test="rightToUse4 != null">#{rightToUse4},</if>
            <if test="unitType4 != null">#{unitType4},</if>
            <if test="unitAddress4 != null">#{unitAddress4},</if>
            <if test="unitContact4 != null">#{unitContact4},</if>
            <if test="unitPhone4 != null">#{unitPhone4},</if>
            <if test="rightToUse5 != null">#{rightToUse5},</if>
            <if test="unitType5 != null">#{unitType5},</if>
            <if test="unitAddress5 != null">#{unitAddress5},</if>
            <if test="unitContact5 != null">#{unitContact5},</if>
            <if test="unitPhone5 != null">#{unitPhone5},</if>
            <if test="shapeArea != null">#{shapeArea},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="attribute != null">#{attribute},</if>
            <if test="projectType != null">#{projectType},</if>
        </trim>
    </insert>

    <update id="updateParcelInformation" parameterType="ParcelInformation">
        update shcy_parcel_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="parcelNo != null">parcel_no = #{parcelNo},</if>
            <if test="lotNumber != null">lot_number = #{lotNumber},</if>
            <if test="landRights != null">land_rights = #{landRights},</if>
            <if test="landUseTrial != null">land_use_trial = #{landUseTrial},</if>
            <if test="belongCommittee != null">belong_committee = #{belongCommittee},</if>
            <if test="ownership != null">ownership = #{ownership},</if>
            <if test="isGyzc != null">is_gyzc = #{isGyzc},</if>
            <if test="ownershipContact != null">ownership_contact = #{ownershipContact},</if>
            <if test="ownershipPhone != null">ownership_phone = #{ownershipPhone},</if>
            <if test="propertyCompany != null">property_company = #{propertyCompany},</if>
            <if test="propertyContact != null">property_contact = #{propertyContact},</if>
            <if test="propertyPhone != null">property_phone = #{propertyPhone},</if>
            <if test="rightToUse != null">right_to_use = #{rightToUse},</if>
            <if test="unitType != null">unit_type = #{unitType},</if>
            <if test="unitAddress != null">unit_address = #{unitAddress},</if>
            <if test="unitContact != null">unit_contact = #{unitContact},</if>
            <if test="unitPhone != null">unit_phone = #{unitPhone},</if>
            <if test="rightToUse1 != null">right_to_use1 = #{rightToUse1},</if>
            <if test="unitType1 != null">unit_type1 = #{unitType1},</if>
            <if test="unitAddress1 != null">unit_address1 = #{unitAddress1},</if>
            <if test="unitContact1 != null">unit_contact1 = #{unitContact1},</if>
            <if test="unitPhone1 != null">unit_phone1 = #{unitPhone1},</if>
            <if test="rightToUse2 != null">right_to_use2 = #{rightToUse2},</if>
            <if test="unitType2 != null">unit_type2 = #{unitType2},</if>
            <if test="unitAddress2 != null">unit_address2 = #{unitAddress2},</if>
            <if test="unitContact2 != null">unit_contact2 = #{unitContact2},</if>
            <if test="unitPhone2 != null">unit_phone2 = #{unitPhone2},</if>
            <if test="rightToUse3 != null">right_to_use3 = #{rightToUse3},</if>
            <if test="unitType3 != null">unit_type3 = #{unitType3},</if>
            <if test="unitAddress3 != null">unit_address3 = #{unitAddress3},</if>
            <if test="unitContact3 != null">unit_contact3 = #{unitContact3},</if>
            <if test="unitPhone3 != null">unit_phone3 = #{unitPhone3},</if>
            <if test="rightToUse4 != null">right_to_use4 = #{rightToUse4},</if>
            <if test="unitType4 != null">unit_type4 = #{unitType4},</if>
            <if test="unitAddress4 != null">unit_address4 = #{unitAddress4},</if>
            <if test="unitContact4 != null">unit_contact4 = #{unitContact4},</if>
            <if test="unitPhone4 != null">unit_phone4 = #{unitPhone4},</if>
            <if test="rightToUse5 != null">right_to_use5 = #{rightToUse5},</if>
            <if test="unitType5 != null">unit_type5 = #{unitType5},</if>
            <if test="unitAddress5 != null">unit_address5 = #{unitAddress5},</if>
            <if test="unitContact5 != null">unit_contact5 = #{unitContact5},</if>
            <if test="unitPhone5 != null">unit_phone5 = #{unitPhone5},</if>
            <if test="shapeArea != null">shape_area = #{shapeArea},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="attribute != null">attribute = #{attribute},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteParcelInformationById" parameterType="Long">
        delete from shcy_parcel_information where id = #{id}
    </delete>

    <delete id="deleteParcelInformationByIds" parameterType="String">
        delete from shcy_parcel_information where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
