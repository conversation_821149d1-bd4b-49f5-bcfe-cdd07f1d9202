<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.PersonnelGridMapper">
    
    <resultMap type="PersonnelGrid" id="PersonnelGridResult">
        <result property="id"    column="id"    />
        <result property="xm"    column="xm"    />
        <result property="zm"    column="zm"    />
        <result property="qmrq"    column="qmrq"    />
        <result property="hxdd"    column="hxdd"    />
        <result property="jzdd"    column="jzdd"    />
        <result property="jzdcjw"    column="jzdcjw"    />
        <result property="sqjzcbdx"    column="sqjzcbdx"    />
        <result property="ysdj"    column="ysdj"    />
        <result property="nrwghglyy"    column="nrwghglyy"    />
        <result property="cjllxyxm"    column="cjllxyxm"    />
        <result property="cjllxylxfs"    column="cjllxylxfs"    />
        <result property="wgxxyxm"    column="wgxxyxm"    />
        <result property="wgxxylxfs"    column="wgxxylxfs"    />
        <result property="bz"    column="bz"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPersonnelGridVo">
        select id, xm, zm, qmrq, hxdd, jzdd, jzdcjw, sqjzcbdx, ysdj, nrwghglyy, cjllxyxm, cjllxylxfs, wgxxyxm, wgxxylxfs, bz, type, coordinate, create_by, create_time, update_by, update_time from shcy_personnel_grid
    </sql>

    <select id="selectPersonnelGridList" parameterType="PersonnelGrid" resultMap="PersonnelGridResult">
        <include refid="selectPersonnelGridVo"/>
        <where>  
            <if test="xm != null  and xm != ''"> and xm like concat('%', #{xm}, '%')</if>
        </where>
    </select>
    
    <select id="selectPersonnelGridById" parameterType="Long" resultMap="PersonnelGridResult">
        <include refid="selectPersonnelGridVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPersonnelGrid" parameterType="PersonnelGrid" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_personnel_grid
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xm != null">xm,</if>
            <if test="zm != null">zm,</if>
            <if test="qmrq != null">qmrq,</if>
            <if test="hxdd != null">hxdd,</if>
            <if test="jzdd != null">jzdd,</if>
            <if test="jzdcjw != null">jzdcjw,</if>
            <if test="sqjzcbdx != null">sqjzcbdx,</if>
            <if test="ysdj != null">ysdj,</if>
            <if test="nrwghglyy != null">nrwghglyy,</if>
            <if test="cjllxyxm != null">cjllxyxm,</if>
            <if test="cjllxylxfs != null">cjllxylxfs,</if>
            <if test="wgxxyxm != null">wgxxyxm,</if>
            <if test="wgxxylxfs != null">wgxxylxfs,</if>
            <if test="bz != null">bz,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xm != null">#{xm},</if>
            <if test="zm != null">#{zm},</if>
            <if test="qmrq != null">#{qmrq},</if>
            <if test="hxdd != null">#{hxdd},</if>
            <if test="jzdd != null">#{jzdd},</if>
            <if test="jzdcjw != null">#{jzdcjw},</if>
            <if test="sqjzcbdx != null">#{sqjzcbdx},</if>
            <if test="ysdj != null">#{ysdj},</if>
            <if test="nrwghglyy != null">#{nrwghglyy},</if>
            <if test="cjllxyxm != null">#{cjllxyxm},</if>
            <if test="cjllxylxfs != null">#{cjllxylxfs},</if>
            <if test="wgxxyxm != null">#{wgxxyxm},</if>
            <if test="wgxxylxfs != null">#{wgxxylxfs},</if>
            <if test="bz != null">#{bz},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePersonnelGrid" parameterType="PersonnelGrid">
        update shcy_personnel_grid
        <trim prefix="SET" suffixOverrides=",">
            <if test="xm != null">xm = #{xm},</if>
            <if test="zm != null">zm = #{zm},</if>
            <if test="qmrq != null">qmrq = #{qmrq},</if>
            <if test="hxdd != null">hxdd = #{hxdd},</if>
            <if test="jzdd != null">jzdd = #{jzdd},</if>
            <if test="jzdcjw != null">jzdcjw = #{jzdcjw},</if>
            <if test="sqjzcbdx != null">sqjzcbdx = #{sqjzcbdx},</if>
            <if test="ysdj != null">ysdj = #{ysdj},</if>
            <if test="nrwghglyy != null">nrwghglyy = #{nrwghglyy},</if>
            <if test="cjllxyxm != null">cjllxyxm = #{cjllxyxm},</if>
            <if test="cjllxylxfs != null">cjllxylxfs = #{cjllxylxfs},</if>
            <if test="wgxxyxm != null">wgxxyxm = #{wgxxyxm},</if>
            <if test="wgxxylxfs != null">wgxxylxfs = #{wgxxylxfs},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelGridById" parameterType="Long">
        delete from shcy_personnel_grid where id = #{id}
    </delete>

    <delete id="deletePersonnelGridByIds" parameterType="String">
        delete from shcy_personnel_grid where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>