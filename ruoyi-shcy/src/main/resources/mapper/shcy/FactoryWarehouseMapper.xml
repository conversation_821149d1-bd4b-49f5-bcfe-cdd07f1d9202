<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.FactoryWarehouseMapper">
    
    <resultMap type="FactoryWarehouse" id="FactoryWarehouseResult">
        <result property="id"    column="id"    />
        <result property="unitName"    column="unit_name"    />
        <result property="areaName"    column="area_name"    />
        <result property="propertyUnit"    column="property_unit"    />
        <result property="isManagedByTglw"    column="is_managed_by_tglw"    />
        <result property="buildingPartition"    column="building_partition"    />
        <result property="buildingAddress"    column="building_address"    />
        <result property="buildingStructure"    column="building_structure"    />
        <result property="fireResistanceLevel"    column="fire_resistance_level"    />
        <result property="usageStatus"    column="usage_status"    />
        <result property="buildingTypeTag"    column="building_type_tag"    />
        <result property="functionTag1"    column="function_tag_1"    />
        <result property="functionTag2"    column="function_tag_2"    />
        <result property="buildingArea"    column="building_area"    />
        <result property="buildingHeight"    column="building_height"    />
        <result property="floorCount"    column="floor_count"    />
        <result property="maxFloorHeight"    column="max_floor_height"    />
        <result property="mezzanineArea"    column="mezzanine_area"    />
        <result property="attachmentArea"    column="attachment_area"    />
        <result property="fireFacilities"    column="fire_facilities"    />
        <result property="mainMaterials"    column="main_materials"    />
        <result property="fireHazardLevel"    column="fire_hazard_level"    />
        <result property="riskLevel"    column="risk_level"    />
    </resultMap>

    <sql id="selectFactoryWarehouseVo">
        select id, unit_name, area_name, property_unit, is_managed_by_tglw, building_partition, building_address, building_structure, fire_resistance_level, usage_status, building_type_tag, function_tag_1, function_tag_2, building_area, building_height, floor_count, max_floor_height, mezzanine_area, attachment_area, fire_facilities, main_materials, fire_hazard_level, risk_level from shcy_factory_warehouse
    </sql>

    <select id="selectFactoryWarehouseList" parameterType="FactoryWarehouse" resultMap="FactoryWarehouseResult">
        <include refid="selectFactoryWarehouseVo"/>
        <where>  
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
        </where>
    </select>
    
    <select id="selectFactoryWarehouseById" parameterType="Long" resultMap="FactoryWarehouseResult">
        <include refid="selectFactoryWarehouseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFactoryWarehouse" parameterType="FactoryWarehouse" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_factory_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitName != null">unit_name,</if>
            <if test="areaName != null">area_name,</if>
            <if test="propertyUnit != null">property_unit,</if>
            <if test="isManagedByTglw != null">is_managed_by_tglw,</if>
            <if test="buildingPartition != null">building_partition,</if>
            <if test="buildingAddress != null">building_address,</if>
            <if test="buildingStructure != null">building_structure,</if>
            <if test="fireResistanceLevel != null">fire_resistance_level,</if>
            <if test="usageStatus != null">usage_status,</if>
            <if test="buildingTypeTag != null">building_type_tag,</if>
            <if test="functionTag1 != null">function_tag_1,</if>
            <if test="functionTag2 != null">function_tag_2,</if>
            <if test="buildingArea != null">building_area,</if>
            <if test="buildingHeight != null">building_height,</if>
            <if test="floorCount != null">floor_count,</if>
            <if test="maxFloorHeight != null">max_floor_height,</if>
            <if test="mezzanineArea != null">mezzanine_area,</if>
            <if test="attachmentArea != null">attachment_area,</if>
            <if test="fireFacilities != null">fire_facilities,</if>
            <if test="mainMaterials != null">main_materials,</if>
            <if test="fireHazardLevel != null">fire_hazard_level,</if>
            <if test="riskLevel != null">risk_level,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitName != null">#{unitName},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="propertyUnit != null">#{propertyUnit},</if>
            <if test="isManagedByTglw != null">#{isManagedByTglw},</if>
            <if test="buildingPartition != null">#{buildingPartition},</if>
            <if test="buildingAddress != null">#{buildingAddress},</if>
            <if test="buildingStructure != null">#{buildingStructure},</if>
            <if test="fireResistanceLevel != null">#{fireResistanceLevel},</if>
            <if test="usageStatus != null">#{usageStatus},</if>
            <if test="buildingTypeTag != null">#{buildingTypeTag},</if>
            <if test="functionTag1 != null">#{functionTag1},</if>
            <if test="functionTag2 != null">#{functionTag2},</if>
            <if test="buildingArea != null">#{buildingArea},</if>
            <if test="buildingHeight != null">#{buildingHeight},</if>
            <if test="floorCount != null">#{floorCount},</if>
            <if test="maxFloorHeight != null">#{maxFloorHeight},</if>
            <if test="mezzanineArea != null">#{mezzanineArea},</if>
            <if test="attachmentArea != null">#{attachmentArea},</if>
            <if test="fireFacilities != null">#{fireFacilities},</if>
            <if test="mainMaterials != null">#{mainMaterials},</if>
            <if test="fireHazardLevel != null">#{fireHazardLevel},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
         </trim>
    </insert>

    <update id="updateFactoryWarehouse" parameterType="FactoryWarehouse">
        update shcy_factory_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="propertyUnit != null">property_unit = #{propertyUnit},</if>
            <if test="isManagedByTglw != null">is_managed_by_tglw = #{isManagedByTglw},</if>
            <if test="buildingPartition != null">building_partition = #{buildingPartition},</if>
            <if test="buildingAddress != null">building_address = #{buildingAddress},</if>
            <if test="buildingStructure != null">building_structure = #{buildingStructure},</if>
            <if test="fireResistanceLevel != null">fire_resistance_level = #{fireResistanceLevel},</if>
            <if test="usageStatus != null">usage_status = #{usageStatus},</if>
            <if test="buildingTypeTag != null">building_type_tag = #{buildingTypeTag},</if>
            <if test="functionTag1 != null">function_tag_1 = #{functionTag1},</if>
            <if test="functionTag2 != null">function_tag_2 = #{functionTag2},</if>
            <if test="buildingArea != null">building_area = #{buildingArea},</if>
            <if test="buildingHeight != null">building_height = #{buildingHeight},</if>
            <if test="floorCount != null">floor_count = #{floorCount},</if>
            <if test="maxFloorHeight != null">max_floor_height = #{maxFloorHeight},</if>
            <if test="mezzanineArea != null">mezzanine_area = #{mezzanineArea},</if>
            <if test="attachmentArea != null">attachment_area = #{attachmentArea},</if>
            <if test="fireFacilities != null">fire_facilities = #{fireFacilities},</if>
            <if test="mainMaterials != null">main_materials = #{mainMaterials},</if>
            <if test="fireHazardLevel != null">fire_hazard_level = #{fireHazardLevel},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFactoryWarehouseById" parameterType="Long">
        delete from shcy_factory_warehouse where id = #{id}
    </delete>

    <delete id="deleteFactoryWarehouseByIds" parameterType="String">
        delete from shcy_factory_warehouse where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>