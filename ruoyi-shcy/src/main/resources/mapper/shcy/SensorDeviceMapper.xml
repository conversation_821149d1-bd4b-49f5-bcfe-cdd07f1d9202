<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SensorDeviceMapper">
    
    <resultMap type="SensorDevice" id="SensorDeviceResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="sensorimeiTypeName"    column="sensorimei_type_name"    />
        <result property="status"    column="status"    />
        <result property="imei"    column="imei"    />
        <result property="value"    column="value"    />
        <result property="unit"    column="unit"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="waterLevelType"    column="water_level_type"    />
    </resultMap>

    <sql id="selectSensorDeviceVo">
        select id, device_name, sensorimei_type_name, status, imei, value, unit, update_time, type, coordinate, water_level_type from shcy_sensor_device
    </sql>

    <select id="selectSensorDeviceList" parameterType="SensorDevice" resultMap="SensorDeviceResult">
        <include refid="selectSensorDeviceVo"/>
        <where>  
            <if test="imei != null  and imei != ''"> and imei = #{imei}</if>
        </where>
    </select>
    
    <select id="selectSensorDeviceById" parameterType="Long" resultMap="SensorDeviceResult">
        <include refid="selectSensorDeviceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSensorDevice" parameterType="SensorDevice" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_sensor_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">device_name,</if>
            <if test="sensorimeiTypeName != null">sensorimei_type_name,</if>
            <if test="status != null">status,</if>
            <if test="imei != null">imei,</if>
            <if test="value != null">value,</if>
            <if test="unit != null">unit,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="waterLevelType != null">water_level_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">#{deviceName},</if>
            <if test="sensorimeiTypeName != null">#{sensorimeiTypeName},</if>
            <if test="status != null">#{status},</if>
            <if test="imei != null">#{imei},</if>
            <if test="value != null">#{value},</if>
            <if test="unit != null">#{unit},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="waterLevelType != null">#{waterLevelType},</if>
         </trim>
    </insert>

    <update id="updateSensorDevice" parameterType="SensorDevice">
        update shcy_sensor_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="sensorimeiTypeName != null">sensorimei_type_name = #{sensorimeiTypeName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="imei != null">imei = #{imei},</if>
            <if test="value != null">value = #{value},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="waterLevelType != null">water_level_type = #{waterLevelType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSensorDeviceById" parameterType="Long">
        delete from shcy_sensor_device where id = #{id}
    </delete>

    <delete id="deleteSensorDeviceByIds" parameterType="String">
        delete from shcy_sensor_device where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSensorDeviceByImei" parameterType="String" resultMap="SensorDeviceResult">
        <include refid="selectSensorDeviceVo"/>
        where imei = #{imei} limit 1
    </select>
</mapper>