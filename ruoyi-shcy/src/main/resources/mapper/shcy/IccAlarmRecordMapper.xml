<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.IccAlarmRecordMapper">

    <resultMap type="IccAlarmRecord" id="IccAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="alarmDate"    column="alarm_date"    />
        <result property="alarmPosition"    column="alarm_position"    />
        <result property="alarmTypeName"    column="alarm_type_name"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="orgName"    column="org_name"    />
        <result property="alarmStat"    column="alarm_stat"    />
        <result property="handleStat"    column="handle_stat"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="handleDate"    column="handle_date"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="linkNames"    column="link_names"    />
        <result property="alarmGrade"    column="alarm_grade"    />
        <result property="nodeCode"    column="node_code"    />
        <result property="alarmPicture"    column="alarm_picture"    />
        <result property="taskWebName"    column="task_web_name"    />
        <result property="alarmWebUrl"    column="alarm_web_url"    />
        <result property="alarmAppUrl"    column="alarm_app_url"    />
        <result property="taskWebUrl"    column="task_web_url"    />
        <result property="taskAppUrl"    column="task_app_url"    />
        <result property="status"    column="status"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="responsiblePerson" column="responsible_person" />
        <result property="alarmRecordId" column="alarm_record_id" />
        <result property="carRecordId" column="car_record_id" />
    </resultMap>

    <sql id="selectIccAlarmRecordVo">
        select id, alarm_date, alarm_position, alarm_type_name, alarm_type, org_name, alarm_stat, handle_stat, handle_user, handle_date, alarm_code, link_names, alarm_grade, node_code, alarm_picture, task_web_name, alarm_web_url, alarm_app_url, task_web_url, task_app_url, status, license_plate, vehicle_type, responsible_person, alarm_record_id, car_record_id from icc_alarm_record
    </sql>

    <select id="selectIccAlarmRecordList" parameterType="IccAlarmRecord" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(alarm_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(alarm_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by alarm_date desc
    </select>

    <select id="selectIccAlarmRecordById" parameterType="Long" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertIccAlarmRecord" parameterType="IccAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into icc_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmDate != null">alarm_date,</if>
            <if test="alarmPosition != null">alarm_position,</if>
            <if test="alarmTypeName != null">alarm_type_name,</if>
            <if test="alarmType != null">alarm_type,</if>
            <if test="orgName != null">org_name,</if>
            <if test="alarmStat != null">alarm_stat,</if>
            <if test="handleStat != null">handle_stat,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="handleDate != null">handle_date,</if>
            <if test="alarmCode != null">alarm_code,</if>
            <if test="linkNames != null">link_names,</if>
            <if test="alarmGrade != null">alarm_grade,</if>
            <if test="nodeCode != null">node_code,</if>
            <if test="alarmPicture != null">alarm_picture,</if>
            <if test="taskWebName != null">task_web_name,</if>
            <if test="alarmWebUrl != null">alarm_web_url,</if>
            <if test="alarmAppUrl != null">alarm_app_url,</if>
            <if test="taskWebUrl != null">task_web_url,</if>
            <if test="taskAppUrl != null">task_app_url,</if>
            <if test="status != null">status,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="alarmRecordId != null">alarm_record_id,</if>
            <if test="carRecordId != null">car_record_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmDate != null">#{alarmDate},</if>
            <if test="alarmPosition != null">#{alarmPosition},</if>
            <if test="alarmTypeName != null">#{alarmTypeName},</if>
            <if test="alarmType != null">#{alarmType},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="alarmStat != null">#{alarmStat},</if>
            <if test="handleStat != null">#{handleStat},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="handleDate != null">#{handleDate},</if>
            <if test="alarmCode != null">#{alarmCode},</if>
            <if test="linkNames != null">#{linkNames},</if>
            <if test="alarmGrade != null">#{alarmGrade},</if>
            <if test="nodeCode != null">#{nodeCode},</if>
            <if test="alarmPicture != null">#{alarmPicture},</if>
            <if test="taskWebName != null">#{taskWebName},</if>
            <if test="alarmWebUrl != null">#{alarmWebUrl},</if>
            <if test="alarmAppUrl != null">#{alarmAppUrl},</if>
            <if test="taskWebUrl != null">#{taskWebUrl},</if>
            <if test="taskAppUrl != null">#{taskAppUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="alarmRecordId != null">#{alarmRecordId},</if>
            <if test="carRecordId != null">#{carRecordId},</if>
         </trim>
    </insert>

    <update id="updateIccAlarmRecord" parameterType="IccAlarmRecord">
        update icc_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmDate != null">alarm_date = #{alarmDate},</if>
            <if test="alarmPosition != null">alarm_position = #{alarmPosition},</if>
            <if test="alarmTypeName != null">alarm_type_name = #{alarmTypeName},</if>
            <if test="alarmType != null">alarm_type = #{alarmType},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="alarmStat != null">alarm_stat = #{alarmStat},</if>
            <if test="handleStat != null">handle_stat = #{handleStat},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="handleDate != null">handle_date = #{handleDate},</if>
            <if test="alarmCode != null">alarm_code = #{alarmCode},</if>
            <if test="linkNames != null">link_names = #{linkNames},</if>
            <if test="alarmGrade != null">alarm_grade = #{alarmGrade},</if>
            <if test="nodeCode != null">node_code = #{nodeCode},</if>
            <if test="alarmPicture != null">alarm_picture = #{alarmPicture},</if>
            <if test="taskWebName != null">task_web_name = #{taskWebName},</if>
            <if test="alarmWebUrl != null">alarm_web_url = #{alarmWebUrl},</if>
            <if test="alarmAppUrl != null">alarm_app_url = #{alarmAppUrl},</if>
            <if test="taskWebUrl != null">task_web_url = #{taskWebUrl},</if>
            <if test="taskAppUrl != null">task_app_url = #{taskAppUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="alarmRecordId != null">alarm_record_id = #{alarmRecordId},</if>
            <if test="carRecordId != null">car_record_id = #{carRecordId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIccAlarmRecordById" parameterType="Long">
        delete from icc_alarm_record where id = #{id}
    </delete>

    <delete id="deleteIccAlarmRecordByIds" parameterType="String">
        delete from icc_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectIccAlarmRecordUnprocessedList" parameterType="IccAlarmRecord" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where (status is null or status = '')
        <if test="alarmTypeName != null  and alarmTypeName != ''"> and alarm_type_name = #{alarmTypeName}</if>
        <if test="alarmPosition != null  and alarmPosition != ''"> and alarm_position = #{alarmPosition}</if>
    </select>

    <select id="selectIccAlarmRecordProcessingList" parameterType="IccAlarmRecord" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where status = '1'
        <if test="alarmTypeName != null  and alarmTypeName != ''"> and alarm_type_name = #{alarmTypeName}</if>
        <if test="alarmPosition != null  and alarmPosition != ''"> and alarm_position = #{alarmPosition}</if>
    </select>

    <select id="selectIccAlarmRecordProcessedList" parameterType="IccAlarmRecord" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where (status = '0' or status = '2')
        <if test="alarmTypeName != null  and alarmTypeName != ''"> and alarm_type_name = #{alarmTypeName}</if>
        <if test="alarmPosition != null  and alarmPosition != ''"> and alarm_position = #{alarmPosition}</if>
    </select>

    <update id="updateIccAlarmRecordStatus" parameterType="IccAlarmRecord">
        update icc_alarm_record set status = #{status} where id = #{id}
    </update>

    <select id="selectIccAlarmRecordUnprocessedAndProcessingList"  parameterType="IccAlarmRecord" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where (status is null or status = '' or status = '1')
        <if test="alarmTypeName != null  and alarmTypeName != ''"> and alarm_type_name = #{alarmTypeName}</if>
        <if test="alarmPosition != null  and alarmPosition != ''"> and alarm_position = #{alarmPosition}</if>
    </select>

    <select id="selectIccAlarmRecordByCarRecordId" parameterType="Long" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where car_record_id = #{carRecordId}
    </select>

    <select id="selectIccAlarmRecordByAlarmRecordId" parameterType="Long" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where alarm_record_id = #{alarmRecordId}
    </select>

    <select id="selectIccAlarmRecordByIds" parameterType="Long[]" resultMap="IccAlarmRecordResult">
        <include refid="selectIccAlarmRecordVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
