<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyRoadMapper">
    
    <resultMap type="ShcyRoad" id="ShcyRoadResult">
        <result property="id"    column="id"    />
        <result property="road"    column="road"    />
        <result property="roadUp"    column="road_up"    />
        <result property="roadDown"    column="road_down"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectShcyRoadVo">
        select id, road, road_up, road_down, create_by, create_time, update_time from shcy_road
    </sql>

    <select id="selectShcyRoadList" parameterType="ShcyRoad" resultMap="ShcyRoadResult">
        <include refid="selectShcyRoadVo"/>
        <where>  
            <if test="road != null  and road != ''"> and road = #{road}</if>
            <if test="roadUp != null  and roadUp != ''"> and road_up = #{roadUp}</if>
            <if test="roadDown != null  and roadDown != ''"> and road_down = #{roadDown}</if>
        </where>
    </select>
    
    <select id="selectShcyRoadById" parameterType="Long" resultMap="ShcyRoadResult">
        <include refid="selectShcyRoadVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertShcyRoad" parameterType="ShcyRoad" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="road != null">road,</if>
            <if test="roadUp != null">road_up,</if>
            <if test="roadDown != null">road_down,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="road != null">#{road},</if>
            <if test="roadUp != null">#{roadUp},</if>
            <if test="roadDown != null">#{roadDown},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateShcyRoad" parameterType="ShcyRoad">
        update shcy_road
        <trim prefix="SET" suffixOverrides=",">
            <if test="road != null">road = #{road},</if>
            <if test="roadUp != null">road_up = #{roadUp},</if>
            <if test="roadDown != null">road_down = #{roadDown},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyRoadById" parameterType="Long">
        delete from shcy_road where id = #{id}
    </delete>

    <delete id="deleteShcyRoadByIds" parameterType="String">
        delete from shcy_road where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>