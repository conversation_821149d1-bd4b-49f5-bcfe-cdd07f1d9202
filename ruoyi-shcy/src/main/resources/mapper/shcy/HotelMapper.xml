<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.HotelMapper">

    <resultMap type="Hotel" id="HotelResult">
        <result property="id"    column="id"    />
        <result property="committee"    column="committee"    />
        <result property="committeeId"    column="committee_id"    />
        <result property="residential"    column="residential"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="name"    column="name"    />
        <result property="licenseName"    column="license_name"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="contactPhone"    column="contact_phone"    />
    </resultMap>

    <sql id="selectHotelVo">
        select id, committee, committee_id, residential, residential_id, longitude, latitude, create_by, create_time, update_time, type, coordinate, name,license_name, legal_person, contact_phone from shcy_hotel
    </sql>

    <select id="selectHotelList" parameterType="Hotel" resultMap="HotelResult">
        <include refid="selectHotelVo"/>
        <where>
            <if test="committee != null  and committee != ''"> and committee = #{committee}</if>
            <if test="committeeId != null "> and committee_id = #{committeeId}</if>
            <if test="residential != null  and residential != ''"> and residential = #{residential}</if>
            <if test="residentialId != null "> and residential_id = #{residentialId}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="licenseName != null  and licenseName != ''"> and license_name like concat('%', #{licenseName}, '%')</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
        </where>
    </select>

    <select id="selectHotelById" parameterType="Long" resultMap="HotelResult">
        <include refid="selectHotelVo"/>
        where id = #{id}
    </select>

    <insert id="insertHotel" parameterType="Hotel" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_hotel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="committee != null">committee,</if>
            <if test="committeeId != null">committee_id,</if>
            <if test="residential != null">residential,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="name != null">name,</if>
            <if test="licenseName != null">license_name,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="committee != null">#{committee},</if>
            <if test="committeeId != null">#{committeeId},</if>
            <if test="residential != null">#{residential},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="name != null">#{name},</if>
            <if test="licenseName != null">#{licenseName},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
        </trim>
    </insert>

    <update id="updateHotel" parameterType="Hotel">
        update shcy_hotel
        <trim prefix="SET" suffixOverrides=",">
            <if test="committee != null">committee = #{committee},</if>
            <if test="committeeId != null">committee_id = #{committeeId},</if>
            <if test="residential != null">residential = #{residential},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="name != null">name = #{name},</if>
            <if test="licenseName != null">license_name = #{licenseName},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHotelById" parameterType="Long">
        delete from shcy_hotel where id = #{id}
    </delete>

    <delete id="deleteHotelByIds" parameterType="String">
        delete from shcy_hotel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
