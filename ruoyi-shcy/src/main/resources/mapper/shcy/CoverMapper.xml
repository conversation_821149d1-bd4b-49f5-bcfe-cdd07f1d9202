<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.CoverMapper">

    <resultMap type="Cover" id="CoverResult">
        <result property="id"    column="id"    />
        <result property="fieldStationNo"    column="field_station_no"    />
        <result property="pipelineProperty"    column="pipeline_property"    />
        <result property="burialDepth"    column="burial_depth"    />
        <result property="pipelineMaterial"    column="pipeline_material"    />
        <result property="road"    column="road"    />
        <result property="wellDepth"    column="well_depth"    />
        <result property="coverMaterial"    column="cover_material"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="wsgc" column="wsgc" />
    </resultMap>

    <sql id="selectCoverVo">
        select id, field_station_no, pipeline_property, burial_depth, pipeline_material, road, well_depth, cover_material, type, coordinate, create_time, create_by, update_time,wsgc from shcy_cover
    </sql>

    <select id="selectCoverList" parameterType="Cover" resultMap="CoverResult">
        <include refid="selectCoverVo"/>
        <where>
            <if test="fieldStationNo != null  and fieldStationNo != ''"> and field_station_no = #{fieldStationNo}</if>
            <if test="pipelineProperty != null  and pipelineProperty != ''"> and pipeline_property = #{pipelineProperty}</if>
            <if test="burialDepth != null  and burialDepth != ''"> and burial_depth = #{burialDepth}</if>
            <if test="pipelineMaterial != null  and pipelineMaterial != ''"> and pipeline_material = #{pipelineMaterial}</if>
            <if test="road != null  and road != ''"> and road = #{road}</if>
            <if test="wellDepth != null  and wellDepth != ''"> and well_depth = #{wellDepth}</if>
            <if test="coverMaterial != null  and coverMaterial != ''"> and cover_material = #{coverMaterial}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and coordinate = #{coordinate}</if>
            <if test="wsgc != null  and wsgc != ''"> and wsgc = #{wsgc}</if>
        </where>
    </select>

    <select id="selectCoverById" parameterType="Long" resultMap="CoverResult">
        <include refid="selectCoverVo"/>
        where id = #{id}
    </select>

    <insert id="insertCover" parameterType="Cover" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_cover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fieldStationNo != null">field_station_no,</if>
            <if test="pipelineProperty != null">pipeline_property,</if>
            <if test="burialDepth != null">burial_depth,</if>
            <if test="pipelineMaterial != null">pipeline_material,</if>
            <if test="road != null">road,</if>
            <if test="wellDepth != null">well_depth,</if>
            <if test="coverMaterial != null">cover_material,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="wsgc != null">wsgc,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fieldStationNo != null">#{fieldStationNo},</if>
            <if test="pipelineProperty != null">#{pipelineProperty},</if>
            <if test="burialDepth != null">#{burialDepth},</if>
            <if test="pipelineMaterial != null">#{pipelineMaterial},</if>
            <if test="road != null">#{road},</if>
            <if test="wellDepth != null">#{wellDepth},</if>
            <if test="coverMaterial != null">#{coverMaterial},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="wsgc != null">#{wsgc},</if>
         </trim>
    </insert>

    <update id="updateCover" parameterType="Cover">
        update shcy_cover
        <trim prefix="SET" suffixOverrides=",">
            <if test="fieldStationNo != null">field_station_no = #{fieldStationNo},</if>
            <if test="pipelineProperty != null">pipeline_property = #{pipelineProperty},</if>
            <if test="burialDepth != null">burial_depth = #{burialDepth},</if>
            <if test="pipelineMaterial != null">pipeline_material = #{pipelineMaterial},</if>
            <if test="road != null">road = #{road},</if>
            <if test="wellDepth != null">well_depth = #{wellDepth},</if>
            <if test="coverMaterial != null">cover_material = #{coverMaterial},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="wsgc != null">wsgc = #{wsgc},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCoverById" parameterType="Long">
        delete from shcy_cover where id = #{id}
    </delete>

    <delete id="deleteCoverByIds" parameterType="String">
        delete from shcy_cover where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
