<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.HistoryAlarmRecordMapper">
    
    <resultMap type="HistoryAlarmRecord" id="HistoryAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="deviceImei"    column="device_imei"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="liquidLevelDeviceId"    column="liquid_level_device_id"    />
    </resultMap>

    <sql id="selectHistoryAlarmRecordVo">
        select id, device_imei, alarm_time, liquid_level_device_id from shcy_history_alarm_record
    </sql>

    <select id="selectHistoryAlarmRecordList" parameterType="HistoryAlarmRecord" resultMap="HistoryAlarmRecordResult">
        <include refid="selectHistoryAlarmRecordVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectHistoryAlarmRecordById" parameterType="Long" resultMap="HistoryAlarmRecordResult">
        <include refid="selectHistoryAlarmRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHistoryAlarmRecord" parameterType="HistoryAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_history_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deviceImei != null">device_imei,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="liquidLevelDeviceId != null">liquid_level_device_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deviceImei != null">#{deviceImei},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="liquidLevelDeviceId != null">#{liquidLevelDeviceId},</if>
         </trim>
    </insert>

    <update id="updateHistoryAlarmRecord" parameterType="HistoryAlarmRecord">
        update shcy_history_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceImei != null">device_imei = #{deviceImei},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="liquidLevelDeviceId != null">liquid_level_device_id = #{liquidLevelDeviceId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHistoryAlarmRecordById" parameterType="Long">
        delete from shcy_history_alarm_record where id = #{id}
    </delete>

    <delete id="deleteHistoryAlarmRecordByIds" parameterType="String">
        delete from shcy_history_alarm_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHistoryAlarmRecordCount" resultType="com.ruoyi.shcy.dto.HistoryAlarmRecordCountDTO">
        SELECT liquid_level_device_id AS liquidLevelDeviceId, COUNT(*) AS count
        FROM shcy_history_alarm_record
        GROUP BY liquid_level_device_id
    </select>
</mapper>