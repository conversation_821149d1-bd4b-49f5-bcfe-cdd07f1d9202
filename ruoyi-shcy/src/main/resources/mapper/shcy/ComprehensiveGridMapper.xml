<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ComprehensiveGridMapper">
    
    <resultMap type="ComprehensiveGrid" id="ComprehensiveGridResult">
        <result property="id"    column="id"    />
        <result property="gridName"    column="grid_name"    />
        <result property="gridNumber"    column="grid_number"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="fillColor"    column="fill_color"    />
        <result property="outlineColor"    column="outline_color"    />
        <result property="coverageArea"    column="coverage_area"    />
        <result property="residentialAreas"    column="residential_areas"    />
        <result property="areaSize"    column="area_size"    />
        <result property="population"    column="population"    />
        <result property="marketEntities"    column="market_entities"    />
        <result property="newEmployment"    column="new_employment"    />
    </resultMap>

    <sql id="selectComprehensiveGridVo">
        select id, grid_name, grid_number, type, coordinate, fill_color, outline_color, coverage_area, residential_areas, area_size, population, market_entities, new_employment from shcy_comprehensive_grid
    </sql>

    <select id="selectComprehensiveGridList" parameterType="ComprehensiveGrid" resultMap="ComprehensiveGridResult">
        <include refid="selectComprehensiveGridVo"/>
        <where>  
            <if test="gridName != null  and gridName != ''"> and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridNumber != null  and gridNumber != ''"> and grid_number = #{gridNumber}</if>
        </where>
    </select>
    
    <select id="selectComprehensiveGridById" parameterType="Long" resultMap="ComprehensiveGridResult">
        <include refid="selectComprehensiveGridVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertComprehensiveGrid" parameterType="ComprehensiveGrid" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_comprehensive_grid
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gridName != null">grid_name,</if>
            <if test="gridNumber != null">grid_number,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="fillColor != null">fill_color,</if>
            <if test="outlineColor != null">outline_color,</if>
            <if test="coverageArea != null">coverage_area,</if>
            <if test="residentialAreas != null">residential_areas,</if>
            <if test="areaSize != null">area_size,</if>
            <if test="population != null">population,</if>
            <if test="marketEntities != null">market_entities,</if>
            <if test="newEmployment != null">new_employment,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gridName != null">#{gridName},</if>
            <if test="gridNumber != null">#{gridNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="fillColor != null">#{fillColor},</if>
            <if test="outlineColor != null">#{outlineColor},</if>
            <if test="coverageArea != null">#{coverageArea},</if>
            <if test="residentialAreas != null">#{residentialAreas},</if>
            <if test="areaSize != null">#{areaSize},</if>
            <if test="population != null">#{population},</if>
            <if test="marketEntities != null">#{marketEntities},</if>
            <if test="newEmployment != null">#{newEmployment},</if>
         </trim>
    </insert>

    <update id="updateComprehensiveGrid" parameterType="ComprehensiveGrid">
        update shcy_comprehensive_grid
        <trim prefix="SET" suffixOverrides=",">
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridNumber != null">grid_number = #{gridNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="fillColor != null">fill_color = #{fillColor},</if>
            <if test="outlineColor != null">outline_color = #{outlineColor},</if>
            <if test="coverageArea != null">coverage_area = #{coverageArea},</if>
            <if test="residentialAreas != null">residential_areas = #{residentialAreas},</if>
            <if test="areaSize != null">area_size = #{areaSize},</if>
            <if test="population != null">population = #{population},</if>
            <if test="marketEntities != null">market_entities = #{marketEntities},</if>
            <if test="newEmployment != null">new_employment = #{newEmployment},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteComprehensiveGridById" parameterType="Long">
        delete from shcy_comprehensive_grid where id = #{id}
    </delete>

    <delete id="deleteComprehensiveGridByIds" parameterType="String">
        delete from shcy_comprehensive_grid where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>