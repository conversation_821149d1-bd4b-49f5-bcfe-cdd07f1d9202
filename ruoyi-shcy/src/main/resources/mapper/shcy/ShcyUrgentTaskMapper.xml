<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ShcyUrgentTaskMapper">

    <resultMap type="ShcyUrgentTask" id="ShcyUrgentTaskResult">
        <result property="id"    column="id"    />
        <result property="eventNo"    column="event_no"    />
        <result property="alarmLocation"    column="alarm_location"    />
        <result property="region"    column="region"    />
        <result property="sandbagPhoto"    column="sandbag_photo"    />
        <result property="dutyPersonnelPhoto"    column="duty_personnel_photo"    />
        <result property="otherHazardStatus"    column="other_hazard_status"    />
        <result property="otherHazard"    column="other_hazard"    />
        <result property="createTime"    column="create_time"    />
        <result property="disposalDeadline"    column="disposal_deadline"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="caseFinishTime"    column="case_finish_time"    />
        <result property="dealInTimeState"    column="deal_in_time_state"    />
        <result property="caseDealBy"    column="case_deal_by"    />
    </resultMap>

    <sql id="selectShcyUrgentTaskVo">
        select id, event_no, alarm_location,case_deal_by, region, sandbag_photo, duty_personnel_photo, other_hazard_status, other_hazard, create_time, disposal_deadline, circulation_state, case_finish_time, deal_in_time_state from shcy_urgent_task
    </sql>

    <select id="selectShcyUrgentTaskList" parameterType="ShcyUrgentTask" resultMap="ShcyUrgentTaskResult">
        <include refid="selectShcyUrgentTaskVo"/>
        <where>
            <if test="eventNo != null  and eventNo != ''"> and event_no like concat('%', #{eventNo}, '%')</if>
            <if test="alarmLocation != null  and alarmLocation != ''"> and alarm_location like concat('%', #{alarmLocation}, '%')</if>
            <if test="caseDealBy != null  and caseDealBy != ''"> and case_deal_by like concat('%', #{caseDealBy}, '%')</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or alarm_location like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

    <select id="selectShcyUrgentTaskById" parameterType="Long" resultMap="ShcyUrgentTaskResult">
        <include refid="selectShcyUrgentTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertShcyUrgentTask" parameterType="ShcyUrgentTask" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_urgent_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventNo != null">event_no,</if>
            <if test="alarmLocation != null">alarm_location,</if>
            <if test="region != null">region,</if>
            <if test="sandbagPhoto != null">sandbag_photo,</if>
            <if test="dutyPersonnelPhoto != null">duty_personnel_photo,</if>
            <if test="otherHazardStatus != null">other_hazard_status,</if>
            <if test="otherHazard != null">other_hazard,</if>
            <if test="createTime != null">create_time,</if>
            <if test="disposalDeadline != null">disposal_deadline,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="caseFinishTime != null">case_finish_time,</if>
            <if test="dealInTimeState != null">deal_in_time_state,</if>
            <if test="caseDealBy != null">case_deal_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventNo != null">#{eventNo},</if>
            <if test="alarmLocation != null">#{alarmLocation},</if>
            <if test="region != null">#{region},</if>
            <if test="sandbagPhoto != null">#{sandbagPhoto},</if>
            <if test="dutyPersonnelPhoto != null">#{dutyPersonnelPhoto},</if>
            <if test="otherHazardStatus != null">#{otherHazardStatus},</if>
            <if test="otherHazard != null">#{otherHazard},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="disposalDeadline != null">#{disposalDeadline},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="caseFinishTime != null">#{caseFinishTime},</if>
            <if test="dealInTimeState != null">#{dealInTimeState},</if>
            <if test="caseDealBy != null">#{caseDealBy},</if>
         </trim>
    </insert>

    <update id="updateShcyUrgentTask" parameterType="ShcyUrgentTask">
        update shcy_urgent_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventNo != null">event_no = #{eventNo},</if>
            <if test="alarmLocation != null">alarm_location = #{alarmLocation},</if>
            <if test="region != null">region = #{region},</if>
            <if test="sandbagPhoto != null">sandbag_photo = #{sandbagPhoto},</if>
            <if test="dutyPersonnelPhoto != null">duty_personnel_photo = #{dutyPersonnelPhoto},</if>
            <if test="otherHazardStatus != null">other_hazard_status = #{otherHazardStatus},</if>
            <if test="otherHazard != null">other_hazard = #{otherHazard},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="disposalDeadline != null">disposal_deadline = #{disposalDeadline},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="caseFinishTime != null">case_finish_time = #{caseFinishTime},</if>
            <if test="dealInTimeState != null">deal_in_time_state = #{dealInTimeState},</if>
            <if test="caseDealBy != null">case_deal_by = #{caseDealBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShcyUrgentTaskById" parameterType="Long">
        delete from shcy_urgent_task where id = #{id}
    </delete>

    <delete id="deleteShcyUrgentTaskByIds" parameterType="String">
        delete from shcy_urgent_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="ShcyUrgentTask" resultType="long">
        select count(1) from shcy_urgent_task
        <where>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (event_no like concat('%', #{params.keyword}, '%')
                or alarm_location like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>

</mapper>
