<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.IccDeviceMapper">
    
    <resultMap type="IccDevice" id="IccDeviceResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="deviceStatus"    column="device_status"    />
    </resultMap>

    <sql id="selectIccDeviceVo">
        select id, device_name, type, coordinate, device_status from icc_device
    </sql>

    <select id="selectIccDeviceList" parameterType="IccDevice" resultMap="IccDeviceResult">
        <include refid="selectIccDeviceVo"/>
        <where>  
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
        </where>
    </select>
    
    <select id="selectIccDeviceById" parameterType="Long" resultMap="IccDeviceResult">
        <include refid="selectIccDeviceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertIccDevice" parameterType="IccDevice">
        insert into icc_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="deviceStatus != null">device_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
         </trim>
    </insert>

    <update id="updateIccDevice" parameterType="IccDevice">
        update icc_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIccDeviceById" parameterType="Long">
        delete from icc_device where id = #{id}
    </delete>

    <delete id="deleteIccDeviceByIds" parameterType="String">
        delete from icc_device where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectHjzzAddressList" resultType="java.lang.String">
        select device_name from icc_device
    </select>
</mapper>