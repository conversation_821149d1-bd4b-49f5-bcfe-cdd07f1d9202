<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.DuplicateClassifyMapper">
    
    <resultMap type="DuplicateClassify" id="DuplicateClassifyResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectDuplicateClassifyVo">
        select id, name, parent_id, order_num from shcy_duplicate_classify
    </sql>

    <select id="selectDuplicateClassifyList" parameterType="DuplicateClassify" resultMap="DuplicateClassifyResult">
        <include refid="selectDuplicateClassifyVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by order_num asc
    </select>
    
    <select id="selectDuplicateClassifyById" parameterType="Long" resultMap="DuplicateClassifyResult">
        <include refid="selectDuplicateClassifyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDuplicateClassify" parameterType="DuplicateClassify" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_duplicate_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateDuplicateClassify" parameterType="DuplicateClassify">
        update shcy_duplicate_classify
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDuplicateClassifyById" parameterType="Long">
        delete from shcy_duplicate_classify where id = #{id}
    </delete>

    <delete id="deleteDuplicateClassifyByIds" parameterType="String">
        delete from shcy_duplicate_classify where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>