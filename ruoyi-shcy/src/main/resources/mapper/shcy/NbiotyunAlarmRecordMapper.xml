<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.NbiotyunAlarmRecordMapper">
    <sql id="Base_Column_List">
        id,
        alarm_time,
        device_imei,
        dept_name,
        device_type_name,
        device_state,
        device_version_name,
        device_event_name,
        device_attr_value,
        handle_time,
        `handler`,
        alarm_reason,
        detailed_location,
        remark,
        create_time,
        update_time,
        `status`,
        responsible_person
    </sql>
    <resultMap type="NbiotyunAlarmRecord" id="NbiotyunAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="deviceImei"    column="device_imei"    />
        <result property="deptName"    column="dept_name"    />
        <result property="deviceTypeName"    column="device_type_name"    />
        <result property="deviceState"    column="device_state"    />
        <result property="deviceVersionName"    column="device_version_name"    />
        <result property="deviceEventName"    column="device_event_name"    />
        <result property="deviceAttrValue"    column="device_attr_value"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handler"    column="handler"    />
        <result property="alarmReason"    column="alarm_reason"    />
        <result property="detailedLocation"    column="detailed_location"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="responsiblePerson" column="responsible_person" />
    </resultMap>

    <sql id="selectNbiotyunAlarmRecordVo">
        select id, alarm_time, device_imei, dept_name, device_type_name, device_state, device_version_name, device_event_name, device_attr_value, handle_time, handler, alarm_reason, detailed_location, remark, create_time, update_time, status, responsible_person from nbiotyun_alarm_record
    </sql>

    <select id="selectNbiotyunAlarmRecordList" parameterType="NbiotyunAlarmRecord" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        <where>
            <if test="deviceImei != null  and deviceImei != ''"> and device_imei = #{deviceImei}</if>
            <if test="detailedLocation != null  and detailedLocation != ''"> and detailed_location = #{detailedLocation}</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''"> and device_type_name like concat('%', #{deviceTypeName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectNbiotyunAlarmRecordUnprocessedList" parameterType="NbiotyunAlarmRecord" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        where (status is null or status = '')
        <if test="detailedLocation != null  and detailedLocation != ''"> and detailed_location = #{detailedLocation}</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectNbiotyunAlarmRecordProcessingList" parameterType="NbiotyunAlarmRecord" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        where status = '1'
        <if test="detailedLocation != null  and detailedLocation != ''"> and detailed_location = #{detailedLocation}</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectNbiotyunAlarmRecordProcessedList" parameterType="NbiotyunAlarmRecord" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        where (status = '0' or status = '2')
        <if test="detailedLocation != null  and detailedLocation != ''"> and detailed_location = #{detailedLocation}</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(alarm_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectNbiotyunAlarmRecordById" parameterType="Long" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectNbiotyunAlarmRecordGroupByList"  parameterType="NbiotyunAlarmRecord" resultMap="NbiotyunAlarmRecordResult">
        SELECT
            id, alarm_time, device_imei, dept_name, device_type_name, device_state, device_version_name, device_event_name, device_attr_value, handle_time, handler, alarm_reason, detailed_location, remark, create_time, update_time, status, responsible_person
        FROM
            `nbiotyun_alarm_record`
        WHERE
                id IN ( SELECT SUBSTRING_INDEX( group_concat( id ORDER BY `alarm_time` DESC ), ',', 1 ) FROM `nbiotyun_alarm_record` GROUP BY device_imei )
        ORDER BY
            `alarm_time` DESC
    </select>

    <select id="getAlarmRecordByImei" parameterType="String" resultMap="NbiotyunAlarmRecordResult">
        <include refid="selectNbiotyunAlarmRecordVo"/>
        WHERE device_imei = #{deviceImei}
        ORDER BY alarm_time DESC
        LIMIT 1
    </select>

    <insert id="insertNbiotyunAlarmRecord" parameterType="NbiotyunAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into nbiotyun_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="deviceImei != null">device_imei,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="deviceTypeName != null">device_type_name,</if>
            <if test="deviceState != null">device_state,</if>
            <if test="deviceVersionName != null">device_version_name,</if>
            <if test="deviceEventName != null">device_event_name,</if>
            <if test="deviceAttrValue != null">device_attr_value,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handler != null">handler,</if>
            <if test="alarmReason != null">alarm_reason,</if>
            <if test="detailedLocation != null">detailed_location,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="deviceImei != null">#{deviceImei},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="deviceTypeName != null">#{deviceTypeName},</if>
            <if test="deviceState != null">#{deviceState},</if>
            <if test="deviceVersionName != null">#{deviceVersionName},</if>
            <if test="deviceEventName != null">#{deviceEventName},</if>
            <if test="deviceAttrValue != null">#{deviceAttrValue},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handler != null">#{handler},</if>
            <if test="alarmReason != null">#{alarmReason},</if>
            <if test="detailedLocation != null">#{detailedLocation},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
         </trim>
    </insert>

    <update id="updateNbiotyunAlarmRecord" parameterType="NbiotyunAlarmRecord">
        update nbiotyun_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="deviceImei != null">device_imei = #{deviceImei},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="deviceTypeName != null">device_type_name = #{deviceTypeName},</if>
            <if test="deviceState != null">device_state = #{deviceState},</if>
            <if test="deviceVersionName != null">device_version_name = #{deviceVersionName},</if>
            <if test="deviceEventName != null">device_event_name = #{deviceEventName},</if>
            <if test="deviceAttrValue != null">device_attr_value = #{deviceAttrValue},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="alarmReason != null">alarm_reason = #{alarmReason},</if>
            <if test="detailedLocation != null">detailed_location = #{detailedLocation},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNbiotyunAlarmRecordById" parameterType="Long">
        delete from nbiotyun_alarm_record where id = #{id}
    </delete>

    <delete id="deleteNbiotyunAlarmRecordByIds" parameterType="String">
        delete from nbiotyun_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateNbiotyunAlarmRecordStatus" parameterType="NbiotyunAlarmRecord">
        update nbiotyun_alarm_record set status = #{status} where id = #{id}
    </update>
</mapper>
