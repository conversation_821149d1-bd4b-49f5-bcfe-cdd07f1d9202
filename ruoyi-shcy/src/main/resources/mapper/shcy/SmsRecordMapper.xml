<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SmsRecordMapper">
    
    <resultMap type="SmsRecord" id="SmsRecordResult">
        <result property="smsId"    column="sms_id"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="smsContent"    column="sms_content"    />
        <result property="templateCode"    column="template_code"    />
        <result property="sendTime"    column="send_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSmsRecordVo">
        select sms_id, phone_number, sms_content, template_code, send_time, status, create_by, create_time, update_by, update_time, remark from shcy_sms_record
    </sql>

    <select id="selectSmsRecordList" parameterType="SmsRecord" resultMap="SmsRecordResult">
        <include refid="selectSmsRecordVo"/>
        <where>  
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode}</if>
        </where>
    </select>
    
    <select id="selectSmsRecordBySmsId" parameterType="Long" resultMap="SmsRecordResult">
        <include refid="selectSmsRecordVo"/>
        where sms_id = #{smsId}
    </select>
        
    <insert id="insertSmsRecord" parameterType="SmsRecord" useGeneratedKeys="true" keyProperty="smsId">
        insert into shcy_sms_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phoneNumber != null">phone_number,</if>
            <if test="smsContent != null">sms_content,</if>
            <if test="templateCode != null">template_code,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="smsContent != null">#{smsContent},</if>
            <if test="templateCode != null">#{templateCode},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSmsRecord" parameterType="SmsRecord">
        update shcy_sms_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="smsContent != null">sms_content = #{smsContent},</if>
            <if test="templateCode != null">template_code = #{templateCode},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sms_id = #{smsId}
    </update>

    <delete id="deleteSmsRecordBySmsId" parameterType="Long">
        delete from shcy_sms_record where sms_id = #{smsId}
    </delete>

    <delete id="deleteSmsRecordBySmsIds" parameterType="String">
        delete from shcy_sms_record where sms_id in 
        <foreach item="smsId" collection="array" open="(" separator="," close=")">
            #{smsId}
        </foreach>
    </delete>
</mapper>