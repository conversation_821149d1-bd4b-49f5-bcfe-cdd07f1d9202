<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.PumpStationMapper">
    
    <resultMap type="PumpStation" id="PumpStationResult">
        <result property="id"    column="id"    />
        <result property="pumpStationName"    column="pump_station_name"    />
        <result property="pumpStationType"    column="pump_station_type"    />
        <result property="lotNumber"    column="lot_number"    />
        <result property="parentLotNumber"    column="parent_lot_number"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="fillColor"    column="fill_color"    />
        <result property="outlineColor"    column="outline_color"    />
    </resultMap>

    <sql id="selectPumpStationVo">
        select id, pump_station_name, pump_station_type, lot_number, parent_lot_number, type, coordinate, fill_color, outline_color from shcy_pump_station
    </sql>

    <select id="selectPumpStationList" parameterType="PumpStation" resultMap="PumpStationResult">
        <include refid="selectPumpStationVo"/>
        <where>  
            <if test="pumpStationName != null  and pumpStationName != ''"> and pump_station_name like concat('%', #{pumpStationName}, '%')</if>
        </where>
    </select>
    
    <select id="selectPumpStationById" parameterType="Long" resultMap="PumpStationResult">
        <include refid="selectPumpStationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPumpStation" parameterType="PumpStation" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_pump_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pumpStationName != null">pump_station_name,</if>
            <if test="pumpStationType != null">pump_station_type,</if>
            <if test="lotNumber != null">lot_number,</if>
            <if test="parentLotNumber != null">parent_lot_number,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="fillColor != null">fill_color,</if>
            <if test="outlineColor != null">outline_color,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pumpStationName != null">#{pumpStationName},</if>
            <if test="pumpStationType != null">#{pumpStationType},</if>
            <if test="lotNumber != null">#{lotNumber},</if>
            <if test="parentLotNumber != null">#{parentLotNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="fillColor != null">#{fillColor},</if>
            <if test="outlineColor != null">#{outlineColor},</if>
         </trim>
    </insert>

    <update id="updatePumpStation" parameterType="PumpStation">
        update shcy_pump_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="pumpStationName != null">pump_station_name = #{pumpStationName},</if>
            <if test="pumpStationType != null">pump_station_type = #{pumpStationType},</if>
            <if test="lotNumber != null">lot_number = #{lotNumber},</if>
            <if test="parentLotNumber != null">parent_lot_number = #{parentLotNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="fillColor != null">fill_color = #{fillColor},</if>
            <if test="outlineColor != null">outline_color = #{outlineColor},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePumpStationById" parameterType="Long">
        delete from shcy_pump_station where id = #{id}
    </delete>

    <delete id="deletePumpStationByIds" parameterType="String">
        delete from shcy_pump_station where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPumpStationListByIds" parameterType="String" resultMap="PumpStationResult">
        <include refid="selectPumpStationVo"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>