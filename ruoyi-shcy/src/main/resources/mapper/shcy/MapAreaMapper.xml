<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.MapAreaMapper">
    
    <resultMap type="MapArea" id="MapAreaResult">
        <result property="areaId"    column="area_id"    />
        <result property="areaName"    column="area_name"    />
        <result property="areaRange"    column="area_range"    />
    </resultMap>

    <sql id="selectMapAreaVo">
        select area_id, area_name, area_range from shcy_map_area
    </sql>

    <select id="selectMapAreaList" parameterType="MapArea" resultMap="MapAreaResult">
        <include refid="selectMapAreaVo"/>
        <where>  
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
        </where>
    </select>
    
    <select id="selectMapAreaByAreaId" parameterType="Long" resultMap="MapAreaResult">
        <include refid="selectMapAreaVo"/>
        where area_id = #{areaId}
    </select>
        
    <insert id="insertMapArea" parameterType="MapArea" useGeneratedKeys="true" keyProperty="areaId">
        insert into shcy_map_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaName != null">area_name,</if>
            <if test="areaRange != null">area_range,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaName != null">#{areaName},</if>
            <if test="areaRange != null">#{areaRange},</if>
         </trim>
    </insert>

    <update id="updateMapArea" parameterType="MapArea">
        update shcy_map_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="areaRange != null">area_range = #{areaRange},</if>
        </trim>
        where area_id = #{areaId}
    </update>

    <delete id="deleteMapAreaByAreaId" parameterType="Long">
        delete from shcy_map_area where area_id = #{areaId}
    </delete>

    <delete id="deleteMapAreaByAreaIds" parameterType="String">
        delete from shcy_map_area where area_id in 
        <foreach item="areaId" collection="array" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </delete>
</mapper>