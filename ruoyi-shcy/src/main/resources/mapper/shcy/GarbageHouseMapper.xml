<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.GarbageHouseMapper">

    <resultMap type="GarbageHouse" id="GarbageHouseResult">
        <result property="id"    column="id"    />
        <result property="street"    column="street"    />
        <result property="committee"    column="committee"    />
        <result property="committeeId"    column="committee_id"    />
        <result property="residential"    column="residential"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="siteAddress"    column="site_address"    />
        <result property="village"    column="village"    />
        <result property="garbageType"    column="garbage_type"    />
        <result property="garbageOpenTime"    column="garbage_open_time"    />
        <result property="residentialHealthDirector"    column="residential_health_director"    />
        <result property="healthDirectorPhone"    column="health_director_phone"    />
        <result property="residentialSecretary"    column="residential_secretary"    />
        <result property="secretaryPhone"    column="secretary_phone"    />
        <result property="residentialCleaning"    column="residential_cleaning"    />
        <result property="cleaningPhone"    column="cleaning_phone"    />
        <result property="residentialManage"    column="residential_manage"    />
        <result property="managePhone"    column="manage_phone"    />
        <result property="longitudeLatitude"    column="longitude_latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="name"    column="name"    />
    </resultMap>

    <sql id="selectGarbageHouseVo">
        select id, street, committee, committee_id, residential, residential_id, site_address, village, garbage_type, garbage_open_time, residential_health_director, health_director_phone, residential_secretary, secretary_phone, residential_cleaning, cleaning_phone, residential_manage, manage_phone, longitude_latitude, longitude, latitude, create_time, create_by, update_time, type, coordinate, name from shcy_garbage_house
    </sql>

<!--    <select id="selectGarbageHouseList" parameterType="GarbageHouse" resultMap="GarbageHouseResult">-->
    <select id="selectGarbageHouseList" parameterType="GarbageHouse" resultType="com.ruoyi.shcy.domain.GarbageHouse">
        SELECT
        sgh.id as id,
        sgh.street as street,
        sc.committee_name as committee,
        sc.id as committeeId,
        sr.residential as residential,
        sr.id as residentialId,
        sgh.site_address as siteAddress,
        sgh.village as village,
        sgh.garbage_type as garbageType,
        sgh.garbage_open_time as garbageOpenTime,
        sgh.residential_health_director as residentialHealthDirector,
        sgh.health_director_phone as healthDirectorPhone,
        sgh.residential_secretary as residentialSecretary,
        sgh.secretary_phone as secretaryPhone,
        sgh.residential_cleaning as residentialCleaning,
        sgh.cleaning_phone as cleaningPhone,
        sgh.residential_manage as residentialManage,
        sgh.manage_phone as managePhone,
        sgh.longitude_latitude as longitudeLatitude,
        sgh.longitude as longitude,
        sgh.latitude as latitude,
        sgh.create_time as createTime,
        sgh.create_by as createBy,
        sgh.update_time as updateTime,
        sgh.type  as type,
        sgh.coordinate as coordinate,
        sgh.name as name
        FROM
        shcy_garbage_house sgh left join  shcy_committee sc on sgh.committee_id =sc.id
        left join shcy_residentials sr on sgh.residential_id = sr.id
        <where>
            <if test="street != null  and street != ''"> and  sgh.street = #{street}</if>
            <if test="committee != null  and committee != ''"> and  sc.committee_name = #{committee}</if>
            <if test="committeeId != null "> and sc.id = #{committeeId}</if>
            <if test="residential != null  and residential != ''"> and sr.residential = #{residential}</if>
            <if test="residentialId != null "> and sr.id = #{residentialId}</if>
            <if test="siteAddress != null  and siteAddress != ''"> and sgh.site_address = #{siteAddress}</if>
            <if test="village != null  and village != ''"> and sgh.village = #{village}</if>
            <if test="garbageType != null  and garbageType != ''"> and sgh.garbage_type = #{garbageType}</if>
            <if test="garbageOpenTime != null  and garbageOpenTime != ''"> and sgh.garbage_open_time = #{garbageOpenTime}</if>
            <if test="residentialHealthDirector != null  and residentialHealthDirector != ''"> and sgh.residential_health_director = #{residentialHealthDirector}</if>
            <if test="healthDirectorPhone != null  and healthDirectorPhone != ''"> and sgh.health_director_phone = #{healthDirectorPhone}</if>
            <if test="residentialSecretary != null  and residentialSecretary != ''"> and sgh.residential_secretary = #{residentialSecretary}</if>
            <if test="secretaryPhone != null  and secretaryPhone != ''"> and sgh.secretary_phone = #{secretaryPhone}</if>
            <if test="residentialCleaning != null  and residentialCleaning != ''"> and sgh.residential_cleaning = #{residentialCleaning}</if>
            <if test="cleaningPhone != null  and cleaningPhone != ''"> and sgh.cleaning_phone = #{cleaningPhone}</if>
            <if test="residentialManage != null  and residentialManage != ''"> and sgh.residential_manage = #{residentialManage}</if>
            <if test="managePhone != null  and managePhone != ''"> and sgh.manage_phone = #{managePhone}</if>
            <if test="longitudeLatitude != null  and longitudeLatitude != ''"> and sgh.longitude_latitude = #{longitudeLatitude}</if>
            <if test="longitude != null  and longitude != ''"> and sgh.longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and sgh.latitude = #{latitude}</if>
            <if test="type != null  and type != ''"> and sgh.type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and sgh.coordinate = #{coordinate}</if>
            <if test="name != null  and name != ''"> and sgh.name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectGarbageHouseById" parameterType="Long" resultMap="GarbageHouseResult">
        <include refid="selectGarbageHouseVo"/>
        where id = #{id}
    </select>

    <insert id="insertGarbageHouse" parameterType="GarbageHouse" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_garbage_house
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="street != null">street,</if>
            <if test="committee != null">committee,</if>
            <if test="committeeId != null">committee_id,</if>
            <if test="residential != null">residential,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="siteAddress != null">site_address,</if>
            <if test="village != null">village,</if>
            <if test="garbageType != null">garbage_type,</if>
            <if test="garbageOpenTime != null">garbage_open_time,</if>
            <if test="residentialHealthDirector != null">residential_health_director,</if>
            <if test="healthDirectorPhone != null">health_director_phone,</if>
            <if test="residentialSecretary != null">residential_secretary,</if>
            <if test="secretaryPhone != null">secretary_phone,</if>
            <if test="residentialCleaning != null">residential_cleaning,</if>
            <if test="cleaningPhone != null">cleaning_phone,</if>
            <if test="residentialManage != null">residential_manage,</if>
            <if test="managePhone != null">manage_phone,</if>
            <if test="longitudeLatitude != null">longitude_latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="name != null">name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="street != null">#{street},</if>
            <if test="committee != null">#{committee},</if>
            <if test="committeeId != null">#{committeeId},</if>
            <if test="residential != null">#{residential},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="siteAddress != null">#{siteAddress},</if>
            <if test="village != null">#{village},</if>
            <if test="garbageType != null">#{garbageType},</if>
            <if test="garbageOpenTime != null">#{garbageOpenTime},</if>
            <if test="residentialHealthDirector != null">#{residentialHealthDirector},</if>
            <if test="healthDirectorPhone != null">#{healthDirectorPhone},</if>
            <if test="residentialSecretary != null">#{residentialSecretary},</if>
            <if test="secretaryPhone != null">#{secretaryPhone},</if>
            <if test="residentialCleaning != null">#{residentialCleaning},</if>
            <if test="cleaningPhone != null">#{cleaningPhone},</if>
            <if test="residentialManage != null">#{residentialManage},</if>
            <if test="managePhone != null">#{managePhone},</if>
            <if test="longitudeLatitude != null">#{longitudeLatitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>

    <update id="updateGarbageHouse" parameterType="GarbageHouse">
        update shcy_garbage_house
        <trim prefix="SET" suffixOverrides=",">
            <if test="street != null">street = #{street},</if>
            <if test="committee != null">committee = #{committee},</if>
            <if test="committeeId != null">committee_id = #{committeeId},</if>
            <if test="residential != null">residential = #{residential},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="siteAddress != null">site_address = #{siteAddress},</if>
            <if test="village != null">village = #{village},</if>
            <if test="garbageType != null">garbage_type = #{garbageType},</if>
            <if test="garbageOpenTime != null">garbage_open_time = #{garbageOpenTime},</if>
            <if test="residentialHealthDirector != null">residential_health_director = #{residentialHealthDirector},</if>
            <if test="healthDirectorPhone != null">health_director_phone = #{healthDirectorPhone},</if>
            <if test="residentialSecretary != null">residential_secretary = #{residentialSecretary},</if>
            <if test="secretaryPhone != null">secretary_phone = #{secretaryPhone},</if>
            <if test="residentialCleaning != null">residential_cleaning = #{residentialCleaning},</if>
            <if test="cleaningPhone != null">cleaning_phone = #{cleaningPhone},</if>
            <if test="residentialManage != null">residential_manage = #{residentialManage},</if>
            <if test="managePhone != null">manage_phone = #{managePhone},</if>
            <if test="longitudeLatitude != null">longitude_latitude = #{longitudeLatitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="name != null">name = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGarbageHouseById" parameterType="Long">
        delete from shcy_garbage_house where id = #{id}
    </delete>

    <delete id="deleteGarbageHouseByIds" parameterType="String">
        delete from shcy_garbage_house where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
