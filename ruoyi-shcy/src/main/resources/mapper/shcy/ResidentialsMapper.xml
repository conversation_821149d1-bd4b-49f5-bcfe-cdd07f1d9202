<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ResidentialsMapper">

    <resultMap type="Residentials" id="ResidentialsResult">
        <result property="id"    column="id"    />
        <result property="residential"    column="residential"    />
        <result property="committee"    column="committee"    />
        <result property="committeeId"    column="committee_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="siteRange"    column="site_range"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="type"    column="type"    />
        <result property="coordinate"    column="coordinate"    />
    </resultMap>

    <sql id="selectResidentialsVo">
        select id, residential, committee, committee_id, create_time, create_by, update_time, site_range, longitude, latitude, type, coordinate from shcy_residentials
    </sql>

<!--    <select id="selectResidentialsList" parameterType="Residentials" resultMap="ResidentialsResult">-->
    <select id="selectResidentialsList" parameterType="Residentials" resultType="com.ruoyi.shcy.domain.Residentials">
<!--        <include refid="selectResidentialsVo"/>-->
        SELECT
        sr.id as id,
        sr.residential as residential,
        sc.committee_name as committee,
        sc.id as committeeId,
        sr.create_time as createTime,
        sr.create_by as createBy,
        sr.update_time as updateTime,
        sr.site_range as siteRange,
        sr.longitude as longitude,
        sr.latitude as longitude,
        sr.type as type,
        sr.coordinate as coordinate
        FROM
        shcy_residentials sr  left join  shcy_committee sc on sr.committee_id =sc.id
        <where>
            <if test="residential != null  and residential != ''"> and sr.residential = #{residential}</if>
            <if test="committee != null  and committee != ''"> and sc.committee_name = #{committee}</if>
            <if test="committeeId != null "> and sc.id = #{committeeId}</if>
            <if test="siteRange != null  and siteRange != ''"> and sr.site_range = #{siteRange}</if>
            <if test="longitude != null  and longitude != ''"> and sr.longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and sr.latitude = #{latitude}</if>
            <if test="type != null  and type != ''"> and sr.type = #{type}</if>
            <if test="coordinate != null  and coordinate != ''"> and sr.coordinate = #{coordinate}</if>
        </where>
    </select>

    <select id="selectResidentialsById" parameterType="Long" resultMap="ResidentialsResult">
        <include refid="selectResidentialsVo"/>
        where id = #{id}
    </select>

    <insert id="insertResidentials" parameterType="Residentials" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_residentials
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="residential != null">residential,</if>
            <if test="committee != null">committee,</if>
            <if test="committeeId != null">committee_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="siteRange != null">site_range,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="type != null">type,</if>
            <if test="coordinate != null">coordinate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="residential != null">#{residential},</if>
            <if test="committee != null">#{committee},</if>
            <if test="committeeId != null">#{committeeId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="siteRange != null">#{siteRange},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="type != null">#{type},</if>
            <if test="coordinate != null">#{coordinate},</if>
        </trim>
    </insert>

    <update id="updateResidentials" parameterType="Residentials">
        update shcy_residentials
        <trim prefix="SET" suffixOverrides=",">
            <if test="residential != null">residential = #{residential},</if>
            <if test="committee != null">committee = #{committee},</if>
            <if test="committeeId != null">committee_id = #{committeeId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="siteRange != null">site_range = #{siteRange},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResidentialsById" parameterType="Long">
        delete from shcy_residentials where id = #{id}
    </delete>

    <delete id="deleteResidentialsByIds" parameterType="String">
        delete from shcy_residentials where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
