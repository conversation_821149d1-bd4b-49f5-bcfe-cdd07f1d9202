<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.ComprehensiveInspectionMapper">
    
    <resultMap type="ComprehensiveInspection" id="ComprehensiveInspectionResult">
        <result property="id"    column="id"    />
        <result property="taskName"    column="task_name"    />
        <result property="gridArea"    column="grid_area"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="inspectionDate"    column="inspection_date"    />
        <result property="isHighEmission"    column="is_high_emission"    />
        <result property="highEmissionPhoto"    column="high_emission_photo"    />
        <result property="hasPurifier"    column="has_purifier"    />
        <result property="purifierPhoto"    column="purifier_photo"    />
        <result property="meetDistanceReq"    column="meet_distance_req"    />
        <result property="hasGreaseTrap"    column="has_grease_trap"    />
        <result property="greaseTrapPhoto"    column="grease_trap_photo"    />
        <result property="sewageProperlyManaged"    column="sewage_properly_managed"    />
        <result property="otherIssues"    column="other_issues"    />
        <result property="otherIssuesPhoto"    column="other_issues_photo"    />
        <result property="inspectorCount"    column="inspector_count"    />
        <result property="inspectionPhoto"    column="inspection_photo"    />
        <result property="reviewDate"    column="review_date"    />
        <result property="highEmissionDisposal"    column="high_emission_disposal"    />
        <result property="highEmissionDisposalPhoto"    column="high_emission_disposal_photo"    />
        <result property="purifierDisposal"    column="purifier_disposal"    />
        <result property="purifierDisposalPhoto"    column="purifier_disposal_photo"    />
        <result property="greaseTrapDisposal"    column="grease_trap_disposal"    />
        <result property="greaseTrapDisposalPhoto"    column="grease_trap_disposal_photo"    />
        <result property="sewageDisposal"    column="sewage_disposal"    />
        <result property="circulationState"    column="circulation_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectComprehensiveInspectionVo">
        select id, task_name, grid_area, company_name, company_address, inspection_date, is_high_emission, high_emission_photo, has_purifier, purifier_photo, meet_distance_req, has_grease_trap, grease_trap_photo, sewage_properly_managed, other_issues, other_issues_photo, inspector_count, inspection_photo, review_date, high_emission_disposal, high_emission_disposal_photo, purifier_disposal, purifier_disposal_photo, grease_trap_disposal, grease_trap_disposal_photo, sewage_disposal, circulation_state, create_by, create_time, update_by, update_time from shcy_comprehensive_inspection
    </sql>

    <select id="selectComprehensiveInspectionList" parameterType="ComprehensiveInspection" resultMap="ComprehensiveInspectionResult">
        <include refid="selectComprehensiveInspectionVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="gridArea != null  and gridArea != ''"> and grid_area = #{gridArea}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (task_name like concat('%', #{params.keyword}, '%')
                or grid_area like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>
    
    <select id="selectComprehensiveInspectionById" parameterType="Long" resultMap="ComprehensiveInspectionResult">
        <include refid="selectComprehensiveInspectionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertComprehensiveInspection" parameterType="ComprehensiveInspection" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_comprehensive_inspection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">task_name,</if>
            <if test="gridArea != null">grid_area,</if>
            <if test="companyName != null">company_name,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="inspectionDate != null">inspection_date,</if>
            <if test="isHighEmission != null">is_high_emission,</if>
            <if test="highEmissionPhoto != null">high_emission_photo,</if>
            <if test="hasPurifier != null">has_purifier,</if>
            <if test="purifierPhoto != null">purifier_photo,</if>
            <if test="meetDistanceReq != null">meet_distance_req,</if>
            <if test="hasGreaseTrap != null">has_grease_trap,</if>
            <if test="greaseTrapPhoto != null">grease_trap_photo,</if>
            <if test="sewageProperlyManaged != null">sewage_properly_managed,</if>
            <if test="otherIssues != null">other_issues,</if>
            <if test="otherIssuesPhoto != null">other_issues_photo,</if>
            <if test="inspectorCount != null">inspector_count,</if>
            <if test="inspectionPhoto != null">inspection_photo,</if>
            <if test="reviewDate != null">review_date,</if>
            <if test="highEmissionDisposal != null">high_emission_disposal,</if>
            <if test="highEmissionDisposalPhoto != null">high_emission_disposal_photo,</if>
            <if test="purifierDisposal != null">purifier_disposal,</if>
            <if test="purifierDisposalPhoto != null">purifier_disposal_photo,</if>
            <if test="greaseTrapDisposal != null">grease_trap_disposal,</if>
            <if test="greaseTrapDisposalPhoto != null">grease_trap_disposal_photo,</if>
            <if test="sewageDisposal != null">sewage_disposal,</if>
            <if test="circulationState != null">circulation_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">#{taskName},</if>
            <if test="gridArea != null">#{gridArea},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="inspectionDate != null">#{inspectionDate},</if>
            <if test="isHighEmission != null">#{isHighEmission},</if>
            <if test="highEmissionPhoto != null">#{highEmissionPhoto},</if>
            <if test="hasPurifier != null">#{hasPurifier},</if>
            <if test="purifierPhoto != null">#{purifierPhoto},</if>
            <if test="meetDistanceReq != null">#{meetDistanceReq},</if>
            <if test="hasGreaseTrap != null">#{hasGreaseTrap},</if>
            <if test="greaseTrapPhoto != null">#{greaseTrapPhoto},</if>
            <if test="sewageProperlyManaged != null">#{sewageProperlyManaged},</if>
            <if test="otherIssues != null">#{otherIssues},</if>
            <if test="otherIssuesPhoto != null">#{otherIssuesPhoto},</if>
            <if test="inspectorCount != null">#{inspectorCount},</if>
            <if test="inspectionPhoto != null">#{inspectionPhoto},</if>
            <if test="reviewDate != null">#{reviewDate},</if>
            <if test="highEmissionDisposal != null">#{highEmissionDisposal},</if>
            <if test="highEmissionDisposalPhoto != null">#{highEmissionDisposalPhoto},</if>
            <if test="purifierDisposal != null">#{purifierDisposal},</if>
            <if test="purifierDisposalPhoto != null">#{purifierDisposalPhoto},</if>
            <if test="greaseTrapDisposal != null">#{greaseTrapDisposal},</if>
            <if test="greaseTrapDisposalPhoto != null">#{greaseTrapDisposalPhoto},</if>
            <if test="sewageDisposal != null">#{sewageDisposal},</if>
            <if test="circulationState != null">#{circulationState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateComprehensiveInspection" parameterType="ComprehensiveInspection">
        update shcy_comprehensive_inspection
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="gridArea != null">grid_area = #{gridArea},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="inspectionDate != null">inspection_date = #{inspectionDate},</if>
            <if test="isHighEmission != null">is_high_emission = #{isHighEmission},</if>
            <if test="highEmissionPhoto != null">high_emission_photo = #{highEmissionPhoto},</if>
            <if test="hasPurifier != null">has_purifier = #{hasPurifier},</if>
            <if test="purifierPhoto != null">purifier_photo = #{purifierPhoto},</if>
            <if test="meetDistanceReq != null">meet_distance_req = #{meetDistanceReq},</if>
            <if test="hasGreaseTrap != null">has_grease_trap = #{hasGreaseTrap},</if>
            <if test="greaseTrapPhoto != null">grease_trap_photo = #{greaseTrapPhoto},</if>
            <if test="sewageProperlyManaged != null">sewage_properly_managed = #{sewageProperlyManaged},</if>
            <if test="otherIssues != null">other_issues = #{otherIssues},</if>
            <if test="otherIssuesPhoto != null">other_issues_photo = #{otherIssuesPhoto},</if>
            <if test="inspectorCount != null">inspector_count = #{inspectorCount},</if>
            <if test="inspectionPhoto != null">inspection_photo = #{inspectionPhoto},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="highEmissionDisposal != null">high_emission_disposal = #{highEmissionDisposal},</if>
            <if test="highEmissionDisposalPhoto != null">high_emission_disposal_photo = #{highEmissionDisposalPhoto},</if>
            <if test="purifierDisposal != null">purifier_disposal = #{purifierDisposal},</if>
            <if test="purifierDisposalPhoto != null">purifier_disposal_photo = #{purifierDisposalPhoto},</if>
            <if test="greaseTrapDisposal != null">grease_trap_disposal = #{greaseTrapDisposal},</if>
            <if test="greaseTrapDisposalPhoto != null">grease_trap_disposal_photo = #{greaseTrapDisposalPhoto},</if>
            <if test="sewageDisposal != null">sewage_disposal = #{sewageDisposal},</if>
            <if test="circulationState != null">circulation_state = #{circulationState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteComprehensiveInspectionById" parameterType="Long">
        delete from shcy_comprehensive_inspection where id = #{id}
    </delete>

    <delete id="deleteComprehensiveInspectionByIds" parameterType="String">
        delete from shcy_comprehensive_inspection where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCaseCount" parameterType="ComprehensiveInspection" resultType="long">
        select count(1) from shcy_comprehensive_inspection
        <where>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="gridArea != null  and gridArea != ''"> and grid_area = #{gridArea}</if>
            <if test="circulationState != null  and circulationState != ''"> and circulation_state = #{circulationState}</if>
            <if test="params.keyword != null  and params.keyword != ''"> and (task_name like concat('%', #{params.keyword}, '%')
                or grid_area like concat('%', #{params.keyword}, '%'))
            </if>
        </where>
    </select>
</mapper>