package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.DuplicateClassify;
import com.ruoyi.shcy.service.IDuplicateClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 重复归类Controller
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@RestController
@RequestMapping("/shcy/duplicateClassify")
public class DuplicateClassifyController extends BaseController
{
    @Autowired
    private IDuplicateClassifyService duplicateClassifyService;

    /**
     * 查询重复归类列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:list')")
    @GetMapping("/list")
    public AjaxResult list(DuplicateClassify duplicateClassify)
    {
        List<DuplicateClassify> list = duplicateClassifyService.selectDuplicateClassifyList(duplicateClassify);
        return AjaxResult.success(list);
    }

    /**
     * 导出重复归类列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:export')")
    @Log(title = "重复归类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DuplicateClassify duplicateClassify)
    {
        List<DuplicateClassify> list = duplicateClassifyService.selectDuplicateClassifyList(duplicateClassify);
        List<DuplicateClassify> duplicateClassifies = buildTree(list);
        List<DuplicateClassify> flatList = flattenTree(duplicateClassifies);
        ExcelUtil<DuplicateClassify> util = new ExcelUtil<DuplicateClassify>(DuplicateClassify.class);
        util.exportExcel(response, flatList, "重复归类数据");
    }

    /**
     * 构建树形结构
     */
    private List<DuplicateClassify> buildTree(List<DuplicateClassify> list) {
        Map<Long, List<DuplicateClassify>> childrenMap = new HashMap<>();
        Map<Long, DuplicateClassify> nodeMap = new HashMap<>();
        List<DuplicateClassify> root = new ArrayList<>();

        // 构建映射关系
        for (DuplicateClassify node : list) {
            Long parentId = node.getParentId();
            childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(node);
            nodeMap.put(node.getId(), node);
        }

        // 找出根节点
        for (DuplicateClassify node : list) {
            if (!nodeMap.containsKey(node.getParentId())) {
                root.add(node);
            }
        }

        // 递归设置子节点
        for (DuplicateClassify node : root) {
            setChildren(node, childrenMap);
        }

        return root;
    }

    /**
     * 递归设置子节点
     */
    private void setChildren(DuplicateClassify node, Map<Long, List<DuplicateClassify>> childrenMap) {
        List<DuplicateClassify> children = childrenMap.get(node.getId());
        if (children != null) {
            node.setChildren(children);
            for (DuplicateClassify child : children) {
                setChildren(child, childrenMap);
            }
        }
    }

    private List<DuplicateClassify> flattenTree(List<DuplicateClassify> tree) {
        List<DuplicateClassify> flatList = new ArrayList<>();
        for (DuplicateClassify node : tree) {
            addNodeAndChildren(node, flatList);
        }
        return flatList;
    }

    private void addNodeAndChildren(DuplicateClassify node, List<DuplicateClassify> flatList) {
        flatList.add(node);
        for (DuplicateClassify child : node.getChildren()) {
            addNodeAndChildren(child, flatList);
        }
    }

    /**
     * 获取重复归类详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(duplicateClassifyService.selectDuplicateClassifyById(id));
    }

    /**
     * 新增重复归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:add')")
    @Log(title = "重复归类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DuplicateClassify duplicateClassify)
    {
        return toAjax(duplicateClassifyService.insertDuplicateClassify(duplicateClassify));
    }

    /**
     * 修改重复归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:edit')")
    @Log(title = "重复归类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DuplicateClassify duplicateClassify)
    {
        return toAjax(duplicateClassifyService.updateDuplicateClassify(duplicateClassify));
    }

    /**
     * 删除重复归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:duplicateClassify:remove')")
    @Log(title = "重复归类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(duplicateClassifyService.deleteDuplicateClassifyByIds(ids));
    }
}
