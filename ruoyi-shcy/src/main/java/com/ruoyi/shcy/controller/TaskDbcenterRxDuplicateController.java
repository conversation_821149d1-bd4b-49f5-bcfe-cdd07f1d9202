package com.ruoyi.shcy.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.DbCenterRxDuplicateVO;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 重复工单Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/shcy/dbcenterRxDuplicate")
public class TaskDbcenterRxDuplicateController extends BaseController
{
    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    /**
     * 查询重复工单列表
     */
    @GetMapping("/list")
    public AjaxResult list(TaskDbcenter taskDbcenter, String requestItem, Integer duplicateCount) {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.setIsduplicate("是");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);

        // 将所有操作整合到一个 Stream 链中
        List<DbCenterRxDuplicateVO> result = convertToDbCenterRxDuplicateVO(list) // 1. 获取初始流
                .stream() // 2. 创建 Stream
                // 3. 条件过滤：如果 requestItem 不为空，则应用该过滤条件
                .filter(item -> requestItem == null || requestItem.isEmpty() || item.getRequestItem().contains(requestItem))
                // 4. 条件过滤：如果 duplicateCount 不为空，则应用该过滤条件
                .filter(item -> duplicateCount == null || item.getDuplicateCount() >= duplicateCount)
                // 5. 排序
                .sorted(Comparator.comparing(DbCenterRxDuplicateVO::getDuplicateCount).reversed())
                // 6. 终端操作：将结果收集到一个新的 List 中
                .collect(Collectors.toList());

        return AjaxResult.success(result);
    }

    /**
     * 将TaskDbcenter列表转换为DbCenterRxDuplicateVO列表
     */
    private List<DbCenterRxDuplicateVO> convertToDbCenterRxDuplicateVO(List<TaskDbcenter> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 按relatedhotlinesn分组统计重复次数
        Map<String, Long> duplicateCountMap = list.stream()
                .filter(item -> item.getRelatedhotlinesn() != null)
                .collect(Collectors.groupingBy(
                        TaskDbcenter::getRelatedhotlinesn,
                        Collectors.counting()
                ));

        // 计算所有重复次数的总和
        long totalDuplicateCount = duplicateCountMap.values().stream()
                .mapToLong(Long::longValue)
                .sum();

        // 按relatedhotlinesn分组，取每组的第一个元素作为代表
        Map<String, TaskDbcenter> groupedMap = list.stream()
                .filter(item -> item.getRelatedhotlinesn() != null)
                .collect(Collectors.toMap(
                        TaskDbcenter::getRelatedhotlinesn,
                        item -> item,
                        (existing, replacement) -> existing // 保留第一个
                ));

        // 转换为VO列表
        return groupedMap.values().stream()
                .map(item -> {
                    DbCenterRxDuplicateVO vo = new DbCenterRxDuplicateVO();
                    vo.setRequestItem(item.getReporter()); // 诉求事项 = reporter
                    vo.setDuplicateOrderNo(item.getRecenthotlinesn()); // 重复工单号 = recenthotlinesn
                    vo.setRequestContent(item.getDescription()); // 诉求内容 = description
                    
                    // 重复次数
                    Long count = duplicateCountMap.get(item.getRelatedhotlinesn());
                    vo.setDuplicateCount(count != null ? count.intValue() : 0);
                    
                    // 重复占比 = 当前重复次数 / 总重复次数
                    if (totalDuplicateCount > 0 && count != null) {
                        BigDecimal ratio = BigDecimal.valueOf(count)
                                .divide(BigDecimal.valueOf(totalDuplicateCount), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100)); // 转换为百分比
                        vo.setDuplicateRatio(ratio);
                    } else {
                        vo.setDuplicateRatio(BigDecimal.ZERO);
                    }
                    
                    return vo;
                })
                .collect(Collectors.toList());
    }

}
