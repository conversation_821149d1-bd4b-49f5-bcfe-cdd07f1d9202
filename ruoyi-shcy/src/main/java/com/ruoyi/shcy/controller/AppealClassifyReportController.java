package com.ruoyi.shcy.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.AppealClassifyReportVO;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 诉求归类统计Controller
 *
 */
@RestController
@RequestMapping("/shcy/appealClassifyReport")
public class AppealClassifyReportController {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @GetMapping("/list")
    public AjaxResult list(TaskDbcenter taskDbcenter) {
        List<AppealClassifyReportVO> appealClassifyReportVOS = new ArrayList<>();
        taskDbcenter.setInfosourcename("12345上报");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);

        if (list != null && !list.isEmpty()) {
            if (taskDbcenter.getSubexecutedeptnameMh() != null) {
                // 当三级主责部门不为空时，按三级主责部门、诉求大类、诉求小类分组统计
                Map<String, Map<String, Map<String, Long>>> groupResult = list.stream()
                    .collect(Collectors.groupingBy(
                        task -> task.getSubexecutedeptnameMh() != null ? task.getSubexecutedeptnameMh() : "未知部门",
                        Collectors.groupingBy(
                            task -> task.getParentappealclassification() != null && !task.getParentappealclassification().trim().isEmpty() 
                                ? task.getParentappealclassification() : "未分类",
                            Collectors.groupingBy(
                                task -> task.getAppealclassification() != null && !task.getAppealclassification().trim().isEmpty() 
                                    ? task.getAppealclassification() : "未分类",
                                Collectors.counting()
                            )
                        )
                    ));

                groupResult.forEach((deptName, parentMap) -> {
                    parentMap.forEach((parentClass, childMap) -> {
                        childMap.forEach((childClass, count) -> {
                            AppealClassifyReportVO vo = new AppealClassifyReportVO();
                            vo.setSubexecutedeptnameMh(deptName);
                            vo.setParentappealclassification(parentClass);
                            vo.setAppealclassification(childClass);
                            vo.setCount(count.intValue());
                            appealClassifyReportVOS.add(vo);
                        });
                    });
                });
            } else if (taskDbcenter.getParentappealclassification() != null) {
                // 当三级主责部门为空，诉求大类不为空时，按三级主责部门、诉求小类分组统计
                Map<String, Map<String, Long>> groupResult = list.stream()
                    .collect(Collectors.groupingBy(
                        task -> task.getSubexecutedeptnameMh() != null && !task.getSubexecutedeptnameMh().trim().isEmpty()
                            ? task.getSubexecutedeptnameMh() : "未知部门",
                        Collectors.groupingBy(
                            task -> task.getAppealclassification() != null && !task.getAppealclassification().trim().isEmpty() 
                                ? task.getAppealclassification() : "未分类",
                            Collectors.counting()
                        )
                    ));

                groupResult.forEach((deptName, childMap) -> {
                    childMap.forEach((childClass, count) -> {
                        AppealClassifyReportVO vo = new AppealClassifyReportVO();
                        vo.setSubexecutedeptnameMh(deptName);
                        vo.setParentappealclassification(taskDbcenter.getParentappealclassification());
                        vo.setAppealclassification(childClass);
                        vo.setCount(count.intValue());
                        appealClassifyReportVOS.add(vo);
                    });
                });
            }
        }

        return AjaxResult.success(appealClassifyReportVOS);
    }
}
