package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.dahuatech.icc.exception.ClientException;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.dto.IccAlarmRecordDTO;
import com.ruoyi.shcy.dto.IccAlarmRecordHandleDTO;
import com.ruoyi.shcy.service.IIccAlarmRecordService;
import com.ruoyi.shcy.service.IIccDeviceService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * ICC报警记录Controller
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@RestController
@RequestMapping("/shcy/iccAlarmRecord")
public class IccAlarmRecordController extends BaseController
{
    @Autowired
    private IIccAlarmRecordService iccAlarmRecordService;

    @Autowired
    private IccService iccService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IIccDeviceService iccDeviceService;

    /**
     * 查询ICC报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(IccAlarmRecord iccAlarmRecord)
    {
        startPage();
        List<IccAlarmRecord> list = iccAlarmRecordService.selectIccAlarmRecordList(iccAlarmRecord);
        return getDataTable(list);
    }

    /**
     * 导出ICC报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:export')")
    @Log(title = "ICC报警记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IccAlarmRecord iccAlarmRecord)
    {
        List<IccAlarmRecord> list = iccAlarmRecordService.selectIccAlarmRecordList(iccAlarmRecord);
        ExcelUtil<IccAlarmRecord> util = new ExcelUtil<IccAlarmRecord>(IccAlarmRecord.class);
        util.exportExcel(response, list, "ICC报警记录数据");
    }

    /**
     * 获取ICC报警记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(iccAlarmRecordService.selectIccAlarmRecordById(id));
    }

    /**
     * 新增ICC报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:add')")
    @Log(title = "ICC报警记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IccAlarmRecord iccAlarmRecord)
    {
        return toAjax(iccAlarmRecordService.insertIccAlarmRecord(iccAlarmRecord));
    }

    /**
     * 修改ICC报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:edit')")
    @Log(title = "ICC报警记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IccAlarmRecord iccAlarmRecord)
    {
        return toAjax(iccAlarmRecordService.updateIccAlarmRecord(iccAlarmRecord));
    }

    /**
     * 删除ICC报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccAlarmRecord:remove')")
    @Log(title = "ICC报警记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(iccAlarmRecordService.deleteIccAlarmRecordByIds(ids));
    }

    /**
     * 获取icc访问令牌
     *
     * @return {@link AjaxResult}
     * @throws ClientException 客户端异常
     */
    @GetMapping("/getIccAccessToken")
    public AjaxResult getIccAccessToken() throws ClientException {
        return AjaxResult.success("操作成功", iccService.getAccessToken());
    }

    @GetMapping("/hjzz/list")
    public TableDataInfo hjzzList(IccAlarmRecord iccAlarmRecord) {
        startPage();
        List<IccAlarmRecord> list = new ArrayList<>();
        if (StrUtil.isEmpty(iccAlarmRecord.getStatus())) {
            list = iccAlarmRecordService.selectIccAlarmRecordList(iccAlarmRecord);
        } else {
            switch (iccAlarmRecord.getStatus()) {
                case HjzzConstants.ALARM_STATE_UNPROCESSED:
                    list = iccAlarmRecordService.selectIccAlarmRecordUnprocessedList(iccAlarmRecord);
                    break;
                case HjzzConstants.ALARM_STATE_PROCESSING:
                    list = iccAlarmRecordService.selectIccAlarmRecordProcessingList(iccAlarmRecord);
                    break;
                case HjzzConstants.ALARM_STATE_PROCESSED:
                    list = iccAlarmRecordService.selectIccAlarmRecordProcessedList(iccAlarmRecord);
                    break;
                case HjzzConstants.ALARM_STATE_UNPROCESSED_PROCESSED:
                    list = iccAlarmRecordService.selectIccAlarmRecordUnprocessedAndProcessingList(iccAlarmRecord);
                    break;
            }
        }
        List<IccAlarmRecordDTO> result = BeanUtil.copyToList(list, IccAlarmRecordDTO.class);
        return getDataTable(result);
    }

    @PostMapping("/hjzz/handle")
    @RepeatSubmit
    public AjaxResult hjzzHandle(@RequestBody IccAlarmRecordHandleDTO iccAlarmRecordHandleDTO)
    {
        return toAjax(iccAlarmRecordService.hjzzHandle(iccAlarmRecordHandleDTO));
    }

    @GetMapping("/hjzz/type")
    public AjaxResult hjzzType()
    {
        List<SysDictData> data = dictTypeService.selectDictDataByType("hjzz_case_type");
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<>();
        }
        return AjaxResult.success(data);
    }

    @GetMapping("/hjzz/address")
    public AjaxResult hjzzAddress()
    {
        return AjaxResult.success(iccDeviceService.selectHjzzAddressList());
    }


}
