package com.ruoyi.shcy.controller;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;
import com.ruoyi.shcy.service.IShcyFloodHazardTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 隐患排查类型Controller
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@RestController
@RequestMapping("/shcy/hazardType")
public class ShcyFloodHazardTypeController extends BaseController
{
    @Autowired
    private IShcyFloodHazardTypeService shcyFloodHazardTypeService;

    /**
     * 查询隐患排查类型列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:list')")
    @GetMapping("/list")
    public AjaxResult list(ShcyFloodHazardType shcyFloodHazardType)
    {
        List<ShcyFloodHazardType> list = shcyFloodHazardTypeService.selectShcyFloodHazardTypeList(shcyFloodHazardType);
        return AjaxResult.success(list);
    }

    @GetMapping("/listParentType")
    public AjaxResult listParentType(ShcyFloodHazardType shcyFloodHazardType)
    {
        List<ShcyFloodHazardType> list = shcyFloodHazardTypeService.selectShcyFloodHazardTypeList(shcyFloodHazardType);
        List<ShcyFloodHazardType> list1=new ArrayList<ShcyFloodHazardType>();
        for(ShcyFloodHazardType theA:list)
        {
            String[] ids=theA.getAncestors().split(",");
            if(ids.length ==2)
            {
                list1.add(theA);
            }
        }
        return AjaxResult.success(list1);
    }

    /**
     * 导出隐患排查类型列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:export')")
    @Log(title = "隐患排查类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyFloodHazardType shcyFloodHazardType)
    {
        List<ShcyFloodHazardType> list = shcyFloodHazardTypeService.selectShcyFloodHazardTypeList(shcyFloodHazardType);
        ExcelUtil<ShcyFloodHazardType> util = new ExcelUtil<ShcyFloodHazardType>(ShcyFloodHazardType.class);
        util.exportExcel(response, list, "隐患排查类型数据");
    }

    /**
     * 获取隐患排查类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyFloodHazardTypeService.selectShcyFloodHazardTypeById(id));
    }

    /**
     * 新增隐患排查类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:add')")
    @Log(title = "隐患排查类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyFloodHazardType shcyFloodHazardType)
    {
        shcyFloodHazardType.setCreateBy(getUsername());
        return toAjax(shcyFloodHazardTypeService.insertShcyFloodHazardType(shcyFloodHazardType));
    }

    /**
     * 修改隐患排查类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:edit')")
    @Log(title = "隐患排查类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyFloodHazardType shcyFloodHazardType)
    {
        Long id = shcyFloodHazardType.getId();
        if (shcyFloodHazardType.getParentId().equals(id))
        {
            return AjaxResult.error("修改'" + shcyFloodHazardType.getName() + "'失败，上级部门不能是自己");
        }
        shcyFloodHazardType.setUpdateBy(getUsername());
        return toAjax(shcyFloodHazardTypeService.updateShcyFloodHazardType(shcyFloodHazardType));
    }

    /**
     * 删除隐患排查类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:remove')")
    @Log(title = "隐患排查类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        if (shcyFloodHazardTypeService.hasChildById(id))
        {
            return AjaxResult.error("存在子类型,不允许删除");
        }
        return toAjax(shcyFloodHazardTypeService.deleteShcyFloodHazardTypeById(id));
    }

    /**
     * 查询列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardType:list')")
    @GetMapping("/list/exclude/{id}")
    public AjaxResult excludeChild(@PathVariable(value = "id", required = false) Long id)
    {
        List<ShcyFloodHazardType> types = shcyFloodHazardTypeService.selectShcyFloodHazardTypeList(new ShcyFloodHazardType());
        Iterator<ShcyFloodHazardType> it = types.iterator();
        while (it.hasNext())
        {
            ShcyFloodHazardType d = (ShcyFloodHazardType) it.next();
            if (d.getId().intValue() == id
                    || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), id + ""))
            {
                it.remove();
            }
        }
        return AjaxResult.success(types);
    }
}
