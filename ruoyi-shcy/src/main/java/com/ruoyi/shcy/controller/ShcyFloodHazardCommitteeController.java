package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyFloodHazardCommittee;
import com.ruoyi.shcy.service.IShcyFloodHazardCommitteeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 隐患排查居委会对应表Controller
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@RestController
@RequestMapping("/shcy/hazardCommittee")
public class ShcyFloodHazardCommitteeController extends BaseController
{
    @Autowired
    private IShcyFloodHazardCommitteeService shcyFloodHazardCommitteeService;

    /**
     * 查询隐患排查居委会对应表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        startPage();
        List<ShcyFloodHazardCommittee> list = shcyFloodHazardCommitteeService.selectShcyFloodHazardCommitteeList(shcyFloodHazardCommittee);
        return getDataTable(list);
    }

    @GetMapping("/allList")
    public AjaxResult allList(ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        List<ShcyFloodHazardCommittee> list = shcyFloodHazardCommitteeService.selectShcyFloodHazardCommitteeList(shcyFloodHazardCommittee);
        return AjaxResult.success(list);
    }

    /**
     * 导出隐患排查居委会对应表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:export')")
    @Log(title = "隐患排查居委会对应表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        List<ShcyFloodHazardCommittee> list = shcyFloodHazardCommitteeService.selectShcyFloodHazardCommitteeList(shcyFloodHazardCommittee);
        ExcelUtil<ShcyFloodHazardCommittee> util = new ExcelUtil<ShcyFloodHazardCommittee>(ShcyFloodHazardCommittee.class);
        util.exportExcel(response, list, "隐患排查居委会对应表数据");
    }

    /**
     * 获取隐患排查居委会对应表详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyFloodHazardCommitteeService.selectShcyFloodHazardCommitteeById(id));
    }

    /**
     * 新增隐患排查居委会对应表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:add')")
    @Log(title = "隐患排查居委会对应表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        shcyFloodHazardCommittee.setCreateBy(getUsername());
        return toAjax(shcyFloodHazardCommitteeService.insertShcyFloodHazardCommittee(shcyFloodHazardCommittee));
    }

    /**
     * 修改隐患排查居委会对应表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:edit')")
    @Log(title = "隐患排查居委会对应表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        shcyFloodHazardCommittee.setUpdateBy(getUsername());
        return toAjax(shcyFloodHazardCommitteeService.updateShcyFloodHazardCommittee(shcyFloodHazardCommittee));
    }

    /**
     * 删除隐患排查居委会对应表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCommittee:remove')")
    @Log(title = "隐患排查居委会对应表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyFloodHazardCommitteeService.deleteShcyFloodHazardCommitteeByIds(ids));
    }
}
