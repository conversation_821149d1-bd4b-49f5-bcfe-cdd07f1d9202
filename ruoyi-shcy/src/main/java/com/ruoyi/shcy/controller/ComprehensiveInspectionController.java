package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ComprehensiveInspection;
import com.ruoyi.shcy.service.IComprehensiveInspectionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 综合检查Controller
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@RestController
@RequestMapping("/shcy/comprehensiveInspection")
public class ComprehensiveInspectionController extends BaseController
{
    @Autowired
    private IComprehensiveInspectionService comprehensiveInspectionService;

    /**
     * 查询综合检查列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(ComprehensiveInspection comprehensiveInspection)
    {
        startPage();
        List<ComprehensiveInspection> list = comprehensiveInspectionService.selectComprehensiveInspectionList(comprehensiveInspection);
        return getDataTable(list);
    }

    /**
     * 导出综合检查列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:export')")
    @Log(title = "综合检查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ComprehensiveInspection comprehensiveInspection)
    {
        List<ComprehensiveInspection> list = comprehensiveInspectionService.selectComprehensiveInspectionList(comprehensiveInspection);
        ExcelUtil<ComprehensiveInspection> util = new ExcelUtil<ComprehensiveInspection>(ComprehensiveInspection.class);
        util.exportExcel(response, list, "综合检查数据");
    }

    /**
     * 获取综合检查详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(comprehensiveInspectionService.selectComprehensiveInspectionById(id));
    }

    /**
     * 新增综合检查
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:add')")
    @Log(title = "综合检查", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ComprehensiveInspection comprehensiveInspection)
    {
        return toAjax(comprehensiveInspectionService.insertComprehensiveInspection(comprehensiveInspection));
    }

    /**
     * 修改综合检查
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:edit')")
    @Log(title = "综合检查", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ComprehensiveInspection comprehensiveInspection)
    {
        return toAjax(comprehensiveInspectionService.updateComprehensiveInspection(comprehensiveInspection));
    }

    /**
     * 删除综合检查
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveInspection:remove')")
    @Log(title = "综合检查", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(comprehensiveInspectionService.deleteComprehensiveInspectionByIds(ids));
    }
}
