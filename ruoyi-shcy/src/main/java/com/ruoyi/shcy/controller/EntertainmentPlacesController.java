package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.EntertainmentPlaces;
import com.ruoyi.shcy.service.IEntertainmentPlacesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 娱乐场所Controller
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@RestController
@RequestMapping("/shcy/entainment")
public class EntertainmentPlacesController extends BaseController
{
    @Autowired
    private IEntertainmentPlacesService entertainmentPlacesService;

    /**
     * 查询娱乐场所列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:list')")
    @GetMapping("/list")
    public TableDataInfo list(EntertainmentPlaces entertainmentPlaces)
    {
        startPage();
        List<EntertainmentPlaces> list = entertainmentPlacesService.selectEntertainmentPlacesList(entertainmentPlaces);
        return getDataTable(list);
    }

    /**
     * 导出娱乐场所列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:export')")
    @Log(title = "娱乐场所", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EntertainmentPlaces entertainmentPlaces)
    {
        List<EntertainmentPlaces> list = entertainmentPlacesService.selectEntertainmentPlacesList(entertainmentPlaces);
        ExcelUtil<EntertainmentPlaces> util = new ExcelUtil<EntertainmentPlaces>(EntertainmentPlaces.class);
        util.exportExcel(response, list, "娱乐场所数据");
    }

    /**
     * 获取娱乐场所详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(entertainmentPlacesService.selectEntertainmentPlacesById(id));
    }

    /**
     * 新增娱乐场所
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:add')")
    @Log(title = "娱乐场所", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EntertainmentPlaces entertainmentPlaces)
    {
        return toAjax(entertainmentPlacesService.insertEntertainmentPlaces(entertainmentPlaces));
    }

    /**
     * 修改娱乐场所
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:edit')")
    @Log(title = "娱乐场所", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EntertainmentPlaces entertainmentPlaces)
    {
        return toAjax(entertainmentPlacesService.updateEntertainmentPlaces(entertainmentPlaces));
    }

    /**
     * 删除娱乐场所
     */
    @PreAuthorize("@ss.hasPermi('shcy:entainment:remove')")
    @Log(title = "娱乐场所", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(entertainmentPlacesService.deleteEntertainmentPlacesByIds(ids));
    }
}
