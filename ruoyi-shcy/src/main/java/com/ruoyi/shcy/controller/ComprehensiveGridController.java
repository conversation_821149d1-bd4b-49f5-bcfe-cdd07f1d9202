package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ComprehensiveGrid;
import com.ruoyi.shcy.service.IComprehensiveGridService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 综合网格Controller
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
@RestController
@RequestMapping("/shcy/comprehensiveGrid")
public class ComprehensiveGridController extends BaseController
{
    @Autowired
    private IComprehensiveGridService comprehensiveGridService;

    /**
     * 查询综合网格列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:list')")
    @GetMapping("/list")
    public TableDataInfo list(ComprehensiveGrid comprehensiveGrid)
    {
        startPage();
        List<ComprehensiveGrid> list = comprehensiveGridService.selectComprehensiveGridList(comprehensiveGrid);
        return getDataTable(list);
    }

    /**
     * 导出综合网格列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:export')")
    @Log(title = "综合网格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ComprehensiveGrid comprehensiveGrid)
    {
        List<ComprehensiveGrid> list = comprehensiveGridService.selectComprehensiveGridList(comprehensiveGrid);
        ExcelUtil<ComprehensiveGrid> util = new ExcelUtil<ComprehensiveGrid>(ComprehensiveGrid.class);
        util.exportExcel(response, list, "综合网格数据");
    }

    /**
     * 获取综合网格详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(comprehensiveGridService.selectComprehensiveGridById(id));
    }

    /**
     * 新增综合网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:add')")
    @Log(title = "综合网格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ComprehensiveGrid comprehensiveGrid)
    {
        return toAjax(comprehensiveGridService.insertComprehensiveGrid(comprehensiveGrid));
    }

    /**
     * 修改综合网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:edit')")
    @Log(title = "综合网格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ComprehensiveGrid comprehensiveGrid)
    {
        return toAjax(comprehensiveGridService.updateComprehensiveGrid(comprehensiveGrid));
    }

    /**
     * 删除综合网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:comprehensiveGrid:remove')")
    @Log(title = "综合网格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(comprehensiveGridService.deleteComprehensiveGridByIds(ids));
    }
}
