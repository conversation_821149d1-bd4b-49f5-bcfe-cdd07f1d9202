package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.HistoryAlarmRecord;
import com.ruoyi.shcy.service.IHistoryAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 历史报警记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/shcy/historyAlarmRecord")
public class HistoryAlarmRecordController extends BaseController
{
    @Autowired
    private IHistoryAlarmRecordService historyAlarmRecordService;

    /**
     * 查询历史报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(HistoryAlarmRecord historyAlarmRecord)
    {
        startPage();
        List<HistoryAlarmRecord> list = historyAlarmRecordService.selectHistoryAlarmRecordList(historyAlarmRecord);
        return getDataTable(list);
    }

    /**
     * 导出历史报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:export')")
    @Log(title = "历史报警记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HistoryAlarmRecord historyAlarmRecord)
    {
        List<HistoryAlarmRecord> list = historyAlarmRecordService.selectHistoryAlarmRecordList(historyAlarmRecord);
        ExcelUtil<HistoryAlarmRecord> util = new ExcelUtil<HistoryAlarmRecord>(HistoryAlarmRecord.class);
        util.exportExcel(response, list, "历史报警记录数据");
    }

    /**
     * 获取历史报警记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(historyAlarmRecordService.selectHistoryAlarmRecordById(id));
    }

    /**
     * 新增历史报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:add')")
    @Log(title = "历史报警记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HistoryAlarmRecord historyAlarmRecord)
    {
        return toAjax(historyAlarmRecordService.insertHistoryAlarmRecord(historyAlarmRecord));
    }

    /**
     * 修改历史报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:edit')")
    @Log(title = "历史报警记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HistoryAlarmRecord historyAlarmRecord)
    {
        return toAjax(historyAlarmRecordService.updateHistoryAlarmRecord(historyAlarmRecord));
    }

    /**
     * 删除历史报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:historyAlarmRecord:remove')")
    @Log(title = "历史报警记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(historyAlarmRecordService.deleteHistoryAlarmRecordByIds(ids));
    }
}
