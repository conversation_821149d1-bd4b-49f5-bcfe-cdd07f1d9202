package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.LargeGarbageSite;
import com.ruoyi.shcy.service.ILargeGarbageSiteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区大件垃圾堆放点Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/largeGarbageSite")
public class LargeGarbageSiteController extends BaseController
{
    @Autowired
    private ILargeGarbageSiteService largeGarbageSiteService;

    /**
     * 查询小区大件垃圾堆放点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:list')")
    @GetMapping("/list")
    public TableDataInfo list(LargeGarbageSite largeGarbageSite)
    {
        startPage();
        List<LargeGarbageSite> list = largeGarbageSiteService.selectLargeGarbageSiteList(largeGarbageSite);
        return getDataTable(list);
    }

    /**
     * 导出小区大件垃圾堆放点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:export')")
    @Log(title = "小区大件垃圾堆放点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LargeGarbageSite largeGarbageSite)
    {
        List<LargeGarbageSite> list = largeGarbageSiteService.selectLargeGarbageSiteList(largeGarbageSite);
        ExcelUtil<LargeGarbageSite> util = new ExcelUtil<LargeGarbageSite>(LargeGarbageSite.class);
        util.exportExcel(response, list, "小区大件垃圾堆放点数据");
    }

    /**
     * 获取小区大件垃圾堆放点详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(largeGarbageSiteService.selectLargeGarbageSiteById(id));
    }

    /**
     * 新增小区大件垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:add')")
    @Log(title = "小区大件垃圾堆放点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LargeGarbageSite largeGarbageSite)
    {
        return toAjax(largeGarbageSiteService.insertLargeGarbageSite(largeGarbageSite));
    }

    /**
     * 修改小区大件垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:edit')")
    @Log(title = "小区大件垃圾堆放点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LargeGarbageSite largeGarbageSite)
    {
        return toAjax(largeGarbageSiteService.updateLargeGarbageSite(largeGarbageSite));
    }

    /**
     * 删除小区大件垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:largeGarbageSite:remove')")
    @Log(title = "小区大件垃圾堆放点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(largeGarbageSiteService.deleteLargeGarbageSiteByIds(ids));
    }
}
