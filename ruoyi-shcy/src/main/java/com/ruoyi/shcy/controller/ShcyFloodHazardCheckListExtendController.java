package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyFloodHazardCheckListExtend;
import com.ruoyi.shcy.service.IShcyFloodHazardCheckListExtendService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 隐患排查子表Controller
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@RestController
@RequestMapping("/shcy/hazardCheckExtend")
public class ShcyFloodHazardCheckListExtendController extends BaseController
{
    @Autowired
    private IShcyFloodHazardCheckListExtendService shcyFloodHazardCheckListExtendService;

    /**
     * 查询隐患排查子表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        startPage();
        List<ShcyFloodHazardCheckListExtend> list = shcyFloodHazardCheckListExtendService.selectShcyFloodHazardCheckListExtendList(shcyFloodHazardCheckListExtend);
        return getDataTable(list);
    }

    /**
     * 导出隐患排查子表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:export')")
    @Log(title = "隐患排查子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        List<ShcyFloodHazardCheckListExtend> list = shcyFloodHazardCheckListExtendService.selectShcyFloodHazardCheckListExtendList(shcyFloodHazardCheckListExtend);
        ExcelUtil<ShcyFloodHazardCheckListExtend> util = new ExcelUtil<ShcyFloodHazardCheckListExtend>(ShcyFloodHazardCheckListExtend.class);
        util.exportExcel(response, list, "隐患排查子表数据");
    }

    /**
     * 获取隐患排查子表详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyFloodHazardCheckListExtendService.selectShcyFloodHazardCheckListExtendById(id));
    }

    @GetMapping(value = "/getExtend/{checkId}")
    public AjaxResult getExtend(@PathVariable("checkId") Long checkId)
    {
        ShcyFloodHazardCheckListExtend theShcyFloodHazardCheckListExtend=new ShcyFloodHazardCheckListExtend();
        theShcyFloodHazardCheckListExtend.setCheckId(checkId);
        return AjaxResult.success(shcyFloodHazardCheckListExtendService.selectShcyFloodHazardCheckListExtendList(theShcyFloodHazardCheckListExtend));
    }


    /**
     * 新增隐患排查子表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:add')")
    @Log(title = "隐患排查子表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        return toAjax(shcyFloodHazardCheckListExtendService.insertShcyFloodHazardCheckListExtend(shcyFloodHazardCheckListExtend));
    }

    /**
     * 修改隐患排查子表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:edit')")
    @Log(title = "隐患排查子表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        return toAjax(shcyFloodHazardCheckListExtendService.updateShcyFloodHazardCheckListExtend(shcyFloodHazardCheckListExtend));
    }

    /**
     * 删除隐患排查子表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hazardCheckExtend:remove')")
    @Log(title = "隐患排查子表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyFloodHazardCheckListExtendService.deleteShcyFloodHazardCheckListExtendByIds(ids));
    }

    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody List<ShcyFloodHazardCheckListExtend> list)
    {
        for(ShcyFloodHazardCheckListExtend theShcyFloodHazardCheckListExtend:list)
        {
            theShcyFloodHazardCheckListExtend.setUpdateBy(getUsername());
            shcyFloodHazardCheckListExtendService.updateShcyFloodHazardCheckListExtend(theShcyFloodHazardCheckListExtend);
        }
        return success();
    }
}
