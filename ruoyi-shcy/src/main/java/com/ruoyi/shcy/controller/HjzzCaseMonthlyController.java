package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.HjzzCaseMonthly;
import com.ruoyi.shcy.service.IHjzzCaseMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 每月案件情况分析Controller
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
@RestController
@RequestMapping("/shcy/hjzzCaseMonthly")
public class HjzzCaseMonthlyController extends BaseController
{
    @Autowired
    private IHjzzCaseMonthlyService hjzzCaseMonthlyService;

    /**
     * 查询每月案件情况分析列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(HjzzCaseMonthly hjzzCaseMonthly)
    {
        startPage();
        List<HjzzCaseMonthly> list = hjzzCaseMonthlyService.selectHjzzCaseMonthlyList(hjzzCaseMonthly);
        return getDataTable(list);
    }

    /**
     * 导出每月案件情况分析列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:export')")
    @Log(title = "每月案件情况分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HjzzCaseMonthly hjzzCaseMonthly)
    {
        List<HjzzCaseMonthly> list = hjzzCaseMonthlyService.selectHjzzCaseMonthlyList(hjzzCaseMonthly);
        ExcelUtil<HjzzCaseMonthly> util = new ExcelUtil<HjzzCaseMonthly>(HjzzCaseMonthly.class);
        util.exportExcel(response, list, "每月案件情况分析数据");
    }

    /**
     * 获取每月案件情况分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hjzzCaseMonthlyService.selectHjzzCaseMonthlyById(id));
    }

    /**
     * 新增每月案件情况分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:add')")
    @Log(title = "每月案件情况分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HjzzCaseMonthly hjzzCaseMonthly)
    {
        return toAjax(hjzzCaseMonthlyService.insertHjzzCaseMonthly(hjzzCaseMonthly));
    }

    /**
     * 修改每月案件情况分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:edit')")
    @Log(title = "每月案件情况分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HjzzCaseMonthly hjzzCaseMonthly)
    {
        return toAjax(hjzzCaseMonthlyService.updateHjzzCaseMonthly(hjzzCaseMonthly));
    }

    /**
     * 删除每月案件情况分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseMonthly:remove')")
    @Log(title = "每月案件情况分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hjzzCaseMonthlyService.deleteHjzzCaseMonthlyByIds(ids));
    }

    @GetMapping("/getHjzzCaseMonthlyDisposition")
    public AjaxResult getHjzzCaseMonthlyDisposition(HjzzCaseMonthly hjzzCaseMonthly)
    {
        HjzzCaseMonthly caseMonthly = hjzzCaseMonthlyService.selectHjzzCaseMonthlyByYearAndMonth(hjzzCaseMonthly);
        return AjaxResult.success(caseMonthly);
    }

    @PutMapping("/updateHjzzCaseMonthlyDisposition")
    public AjaxResult updateHjzzCaseMonthlyDisposition(@RequestBody HjzzCaseMonthly hjzzCaseMonthly)
    {
        HjzzCaseMonthly caseMonthly = hjzzCaseMonthlyService.selectHjzzCaseMonthlyByYearAndMonth(hjzzCaseMonthly);
        if (caseMonthly == null) {
            hjzzCaseMonthly.setCreateBy(getUsername());
            return toAjax(hjzzCaseMonthlyService.insertHjzzCaseMonthly(hjzzCaseMonthly));
        }
        hjzzCaseMonthly.setUpdateBy(getUsername());
        return toAjax(hjzzCaseMonthlyService.updateHjzzCaseMonthly(hjzzCaseMonthly));
    }
}
