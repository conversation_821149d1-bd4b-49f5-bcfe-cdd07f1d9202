package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyWxAccessToken;
import com.ruoyi.shcy.service.IShcyWxAccessTokenService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-04-18
 */
@RestController
@RequestMapping("/shcy/token")
public class ShcyWxAccessTokenController extends BaseController
{
    @Autowired
    private IShcyWxAccessTokenService shcyWxAccessTokenService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyWxAccessToken shcyWxAccessToken)
    {
        startPage();
        List<ShcyWxAccessToken> list = shcyWxAccessTokenService.selectShcyWxAccessTokenList(shcyWxAccessToken);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyWxAccessToken shcyWxAccessToken)
    {
        List<ShcyWxAccessToken> list = shcyWxAccessTokenService.selectShcyWxAccessTokenList(shcyWxAccessToken);
        ExcelUtil<ShcyWxAccessToken> util = new ExcelUtil<ShcyWxAccessToken>(ShcyWxAccessToken.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyWxAccessTokenService.selectShcyWxAccessTokenById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyWxAccessToken shcyWxAccessToken)
    {
        return toAjax(shcyWxAccessTokenService.insertShcyWxAccessToken(shcyWxAccessToken));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyWxAccessToken shcyWxAccessToken)
    {
        return toAjax(shcyWxAccessTokenService.updateShcyWxAccessToken(shcyWxAccessToken));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('shcy:token:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyWxAccessTokenService.deleteShcyWxAccessTokenByIds(ids));
    }
}
