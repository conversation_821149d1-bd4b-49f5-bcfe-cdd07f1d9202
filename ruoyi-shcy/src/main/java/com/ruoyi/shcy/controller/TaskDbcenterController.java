package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.dto.TaskDbcenterDTO;
import com.ruoyi.shcy.dto.TaskDbcenterImageDTO;
import com.ruoyi.shcy.service.IShcyWghService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 网格化案件信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@RestController
@RequestMapping("/shcy/dbcenter")
public class TaskDbcenterController extends BaseController
{
    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IShcyWghService shcyWghService;

    /**
     * 查询网格化案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("executedeptnames").toString())) {
            taskDbcenter.getParams().put("executedeptnames", taskDbcenter.getParams().get("executedeptnames").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterListDesc(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listDcz')")
    @GetMapping("/listDcz")
    public TableDataInfo listDcz(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setStatusname("待督办");
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listYja')")
    @GetMapping("/listYja")
    public TableDataInfo listYja(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setStatusname("已结案");
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listTd')")
    @GetMapping("/listTd")
    public TableDataInfo listTd(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setStatusname("已退回其他平台");
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listQjdc')")
    @GetMapping("/listQjdc")
    public TableDataInfo listQjdc(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setInfosourcename("区级网格上报");
        // taskDbcenter.getParams().put("typeName", "12345上报");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listJsqzxdc')")
    @GetMapping("/listJsqzxdc")
    public TableDataInfo listJsqzxdc(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setInfosourcename("金山区专项调查");
        // taskDbcenter.getParams().put("typeName", "12345上报");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listTfsj')")
    @GetMapping("/listTfsj")
    public TableDataInfo listTfsj(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
       taskDbcenter.getParams().put("dbcenterEmergentParam", true);
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return getDataTable(list);
    }


    /**
     * 查询网格化案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listWfp')")
    @GetMapping("/listWfp")
    public TableDataInfo listWfp(TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        taskDbcenter.setStatusname("待督办");
        taskDbcenter.setSubexecutedeptnameMh("石化街道综合行政执法队");
        taskDbcenter.setCirculationState("31");
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        // for(TaskDbcenter theTaskDbcenter:list)
        // {
        //     ShcyWgh theWgh=new ShcyWgh()
        //     theWgh.setWghId(theTaskDbcenter.getId());
        //
        //     List<ShcyWgh> wghList=shcyWghService.selectShcyWghList(theWgh);
        //
        //     if(!wghList.isEmpty())
        //     {
        //         theTaskDbcenter.setFpFlag("1");
        //         theTaskDbcenter.setCirculationState(wghList.get(0).getCirculationState());
        //     }
        //     else
        //     {
        //         theTaskDbcenter.setFpFlag("0");
        //         theTaskDbcenter.setCirculationState("3");
        //     }
        // }

        if (!list.isEmpty()) {
            // 收集所有TaskDbcenter的ID
            List<Long> taskDbcenterIds = list.stream().map(TaskDbcenter::getId).collect(Collectors.toList());

            // 一次性查询所有相关的ShcyWgh对象
            List<ShcyWgh> allRelatedWghs = shcyWghService.selectShcyWghListByTaskDbcenterIds(taskDbcenterIds);

            // 将ShcyWgh对象存储在一个Map中，键是TaskDbcenter的ID
            Map<Long, List<ShcyWgh>> wghMap = allRelatedWghs.stream().collect(Collectors.groupingBy(ShcyWgh::getWghId));

            // 遍历TaskDbcenter列表，使用Map来获取相关的ShcyWgh对象
            for (TaskDbcenter theTaskDbcenter : list) {
                List<ShcyWgh> wghList = wghMap.getOrDefault(theTaskDbcenter.getId(), Collections.emptyList());

                if (!wghList.isEmpty()) {
                    theTaskDbcenter.setFpFlag("1");
                } else {
                    theTaskDbcenter.setFpFlag("0");
                    theTaskDbcenter.setCirculationState("3");
                }
            }
        }

        return getDataTable(list);
    }

    /**
     * 导出网格化案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:export')")
    @Log(title = "网格化案件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("executedeptnames").toString())) {
            taskDbcenter.getParams().put("executedeptnames", taskDbcenter.getParams().get("executedeptnames").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterListDesc(taskDbcenter);
        List<TaskDbcenterDTO> exportList = BeanUtil.copyToList(list, TaskDbcenterDTO.class);
        ExcelUtil<TaskDbcenterDTO> util = new ExcelUtil<TaskDbcenterDTO>(TaskDbcenterDTO.class);
        util.exportExcel(response, exportList, "网格化案件信息数据");
    }

    /**
     * 导出网格化案件信息列表（图片）
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:export')")
    @Log(title = "网格化案件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportImage")
    public void exportImage(HttpServletResponse response, TaskDbcenter taskDbcenter, Boolean dbcenterFlagParam, Boolean dbcenterEmergentParam)
    {
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        if (dbcenterFlagParam != null) {
            taskDbcenter.getParams().put("dbcenterFlagParam", dbcenterFlagParam);
        }
        if (dbcenterEmergentParam != null) {
            taskDbcenter.getParams().put("dbcenterEmergentParam", dbcenterEmergentParam);
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("executedeptnames").toString())) {
            taskDbcenter.getParams().put("executedeptnames", taskDbcenter.getParams().get("executedeptnames").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterListDesc(taskDbcenter);
        List<TaskDbcenterImageDTO> exportList = BeanUtil.copyToList(list, TaskDbcenterImageDTO.class);
        exportList.forEach(item-> {
            if(CollUtil.isNotEmpty(item.getImagefilenames())){
                // 根据getImagefilenames的size set对应的images1、images2、images3的值
                for (int i = 0; i < item.getImagefilenames().size(); i++) {
                    if (i == 0) {
                        item.setImages1(item.getImagefilenames().get(i));
                    } else if (i == 1) {
                        item.setImages2(item.getImagefilenames().get(i));
                    } else if (i == 2) {
                        item.setImages3(item.getImagefilenames().get(i));
                    } else if (i == 3) {
                        item.setImages4(item.getImagefilenames().get(i));
                    } else if (i == 4) {
                        item.setImages5(item.getImagefilenames().get(i));
                    }
                }
            }
        });
        ExcelUtil<TaskDbcenterImageDTO> util = new ExcelUtil<TaskDbcenterImageDTO>(TaskDbcenterImageDTO.class);
        util.exportExcel(response, exportList, "网格化案件信息数据");
    }

    /**
     * 获取网格化案件信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(taskDbcenterService.selectTaskDbcenterById(id));
    }

    /**
     * 新增网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:add')")
    @Log(title = "网格化案件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskDbcenter taskDbcenter)
    {
        return toAjax(taskDbcenterService.insertTaskDbcenter(taskDbcenter));
    }

    /**
     * 修改网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:edit')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskDbcenter taskDbcenter)
    {
        return toAjax(taskDbcenterService.updateTaskDbcenter(taskDbcenter));
    }

    /**
     * 删除网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:remove')")
    @Log(title = "网格化案件信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(taskDbcenterService.deleteTaskDbcenterByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:sync')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/syncDbcenter")
    public AjaxResult syncDbcenter(@RequestBody String[] dataList)
    {
        return toAjax(taskDbcenterService.syncDbcenter(dataList));
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:fenpai')")
    @GetMapping("/getUserInfoForWgh")
    public  AjaxResult getUserInfoForWgh()
    {
        SysUser theUser=new SysUser();
        theUser.setDeptId(Long.parseLong("233"));
        List<SysUser> users=sysUserService.selectUserList(theUser);
        return AjaxResult.success(users);
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:zf')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/zf")
    public AjaxResult zf(@RequestBody TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setCirculationState("5");
        return toAjax(taskDbcenterService.updateTaskDbcenter(taskDbcenter));
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenter:listDbgd')")
    @GetMapping("/listDbgd")
    public TableDataInfo listDbgd(TaskDbcenter taskDbcenter)
    {
        // 处理时间范围参数
        Object beginTimeObj = taskDbcenter.getParams().get("beginTime");
        Object endTimeObj = taskDbcenter.getParams().get("endTime");
        if (beginTimeObj != null && cn.hutool.core.util.StrUtil.isNotEmpty(beginTimeObj.toString())) {
            taskDbcenter.getParams().put("beginTime", beginTimeObj.toString());
        }
        if (endTimeObj != null && cn.hutool.core.util.StrUtil.isNotEmpty(endTimeObj.toString())) {
            taskDbcenter.getParams().put("endTime", endTimeObj.toString());
        }
        // 处理执行部门参数
        Object executedeptnamesObj = taskDbcenter.getParams().get("executedeptnames");
        if (executedeptnamesObj != null && cn.hutool.core.util.StrUtil.isNotEmpty(executedeptnamesObj.toString())) {
            taskDbcenter.getParams().put("executedeptnames", executedeptnamesObj.toString().split(","));
        }
        taskDbcenter.setStatusname("待督办");
        // 排除12345上报
        taskDbcenter.getParams().put("typeName", "12345上报");
        // 排除兼职监督员上报
        taskDbcenter.getParams().put("typeName1", "兼职监督员上报");
        // 排除小区事务
        taskDbcenter.getParams().put("infobcname", "小区事务");
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterDbgdList(taskDbcenter);
        return getDataTable(list);
    }

}
