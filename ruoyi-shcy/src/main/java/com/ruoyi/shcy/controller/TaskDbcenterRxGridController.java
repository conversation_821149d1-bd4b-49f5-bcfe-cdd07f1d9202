package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.dto.TaskDbcenterRxGridDTO;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 综合网格案件信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/shcy/dbcenterRxGrid")
public class TaskDbcenterRxGridController extends BaseController
{
    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    /**
     * 查询12345热线案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxGrid:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskDbcenter taskDbcenter)
    {
        // 获取用户的部门
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 设置所属网格为用户的部门
        taskDbcenter.setStreetarea(deptName);

        if ("空缺".equals(taskDbcenter.getResidentialarea())) {
            taskDbcenter.getParams().put("vacancy", true);
            taskDbcenter.setResidentialarea(null);
        }
        if ("空缺".equals(taskDbcenter.getStreetarea())) {
            taskDbcenter.getParams().put("streetvacancy", true);
            taskDbcenter.setStreetarea(null);
        }
        if ("空缺".equals(taskDbcenter.getCommunity())) {
            taskDbcenter.getParams().put("communityvacancy", true);
            taskDbcenter.setCommunity(null);
        }
        if ("空缺".equals(taskDbcenter.getProperty())) {
            taskDbcenter.getParams().put("propertyvacancy", true);
            taskDbcenter.setProperty(null);
        }
        // 诉求大类空缺
        if ("空缺".equals(taskDbcenter.getParentappealclassification())) {
            taskDbcenter.getParams().put("parentappealclassificationvacancy", true);
            taskDbcenter.setParentappealclassification(null);
        }
        // 诉求小类空缺
        if ("空缺".equals(taskDbcenter.getParams().get("appealclassifications").toString())) {
            taskDbcenter.getParams().put("appealclassificationsvacancy", true);
            taskDbcenter.getParams().put("appealclassifications", null);
        } else {
            if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("appealclassifications").toString())) {
                taskDbcenter.getParams().put("appealclassifications", taskDbcenter.getParams().get("appealclassifications").toString().split(","));
            }
        }
        // 满意度
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }
        // 重点专项
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        taskDbcenter.setInfosourcename("12345上报");
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        return getDataTable(list);
    }

    /**
     * 导出12345热线案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxGrid:export')")
    @Log(title = "12345热线案件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskDbcenter taskDbcenter)
    {
        // 获取用户的部门
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 设置所属网格为用户的部门
        taskDbcenter.setStreetarea(deptName);

        if ("空缺".equals(taskDbcenter.getResidentialarea())) {
            taskDbcenter.getParams().put("vacancy", true);
            taskDbcenter.setResidentialarea(null);
        }
        if ("空缺".equals(taskDbcenter.getStreetarea())) {
            taskDbcenter.getParams().put("streetvacancy", true);
            taskDbcenter.setStreetarea(null);
        }
        if ("空缺".equals(taskDbcenter.getCommunity())) {
            taskDbcenter.getParams().put("communityvacancy", true);
            taskDbcenter.setCommunity(null);
        }
        if ("空缺".equals(taskDbcenter.getProperty())) {
            taskDbcenter.getParams().put("propertyvacancy", true);
            taskDbcenter.setProperty(null);
        }
        // 诉求大类空缺
        if ("空缺".equals(taskDbcenter.getParentappealclassification())) {
            taskDbcenter.getParams().put("parentappealclassificationvacancy", true);
            taskDbcenter.setParentappealclassification(null);
        }
        // 诉求小类空缺
        if ("空缺".equals(taskDbcenter.getParams().get("appealclassifications").toString())) {
            taskDbcenter.getParams().put("appealclassificationsvacancy", true);
            taskDbcenter.getParams().put("appealclassifications", null);
        } else {
            if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("appealclassifications").toString())) {
                taskDbcenter.getParams().put("appealclassifications", taskDbcenter.getParams().get("appealclassifications").toString().split(","));
            }
        }
        // 满意度
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }
        // 重点专项
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        taskDbcenter.setInfosourcename("12345上报");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        List<TaskDbcenterRxGridDTO> exportList = BeanUtil.copyToList(list, TaskDbcenterRxGridDTO.class);
        ExcelUtil<TaskDbcenterRxGridDTO> util = new ExcelUtil<TaskDbcenterRxGridDTO>(TaskDbcenterRxGridDTO.class);
        util.exportExcel(response, exportList, "综合网格工单信息数据");
    }
}
