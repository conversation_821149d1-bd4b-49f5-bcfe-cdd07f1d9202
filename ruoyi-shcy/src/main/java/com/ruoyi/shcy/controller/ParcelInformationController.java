package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ParcelInformation;
import com.ruoyi.shcy.service.IParcelInformationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 宗地信息Controller
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@RestController
@RequestMapping("/shcy/parcel")
public class ParcelInformationController extends BaseController
{
    @Autowired
    private IParcelInformationService parcelInformationService;

    /**
     * 查询宗地信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:list')")
    @GetMapping("/list")
    public TableDataInfo list(ParcelInformation parcelInformation)
    {
        startPage();
        List<ParcelInformation> list = parcelInformationService.selectParcelInformationList(parcelInformation);
        return getDataTable(list);
    }

    /**
     * 导出宗地信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:export')")
    @Log(title = "宗地信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ParcelInformation parcelInformation)
    {
        List<ParcelInformation> list = parcelInformationService.selectParcelInformationList(parcelInformation);
        ExcelUtil<ParcelInformation> util = new ExcelUtil<ParcelInformation>(ParcelInformation.class);
        util.exportExcel(response, list, "宗地信息数据");
    }

    /**
     * 获取宗地信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(parcelInformationService.selectParcelInformationById(id));
    }

    /**
     * 新增宗地信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:add')")
    @Log(title = "宗地信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ParcelInformation parcelInformation)
    {
        return toAjax(parcelInformationService.insertParcelInformation(parcelInformation));
    }

    /**
     * 修改宗地信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:edit')")
    @Log(title = "宗地信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ParcelInformation parcelInformation)
    {
        return toAjax(parcelInformationService.updateParcelInformation(parcelInformation));
    }

    /**
     * 删除宗地信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:parcel:remove')")
    @Log(title = "宗地信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(parcelInformationService.deleteParcelInformationByIds(ids));
    }
}
