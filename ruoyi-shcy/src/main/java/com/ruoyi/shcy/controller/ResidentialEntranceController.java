package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ResidentialEntrance;
import com.ruoyi.shcy.service.IResidentialEntranceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区出入门口Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/residentialEntrance")
public class ResidentialEntranceController extends BaseController
{
    @Autowired
    private IResidentialEntranceService residentialEntranceService;

    /**
     * 查询小区出入门口列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:list')")
    @GetMapping("/list")
    public TableDataInfo list(ResidentialEntrance residentialEntrance)
    {
        startPage();
        List<ResidentialEntrance> list = residentialEntranceService.selectResidentialEntranceList(residentialEntrance);
        return getDataTable(list);
    }

    /**
     * 导出小区出入门口列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:export')")
    @Log(title = "小区出入门口", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ResidentialEntrance residentialEntrance)
    {
        List<ResidentialEntrance> list = residentialEntranceService.selectResidentialEntranceList(residentialEntrance);
        ExcelUtil<ResidentialEntrance> util = new ExcelUtil<ResidentialEntrance>(ResidentialEntrance.class);
        util.exportExcel(response, list, "小区出入门口数据");
    }

    /**
     * 获取小区出入门口详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(residentialEntranceService.selectResidentialEntranceById(id));
    }

    /**
     * 新增小区出入门口
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:add')")
    @Log(title = "小区出入门口", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ResidentialEntrance residentialEntrance)
    {
        return toAjax(residentialEntranceService.insertResidentialEntrance(residentialEntrance));
    }

    /**
     * 修改小区出入门口
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:edit')")
    @Log(title = "小区出入门口", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ResidentialEntrance residentialEntrance)
    {
        return toAjax(residentialEntranceService.updateResidentialEntrance(residentialEntrance));
    }

    /**
     * 删除小区出入门口
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentialEntrance:remove')")
    @Log(title = "小区出入门口", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(residentialEntranceService.deleteResidentialEntranceByIds(ids));
    }
}
