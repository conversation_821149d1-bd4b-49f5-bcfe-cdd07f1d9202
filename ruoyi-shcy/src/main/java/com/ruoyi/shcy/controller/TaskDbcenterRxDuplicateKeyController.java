package com.ruoyi.shcy.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.domain.TaskDbcenter;

import com.ruoyi.shcy.domain.vo.DuplicateCategory;
import com.ruoyi.shcy.domain.vo.DuplicateSubCategory;
import com.ruoyi.shcy.domain.vo.WorkOrderDetail;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/shcy/dbcenterRxDuplicateKey")
public class TaskDbcenterRxDuplicateKeyController extends BaseController {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    /**
     * 查询重点重复工单列表
     */
    @GetMapping("/list")
    public AjaxResult list(TaskDbcenter taskDbcenter) {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.setIsduplicate("是");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);

        // 查询所有重复工单的首单列表
        List<TaskDbcenter> firstList = taskDbcenterService.selectTaskDbcenterRxDuplicateFirstList();
        // 将首单列表转换为 Map，方便后续查找 key为relatedhotlinesn，值为hotlinesn,如果有相同的relatedhotlinesn，则取第一个
        Map<String, String> firstMap = firstList.stream()
                .collect(Collectors.toMap(TaskDbcenter::getRelatedhotlinesn, TaskDbcenter::getHotlinesn, (a, b) -> a));

        // 1. 根据重点大类(parentduplicateclassification)分组，过滤掉为空的数据
        Map<String, List<TaskDbcenter>> parentDuplicateMap = list.stream()
                .filter(item -> item.getParentduplicateclassification() != null && 
                               !item.getParentduplicateclassification().trim().isEmpty())
                .collect(Collectors.groupingBy(TaskDbcenter::getParentduplicateclassification));

        List<DuplicateCategory> duplicateCategories = new ArrayList<>();
        
        // 先计算总重复次数，需要累加每个重点小类对应的重复次数
        int totalDuplicateCount = 0;

        for (Map.Entry<String, List<TaskDbcenter>> parentEntry : parentDuplicateMap.entrySet()) {
            String parentDuplicateClass = parentEntry.getKey();
            List<TaskDbcenter> parentItems = parentEntry.getValue();

            // 创建重点大类对象
            DuplicateCategory duplicateCategory = new DuplicateCategory();
            duplicateCategory.setParentduplicateclassification(parentDuplicateClass);
            duplicateCategory.setDuplicateCount(parentItems.size());

            // 2. 根据重点小类(duplicateclassification)分组
            Map<String, List<TaskDbcenter>> subDuplicateMap = parentItems.stream()
                    .collect(Collectors.groupingBy(item -> 
                        item.getDuplicateclassification() != null ? item.getDuplicateclassification() : ""));

            List<DuplicateSubCategory> duplicateSubCategories = new ArrayList<>();

            for (Map.Entry<String, List<TaskDbcenter>> subEntry : subDuplicateMap.entrySet()) {
                String subDuplicateClass = subEntry.getKey();
                List<TaskDbcenter> subItems = subEntry.getValue();

                // 创建重点小类对象
                DuplicateSubCategory duplicateSubCategory = new DuplicateSubCategory();
                duplicateSubCategory.setDuplicateclassification(subDuplicateClass);

                // 3. 根据relatedhotlinesn分组，每组取第一个对象
                Map<String, List<TaskDbcenter>> relatedHotlineMap = subItems.stream()
                        .filter(item -> item.getRelatedhotlinesn() != null && 
                                       !item.getRelatedhotlinesn().trim().isEmpty())
                        .collect(Collectors.groupingBy(TaskDbcenter::getRelatedhotlinesn));

                List<WorkOrderDetail> workOrderDetails = new ArrayList<>();

                for (Map.Entry<String, List<TaskDbcenter>> relatedEntry : relatedHotlineMap.entrySet()) {
                    String relatedHotline = relatedEntry.getKey();
                    List<TaskDbcenter> relatedItems = relatedEntry.getValue();

                    // 累加每个重复组的重复次数到总重复次数中
                    totalDuplicateCount += relatedItems.size();

                    // 取第一个对象
                    TaskDbcenter firstItem = relatedItems.get(0);

                    // 4. 创建WorkOrderDetail对象
                    WorkOrderDetail workOrderDetail = new WorkOrderDetail();
                    
                    // 诉求人对应TaskDbcenter中的reporter
                    workOrderDetail.setRequestItem(firstItem.getReporter());
                    
                    // 第一次工单号根据relatedhotlinesn去firstMap找到对应的首单号
                    String firstOrderNo = firstMap.get(relatedHotline);
                    workOrderDetail.setFirstOrderNo(firstOrderNo);
                    
                    // 最新一次工单号对应hotlinesn
                    workOrderDetail.setLatestOrderNo(firstItem.getHotlinesn());
                    
                    // 重复次数对应relatedhotlinesn分组后的数量
                    workOrderDetail.setDuplicateCount(relatedItems.size());

                    workOrderDetails.add(workOrderDetail);
                }

                duplicateSubCategory.setWorkOrderDetails(workOrderDetails);
                duplicateSubCategories.add(duplicateSubCategory);
            }

            duplicateCategory.setDuplicateSubCategories(duplicateSubCategories);
            duplicateCategories.add(duplicateCategory);
        }

        // 计算完所有重复次数后，再设置重复占比
        for (DuplicateCategory duplicateCategory : duplicateCategories) {
            for (DuplicateSubCategory duplicateSubCategory : duplicateCategory.getDuplicateSubCategories()) {
                for (WorkOrderDetail workOrderDetail : duplicateSubCategory.getWorkOrderDetails()) {
                    // 重复占比使用重复次数除以所有重复次数之和再结果乘以100保留两位小数
                    BigDecimal duplicateRatio = BigDecimal.valueOf(workOrderDetail.getDuplicateCount())
                            .divide(BigDecimal.valueOf(totalDuplicateCount), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    workOrderDetail.setDuplicateRatio(duplicateRatio);
                }
            }
        }

        // 直接返回重点大类列表，符合用户要求的JSON格式
        return AjaxResult.success(duplicateCategories);
    }

}
