package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.service.ILiquidLevelDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 液位超限感知设备Controller
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
@RestController
@RequestMapping("/shcy/liquidLevelDevice")
public class LiquidLevelDeviceController extends BaseController
{
    @Autowired
    private ILiquidLevelDeviceService liquidLevelDeviceService;

    /**
     * 查询液位超限感知设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(LiquidLevelDevice liquidLevelDevice)
    {
        startPage();
        List<LiquidLevelDevice> list = liquidLevelDeviceService.selectLiquidLevelDeviceList(liquidLevelDevice);
        return getDataTable(list);
    }

    /**
     * 导出液位超限感知设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:export')")
    @Log(title = "液位超限感知设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiquidLevelDevice liquidLevelDevice)
    {
        List<LiquidLevelDevice> list = liquidLevelDeviceService.selectLiquidLevelDeviceList(liquidLevelDevice);
        ExcelUtil<LiquidLevelDevice> util = new ExcelUtil<LiquidLevelDevice>(LiquidLevelDevice.class);
        util.exportExcel(response, list, "液位超限感知设备数据");
    }

    /**
     * 获取液位超限感知设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(liquidLevelDeviceService.selectLiquidLevelDeviceById(id));
    }

    /**
     * 新增液位超限感知设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:add')")
    @Log(title = "液位超限感知设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LiquidLevelDevice liquidLevelDevice)
    {
        return toAjax(liquidLevelDeviceService.insertLiquidLevelDevice(liquidLevelDevice));
    }

    /**
     * 修改液位超限感知设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:edit')")
    @Log(title = "液位超限感知设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LiquidLevelDevice liquidLevelDevice)
    {
        return toAjax(liquidLevelDeviceService.updateLiquidLevelDevice(liquidLevelDevice));
    }

    /**
     * 删除液位超限感知设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:liquidLevelDevice:remove')")
    @Log(title = "液位超限感知设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(liquidLevelDeviceService.deleteLiquidLevelDeviceByIds(ids));
    }

    @GetMapping("/fxft/address")
    public AjaxResult fxftAddress()
    {
        return AjaxResult.success(liquidLevelDeviceService.fxftAddress());
    }

    @GetMapping("/fxft/{deviceImei}")
    public AjaxResult fxftAddress(@PathVariable("deviceImei") String deviceImei)
    {
        LiquidLevelDevice theL=new LiquidLevelDevice();
        theL.setDeviceImei(deviceImei);
        List<LiquidLevelDevice> li=liquidLevelDeviceService.selectLiquidLevelDeviceList(theL);
        if(li.size()>0)
        {
            return AjaxResult.success(li.get(0));
        }
        return AjaxResult.success(new LiquidLevelDevice());
    }
}
