package com.ruoyi.shcy.controller;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 12345热线统计Controller
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/shcy/dbcenterRxCount")
public class TaskDbcenterRxCountController extends BaseController {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    /**
     * 判断用户部门是否是综合网格部门
     * return 布尔类型
     */
    private boolean isGridDepartment(String deptName) {
        // 获取字典数据
        List<SysDictData> data = dictTypeService.selectDictDataByType("dbcenter_streetarea");
        for (SysDictData dictData : data) {
            if (deptName.equals(dictData.getDictLabel())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据所属网格统计
     */
    @GetMapping("/count1")
    public AjaxResult count1(TaskDbcenter taskDbcenter) {
        taskDbcenter.setInfosourcename("12345上报");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        return AjaxResult.success(list);
    }

    /**
     * 根据所属网格统计居委会、诉求大类、诉求小类及小区数量
     */
    @GetMapping("/countGrid")
    public AjaxResult countGrid(TaskDbcenter taskDbcenter) {
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 判断用户部门是否是综合网格部门
        if (isGridDepartment(deptName)) {
            taskDbcenter.setStreetarea(deptName);
        }
        // 设置数据来源为12345上报
        taskDbcenter.setInfosourcename("12345上报");
        // 查询数据
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        
        // 如果没有数据，返回空数组
        if (list == null || list.isEmpty()) {
            return AjaxResult.success("[]");
        }
        
        // 按网格分组
        Map<String, List<TaskDbcenter>> gridMap = list.stream()
                .filter(item -> item.getStreetarea() != null && !item.getStreetarea().isEmpty())
                .collect(Collectors.groupingBy(TaskDbcenter::getStreetarea));
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 遍历每个网格
        for (Map.Entry<String, List<TaskDbcenter>> gridEntry : gridMap.entrySet()) {
            Map<String, Object> gridData = new HashMap<>();
            String gridName = gridEntry.getKey();
            List<TaskDbcenter> gridItems = gridEntry.getValue();
            
            gridData.put("grid", gridName);
            gridData.put("total_quantity", gridItems.size());
            
            // 按居委会分组
            Map<String, List<TaskDbcenter>> committeesMap = gridItems.stream()
                    .filter(item -> item.getResidentialarea() != null && !item.getResidentialarea().isEmpty())
                    .collect(Collectors.groupingBy(TaskDbcenter::getResidentialarea));
            
            List<Map<String, Object>> committeesList = new ArrayList<>();
            
            // 遍历每个居委会
            for (Map.Entry<String, List<TaskDbcenter>> committeeEntry : committeesMap.entrySet()) {
                Map<String, Object> committeeData = new HashMap<>();
                String committeeName = committeeEntry.getKey();
                List<TaskDbcenter> committeeItems = committeeEntry.getValue();
                
                committeeData.put("name", committeeName);
                committeeData.put("total_quantity", committeeItems.size());
                
                // 按诉求大类分组
                Map<String, List<TaskDbcenter>> appealCategoryMap = committeeItems.stream()
                        .filter(item -> item.getParentappealclassification() != null && !item.getParentappealclassification().isEmpty())
                        .collect(Collectors.groupingBy(TaskDbcenter::getParentappealclassification));
                
                List<Map<String, Object>> appealsList = new ArrayList<>();
                
                // 遍历每个诉求大类
                for (Map.Entry<String, List<TaskDbcenter>> appealCategoryEntry : appealCategoryMap.entrySet()) {
                    Map<String, Object> appealData = new HashMap<>();
                    String categoryName = appealCategoryEntry.getKey();
                    List<TaskDbcenter> categoryItems = appealCategoryEntry.getValue();
                    
                    appealData.put("category", categoryName);
                    appealData.put("quantity", categoryItems.size());
                    
                    // 按诉求小类分组
                    Map<String, List<TaskDbcenter>> subcategoryMap = categoryItems.stream()
                            .filter(item -> item.getAppealclassification() != null && !item.getAppealclassification().isEmpty())
                            .collect(Collectors.groupingBy(TaskDbcenter::getAppealclassification));
                    
                    List<Map<String, Object>> subcategoriesList = new ArrayList<>();
                    
                    // 遍历每个诉求小类
                    for (Map.Entry<String, List<TaskDbcenter>> subcategoryEntry : subcategoryMap.entrySet()) {
                        Map<String, Object> subcategoryData = new HashMap<>();
                        String subcategoryName = subcategoryEntry.getKey();
                        List<TaskDbcenter> subcategoryItems = subcategoryEntry.getValue();
                        
                        subcategoryData.put("name", subcategoryName);
                        subcategoryData.put("quantity", subcategoryItems.size());
                        
                        // 按小区分组 - 添加标准化处理
                        Map<String, Long> communitiesCount = subcategoryItems.stream()
                                .filter(item -> item.getCommunity() != null && !item.getCommunity().isEmpty())
                                .collect(Collectors.groupingBy(
                                        item -> item.getCommunity().trim(), // 去除可能的空格
                                        Collectors.counting()
                                ));
                        
                        // 为空或未指定小区的数据归类为"其他（小区外）"
                        long otherCount = subcategoryItems.stream()
                                .filter(item -> item.getCommunity() == null || item.getCommunity().isEmpty())
                                .count();
                        
                        List<Map<String, Object>> communitiesList = new ArrayList<>();
                        
                        // 添加各小区数据
                        for (Map.Entry<String, Long> communityEntry : communitiesCount.entrySet()) {
                            Map<String, Object> communityData = new HashMap<>();
                            communityData.put("name", communityEntry.getKey());
                            communityData.put("quantity", communityEntry.getValue());
                            communitiesList.add(communityData);
                        }
                        
                        // 如果有未归类的数据，添加"其他（小区外）"
                        if (otherCount > 0) {
                            Map<String, Object> otherData = new HashMap<>();
                            otherData.put("name", "其他（小区外）");
                            otherData.put("quantity", otherCount);
                            communitiesList.add(otherData);
                        }
                        
                        subcategoryData.put("communities", communitiesList);
                        subcategoriesList.add(subcategoryData);
                    }
                    
                    appealData.put("subcategories", subcategoriesList);
                    appealsList.add(appealData);
                }
                
                committeeData.put("appeals", appealsList);
                committeesList.add(committeeData);
            }
            
            gridData.put("residence_committees", committeesList);
            resultList.add(gridData);
        }
        
        // 将结果转为JSON字符串返回
        String jsonResult = JSON.toJSONString(resultList);
        return AjaxResult.success(resultList);
    }

    /**
     * 根据所属小区统计诉求大类和诉求小类数量
     */
    @GetMapping("/countCommunity")
    public AjaxResult countCommunity(TaskDbcenter taskDbcenter) {
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 判断用户部门是否是综合网格部门
        if (isGridDepartment(deptName)) {
            taskDbcenter.setStreetarea(deptName);
        }
        // 设置数据来源为12345上报
        taskDbcenter.setInfosourcename("12345上报");
        // 查询数据
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        
        // 如果没有数据，返回空数组
        if (list == null || list.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }
        
        // 按小区分组
        Map<String, List<TaskDbcenter>> communityMap = list.stream()
                .filter(item -> item.getCommunity() != null && !item.getCommunity().isEmpty())
                .collect(Collectors.groupingBy(TaskDbcenter::getCommunity));
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 遍历每个小区
        for (Map.Entry<String, List<TaskDbcenter>> communityEntry : communityMap.entrySet()) {
            Map<String, Object> communityData = new HashMap<>();
            String communityName = communityEntry.getKey();
            List<TaskDbcenter> communityItems = communityEntry.getValue();
            
            communityData.put("community", communityName);
            communityData.put("total_quantity", communityItems.size());
            
            // 按诉求大类分组
            Map<String, List<TaskDbcenter>> appealCategoryMap = communityItems.stream()
                    .filter(item -> item.getParentappealclassification() != null && !item.getParentappealclassification().isEmpty())
                    .collect(Collectors.groupingBy(TaskDbcenter::getParentappealclassification));
            
            List<Map<String, Object>> appealsList = new ArrayList<>();
            
            // 遍历每个诉求大类
            for (Map.Entry<String, List<TaskDbcenter>> appealCategoryEntry : appealCategoryMap.entrySet()) {
                Map<String, Object> appealData = new HashMap<>();
                String categoryName = appealCategoryEntry.getKey();
                List<TaskDbcenter> categoryItems = appealCategoryEntry.getValue();
                
                appealData.put("category", categoryName);
                appealData.put("quantity", categoryItems.size());
                
                // 按诉求小类分组
                Map<String, List<TaskDbcenter>> subcategoryMap = categoryItems.stream()
                        .filter(item -> item.getAppealclassification() != null && !item.getAppealclassification().isEmpty())
                        .collect(Collectors.groupingBy(TaskDbcenter::getAppealclassification));
                
                List<Map<String, Object>> subcategoriesList = new ArrayList<>();
                
                // 遍历每个诉求小类
                for (Map.Entry<String, List<TaskDbcenter>> subcategoryEntry : subcategoryMap.entrySet()) {
                    Map<String, Object> subcategoryData = new HashMap<>();
                    String subcategoryName = subcategoryEntry.getKey();
                    List<TaskDbcenter> subcategoryItems = subcategoryEntry.getValue();
                    
                    subcategoryData.put("name", subcategoryName);
                    subcategoryData.put("quantity", subcategoryItems.size());
                    
                    subcategoriesList.add(subcategoryData);
                }
                
                appealData.put("subcategories", subcategoriesList);
                appealsList.add(appealData);
            }
            
            communityData.put("appeals", appealsList);
            resultList.add(communityData);
        }
        
        return AjaxResult.success(resultList);
    }
    
    /**
     * 根据所属物业统计小区、诉求大类和诉求小类数量
     */
    @GetMapping("/countProperty")
    public AjaxResult countProperty(TaskDbcenter taskDbcenter) {
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 判断用户部门是否是综合网格部门
        if (isGridDepartment(deptName)) {
            taskDbcenter.setStreetarea(deptName);
        }

        // 设置主责部门为建管中心
        // taskDbcenter.setSubexecutedeptnameMh("石化街道建管中心");

        // 设置数据来源为12345上报
        taskDbcenter.setInfosourcename("12345上报");
        // 查询数据
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        
        // 如果没有数据，返回空数组
        if (list == null || list.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }

        // list中排除掉SubexecutedeptnameMh包含居民区的数据
        // list = list.stream()
        //         .filter(item -> item.getSubexecutedeptnameMh() != null && !item.getSubexecutedeptnameMh().contains("居民区"))
        //         .collect(Collectors.toList());

        // list中SubexecutedeptnameMh为石化街道建管中心、石化街道综合行政执法队、联勤联动、石化街道管违办、石化街道城运中心（应急）的数据
        Set<String> validDepts = new HashSet<>(Arrays.asList(
                "石化街道建管中心",
                "石化街道综合行政执法队",
                "联勤联动",
                "石化街道管违办",
                "石化街道城运中心（应急）"));

        list = list.stream()
                .filter(item -> item.getSubexecutedeptnameMh() != null &&
                        validDepts.contains(item.getSubexecutedeptnameMh()))
                .collect(Collectors.toList());
        
        // 按物业分组
        Map<String, List<TaskDbcenter>> propertyMap = list.stream()
                .filter(item -> item.getProperty() != null && !item.getProperty().isEmpty())
                .collect(Collectors.groupingBy(TaskDbcenter::getProperty));
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 遍历每个物业
        for (Map.Entry<String, List<TaskDbcenter>> propertyEntry : propertyMap.entrySet()) {
            Map<String, Object> propertyData = new HashMap<>();
            String propertyName = propertyEntry.getKey();
            List<TaskDbcenter> propertyItems = propertyEntry.getValue();
            
            propertyData.put("property_management", propertyName);
            propertyData.put("total_quantity", propertyItems.size());
            
            // 按小区分组
            Map<String, List<TaskDbcenter>> communityMap = propertyItems.stream()
                    .filter(item -> item.getCommunity() != null && !item.getCommunity().isEmpty())
                    .collect(Collectors.groupingBy(TaskDbcenter::getCommunity));
            
            List<Map<String, Object>> communitiesList = new ArrayList<>();
            
            // 遍历每个小区
            for (Map.Entry<String, List<TaskDbcenter>> communityEntry : communityMap.entrySet()) {
                Map<String, Object> communityData = new HashMap<>();
                String communityName = communityEntry.getKey();
                List<TaskDbcenter> communityItems = communityEntry.getValue();
                
                communityData.put("name", communityName);
                communityData.put("total_quantity", communityItems.size());
                
                // 按诉求大类分组
                Map<String, List<TaskDbcenter>> appealCategoryMap = communityItems.stream()
                        .filter(item -> item.getParentappealclassification() != null && !item.getParentappealclassification().isEmpty())
                        .collect(Collectors.groupingBy(TaskDbcenter::getParentappealclassification));
                
                List<Map<String, Object>> appealsList = new ArrayList<>();
                
                // 遍历每个诉求大类
                for (Map.Entry<String, List<TaskDbcenter>> appealCategoryEntry : appealCategoryMap.entrySet()) {
                    Map<String, Object> appealData = new HashMap<>();
                    String categoryName = appealCategoryEntry.getKey();
                    List<TaskDbcenter> categoryItems = appealCategoryEntry.getValue();
                    
                    appealData.put("category", categoryName);
                    appealData.put("quantity", categoryItems.size());
                    
                    // 按诉求小类分组
                    Map<String, List<TaskDbcenter>> subcategoryMap = categoryItems.stream()
                            .filter(item -> item.getAppealclassification() != null && !item.getAppealclassification().isEmpty())
                            .collect(Collectors.groupingBy(TaskDbcenter::getAppealclassification));
                    
                    List<Map<String, Object>> subcategoriesList = new ArrayList<>();
                    
                    // 遍历每个诉求小类
                    for (Map.Entry<String, List<TaskDbcenter>> subcategoryEntry : subcategoryMap.entrySet()) {
                        Map<String, Object> subcategoryData = new HashMap<>();
                        String subcategoryName = subcategoryEntry.getKey();
                        List<TaskDbcenter> subcategoryItems = subcategoryEntry.getValue();
                        
                        subcategoryData.put("name", subcategoryName);
                        subcategoryData.put("quantity", subcategoryItems.size());
                        
                        subcategoriesList.add(subcategoryData);
                    }
                    
                    appealData.put("subcategories", subcategoriesList);
                    appealsList.add(appealData);
                }
                
                communityData.put("appeals", appealsList);
                communitiesList.add(communityData);
            }
            
            propertyData.put("communities", communitiesList);
            resultList.add(propertyData);
        }
        
        return AjaxResult.success(resultList);
    }
    
    /**
     * 根据满意度统计网格和居委会数据
     * 满意度计算公式：（满意 + 基本满意*0.8 + 一般*0.6）/ （满意 + 基本满意 + 一般 + 不满意）*100
     */
    @GetMapping("/countSatisfaction")
    public AjaxResult countSatisfaction(TaskDbcenter taskDbcenter) {
        LoginUser loginUser = getLoginUser();
        String deptName = loginUser.getUser().getDept().getDeptName();
        // 判断用户部门是否是综合网格部门
        if (isGridDepartment(deptName)) {
            taskDbcenter.setStreetarea(deptName);
        }
        // 设置数据来源为12345上报
        taskDbcenter.setInfosourcename("12345上报");
        // 查询数据
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        
        // 如果没有数据，返回空数组
        if (list == null || list.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }
        
        // 过滤掉没有满意度的数据
        list = list.stream()
                .filter(item -> item.getSatisfaction() != null && !item.getSatisfaction().isEmpty())
                .collect(Collectors.toList());
        
        if (list.isEmpty()) {
            return AjaxResult.success(new ArrayList<>());
        }
        
        // 按网格分组
        Map<String, List<TaskDbcenter>> gridMap = list.stream()
                .filter(item -> item.getStreetarea() != null && !item.getStreetarea().isEmpty())
                .collect(Collectors.groupingBy(TaskDbcenter::getStreetarea));
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 遍历每个网格
        for (Map.Entry<String, List<TaskDbcenter>> gridEntry : gridMap.entrySet()) {
            Map<String, Object> gridData = new HashMap<>();
            String gridName = gridEntry.getKey();
            List<TaskDbcenter> gridItems = gridEntry.getValue();
            
            gridData.put("grid", gridName);
            
            // 按居委会分组
            Map<String, List<TaskDbcenter>> committeesMap = gridItems.stream()
                    .filter(item -> item.getResidentialarea() != null && !item.getResidentialarea().isEmpty())
                    .collect(Collectors.groupingBy(TaskDbcenter::getResidentialarea));
            
            List<Map<String, Object>> committeesList = new ArrayList<>();
            
            // 遍历每个居委会
            for (Map.Entry<String, List<TaskDbcenter>> committeeEntry : committeesMap.entrySet()) {
                Map<String, Object> committeeData = new HashMap<>();
                String committeeName = committeeEntry.getKey();
                List<TaskDbcenter> committeeItems = committeeEntry.getValue();
                
                committeeData.put("name", committeeName);
                
                // 统计不同满意度的数量
                long satisfied = committeeItems.stream()
                        .filter(item -> "满意".equals(item.getSatisfaction()))
                        .count();
                
                long basicallySatisfied = committeeItems.stream()
                        .filter(item -> "基本满意".equals(item.getSatisfaction()))
                        .count();
                
                long neutral = committeeItems.stream()
                        .filter(item -> "一般".equals(item.getSatisfaction()))
                        .count();
                
                long dissatisfied = committeeItems.stream()
                        .filter(item -> "不满意".equals(item.getSatisfaction()))
                        .count();
                
                // 计算满意度百分比
                double satisfactionRate = 0.0;
                long total = satisfied + basicallySatisfied + neutral + dissatisfied;
                
                if (total > 0) {
                    double weightedSum = satisfied + basicallySatisfied * 0.8 + neutral * 0.6;
                    satisfactionRate = (weightedSum / total) * 100;
                }
                
                // 使用DecimalFormat格式化为两位小数，不足两位补0
                DecimalFormat df = new DecimalFormat("0.00");
                String formattedRate = df.format(satisfactionRate);
                
                committeeData.put("satisfied", satisfied);
                committeeData.put("basically_satisfied", basicallySatisfied);
                committeeData.put("neutral", neutral);
                committeeData.put("dissatisfied", dissatisfied);
                committeeData.put("satisfaction_rate", formattedRate + "%");
                
                committeesList.add(committeeData);
            }
            
            gridData.put("residence_committees", committeesList);
            resultList.add(gridData);
        }
        
        return AjaxResult.success(resultList);
    }
}
