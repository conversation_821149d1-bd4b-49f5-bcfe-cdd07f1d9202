package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.PersonnelGrid;
import com.ruoyi.shcy.service.IPersonnelGridService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 两类人员网格化Controller
 * 
 * <AUTHOR>
 * @date 2023-07-18
 */
@RestController
@RequestMapping("/shcy/personnelGrid")
public class PersonnelGridController extends BaseController
{
    @Autowired
    private IPersonnelGridService personnelGridService;

    /**
     * 查询两类人员网格化列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonnelGrid personnelGrid)
    {
        startPage();
        List<PersonnelGrid> list = personnelGridService.selectPersonnelGridList(personnelGrid);
        return getDataTable(list);
    }

    /**
     * 导出两类人员网格化列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:export')")
    @Log(title = "两类人员网格化", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonnelGrid personnelGrid)
    {
        List<PersonnelGrid> list = personnelGridService.selectPersonnelGridList(personnelGrid);
        ExcelUtil<PersonnelGrid> util = new ExcelUtil<PersonnelGrid>(PersonnelGrid.class);
        util.exportExcel(response, list, "两类人员网格化数据");
    }

    /**
     * 获取两类人员网格化详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(personnelGridService.selectPersonnelGridById(id));
    }

    /**
     * 新增两类人员网格化
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:add')")
    @Log(title = "两类人员网格化", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelGrid personnelGrid)
    {
        return toAjax(personnelGridService.insertPersonnelGrid(personnelGrid));
    }

    /**
     * 修改两类人员网格化
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:edit')")
    @Log(title = "两类人员网格化", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonnelGrid personnelGrid)
    {
        return toAjax(personnelGridService.updatePersonnelGrid(personnelGrid));
    }

    /**
     * 删除两类人员网格化
     */
    @PreAuthorize("@ss.hasPermi('shcy:personnelGrid:remove')")
    @Log(title = "两类人员网格化", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personnelGridService.deletePersonnelGridByIds(ids));
    }
}
