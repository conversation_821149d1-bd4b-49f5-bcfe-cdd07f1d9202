package com.ruoyi.shcy.controller;


import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.HistoryAlarmRecordVO;
import com.ruoyi.nbiotyun.domain.NbAlarmRecordVO;
import com.ruoyi.nbiotyun.service.NbiotyunService;
import com.ruoyi.shcy.domain.HistoryAlarmRecord;
import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import com.ruoyi.shcy.dto.HistoryAlarmRecordCountDTO;
import com.ruoyi.shcy.dto.HistoryAlarmRecordQuery;
import com.ruoyi.shcy.service.IHistoryAlarmRecordService;
import com.ruoyi.shcy.service.ILiquidLevelDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/shcy/nbAlarmRecord")
public class NbAlarmRecordController extends BaseController {

    @Autowired
    private NbiotyunService nbiotyunService;

    @Autowired
    private ILiquidLevelDeviceService liquidLevelDeviceService;

    @Autowired
    private IHistoryAlarmRecordService historyAlarmRecordService;

    /**
     * 查询报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbAlarmRecord:list')")
    @GetMapping("/list")
    public AjaxResult list(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        String startTime = nbiotyunAlarmRecord.getParams().get("beginTime").toString();
        String endTime = nbiotyunAlarmRecord.getParams().get("endTime").toString();
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(null);
        // 将liquidLevelDevices转换为Map，key为deviceImei，value为LiquidLevelDevice
        Map<String, LiquidLevelDevice> liquidLevelDeviceMap = liquidLevelDevices.stream()
                .collect(Collectors.toMap(LiquidLevelDevice::getDeviceImei, Function.identity()));

        List<HistoryAlarmRecordCountDTO> historyAlarmRecordCountDTOS = historyAlarmRecordService.selectHistoryAlarmRecordCount();
        // 将historyAlarmRecordCountDTOS转换为Map，key为deviceImei，value为count
        Map<Long, Integer> historyAlarmRecordCountMap = historyAlarmRecordCountDTOS.stream()
                .filter(historyAlarmRecordCountDTO -> historyAlarmRecordCountDTO.getLiquidLevelDeviceId() != null)
                .collect(Collectors.toMap(HistoryAlarmRecordCountDTO::getLiquidLevelDeviceId, HistoryAlarmRecordCountDTO::getCount));

        // 按deviceImei分组，转换为NbAlarmRecordVO对象
        List<NbAlarmRecordVO> voList = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei))
                .entrySet().stream()
                .map(entry -> {
                    String deviceImei = entry.getKey();
                    List<AlarmRecordDTO> groupedList = entry.getValue();
                    
                    NbAlarmRecordVO vo = new NbAlarmRecordVO();
                    vo.setDeviceImei(deviceImei);
                    vo.setTodayAlarmNum(groupedList.size());
                    
                    // 获取最新的报警时间
                    String latestAlarmTime = groupedList.stream()
                            .max(Comparator.comparing(AlarmRecordDTO::getAlarmTime))
                            .map(AlarmRecordDTO::getAlarmTime)
                            .orElse("");
                    vo.setAlarmTime(latestAlarmTime);

                    // 计算各时段报警数量
                    int lateNightCount = 0;      // 夜间时段 (22:00-08:00)
                    int morningCount = 0;        // 上午时段 (08:00-12:00)
                    int afternoonCount = 0;      // 下午时段 (12:00-15:00)
                    int eveningCount = 0;        // 傍晚时段 (15:00-18:00)
                    int earlyNightCount = 0;     // 夜间前段 (18:00-22:00)

                    for (AlarmRecordDTO alarmRecord : groupedList) {
                        String alarmTimeStr = alarmRecord.getAlarmTime();
                        if (alarmTimeStr != null && alarmTimeStr.length() >= 19) {
                            try {
                                // 提取时间部分 HH:mm:ss
                                String timeStr = alarmTimeStr.substring(11, 19);
                                LocalTime alarmTime = LocalTime.parse(timeStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
                                
                                if (isInTimeRange(alarmTime, LocalTime.of(0, 0), LocalTime.of(7, 59, 59))) {
                                    // 夜间时段 (22:00-08:00)
                                    lateNightCount++;
                                } else if (isInTimeRange(alarmTime, LocalTime.of(8, 0), LocalTime.of(11, 59, 59))) {
                                    // 上午时段 (08:00-12:00)
                                    morningCount++;
                                } else if (isInTimeRange(alarmTime, LocalTime.of(12, 0), LocalTime.of(14, 59, 59))) {
                                    // 下午时段 (12:00-15:00)
                                    afternoonCount++;
                                } else if (isInTimeRange(alarmTime, LocalTime.of(15, 0), LocalTime.of(17, 59, 59))) {
                                    // 傍晚时段 (15:00-18:00)
                                    eveningCount++;
                                } else if (isInTimeRange(alarmTime, LocalTime.of(18, 0), LocalTime.of(23, 59, 59))) {
                                    // 夜间前段 (18:00-22:00)
                                    earlyNightCount++;
                                }
                            } catch (Exception e) {
                                // 时间格式解析异常，跳过该记录
                                logger.warn("报警时间格式解析异常: {}", alarmTimeStr, e);
                            }
                        }
                    }

                    vo.setLateNightAlarmCount(lateNightCount);
                    vo.setMorningAlarmCount(morningCount);
                    vo.setAfternoonAlarmCount(afternoonCount);
                    vo.setEveningAlarmCount(eveningCount);
                    vo.setEarlyNightAlarmCount(earlyNightCount);

                    LiquidLevelDevice liquidLevelDevice = liquidLevelDeviceMap.get(deviceImei);
                    vo.setDeviceName(liquidLevelDevice.getDeviceName());
                    vo.setMonitoredWaterBody(liquidLevelDevice.getMonitoredWaterBody());
                    vo.setPipelineType(liquidLevelDevice.getPipelineType());
                    vo.setDeviceState(liquidLevelDevice.getDeviceState());

                    // 获取历史告警次数 如果为空则设置为0
                    Integer historyAlarmRecordCount = historyAlarmRecordCountMap.get(liquidLevelDevice.getId());
                    if (historyAlarmRecordCount == null) {
                        historyAlarmRecordCount = 0;
                    }
                    vo.setHistoryAlarmNum(historyAlarmRecordCount);
                    
                    return vo;
                })
                .collect(Collectors.toList());
        
        return AjaxResult.success(voList);
    }

    /**
     * 判断时间是否在指定范围内
     */
    private boolean isInTimeRange(LocalTime time, LocalTime start, LocalTime end) {
        return !time.isBefore(start) && !time.isAfter(end);
    }

    /**
     * 查询历史报警记录列表
     */
    @GetMapping("/history/list")
    public AjaxResult historyList(HistoryAlarmRecord historyAlarmRecord, HistoryAlarmRecordQuery historyAlarmRecordQuery)
    {
        // 查询时间段内的报警数量
        List<HistoryAlarmRecord> list = historyAlarmRecordService.selectHistoryAlarmRecordList(historyAlarmRecord);
        Map<Long, Integer> alarmRecordCountMap = list.stream()
                .filter(har -> har.getLiquidLevelDeviceId() != null)
                .collect(Collectors.groupingBy(HistoryAlarmRecord::getLiquidLevelDeviceId, Collectors.counting()))
                .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().intValue()));

        // 查询历史报警数量
        List<HistoryAlarmRecordCountDTO> historyAlarmRecordCountDTOS = historyAlarmRecordService.selectHistoryAlarmRecordCount();
        Map<Long, Integer> historyAlarmRecordCountMap = historyAlarmRecordCountDTOS.stream()
                .filter(historyAlarmRecordCountDTO -> historyAlarmRecordCountDTO.getLiquidLevelDeviceId() != null)
                .collect(Collectors.toMap(HistoryAlarmRecordCountDTO::getLiquidLevelDeviceId, HistoryAlarmRecordCountDTO::getCount));

        // 获取所有设备信息用于数据补充
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(null);

        // 根据HistoryAlarmRecordQuery中的deviceName（模糊查询）、pipelineType（精确查询）、monitoredWaterBody（精确查询）过滤liquidLevelDevices，各自不为空时进行过滤
        if (StrUtil.isNotEmpty(historyAlarmRecordQuery.getDeviceName())) {
            liquidLevelDevices = liquidLevelDevices.stream()
                    .filter(liquidLevelDevice -> liquidLevelDevice.getDeviceName().contains(historyAlarmRecordQuery.getDeviceName()))
                    .collect(Collectors.toList());
        }
        if (StrUtil.isNotEmpty(historyAlarmRecordQuery.getPipelineType())) {
            liquidLevelDevices = liquidLevelDevices.stream()
                    .filter(liquidLevelDevice -> liquidLevelDevice.getPipelineType().equals(historyAlarmRecordQuery.getPipelineType()))
                    .collect(Collectors.toList());
        }
        if (StrUtil.isNotEmpty(historyAlarmRecordQuery.getMonitoredWaterBody())) {
            liquidLevelDevices = liquidLevelDevices.stream()
                    .filter(liquidLevelDevice -> liquidLevelDevice.getMonitoredWaterBody().equals(historyAlarmRecordQuery.getMonitoredWaterBody()))
                    .collect(Collectors.toList());
        }

        // 将liquidLevelDevices转为List<HistoryAlarmRecordVO>, alarmNum为alarmRecordCountMap中对应的值，historyAlarmNum为historyAlarmRecordCountMap中对应的值
        List<HistoryAlarmRecordVO> voList = liquidLevelDevices.stream()
                .map(liquidLevelDevice -> {
                    HistoryAlarmRecordVO vo = new HistoryAlarmRecordVO();
                    vo.setDeviceImei(liquidLevelDevice.getDeviceImei());
                    vo.setDeviceName(liquidLevelDevice.getDeviceName());
                    vo.setMonitoredWaterBody(liquidLevelDevice.getMonitoredWaterBody());
                    vo.setDrainageDirection(liquidLevelDevice.getDrainageDirection());
                    vo.setPipelineType(liquidLevelDevice.getPipelineType());
                    vo.setAlarmNum(alarmRecordCountMap.getOrDefault(liquidLevelDevice.getId(), 0));
                    vo.setHistoryAlarmNum(historyAlarmRecordCountMap.getOrDefault(liquidLevelDevice.getId(), 0));
                    return vo;
                })
                .collect(Collectors.toList());

        return AjaxResult.success(voList);
    }

    /**
     * 获取当天报警情况 - 大屏使用
     */
    @GetMapping("/today")
    public AjaxResult today()
    {
        String startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(null);
        // 将liquidLevelDevices转换为Map，key为deviceImei，value为LiquidLevelDevice
        Map<String, LiquidLevelDevice> liquidLevelDeviceMap = liquidLevelDevices.stream()
                .collect(Collectors.toMap(LiquidLevelDevice::getDeviceImei, Function.identity()));

        // 按deviceImei分组，转换为NbAlarmRecordVO对象
        List<NbAlarmRecordVO> voList = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei))
                .entrySet().stream()
                .map(entry -> {
                    String deviceImei = entry.getKey();
                    List<AlarmRecordDTO> groupedList = entry.getValue();

                    NbAlarmRecordVO vo = new NbAlarmRecordVO();
                    vo.setDeviceImei(deviceImei);
                    vo.setTodayAlarmNum(groupedList.size());

                    LiquidLevelDevice liquidLevelDevice = liquidLevelDeviceMap.get(deviceImei);
                    vo.setDeviceName(liquidLevelDevice.getDeviceName());
                    vo.setMonitoredWaterBody(liquidLevelDevice.getMonitoredWaterBody());
                    vo.setPipelineType(liquidLevelDevice.getPipelineType());

                    return vo;
                })
                .collect(Collectors.toList());

        return AjaxResult.success(voList);
    }

    /**
     * 获取今日统计、本月统计、本年统计
     */
    @GetMapping("/getAlertData")
    public AjaxResult getAlertData() {

        // 1.获取液位设备信息
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(new LiquidLevelDevice());
        Map<String, LiquidLevelDevice> liquidLevelDeviceMap = liquidLevelDevices.stream()
                .collect(Collectors.toMap(LiquidLevelDevice::getDeviceImei, Function.identity()));
        Map<Long, LiquidLevelDevice> liquidLevelDeviceIdMap = liquidLevelDevices.stream()
                .collect(Collectors.toMap(LiquidLevelDevice::getId, Function.identity()));

        // 今日统计总数、今日TOP5总数
        String startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        // 1. 计算今日统计总数
        int todayTotalCount = filteredResult.size();
        
        // 1.1 计算今日雨水统计总数和今日污水统计总数
        int todayRainwaterCount = 0;
        int todaySewageCount = 0;
        for (AlarmRecordDTO alarmRecord : filteredResult) {
            LiquidLevelDevice device = liquidLevelDeviceMap.get(alarmRecord.getDeviceImei());
            if (device != null && device.getMonitoredWaterBody() != null) {
                if (device.getMonitoredWaterBody().contains("雨水")) {
                    todayRainwaterCount++;
                } else if (device.getMonitoredWaterBody().contains("污水")) {
                    todaySewageCount++;
                }
            }
        }
        
        // 2. 计算今日TOP5的设备点位及对应的总数（按deviceImei分组）
        List<Map<String, Object>> todayTop5 = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei, Collectors.counting()))
                .entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    Map<String, Object> deviceStats = new HashMap<>();
                    String deviceImei = entry.getKey();
                    Long count = entry.getValue();
                    LiquidLevelDevice device = liquidLevelDeviceMap.get(deviceImei);
                    String deviceName = device != null ? device.getDeviceName() : "未知设备";
                    String monitoredWaterBody = device != null ? device.getMonitoredWaterBody() : "未知";
                    
                    deviceStats.put("deviceName", deviceName);
                    deviceStats.put("count", count.intValue());
                    deviceStats.put("monitoredWaterBody", monitoredWaterBody);
                    return deviceStats;
                })
                .collect(Collectors.toList());

        // 本月、本年统计总数、本月、本年TOP5总数
        List<HistoryAlarmRecord> historyAlarmRecordList = historyAlarmRecordService.selectHistoryAlarmRecordList(new HistoryAlarmRecord());

        // 获取当前时间用于本月、本年计算
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime monthStart = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime yearStart = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);

        // 3. 计算本月统计总数和TOP5
        List<HistoryAlarmRecord> monthlyRecords = historyAlarmRecordList.stream()
                .filter(record -> {
                    if (record.getAlarmTime() == null) return false;
                    LocalDateTime alarmTime = record.getAlarmTime().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    return !alarmTime.isBefore(monthStart);
                })
                .collect(Collectors.toList());
        
        int monthlyTotalCount = monthlyRecords.size();
        
        // 3.1 计算本月雨水统计总数和本月污水统计总数
        int monthlyRainwaterCount = 0;
        int monthlySewageCount = 0;
        for (HistoryAlarmRecord record : monthlyRecords) {
            if (record.getLiquidLevelDeviceId() != null) {
                LiquidLevelDevice device = liquidLevelDeviceIdMap.get(record.getLiquidLevelDeviceId());
                if (device != null && device.getMonitoredWaterBody() != null) {
                    if (device.getMonitoredWaterBody().contains("雨水")) {
                        monthlyRainwaterCount++;
                    } else if (device.getMonitoredWaterBody().contains("污水")) {
                        monthlySewageCount++;
                    }
                }
            }
        }
        
        List<Map<String, Object>> monthlyTop5 = monthlyRecords.stream()
                .collect(Collectors.groupingBy(HistoryAlarmRecord::getLiquidLevelDeviceId, Collectors.counting()))
                .entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    Map<String, Object> deviceStats = new HashMap<>();
                    Long liquidLevelDeviceId = entry.getKey();
                    Long count = entry.getValue();
                    LiquidLevelDevice device = liquidLevelDeviceIdMap.get(liquidLevelDeviceId);
                    String deviceName = device != null ? device.getDeviceName() : "未知设备";
                    String monitoredWaterBody = device != null ? device.getMonitoredWaterBody() : "未知";
                    
                    deviceStats.put("deviceName", deviceName);
                    deviceStats.put("count", count.intValue());
                    deviceStats.put("monitoredWaterBody", monitoredWaterBody);
                    return deviceStats;
                })
                .collect(Collectors.toList());

        // 4. 计算本年统计总数和TOP5
        List<HistoryAlarmRecord> yearlyRecords = historyAlarmRecordList.stream()
                .filter(record -> {
                    if (record.getAlarmTime() == null) return false;
                    LocalDateTime alarmTime = record.getAlarmTime().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    return !alarmTime.isBefore(yearStart);
                })
                .collect(Collectors.toList());
        
        int yearlyTotalCount = yearlyRecords.size();
        
        // 4.1 计算本年雨水统计总数和本年污水统计总数
        int yearlyRainwaterCount = 0;
        int yearlySewageCount = 0;
        for (HistoryAlarmRecord record : yearlyRecords) {
            if (record.getLiquidLevelDeviceId() != null) {
                LiquidLevelDevice device = liquidLevelDeviceIdMap.get(record.getLiquidLevelDeviceId());
                if (device != null && device.getMonitoredWaterBody() != null) {
                    if (device.getMonitoredWaterBody().contains("雨水")) {
                        yearlyRainwaterCount++;
                    } else if (device.getMonitoredWaterBody().contains("污水")) {
                        yearlySewageCount++;
                    }
                }
            }
        }
        
        List<Map<String, Object>> yearlyTop5 = yearlyRecords.stream()
                .filter(record -> record.getLiquidLevelDeviceId() != null)
                .collect(Collectors.groupingBy(HistoryAlarmRecord::getLiquidLevelDeviceId, Collectors.counting()))
                .entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    Map<String, Object> deviceStats = new HashMap<>();
                    Long liquidLevelDeviceId = entry.getKey();
                    Long count = entry.getValue();
                    LiquidLevelDevice device = liquidLevelDeviceIdMap.get(liquidLevelDeviceId);
                    String deviceName = device != null ? device.getDeviceName() : "未知设备";
                    String monitoredWaterBody = device != null ? device.getMonitoredWaterBody() : "未知";
                    
                    deviceStats.put("deviceName", deviceName);
                    deviceStats.put("count", count.intValue());
                    deviceStats.put("monitoredWaterBody", monitoredWaterBody);
                    return deviceStats;
                })
                .collect(Collectors.toList());

        // 5. 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("todayTotalCount", todayTotalCount);
        resultMap.put("todayTop5", todayTop5);
        resultMap.put("monthlyTotalCount", monthlyTotalCount);
        resultMap.put("monthlyTop5", monthlyTop5);
        resultMap.put("yearlyTotalCount", yearlyTotalCount);
        resultMap.put("yearlyTop5", yearlyTop5);
        
        // 添加雨水和污水统计数据
        resultMap.put("todayRainwaterCount", todayRainwaterCount);
        resultMap.put("todaySewageCount", todaySewageCount);
        resultMap.put("monthlyRainwaterCount", monthlyRainwaterCount);
        resultMap.put("monthlySewageCount", monthlySewageCount);
        resultMap.put("yearlyRainwaterCount", yearlyRainwaterCount);
        resultMap.put("yearlySewageCount", yearlySewageCount);
        
        return AjaxResult.success(resultMap);
    }

}
