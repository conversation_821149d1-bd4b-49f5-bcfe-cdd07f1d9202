package com.ruoyi.nbiotyun.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@Data
public class AlarmRecordDTO {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "deviceImei")
    private String deviceImei;

    @JSONField(name = "deviceEventName")
    private String deviceEventName;

    @JSONField(name = "deviceAttrValue")
    private String deviceAttrValue;

    @JSONField(name = "deviceTypeName")
    private String deviceTypeName;

    @JSO<PERSON>ield(name = "deviceState")
    private String deviceState;

    @JSO<PERSON>ield(name = "detailedLocation")
    private String detailedLocation;

    @JSONField(name = "alarmReason")
    private String alarmReason;

    @JSONField(name = "deviceVersionName")
    private String deviceVersionName;

    @JSONField(name = "alarmTime")
    private String alarmTime;

    @J<PERSON>NField(name = "handler")
    private String handler;

    @J<PERSON><PERSON><PERSON>(name = "handleTime")
    private String handleTime;

    @J<PERSON><PERSON>ield(name = "deptName")
    private String deptName;

    @JSONField(name = "remark")
    private String remark;

    @JSONField(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(name = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
