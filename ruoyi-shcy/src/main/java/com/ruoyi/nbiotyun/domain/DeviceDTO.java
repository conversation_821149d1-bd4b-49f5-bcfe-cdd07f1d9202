package com.ruoyi.nbiotyun.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DeviceDTO {

    @JSONField(name = "deviceId")
    private Long deviceId;

    @JSONField(name = "deviceImei")
    private String deviceImei;

    @JSONField(name = "deviceImsi")
    private String deviceImsi;

    @JSONField(name = "iccid")
    private String iccid;

    @JSONField(name = "registerSign")
    private int registerSign;

    @JSONField(name = "deviceTypeName")
    private String deviceTypeName;

    @J<PERSON><PERSON>ield(name = "deviceModelName")
    private String deviceModelName;

    @JSONField(name = "state")
    private int state;

    @JSONField(name = "latitude")
    private double latitude;

    @JSONField(name = "longitude")
    private double longitude;

    @JSONField(name = "installAddress")
    private String installAddress;

    @JSONField(name = "contact")
    private String contact;

    @JSONField(name = "companyName")
    private String companyName;

    @JSONField(name = "phonenumber")
    private String phonenumber;

    @JSONField(name = "roomName")
    private String roomName;

    @JSONField(name = "region")
    private String region;

    @JSONField(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(name = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
