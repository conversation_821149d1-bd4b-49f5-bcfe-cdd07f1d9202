package com.ruoyi.nbiotyun.domain;

import lombok.Data;

/**
 * 报警记录VO对象
 *
 * <AUTHOR>
 * @date 2025/06/26
 */
@Data
public class NbAlarmRecordVO {

    /**
     * 设备编号
     */
    private String deviceImei;

    /**
     * 设备点位
     */
    private String deviceName;

    /**
     * 监测水体
     */
    private String monitoredWaterBody;

    /**
     * 管网类型
     */
    private String pipelineType;

    /**
     * 设备状态
     */
    private String deviceState;

    /**
     * 夜间时段报警数量 (22:00-08:00)
     */
    private Integer lateNightAlarmCount;

    /**
     * 上午时段报警数量 (08:00-12:00)
     */
    private Integer morningAlarmCount;

    /**
     * 下午时段报警数量 (12:00-15:00)
     */
    private Integer afternoonAlarmCount;

    /**
     * 傍晚时段报警数量 (15:00-18:00)
     */
    private Integer eveningAlarmCount;

    /**
     * 夜间前段报警数量 (18:00-22:00)
     */
    private Integer earlyNightAlarmCount;

    /**
     * 当日报警数
     */
    private Integer todayAlarmNum;

    /**
     * 历史报警数
     */
    private Integer historyAlarmNum;

    /**
     * 最新报警时间
     */
    private String alarmTime;

}
