package com.ruoyi.nbiotyun.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.nbiotyun.config.NbiotyunConfig;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.DeviceDTO;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.*;
import com.ruoyi.shcy.dto.NbiotyunAlarmRecordDTO;
import com.ruoyi.shcy.mapper.*;
import com.ruoyi.system.service.ISysConfigService;
import lombok.AllArgsConstructor;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * nbiotyun服务
 *
 * <AUTHOR>
 * @date 2023/08/31
 */
@Component
@AllArgsConstructor
public class NbiotyunService {

    /**
     * 查询-获取设备列表信息接口URL
     */
    private final String DEV_GET_LIST_URL = "/api/v1/dev/getList";

    /**
     * 查询-设备报警信息接口URL
     */
    private final String DEV_ALARM_RECORD_URL = "/api/v1/dev/alarmRecord";

    /**
     * nbiotyun配置
     */
    private final NbiotyunConfig nbiotyunConfig;

    /**
     * nbiotyun设备映射器
     */
    private final NbiotyunDeviceMapper nbiotyunDeviceMapper;

    private final NbiotyunAlarmRecordMapper nbiotyunAlarmRecordMapper;

    private final LiquidLevelDeviceMapper liquidLevelDeviceMapper;

    private final FxftCaseMapper fxfxCaseMapper;

    private final SmsRecordMapper smsRecordMapper;

    private final ISysConfigService configService;

    private final HistoryAlarmRecordMapper historyAlarmRecordMapper;

    /**
     * 查询-获取设备列表信息接口
     *
     * @return {@link List}<{@link DeviceDTO}>
     */
    public List<DeviceDTO> getList(int pageNum, int pageSize, int fullFlag) {

        // 请求url
        String requestUrl = nbiotyunConfig.getHost() + DEV_GET_LIST_URL;

        // 请求头
        String timestamp = Convert.toStr(System.currentTimeMillis());
        String signature = SecureUtil.md5(DEV_GET_LIST_URL + timestamp + nbiotyunConfig.getAppSecret());
        String appKey = nbiotyunConfig.getAppKey();

        // 请求参数
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("fullFlag", fullFlag);
        String jsonStr = JSON.toJSONString(map);

        // 请求
        String responseStr = HttpRequest.post(requestUrl)
                .header("timestamp", timestamp)
                .header("signature", signature)
                .header("appKey", appKey)
                .body(jsonStr)
                .execute().body();

        // 解析
        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray dataArray = jsonObject.getJSONObject("message").getJSONArray("data");
        List<DeviceDTO> deviceList = dataArray.toJavaList(DeviceDTO.class);
        return deviceList;
    }

    /**
     * 查询-设备报警信息接口
     *
     * @param pageNum   页码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link DeviceDTO}>
     */
    public List<AlarmRecordDTO> alarmRecord(int pageNum, String startTime, String endTime) {

        // 请求url
        String requestUrl = nbiotyunConfig.getHost() + DEV_ALARM_RECORD_URL;

        // 请求头
        String timestamp = Convert.toStr(System.currentTimeMillis());
        String signature = SecureUtil.md5(DEV_ALARM_RECORD_URL + timestamp + nbiotyunConfig.getAppSecret());
        String appKey = nbiotyunConfig.getAppKey();

        // 获取液位设备的imeiList
        List<String> imeiList = liquidLevelDeviceMapper.selectImeiList();

        // List<String> imeiList = Arrays.asList(
        //         "865424050715347",
        //         "867219050609765",
        //         "867219050623204",
        //         "865424050587621",
        //         "867219050499746",
        //         "867219050873809",
        //         "867219050745445",
        //         "867219050830783",
        //         "867219050403672",
        //         "867219050928843",
        //         "867219050618600",
        //         "867219050494143",
        //         "867219050831088",
        //         "867219050985884",
        //         "867219050616521",
        //         "867219050609500",
        //         "867219050618469",
        //         "867219050482189",
        //         "867219050752672",
        //         "867219050499993",
        //         "867219050928926",
        //         "863882046591274",
        //         "867219050627932",
        //         "867219050562477",
        //         "867219050928967",
        //         "867219050550480",
        //         "867219050929312",
        //         "867219050416310"
        // );

        // 请求参数
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageNum", pageNum);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("imeiList", imeiList);
        String jsonStr = JSON.toJSONString(map);

        // 请求
        String responseStr = HttpRequest.post(requestUrl)
                .header("timestamp", timestamp)
                .header("signature", signature)
                .header("appKey", appKey)
                .body(jsonStr)
                .execute().body();

        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray dataArray = jsonObject.getJSONArray("message");
        List<AlarmRecordDTO> alarmRecordList = dataArray.toJavaList(AlarmRecordDTO.class);
        return alarmRecordList;
    }

    /**
     * 同步nbiotyun设备
     *
     * @param deviceDTO 设备dto
     */
    public void syncNbiotyunDevice(DeviceDTO deviceDTO) {
        NbiotyunDevice nbiotyunDevice = nbiotyunDeviceMapper.selectNbiotyunDeviceByDeviceId(deviceDTO.getDeviceId());
        boolean exist = true;
        if (nbiotyunDevice == null) {
            nbiotyunDevice = new NbiotyunDevice();
            exist = false;
        }
        BeanUtil.copyProperties(deviceDTO, nbiotyunDevice);
        if (exist) {
            nbiotyunDeviceMapper.updateNbiotyunDevice(nbiotyunDevice);
        } else {
            nbiotyunDeviceMapper.insertNbiotyunDevice(nbiotyunDevice);
        }
    }

    /**
     * 同步nbiotyun报警记录
     *
     * @param alarmRecordDTO 报警记录dto
     */
    public void syncNbiotyunAlarmRecord(List<AlarmRecordDTO> alarmRecordDTOList) {

        for (AlarmRecordDTO alarmRecordDTO : alarmRecordDTOList) {
            // 判断alarmRecordDTO的deviceAttrValue是否包含液位超限
            if (!alarmRecordDTO.getDeviceAttrValue().contains("液位超限")) {
                continue;
            }
            // 去掉最后的 ".000"
            if (alarmRecordDTO.getAlarmTime().contains(".")) {
                alarmRecordDTO.setAlarmTime(alarmRecordDTO.getAlarmTime().substring(0, alarmRecordDTO.getAlarmTime().indexOf('.')));
            }
            // 获取历史最新的报警记录
            NbiotyunAlarmRecord alarmRecord = nbiotyunAlarmRecordMapper.getAlarmRecordByImei(alarmRecordDTO.getDeviceImei());
            if (alarmRecord != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime dateTime1 = LocalDateTime.parse(alarmRecordDTO.getAlarmTime(), formatter);
                // 去掉最后的 ".000"
                if (alarmRecord.getAlarmTime().contains(".")) {
                    alarmRecord.setAlarmTime(alarmRecord.getAlarmTime().substring(0, alarmRecord.getAlarmTime().indexOf('.')));
                }
                LocalDateTime dateTime2 = LocalDateTime.parse(alarmRecord.getAlarmTime(), formatter);
                if (dateTime1.isBefore(dateTime2)) {
                    continue;
                }
                Duration duration = Duration.between(dateTime1, dateTime2);
                long hoursDifference = duration.toHours();
                // 液位告警重复屏蔽时限
                String timeLimit = configService.selectConfigByKey("shcy.fxft.timeLimit");
                if (Math.abs(hoursDifference) < Integer.parseInt(timeLimit)) {
                    continue;
                }
            }
            NbiotyunAlarmRecord nbiotyunAlarmRecord = nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(alarmRecordDTO.getId());
            boolean exist = (nbiotyunAlarmRecord != null);
            if (exist) {
                continue;
            }
            if (!exist) {
                nbiotyunAlarmRecord = new NbiotyunAlarmRecord();
            }
            BeanUtil.copyProperties(alarmRecordDTO, nbiotyunAlarmRecord);
            LiquidLevelDevice liquidLevelDevice = liquidLevelDeviceMapper.selectLiquidLevelDeviceByDeviceImei(nbiotyunAlarmRecord.getDeviceImei());
            if (liquidLevelDevice == null) {
                continue;
            }
            if ("市政管网".equals(liquidLevelDevice.getPipelineType())) {
                nbiotyunAlarmRecord.setResponsiblePerson("锦石市政");
            } else if ("小区三级管网".equals(liquidLevelDevice.getPipelineType())) {
                nbiotyunAlarmRecord.setResponsiblePerson("建管中心");
            }
            nbiotyunAlarmRecord.setStatus(FxftConstants.UNPROCESSED);
            nbiotyunAlarmRecordMapper.insertNbiotyunAlarmRecord(nbiotyunAlarmRecord);
            // 自动流转
            createFxftCase(nbiotyunAlarmRecord);
            // 发送短信
            sendSmsNotification(liquidLevelDevice.getPipelineType());
        }

    }

    /**
     * 创建防汛防台案件
     *
     * @param nbiotyunAlarmRecord nbiotyun报警记录
     */
    private void createFxftCase(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        FxftCase fxftCase = new FxftCase();
        fxftCase.setCaseName("液位超限报警");
        fxftCase.setCaseType("设备报警");
        NbiotyunAlarmRecordDTO nbiotyunAlarmRecordDTO = new NbiotyunAlarmRecordDTO();
        nbiotyunAlarmRecordDTO.setDeviceAttrValue(nbiotyunAlarmRecord.getDeviceAttrValue());
        fxftCase.setCaseDescription(nbiotyunAlarmRecordDTO.getAlarmReason());
        fxftCase.setCaseDealBy(nbiotyunAlarmRecord.getResponsiblePerson());
        fxftCase.setCirculationState(FxftConstants.UNPROCESSED);
        fxftCase.setDealInTimeState("0");
        fxftCase.setAddress(nbiotyunAlarmRecord.getDetailedLocation());
        fxftCase.setAlarmRecordId(nbiotyunAlarmRecord.getId());

        Date now = new Date();
        fxftCase.setCaseEndTime(DateUtils.addHours(now, 24));
        fxftCase.setDeviceImei(nbiotyunAlarmRecord.getDeviceImei());
        fxftCase.setCaseNumber(DateUtil.format(now, "yyyyMMdd") + RandomUtil.randomNumbers(10));
        // 自动流转没有创建人
        // fxftCase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        fxftCase.setCreateTime(now);

        fxfxCaseMapper.insertFxftCase(fxftCase);
    }

    /**
     * 发送短信
     *
     * @param pipelineType 管网类型
     */
    private void sendSmsNotification(String pipelineType) {
        // 定义数组
        List<String> phoneList = new ArrayList<>();
        // 根据管网类型获取对应的手机号
        if ("市政管网".equals(pipelineType)) {
            phoneList.add(SmsConstants.PHONE_NUMBER_NIJUN);
        } else if ("小区三级管网".equals(pipelineType)) {
            phoneList.add(SmsConstants.PHONE_NUMBER_FANHUI);
            phoneList.add(SmsConstants.PHONE_NUMBER_WENGLIANJUN);
        }
        phoneList.add(SmsConstants.PHONE_NUMBER_ZHOUBING);
        phoneList.add(SmsConstants.PHONE_NUMBER_YANGYUJI);
        phoneList.add(SmsConstants.PHONE_NUMBER_JINXIAOLEI);
        phoneList.add(SmsConstants.PHONE_NUMBER_ZHANGPEI);
        phoneList.add(SmsConstants.PHONE_NUMBER_LVZHENGFEI);
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        phoneList.forEach(phone -> {
            smsBlend.sendMessageAsync(phone, SmsConstants.TEMPLATE_CODE_YWGJ, new LinkedHashMap<>());
        });
    }

    /**
     * 同步数据
     *
     * @param result 结果
     */
    public void syncData(List<AlarmRecordDTO> result, String date, String date1) {

        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        List<String> imeiList = liquidLevelDevices.stream().map(LiquidLevelDevice::getDeviceImei).collect(Collectors.toList());

        // 从result中过滤掉1.deviceImei不在imeiList中的数据2.!alarmRecordDTO.getDeviceAttrValue().contains("液位超限"),生成新的List
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> imeiList.contains(alarmRecordDTO.getDeviceImei()))
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        // 从filteredResult中对alarmRecordDTO.getDeviceImei()分组，每组取alarmRecordDTO.getAlarmTime()最新的一条数据，生成新的List
        List<AlarmRecordDTO> groupedResult = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei))
                .values().stream()
                .map(group -> group.stream().max(Comparator.comparing(AlarmRecordDTO::getAlarmTime)).get())
                .collect(Collectors.toList());

        for (AlarmRecordDTO alarmRecordDTO : groupedResult) {
            // 去掉最后的 ".000"
            if (alarmRecordDTO.getAlarmTime().contains(".")) {
                alarmRecordDTO.setAlarmTime(alarmRecordDTO.getAlarmTime().substring(0, alarmRecordDTO.getAlarmTime().indexOf('.')));
            }

            NbiotyunAlarmRecord nbiotyunAlarmRecord = nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(alarmRecordDTO.getId());
            boolean exist = (nbiotyunAlarmRecord != null);
            if (exist) {
                continue;
            }
            if (!exist) {
                nbiotyunAlarmRecord = new NbiotyunAlarmRecord();
            }
            BeanUtil.copyProperties(alarmRecordDTO, nbiotyunAlarmRecord);

            // 从liquidLevelDevices中找到对应imei的liquidLevelDevice.getPipelineType()判断
            LiquidLevelDevice liquidLevelDevice = liquidLevelDevices.stream().filter(item -> item.getDeviceImei().equals(alarmRecordDTO.getDeviceImei())).findFirst().get();
            if (liquidLevelDevice == null) {
                continue;
            }
            if ("市政管网".equals(liquidLevelDevice.getPipelineType())) {
                nbiotyunAlarmRecord.setResponsiblePerson("锦石市政");
            } else if ("小区三级管网".equals(liquidLevelDevice.getPipelineType())) {
                nbiotyunAlarmRecord.setResponsiblePerson("建管中心");
            }
            nbiotyunAlarmRecord.setStatus(FxftConstants.UNPROCESSED);
            nbiotyunAlarmRecordMapper.insertNbiotyunAlarmRecord(nbiotyunAlarmRecord);
            // 自动流转
            createFxftCase(nbiotyunAlarmRecord);
        }

        // 发送短信
        sendSms(filteredResult, date, date1);

    }

    private void sendSms(List<AlarmRecordDTO> filteredResult, String date, String date1) {
        // IMEI 到参数名的映射
        Map<String, String> imeiToParamMap = new LinkedHashMap<>();
        imeiToParamMap.put("867219050494143", "n1");
        imeiToParamMap.put("865424050715347", "n2");
        imeiToParamMap.put("867219050831088", "n3");
        imeiToParamMap.put("867219050403672", "n4");
        imeiToParamMap.put("867219050985884", "n5");
        imeiToParamMap.put("867219050616521", "n6");
        imeiToParamMap.put("867219050609500", "n7");
        imeiToParamMap.put("867219050618469", "n8");
        imeiToParamMap.put("867219050830783", "n9");
        imeiToParamMap.put("867219050482189", "n10");
        imeiToParamMap.put("867219050752672", "n11");
        imeiToParamMap.put("867219050499746", "n12");
        imeiToParamMap.put("867219050618600", "n13");
        imeiToParamMap.put("867219050609765", "n14");
        imeiToParamMap.put("867219050745445", "n15");
        imeiToParamMap.put("867219050873809", "n16");
        imeiToParamMap.put("865424050587621", "n17");
        imeiToParamMap.put("867219050623204", "n18");
        imeiToParamMap.put("867219050928843", "n19");

        // 按 IMEI 分组统计数量
        Map<String, Long> imeiCountMap = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei, Collectors.counting()));

        // 构建短信参数
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : imeiToParamMap.entrySet()) {
            String imei = entry.getKey();
            String paramName = entry.getValue();
            Long count = imeiCountMap.getOrDefault(imei, 0L);
            params.put(paramName, String.valueOf(count));
        }

        // 检查是否所有参数都为0
        boolean allZero = params.values().stream().allMatch(value -> "0".equals(value));
        if (allZero) {
            return; // 如果所有参数都是0，则不发送短信
        }
        params.put("d1", date);
        params.put("d2", date1);

        // 新增8个参数s1 - s8
        // s1：n1到n11的值大于0的数量（个数）
        // s2：n1到n11的值大于0的值的求和
        String[] n1ToN11 = {"n1", "n2", "n3", "n4", "n5", "n6", "n7", "n8", "n9", "n10", "n11"};
        int s1Count = 0;
        int s2Sum = 0;
        for (String paramName : n1ToN11) {
            int value = Integer.parseInt(params.get(paramName));
            if (value > 0) {
                s1Count++;
                s2Sum += value;
            }
        }

        // s3：n12到n13的值大于0的数量（个数）
        // s4：n12到n13的值大于0的值的求和
        String[] n12ToN13 = {"n12", "n13"};
        int s3Count = 0;
        int s4Sum = 0;
        for (String paramName : n12ToN13) {
            int value = Integer.parseInt(params.get(paramName));
            if (value > 0) {
                s3Count++;
                s4Sum += value;
            }
        }

        // s5：n14到n18的值大于0的数量（个数）
        // s6：n14到n18的值大于0的值的求和
        String[] n14ToN18 = {"n14", "n15", "n16", "n17", "n18"};
        int s5Count = 0;
        int s6Sum = 0;
        for (String paramName : n14ToN18) {
            int value = Integer.parseInt(params.get(paramName));
            if (value > 0) {
                s5Count++;
                s6Sum += value;
            }
        }

        int n19Count = 0;
        int n19Sum = 0;
        int n19value = Integer.parseInt(params.get("n19"));
        if (n19value > 0) {
            n19Count++;
            n19Sum += n19value;
        }
        // s7：s1 + s3 + s5 + n19Count
        int s7Count = s1Count + s3Count + s5Count + n19Count;
        // s8：s2 + s4 + s6 + n19Sum
        int s8Sum = s2Sum + s4Sum + s6Sum + n19Sum;

        params.put("s1", String.valueOf(s1Count));
        params.put("s2", String.valueOf(s2Sum));
        params.put("s3", String.valueOf(s3Count));
        params.put("s4", String.valueOf(s4Sum));
        params.put("s5", String.valueOf(s5Count));
        params.put("s6", String.valueOf(s6Sum));
        params.put("s7", String.valueOf(s7Count));
        params.put("s8", String.valueOf(s8Sum));

        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");

        // 模板1：SMS_489145272 - 发送全部参数 n1-n19
        // List<String> template1Phones = Arrays.asList("13801976380", "13482612567", "13311630751");
        List<String> template1Phones = Arrays.asList("18217278774", "13817684929");
        template1Phones.forEach(phone -> {
            smsBlend.sendMessageAsync(phone, SmsConstants.TEMPLATE_CODE_YWGJ_FULL, params);
        });
        List<String> smsRecordPhones = Arrays.asList("18217278774");
        generateSmsRecord(smsRecordPhones, SmsConstants.TEMPLATE_CODE_YWGJ_FULL, params);

        // 模板2：TEMPLATE_CODE_YWGJ_PART1 - 发送部分参数
        LinkedHashMap<String, String> params2 = new LinkedHashMap<>();
        String[] template2Params = {"n1", "n2", "n3", "n4", "n5", "n6", "n7", "n8", "n9", "n10", "n11", "n14", "n15", "n16", "n17", "n18"};
        boolean hasNonZeroValue2 = false;
        for (String paramName : template2Params) {
            String value = params.get(paramName);
            params2.put(paramName, value);
            if (!"0".equals(value)) {
                hasNonZeroValue2 = true;
            }
        }
        if (hasNonZeroValue2) {
            params2.put("date", date);
            params2.put("date1", date1);

            // 模版2的s1：n1到n11的值大于0的数量（个数）
            // 模版2的s2：n1到n11的值大于0的值的求和
            int s1Count2 = 0;
            int s2Sum2 = 0;
            for (String paramName : n1ToN11) {
                int value = Integer.parseInt(params2.get(paramName));
                if (value > 0) {
                    s1Count2++;
                    s2Sum2 += value;
                }
            }

            // s5 s6
            int s5Count2 = 0;
            int s6Sum2 = 0;
            for (String paramName : n14ToN18) {
                int value = Integer.parseInt(params2.get(paramName));
                if (value > 0) {
                    s5Count2++;
                    s6Sum2 += value;
                }
            }

            // s7 s8
            int s7Count2 = s1Count2 + s5Count2;
            int s8Sum2 = s2Sum2 + s6Sum2;

            params2.put("s1", String.valueOf(s1Count2));
            params2.put("s2", String.valueOf(s2Sum2));
            params2.put("s5", String.valueOf(s5Count2));
            params2.put("s6", String.valueOf(s6Sum2));
            params2.put("s7", String.valueOf(s7Count2));
            params2.put("s8", String.valueOf(s8Sum2));

            // List<String> template2Phones = Arrays.asList("13501744941");
            List<String> template2Phones = Arrays.asList("18217278774");
            template2Phones.forEach(phone ->{
                smsBlend.sendMessageAsync(phone, SmsConstants.TEMPLATE_CODE_YWGJ_PART1, params2);
            });
        }

        // 模板3：TEMPLATE_CODE_YWGJ_PART2 - 发送部分参数
        LinkedHashMap<String, String> params3 = new LinkedHashMap<>();
        String[] template3Params = {"n12", "n13", "n19"};
        boolean hasNonZeroValue3 = false;
        for (String paramName : template3Params) {
            String value = params.get(paramName);
            params3.put(paramName, value);
            if (!"0".equals(value)) {
                hasNonZeroValue3 = true;
            }
        }
        if (hasNonZeroValue3) {
            params3.put("date", date);
            params3.put("date1", date1);

            // s3 s4
            int s3Count3 = 0;
            int s4Sum3 = 0;
            for (String paramName : n12ToN13) {
                int value = Integer.parseInt(params3.get(paramName));
                if (value > 0) {
                    s3Count3++;
                    s4Sum3 += value;
                }
            }

            // n19
            int n19Count3 = 0;
            int n19Sum3 = 0;
            int n19value3 = Integer.parseInt(params3.get("n19"));
            if (n19value3 > 0) {
                n19Count3++;
                n19Sum3 += n19value3;
            }

            // s7 s8
            int s7Count3 = s3Count3 + n19Count3;
            int s8Sum3 = s4Sum3 + n19Sum3;

            params3.put("s3", String.valueOf(s3Count3));
            params3.put("s4", String.valueOf(s4Sum3));
            params3.put("s7", String.valueOf(s7Count3));
            params3.put("s8", String.valueOf(s8Sum3));

            // List<String> template3Phones = Arrays.asList("13162125588", "13816601912");
            List<String> template3Phones = Arrays.asList("18217278774");
            template3Phones.forEach(phone -> {
                smsBlend.sendMessageAsync(phone, SmsConstants.TEMPLATE_CODE_YWGJ_PART2, params3);
            });
        }
    }

    public void syncLiquidLevelDeviceAlarmStatus(List<AlarmRecordDTO> result) {
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        List<String> imeiList = liquidLevelDevices.stream().map(LiquidLevelDevice::getDeviceImei).collect(Collectors.toList());

        // 从result中过滤掉1.deviceImei不在imeiList中的数据2.!alarmRecordDTO.getDeviceAttrValue().contains("液位超限"),生成新的List
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> imeiList.contains(alarmRecordDTO.getDeviceImei()))
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        // 从filteredResult中对alarmRecordDTO.getDeviceImei()分组，每组取alarmRecordDTO.getAlarmTime()最新的一条数据，生成新的List
        List<AlarmRecordDTO> groupedResult = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei))
                .values().stream()
                .map(group -> group.stream().max(Comparator.comparing(AlarmRecordDTO::getAlarmTime)).get())
                .collect(Collectors.toList());

        // 遍历liquidLevelDevices, 如果liquidLevelDevice.getDeviceImei()在groupedResult中，则设置liquidLevelDevice.setAlarmStatus("1")，否则设置liquidLevelDevice.setAlarmStatus("0")
        liquidLevelDevices.forEach(liquidLevelDevice -> {
            if (groupedResult.stream().anyMatch(alarmRecordDTO -> alarmRecordDTO.getDeviceImei().equals(liquidLevelDevice.getDeviceImei()))) {
                liquidLevelDevice.setAlarmStatus("1");
            } else {
                liquidLevelDevice.setAlarmStatus("0");
            }
        });

        // 批量更新liquidLevelDevices
        liquidLevelDeviceMapper.batchUpdateLiquidLevelDevice(liquidLevelDevices);
    }

    public void syncDeviceStatus(List<DeviceDTO> result) {
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        // 将result 转为Map key为imei，value为state
        Map<String, Integer> deviceStatusMap = result.stream().collect(Collectors.toMap(DeviceDTO::getDeviceImei, DeviceDTO::getState));

        // 遍历liquidLevelDevices， 如果liquidLevelDevice.getDeviceImei()在deviceStatusMap且value为4 ,设置为离线，否则全部设置为在线
        liquidLevelDevices.forEach(liquidLevelDevice -> {
            if (deviceStatusMap.containsKey(liquidLevelDevice.getDeviceImei()) && deviceStatusMap.get(liquidLevelDevice.getDeviceImei()) == 4) {
                liquidLevelDevice.setDeviceState("离线");
            } else {
                liquidLevelDevice.setDeviceState("在线");
            }
        });
        liquidLevelDeviceMapper.batchUpdateLiquidLevelDeviceState(liquidLevelDevices);
    }

    /**
     * 生成短信发送记录
     */
    private void generateSmsRecord(List<String> phoneList, String templateCode, LinkedHashMap<String, String> params) {

        phoneList.forEach(phone -> {
            SmsRecord smsRecord = new SmsRecord();
            smsRecord.setPhoneNumber(phone);

            // 根据templateCode判断对于应的短信内容  参数使用params中对应的key的值
            // TEMPLATE_CODE_YWGJ_FULL：时间${d1} 时间段-${d2}内液位设备告警信息如下：（${s7}个${s8}次） 一、市政污水（${s1}个${s2}次） 1.秀林街告警${n1}次 2.新城合浦路口告警${n2}次 3.龙临街小李子门口告警${n3}次 4.临潮三村出口告警${n4}次 5.卫清西路1029号树昕门口告警${n5}次 6.桥英馨苑南门对面告警${n6}次 7.紫薇苑南门对面告警${n7}次 8.东泉街蒙山路东侧告警${n8}次 9.金一东路兰墅酒店告警${n9}次 10.施三路施二路路口告警${n10}次 11.板桥西路滨海二村出门井告警${n11}次 二、小区污水（${s3}个${s4}次） 12.十二村西门北侧告警${n12}次 13.东礁二村门口告警${n13}次 三、市政雨水（${s5}个${s6}次） 14.新城合浦路口告警${n14}次 15.北随塘河路55号告警${n15}次 16.石化一村门口南侧告警${n16}次 17.北随塘河路迎春饭店门口告警${n17}次 18.南康路铂骊酒店西侧告警${n18}次 四、小区雨水 19.石化一村48号告警${n19}次 请相关部门关注
            // TEMPLATE_CODE_YWGJ_PART1：时间${date} 时间段-${date1}内液位设备告警信息如下：（${s7}个${s8}次） 一、市政污水（${s1}个${s2}次） 1.秀林街告警数${n1}次 2.新城合浦路口告警数${n2}次 3.龙临街小李子门口告警数${n3}次 4.临潮三村出口告警数${n4}次 5.卫清西路1029号树昕门口告警数${n5}次 6.桥英馨苑南门对面告警数${n6}次 7.紫薇苑南门对面告警数${n7}次 8.东泉街蒙山路东侧告警数${n8}次 9.金一东路兰墅酒店告警数${n9}次 10.施三路施二路路口告警数${n10}次 11.板桥西路滨海二村出门井告警数${n11}次 二、市政雨水（${s5}个${s6}次） 12.新城合浦路口告警数${n14}次 13.北随塘河路55号告警数${n15}次 14.石化一村门口南侧告警数${n16}次 15.北随塘河路迎春饭店门口告警数${n17}次 16.南康路铂骊酒店西侧告警数${n18}次 请相关部门关注
            // TEMPLATE_CODE_YWGJ_PART2：时间${date} 时间段-${date1}液位设备告警信息如下：（${s7}个${s8}次） 一、小区污水（${s3}个${s4}次） 1.十二村西门北侧告警数${n12}次 2.东礁二村门口告警数${n13}次 二、小区雨水 3.石化一村48号告警数${n19}次 请相关部门关注
            String smsContent = "";
            if (templateCode.equals(SmsConstants.TEMPLATE_CODE_YWGJ_FULL)) {
                smsContent = "时间${d1} 时间段-${d2}内液位设备告警信息如下：（${s7}个${s8}次） 一、市政污水（${s1}个${s2}次） 1.秀林街告警${n1}次 2.新城合浦路口告警${n2}次 3.龙临街小李子门口告警${n3}次 4.临潮三村出口告警${n4}次 5.卫清西路1029号树昕门口告警${n5}次 6.桥英馨苑南门对面告警${n6}次 7.紫薇苑南门对面告警${n7}次 8.东泉街蒙山路东侧告警${n8}次 9.金一东路兰墅酒店告警${n9}次 10.施三路施二路路口告警${n10}次 11.板桥西路滨海二村出门井告警${n11}次 二、小区污水（${s3}个${s4}次） 12.十二村西门北侧告警${n12}次 13.东礁二村门口告警${n13}次 三、市政雨水（${s5}个${s6}次） 14.新城合浦路口告警${n14}次 15.北随塘河路55号告警${n15}次 16.石化一村门口南侧告警${n16}次 17.北随塘河路迎春饭店门口告警${n17}次 18.南康路铂骊酒店西侧告警${n18}次 四、小区雨水 19.石化一村48号告警${n19}次 请相关部门关注";
            } else if (templateCode.equals(SmsConstants.TEMPLATE_CODE_YWGJ_PART1)) {
                smsContent = "时间${date} 时间段-${date1}内液位设备告警信息如下：（${s7}个${s8}次） 一、市政污水（${s1}个${s2}次） 1.秀林街告警数${n1}次 2.新城合浦路口告警数${n2}次 3.龙临街小李子门口告警数${n3}次 4.临潮三村出口告警数${n4}次 5.卫清西路1029号树昕门口告警数${n5}次 6.桥英馨苑南门对面告警数${n6}次 7.紫薇苑南门对面告警数${n7}次 8.东泉街蒙山路东侧告警数${n8}次 9.金一东路兰墅酒店告警数${n9}次 10.施三路施二路路口告警数${n10}次 11.板桥西路滨海二村出门井告警数${n11}次 二、市政雨水（${s5}个${s6}次） 12.新城合浦路口告警数${n14}次 13.北随塘河路55号告警数${n15}次 14.石化一村门口南侧告警数${n16}次 15.北随塘河路迎春饭店门口告警数${n17}次 16.南康路铂骊酒店西侧告警数${n18}次 请相关部门关注";
            } else if (templateCode.equals(SmsConstants.TEMPLATE_CODE_YWGJ_PART2)) {
                smsContent = "时间${date} 时间段-${date1}液位设备告警信息如下：（${s7}个${s8}次） 一、小区污水（${s3}个${s4}次） 1.十二村西门北侧告警数${n12}次 2.东礁二村门口告警数${n13}次 二、小区雨水 3.石化一村48号告警数${n19}次 请相关部门关注";
            }

            smsContent = smsContent.replaceAll("\\$\\{d1\\}", params.get("d1"));
            smsContent = smsContent.replaceAll("\\$\\{d2\\}", params.get("d2"));
            smsContent = smsContent.replaceAll("\\$\\{s1\\}", params.get("s1"));
            smsContent = smsContent.replaceAll("\\$\\{s2\\}", params.get("s2"));
            smsContent = smsContent.replaceAll("\\$\\{s3\\}", params.get("s3")); 
            smsContent = smsContent.replaceAll("\\$\\{s4\\}", params.get("s4"));
            smsContent = smsContent.replaceAll("\\$\\{s5\\}", params.get("s5"));
            smsContent = smsContent.replaceAll("\\$\\{s6\\}", params.get("s6"));
            smsContent = smsContent.replaceAll("\\$\\{s7\\}", params.get("s7"));
            smsContent = smsContent.replaceAll("\\$\\{s8\\}", params.get("s8"));
            smsContent = smsContent.replaceAll("\\$\\{n1\\}", params.get("n1"));
            smsContent = smsContent.replaceAll("\\$\\{n2\\}", params.get("n2"));
            smsContent = smsContent.replaceAll("\\$\\{n3\\}", params.get("n3"));
            smsContent = smsContent.replaceAll("\\$\\{n4\\}", params.get("n4"));
            smsContent = smsContent.replaceAll("\\$\\{n5\\}", params.get("n5"));
            smsContent = smsContent.replaceAll("\\$\\{n6\\}", params.get("n6"));
            smsContent = smsContent.replaceAll("\\$\\{n7\\}", params.get("n7"));
            smsContent = smsContent.replaceAll("\\$\\{n8\\}", params.get("n8"));
            smsContent = smsContent.replaceAll("\\$\\{n9\\}", params.get("n9"));
            smsContent = smsContent.replaceAll("\\$\\{n10\\}", params.get("n10"));
            smsContent = smsContent.replaceAll("\\$\\{n11\\}", params.get("n11"));
            smsContent = smsContent.replaceAll("\\$\\{n12\\}", params.get("n12"));
            smsContent = smsContent.replaceAll("\\$\\{n13\\}", params.get("n13"));
            smsContent = smsContent.replaceAll("\\$\\{n14\\}", params.get("n14"));
            smsContent = smsContent.replaceAll("\\$\\{n15\\}", params.get("n15"));
            smsContent = smsContent.replaceAll("\\$\\{n16\\}", params.get("n16"));
            smsContent = smsContent.replaceAll("\\$\\{n17\\}", params.get("n17"));
            smsContent = smsContent.replaceAll("\\$\\{n18\\}", params.get("n18"));
            smsContent = smsContent.replaceAll("\\$\\{n19\\}", params.get("n19"));
            smsContent = smsContent.replaceAll("\\$\\{date\\}", params.get("date"));
            smsContent = smsContent.replaceAll("\\$\\{date1\\}", params.get("date1"));

            smsRecord.setSmsContent(smsContent);
            smsRecord.setTemplateCode(templateCode);
            smsRecord.setSendTime(new Date());
            smsRecordMapper.insertSmsRecord(smsRecord);
        });

    }

    public void syncHistoryAlarmRecord(List<AlarmRecordDTO> result) {
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        // 将liquidLevelDevices转为Map key为deviceImei，value为id
        Map<String, Long> liquidLevelDeviceMap = liquidLevelDevices.stream().collect(Collectors.toMap(LiquidLevelDevice::getDeviceImei, LiquidLevelDevice::getId));
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());
        for (AlarmRecordDTO alarmRecordDTO : filteredResult) {
            // 判断是否存在
            HistoryAlarmRecord exit = historyAlarmRecordMapper.selectHistoryAlarmRecordById(alarmRecordDTO.getId());
            if (exit != null) {
                continue;
            }
            HistoryAlarmRecord historyAlarmRecord = new HistoryAlarmRecord();
            historyAlarmRecord.setId(alarmRecordDTO.getId());
            historyAlarmRecord.setDeviceImei(alarmRecordDTO.getDeviceImei());
            historyAlarmRecord.setAlarmTime(DateUtil.parse(alarmRecordDTO.getAlarmTime()));
            historyAlarmRecord.setLiquidLevelDeviceId(liquidLevelDeviceMap.get(alarmRecordDTO.getDeviceImei()));
            historyAlarmRecordMapper.insertHistoryAlarmRecord(historyAlarmRecord);
        }
    }
}
