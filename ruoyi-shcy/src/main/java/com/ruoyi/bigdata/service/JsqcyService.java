package com.ruoyi.bigdata.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.config.BigdataConfig;
import com.ruoyi.common.core.redis.RedisCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 金山区城市运行管理平台接口
 */
@Component
@AllArgsConstructor
public class JsqcyService {

    private static final String TOKEN_KEY = "AUTHORIZATION_TOKEN";

    private static final String API_TOKEN_KEY = "api-token";

    // token过期时间 60分钟
    private static final Integer TOKEN_EXPIRE_TIME = 60;

    private final BigdataConfig bigdataConfig;

    private final RedisCache redisCache;

    private String fetchNewToken() {
        Map<String, Object> param = new HashMap<String, Object>() {
            {
                put("appKey", bigdataConfig.getAppKey());
                put("appSecret", bigdataConfig.getAppSecret());
            }
        };
        String response = HttpUtil.get(bigdataConfig.getHost() + "/auth/tokens", param);
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 1) {
                return null;
            }
            return jsonObject.getJSONObject("data").getString("token");
        } catch (Exception e) {
            // 日志记录或其他异常处理
            return null;
        }
    }

    private String refreshToken() {
        String newToken = fetchNewToken();
        // 将新的 token 存储到 Redis，并设置过期时间
        if (StrUtil.isNotEmpty(newToken)) {
            redisCache.setCacheObject(TOKEN_KEY, newToken, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        }
        return newToken;
    }

    // 获取有效 token 的方法
    public String getToken() {
        String token = redisCache.getCacheObject(TOKEN_KEY);
        if (token == null) {
            token = refreshToken();
        }
        return token;
    }

    private String fetchNewApiToken() {
        String response = HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00923/JS00003ZYML01818/data")
                .header("APPID-USER", bigdataConfig.getAppidUser())
                .header("USER-SECRET", bigdataConfig.getUserSecret())
                .header("AUTHORIZATION_TOKEN", getToken())
                .execute()
                .body();
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 1) {
                return null;
            }
            return jsonObject.getJSONObject("data").getString("data");
        } catch (Exception e) {
            // 日志记录或其他异常处理
            return null;
        }
    }

    private String refreshApiToken() {
        String newApiToken = fetchNewApiToken();
        // 将新的 token 存储到 Redis，并设置过期时间
        if (StrUtil.isNotEmpty(newApiToken)) {
            redisCache.setCacheObject(API_TOKEN_KEY, newApiToken, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        }
        return newApiToken;
    }

    // 获取有效 token 的方法
    public String getApiToken() {
        String apiToken = redisCache.getCacheObject(API_TOKEN_KEY);
        if (apiToken == null) {
            apiToken = refreshApiToken();
        }
        return apiToken;
    }

    /**
     * 获取城市预警防汛数据
     */
    public String getCsyjfxData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00916/JS00003ZYML01829/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取城市预警(气象)数据
     */
    public String getCsyjqxData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00915/JS00003ZYML01828/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(预警)数据
     */
    public String getFxftyjData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00914/JS00003ZYML01819/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取天气预报(当天)数据
     */
    public String getTqybData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00913/JS00003ZYML01871/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(综合汛情积水)数据
     */
    public String getFxftzhxqjsData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00912/JS00003ZYML01825/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(综合汛情风速风向)数据
     */
    public String getFxftzhxqfsfxData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00911/JS00003ZYML01824/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(综合汛情水位)数据
     */
    public String getFxftzhxqswData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00910/JS00003ZYML01823/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(综合汛情雨量)数据
     */
    public String getFxftzhxqylData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00909/JS00003ZYML01822/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }

    /**
     * 获取防汛防台(综合汛情泵闸)数据
     */
    public String getFxftzhxqbzData() {
        if (!bigdataConfig.isEnabled()) {
            return null;
        }
        String apiToken = getApiToken();
        String token = getToken();
        return HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00908/JS00003ZYML01821/data")
                .header("api-token", apiToken)
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
    }


}
