package com.ruoyi.bigdata.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.config.BigdataConfig;
import com.ruoyi.bigdata.service.BigdataService;
import com.ruoyi.shcy.domain.TaskDbcenter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@AllArgsConstructor
public class BigdataServiceImpl implements BigdataService {

    private final BigdataConfig bigdataConfig;

    @Override
    public String auth() {
        Map<String, Object> param = new HashMap<String, Object>() {
            {
                put("appKey", bigdataConfig.getAppKey());
                put("appSecret", bigdataConfig.getAppSecret());
            }
        };
        return HttpUtil.get(bigdataConfig.getHost() + "/auth/tokens", param);
    }

    @Override
    public String getToken() {
        String res = this.auth();
        JSONObject jsonObject = JSONObject.parseObject(res);
        if (jsonObject.getInteger("code") == 1) {
            return jsonObject.getJSONObject("data").getString("token");
        }
        return null;
    }

    @Override
    public String getGridCaseInfo(Integer page, Integer size, String discoverTime, String token) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("page", page);
        paramMap.put("size", size);
        paramMap.put("DISCOVERTIME", discoverTime);
        paramMap.put("STREETNAME", "石化街道");
        String result = HttpRequest.get(bigdataConfig.getHost() + "/api/rest/JSQ00781/JS00003ZYML01711/pages")
                .header("AUTHORIZATION_TOKEN", token)
                .form(paramMap)
                .execute().body();
        return result;
    }

    /**
     * 获取网格化案件信息(新)
     * @param page 页码
     * @param size 每页大小
     * @param synctime 同步时间
     * @param token token
     * @return 结果
     */
    @Override
    public String getGridCaseInfos(Integer page, Integer size, String synctime, String token) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("page", page);
        paramMap.put("size", size);
        paramMap.put("SYNCTIME", synctime);
        paramMap.put("STREETNAME", "石化街道");
        String result = HttpRequest.get(bigdataConfig.getHost() + "/api/rest/JSQ00848/JS00003ZYML01921/pages")
                .header("AUTHORIZATION_TOKEN", token)
                .form(paramMap)
                .execute().body();
        return result;
    }

    /**
     * 获取网格化案件信息(新)
     * @param page 页码
     * @param size 每页大小
     * @param discoverTime 发现时间
     * @param token token
     * @return 结果
     */
    @Override
    public String getGridCaseInfosByDiscoverTime(Integer page, Integer size, String discoverTime, String token) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("page", page);
        paramMap.put("size", size);
        paramMap.put("DISCOVERTIME", discoverTime);
        paramMap.put("STREETNAME", "石化街道");
        String result = HttpRequest.get(bigdataConfig.getHost() + "/api/rest/JSQ00849/JS00003ZYML01921/pages")
                .header("AUTHORIZATION_TOKEN", token)
                .form(paramMap)
                .execute().body();
        return result;
    }

}
