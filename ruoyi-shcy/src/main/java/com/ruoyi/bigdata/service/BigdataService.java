package com.ruoyi.bigdata.service;

public interface BigdataService {

    String auth();

    String getToken();

    String getGridCaseInfo(Integer page, Integer size, String discoverTime, String token);

    /**
     * 获取网格化案件信息(新)
     * @param page 页码
     * @param size 每页大小
     * @param synctime 同步时间
     * @param token token
     * @return 结果
     */
    String getGridCaseInfos(Integer page, Integer size, String synctime, String token);

    /**
     * 获取网格化案件信息(新)
     * @param page 页码
     * @param size 每页大小
     * @param discoverTime 发现时间
     * @param token token
     * @return 结果
     */
    String getGridCaseInfosByDiscoverTime(Integer page, Integer size, String discoverTime, String token);


}
