package com.ruoyi.tianhang;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.shcy.domain.SensorData;
import com.ruoyi.shcy.domain.SensorDevice;
import com.ruoyi.shcy.service.ISensorDataService;
import com.ruoyi.shcy.service.ISensorDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sun.management.Sensor;

@Component
@Slf4j
public class TianhangConsumer {

    @Autowired
    private ISensorDataService sensorDataService;

    @Autowired
    private ISensorDeviceService sensorDeviceService;

    @RabbitListener(queues = "jinshan.sensor.river")
    @RabbitHandler
    public void consumeMessage1(String message){
        JSONObject jsonObject = JSONObject.parseObject(message);
        long timestamp = jsonObject.getLongValue("timestamp");
        JSONObject payload = jsonObject.getJSONObject("payload");
        String imei = payload.getString("IMEI");
        double river = payload.getDoubleValue("river");
        java.util.Date date = new java.util.Date(timestamp);
        SensorData sensorData = new SensorData();
        sensorData.setImei(imei);
        sensorData.setValue(String.valueOf(river));
        sensorData.setUnit("m");
        sensorData.setUpdateTime(date);
        sensorDataService.insertSensorData(sensorData);
        SensorDevice sensorDevice = sensorDeviceService.selectSensorDeviceByImei(imei);
        if (sensorDevice != null) {
            sensorDevice.setValue(String.valueOf(river));
            sensorDevice.setUnit("m");
            sensorDevice.setUpdateTime(date);
            sensorDeviceService.updateSensorDevice(sensorDevice);
        }
    }

    @RabbitListener(queues = "jinshan.sensor.rain")
    @RabbitHandler
    public void consumeMessage2(String message){
        JSONObject jsonObject = JSONObject.parseObject(message);
        JSONObject payload = jsonObject.getJSONObject("payload");
        String imei = payload.getString("IMEI");
        String rain = payload.getString("rain_values");
        long uploadTime = payload.getLongValue("upload_time");
        java.util.Date date = new java.util.Date(uploadTime);
        SensorData sensorData = new SensorData();
        sensorData.setImei(imei);
        sensorData.setValue(rain);
        sensorData.setUnit("mm/h");
        sensorData.setUpdateTime(date);
        sensorDataService.insertSensorData(sensorData);
        SensorDevice sensorDevice = sensorDeviceService.selectSensorDeviceByImei(imei);
        if (sensorDevice != null) {
            sensorDevice.setValue(rain);
            sensorDevice.setUnit("mm/h");
            sensorDevice.setUpdateTime(date);
            sensorDeviceService.updateSensorDevice(sensorDevice);
        }
    }

    @RabbitListener(queues = "jinshan.sensor.wind")
    @RabbitHandler
    public void consumeMessage3(String message){
        // System.out.println("wind收到的消息:" + message);
    }

    @RabbitListener(queues = "jinshan.sensor.temperature")
    @RabbitHandler
    public void consumeMessage4(String message){
        JSONObject jsonObject = JSONObject.parseObject(message);
        JSONObject payload = jsonObject.getJSONObject("payload");
        String imei = payload.getString("IMEI");
        long temperature = payload.getLongValue("temperature");
        long uploadTime = payload.getLongValue("upload_time");
        java.util.Date date = new java.util.Date(uploadTime);
        SensorData sensorData = new SensorData();
        sensorData.setImei(imei);
        sensorData.setValue(String.valueOf(temperature));
        sensorData.setUnit("℃");
        sensorData.setUpdateTime(date);
        sensorDataService.insertSensorData(sensorData);
        SensorDevice sensorDevice = sensorDeviceService.selectSensorDeviceByImei(imei);
        if (sensorDevice != null) {
            sensorDevice.setValue(String.valueOf(temperature));
            sensorDevice.setUnit("℃");
            sensorDevice.setUpdateTime(date);
            sensorDeviceService.updateSensorDevice(sensorDevice);
        }
    }


}
