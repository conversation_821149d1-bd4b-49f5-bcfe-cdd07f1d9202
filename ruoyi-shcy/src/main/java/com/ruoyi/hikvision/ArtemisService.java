package com.ruoyi.hikvision;

/**
 * 海康威视智能应用平台API接口
 *
 * <AUTHOR>
 * @date 2023/03/24
 */
public interface ArtemisService {


    /**
     * 分页获取区域列表
     *
     * @param pageNo   指定第几页，从1开始
     * @param pageSize 每页返回的条数
     * @param treeCode 树编号（默认0：国标树）,可通过【获取所有树编码】获取
     * @return {@link String}
     * @throws Exception 异常
     */
    String callPostApiRegions(int pageNo, int pageSize, String treeCode) throws Exception;

    /**
     * 根据区域编号获取下一级区域列表
     *
     * @param parentIndexCode 父区域编号
     * @param treeCode        树编号（默认0，0代表国标树），可通过【获取所有树编码】获取
     * @return {@link String}
     * @throws Exception 异常
     */
    String callPostApiSubRegions(String parentIndexCode, String treeCode) throws Exception;

    String callPostApiPreviewURLs(String cameraIndexCode) throws Exception;

    String callPostApiPreviewURLsWs(String cameraIndexCode) throws Exception;

    /**
     * 根据区域编号获取下级监控点列表
     *
     * @param pageNo          指定第几页，从1开始
     * @param pageSize        每页返回的条数
     * @param regionIndexCode 区域编号（长度1-255位）,可通过【根据区域编号获取下一级区域列表】获取
     * @param treeCode        树编号（默认0：国标树），可通过【获取所有树编码】获取
     * @return {@link String}
     * @throws Exception 异常
     */
    public  String callPostApiGetCameras(int pageNo, int pageSize, String regionIndexCode, String treeCode) throws Exception;

}
