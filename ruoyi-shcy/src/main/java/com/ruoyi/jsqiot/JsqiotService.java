package com.ruoyi.jsqiot;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.DeviceDTO;
import com.ruoyi.nbiotyun.service.NbiotyunService;
import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.mapper.LiquidLevelDeviceMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 金山区物联网平台接口
 */
@Component
@AllArgsConstructor
public class JsqiotService {

    /**
     * HOST地址
     */
    private final String HOST = "http://***************:1188";

    /**
     * 设备注册URL
     */
    private final String DEVICE_REGISTER_URL = HOST + "/register";

    /**
     * 属性上报URL
     */
    private final String PROPERTY_REPORT_URL = HOST + "/report-property";

    /**
     * 事件上报URL
     */
    private final String EVENT_REPORT_URL = HOST + "/report-event";

    /**
     * productId
     */
    private final String PRODUCT_ID = "jiedaoyeweichaoxian";

    private final LiquidLevelDeviceMapper liquidLevelDeviceMapper;

    private final NbiotyunService nbiotyunService;

    /**
     * 设备注册
     */
    public void register() {
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);

        for (LiquidLevelDevice liquidLevelDevice : liquidLevelDevices) {
            // 请求参数
            HashMap<String, Object> map = new HashMap<>();

            // 使用map构建json字符串
            // 设置deviceId，使用liquidLevelDevice的id
            map.put("deviceId", String.valueOf(liquidLevelDevice.getId()));

            // 构建headers对象
            HashMap<String, Object> headers = new HashMap<>();
            headers.put("productId", PRODUCT_ID);
            headers.put("deviceName", liquidLevelDevice.getDeviceName());

            // 构建configuration对象
            HashMap<String, Object> configuration = new HashMap<>();
            configuration.put("geo", liquidLevelDevice.getCoordinate());
            configuration.put("province", "上海市");
            configuration.put("city", "上海市");
            configuration.put("district", "金山区");
            configuration.put("street", "石化街道");
            configuration.put("address", liquidLevelDevice.getAddress());

            headers.put("configuration", configuration);
            map.put("headers", headers);


            String jsonStr = JSON.toJSONString(map);

            // 请求
            String responseStr = HttpRequest.post(DEVICE_REGISTER_URL)
                    .body(jsonStr)
                    .execute().body();

            System.out.println("responseStr======> " + responseStr);

        }
    }

    /**
     * 属性上报
     */
    public void reportProperty() {
        List<DeviceDTO> result = nbiotyunService.getList(1, 100, 1);
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        // 将result 转为Map key为imei，value为state
        Map<String, Integer> deviceStatusMap = result.stream().collect(Collectors.toMap(DeviceDTO::getDeviceImei, DeviceDTO::getState));
        // 遍历liquidLevelDevices， 如果liquidLevelDevice.getDeviceImei()在deviceStatusMap且value为4 ,设置为离线，否则全部设置为在线
        liquidLevelDevices.forEach(liquidLevelDevice -> {
            if (deviceStatusMap.containsKey(liquidLevelDevice.getDeviceImei()) && deviceStatusMap.get(liquidLevelDevice.getDeviceImei()) == 4) {
                liquidLevelDevice.setDeviceState("离线");
            } else {
                liquidLevelDevice.setDeviceState("在线");
            }
        });

        for (LiquidLevelDevice liquidLevelDevice : liquidLevelDevices) {
            // 请求参数
            HashMap<String, Object> map = new HashMap<>();
            String jsonStr = JSON.toJSONString(map);

            // 请求
            String responseStr = HttpRequest.post(PROPERTY_REPORT_URL)
                    .body(jsonStr)
                    .execute().body();

            System.out.println("responseStr======> " + responseStr);
        }


    }

    /**
     * 事件上报
     */
    public void reportEvent() {

        String startTime = DateUtil.yesterday().toDateStr() + " 08:00:00";
        String endTime = DateUtil.today() + " 08:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);

        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceMapper.selectLiquidLevelDeviceList(null);
        // 将liquidLevelDevices转为Map key为deviceImei，value为id
        Map<String, Long> liquidLevelDeviceMap = liquidLevelDevices.stream().collect(Collectors.toMap(LiquidLevelDevice::getDeviceImei, LiquidLevelDevice::getId));
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        // 请求参数
        HashMap<String, Object> map = new HashMap<>();
        String jsonStr = JSON.toJSONString(map);

        // 请求
        String responseStr = HttpRequest.post(EVENT_REPORT_URL)
                .body(jsonStr)
                .execute().body();

        System.out.println("responseStr======> " + responseStr);
    }


}
