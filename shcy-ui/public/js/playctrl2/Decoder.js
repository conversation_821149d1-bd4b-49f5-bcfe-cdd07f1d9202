
var JSPlayerModule = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  
  return (
function(JSPlayerModule) {
  JSPlayerModule = JSPlayerModule || {};

function GROWABLE_HEAP_I8(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAP8}function GROWABLE_HEAP_U8(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAPU8}function GROWABLE_HEAP_I16(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAP16}function GROWABLE_HEAP_U16(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAPU16}function GROWABLE_HEAP_I32(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAP32}function GROWABLE_HEAP_U32(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAPU32}function GROWABLE_HEAP_F32(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAPF32}function GROWABLE_HEAP_F64(){if(wasmMemory.buffer!=buffer){updateGlobalBufferAndViews(wasmMemory.buffer)}return HEAPF64}var Module=typeof JSPlayerModule!=="undefined"?JSPlayerModule:{};var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});if(!Module.expectedDataFileDownloads){Module.expectedDataFileDownloads=0}Module.expectedDataFileDownloads++;(function(){var loadPackage=function(metadata){function runWithFS(){var fileData0="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";Module["FS_createDataFile"]("/","LucidaBrightDemiBold.ttf",decodeBase64(fileData0),true,true,false)}if(Module["calledRun"]){runWithFS()}else{if(!Module["preRun"])Module["preRun"]=[];Module["preRun"].push(runWithFS)}};loadPackage({"files":[]})})();var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=typeof window==="object";var ENVIRONMENT_IS_WORKER=typeof importScripts==="function";var ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";var ENVIRONMENT_IS_PTHREAD=Module["ENVIRONMENT_IS_PTHREAD"]||false;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];function warnOnce(text){if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}}function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function==="function"){var typeNames={"i":"i32","j":"i64","f":"f32","d":"f64"};var type={parameters:[],results:sig[0]=="v"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={"i":127,"j":126,"f":125,"d":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet=="v"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{"e":{"f":func}});var wrappedFunc=instance.exports["f"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."}return wasmTable.length-1}function addFunctionWasm(func,sig){if(!functionsInTableMap){functionsInTableMap=new WeakMap;for(var i=0;i<wasmTable.length;i++){var item=wasmTable.get(i);if(item){functionsInTableMap.set(item,i)}}}if(functionsInTableMap.has(func)){return functionsInTableMap.get(func)}var ret=getEmptyTableSlot();try{wasmTable.set(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}var wrapped=convertJsFunctionToWasm(func,sig);wasmTable.set(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}function addFunction(func,sig){return addFunctionWasm(func,sig)}var tempRet0=0;var setTempRet0=function(value){tempRet0=value};var getTempRet0=function(){return tempRet0};var Atomics_load=Atomics.load;var Atomics_store=Atomics.store;var Atomics_compareExchange=Atomics.compareExchange;var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}function setValue(ptr,value,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":GROWABLE_HEAP_I8()[ptr>>0]=value;break;case"i8":GROWABLE_HEAP_I8()[ptr>>0]=value;break;case"i16":GROWABLE_HEAP_I16()[ptr>>1]=value;break;case"i32":GROWABLE_HEAP_I32()[ptr>>2]=value;break;case"i64":tempI64=[value>>>0,(tempDouble=value,+Math.abs(tempDouble)>=1?tempDouble>0?(Math.min(+Math.floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[ptr>>2]=tempI64[0],GROWABLE_HEAP_I32()[ptr+4>>2]=tempI64[1];break;case"float":GROWABLE_HEAP_F32()[ptr>>2]=value;break;case"double":GROWABLE_HEAP_F64()[ptr>>3]=value;break;default:abort("invalid type for setValue: "+type)}}function getValue(ptr,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":return GROWABLE_HEAP_I8()[ptr>>0];case"i8":return GROWABLE_HEAP_I8()[ptr>>0];case"i16":return GROWABLE_HEAP_I16()[ptr>>1];case"i32":return GROWABLE_HEAP_I32()[ptr>>2];case"i64":return GROWABLE_HEAP_I32()[ptr>>2];case"float":return GROWABLE_HEAP_F32()[ptr>>2];case"double":return GROWABLE_HEAP_F64()[ptr>>3];default:abort("invalid type for getValue: "+type)}return null}var wasmMemory;var wasmModule;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},"array":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string")return UTF8ToString(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret}var ALLOC_NORMAL=0;var ALLOC_STACK=1;function allocate(slab,allocator){var ret;if(allocator==ALLOC_STACK){ret=stackAlloc(slab.length)}else{ret=_malloc(slab.length)}if(slab.subarray||slab.slice){GROWABLE_HEAP_U8().set(slab,ret)}else{GROWABLE_HEAP_U8().set(new Uint8Array(slab),ret)}return ret}function TextDecoderWrapper(encoding){var textDecoder=new TextDecoder(encoding);this.decode=function(data){if(data.buffer instanceof SharedArrayBuffer){data=new Uint8Array(data)}return textDecoder.decode.call(textDecoder,data)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoderWrapper("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(GROWABLE_HEAP_U8(),ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,GROWABLE_HEAP_U8(),outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoderWrapper("utf-16le"):undefined;function UTF16ToString(ptr,maxBytesToRead){var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&GROWABLE_HEAP_U16()[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder){return UTF16Decoder.decode(GROWABLE_HEAP_U8().subarray(ptr,endPtr))}else{var str="";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=GROWABLE_HEAP_I16()[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str}}function stringToUTF16(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);GROWABLE_HEAP_I16()[outPtr>>1]=codeUnit;outPtr+=2}GROWABLE_HEAP_I16()[outPtr>>1]=0;return outPtr-startPtr}function lengthBytesUTF16(str){return str.length*2}function UTF32ToString(ptr,maxBytesToRead){var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=GROWABLE_HEAP_I32()[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str}function stringToUTF32(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}GROWABLE_HEAP_I32()[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}GROWABLE_HEAP_I32()[outPtr>>2]=0;return outPtr-startPtr}function lengthBytesUTF32(str){var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len}function allocateUTF8(str){var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8Array(str,GROWABLE_HEAP_I8(),ret,size);return ret}function writeArrayToMemory(array,buffer){GROWABLE_HEAP_I8().set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){GROWABLE_HEAP_I8()[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)GROWABLE_HEAP_I8()[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;if(ENVIRONMENT_IS_PTHREAD){buffer=Module["buffer"]}function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||524288e3;if(ENVIRONMENT_IS_PTHREAD){wasmMemory=Module["wasmMemory"];buffer=Module["buffer"]}else{if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":1048576e3/65536,"shared":true});if(!(wasmMemory.buffer instanceof SharedArrayBuffer)){err("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag");if(ENVIRONMENT_IS_NODE){console.log("(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)")}throw Error("bad memory")}}}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(ENVIRONMENT_IS_PTHREAD)return;if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(ENVIRONMENT_IS_PTHREAD)return;if(!Module["noFSInit"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function exitRuntime(){if(ENVIRONMENT_IS_PTHREAD)return;runtimeExited=true}function postRun(){if(ENVIRONMENT_IS_PTHREAD)return;if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){if(ENVIRONMENT_IS_PTHREAD){postMessage({"cmd":"onAbort","arg":what})}else{if(Module["onAbort"]){Module["onAbort"](what)}}what+="";err(what);ABORT=true;EXITSTATUS=1;what="abort("+what+"). Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="Decoder.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmTable=Module["asm"]["gd"];addOnInit(Module["asm"]["Gb"]);PThread.tlsInitFunctions.push(Module["asm"]["fd"]);wasmModule=module;if(!ENVIRONMENT_IS_PTHREAD){removeRunDependency("wasm-instantiate")}}if(!ENVIRONMENT_IS_PTHREAD){addRunDependency("wasm-instantiate")}function receiveInstantiationResult(result){receiveInstance(result["instance"],result["module"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}var tempDouble;var tempI64;var ASM_CONSTS={602692:function($0){Module["firstGLContextExt"]=GL.contexts[$0].GLctx.getExtension("WEBGL_lose_context")},602784:function(){Module["firstGLContextExt"].loseContext()},602831:function(){return GROWABLE_HEAP_I8().length}};function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){wasmTable.get(func)()}else{wasmTable.get(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function _emscripten_futex_wake(addr,count){if(addr<=0||addr>GROWABLE_HEAP_I8().length||addr&3!=0||count<0)return-28;if(count==0)return 0;if(count>=2147483647)count=Infinity;var mainThreadWaitAddress=Atomics.load(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2);var mainThreadWoken=0;if(mainThreadWaitAddress==addr){var loadedAddr=Atomics.compareExchange(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2,mainThreadWaitAddress,0);if(loadedAddr==mainThreadWaitAddress){--count;mainThreadWoken=1;if(count<=0)return 1}}var ret=Atomics.notify(GROWABLE_HEAP_I32(),addr>>2,count);if(ret>=0)return ret+mainThreadWoken;throw"Atomics.notify returned an unexpected value "+ret}Module["_emscripten_futex_wake"]=_emscripten_futex_wake;function killThread(pthread_ptr){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! killThread() can only ever be called from main application thread!";if(!pthread_ptr)throw"Internal Error! Null pthread_ptr in killThread!";GROWABLE_HEAP_I32()[pthread_ptr+8>>2]=0;var pthread=PThread.pthreads[pthread_ptr];delete PThread.pthreads[pthread_ptr];pthread.worker.terminate();PThread.freeThreadData(pthread);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(pthread.worker),1);pthread.worker.pthread=undefined}function cancelThread(pthread_ptr){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! cancelThread() can only ever be called from main application thread!";if(!pthread_ptr)throw"Internal Error! Null pthread_ptr in cancelThread!";var pthread=PThread.pthreads[pthread_ptr];pthread.worker.postMessage({"cmd":"cancel"})}function cleanupThread(pthread_ptr){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! cleanupThread() can only ever be called from main application thread!";if(!pthread_ptr)throw"Internal Error! Null pthread_ptr in cleanupThread!";var pthread=PThread.pthreads[pthread_ptr];if(pthread){GROWABLE_HEAP_I32()[pthread_ptr+8>>2]=0;var worker=pthread.worker;PThread.returnWorkerToPool(worker)}}function _exit(status){exit(status)}Module["_exit"]=_exit;function handleException(e){if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}var toLog=e;err("exception thrown: "+toLog);quit_(1,e)}var PThread={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],initMainThreadBlock:function(){},initWorker:function(){},pthreads:{},threadExitHandlers:[],setExitStatus:function(status){EXITSTATUS=status},terminateAllThreads:function(){for(var t in PThread.pthreads){var pthread=PThread.pthreads[t];if(pthread&&pthread.worker){PThread.returnWorkerToPool(pthread.worker)}}PThread.pthreads={};for(var i=0;i<PThread.unusedWorkers.length;++i){var worker=PThread.unusedWorkers[i];worker.terminate()}PThread.unusedWorkers=[];for(var i=0;i<PThread.runningWorkers.length;++i){var worker=PThread.runningWorkers[i];var pthread=worker.pthread;worker.terminate();PThread.freeThreadData(pthread)}PThread.runningWorkers=[]},freeThreadData:function(pthread){if(!pthread)return;if(pthread.threadInfoStruct){_free(pthread.threadInfoStruct)}pthread.threadInfoStruct=0;if(pthread.allocatedOwnStack&&pthread.stackBase)_free(pthread.stackBase);pthread.stackBase=0;if(pthread.worker)pthread.worker.pthread=null},returnWorkerToPool:function(worker){PThread.runWithoutMainThreadQueuedCalls(function(){delete PThread.pthreads[worker.pthread.threadInfoStruct];PThread.unusedWorkers.push(worker);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(worker),1);PThread.freeThreadData(worker.pthread);worker.pthread=undefined})},runWithoutMainThreadQueuedCalls:function(func){GROWABLE_HEAP_I32()[__emscripten_allow_main_runtime_queued_calls>>2]=0;try{func()}finally{GROWABLE_HEAP_I32()[__emscripten_allow_main_runtime_queued_calls>>2]=1}},receiveObjectTransfer:function(data){if(typeof GL!=="undefined"){for(var i in data.offscreenCanvases){GL.offscreenCanvases[i]=data.offscreenCanvases[i]}if(!Module["canvas"]&&data.moduleCanvasId&&GL.offscreenCanvases[data.moduleCanvasId]){Module["canvas"]=GL.offscreenCanvases[data.moduleCanvasId].offscreenCanvas;Module["canvas"].id=data.moduleCanvasId}}},threadInit:function(){for(var i=0;i<PThread.tlsInitFunctions.length;i++){PThread.tlsInitFunctions[i]()}},loadWasmModuleToWorker:function(worker,onFinishedLoading){worker.onmessage=function(e){var d=e["data"];var cmd=d["cmd"];if(worker.pthread)PThread.currentProxiedOperationCallerThread=worker.pthread.threadInfoStruct;if(d["targetThread"]&&d["targetThread"]!=_pthread_self()){var thread=PThread.pthreads[d.targetThread];if(thread){thread.worker.postMessage(e.data,d["transferList"])}else{err('Internal error! Worker sent a message "'+cmd+'" to target pthread '+d["targetThread"]+", but that thread no longer exists!")}PThread.currentProxiedOperationCallerThread=undefined;return}if(cmd==="processQueuedMainThreadWork"){_emscripten_main_thread_process_queued_calls()}else if(cmd==="spawnThread"){spawnThread(e.data)}else if(cmd==="cleanupThread"){cleanupThread(d["thread"])}else if(cmd==="killThread"){killThread(d["thread"])}else if(cmd==="cancelThread"){cancelThread(d["thread"])}else if(cmd==="loaded"){worker.loaded=true;if(onFinishedLoading)onFinishedLoading(worker);if(worker.runPthread){worker.runPthread();delete worker.runPthread}}else if(cmd==="print"){out("Thread "+d["threadId"]+": "+d["text"])}else if(cmd==="printErr"){err("Thread "+d["threadId"]+": "+d["text"])}else if(cmd==="alert"){alert("Thread "+d["threadId"]+": "+d["text"])}else if(cmd==="detachedExit"){PThread.returnWorkerToPool(worker)}else if(cmd==="exitProcess"){try{_exit(d["returnCode"])}catch(e){handleException(e)}}else if(cmd==="cancelDone"){PThread.returnWorkerToPool(worker)}else if(e.data.target==="setimmediate"){worker.postMessage(e.data)}else if(cmd==="onAbort"){if(Module["onAbort"]){Module["onAbort"](d["arg"])}}else{err("worker sent an unknown command "+cmd)}PThread.currentProxiedOperationCallerThread=undefined};worker.onerror=function(e){err("pthread sent an error! "+e.filename+":"+e.lineno+": "+e.message);throw e};worker.postMessage({"cmd":"load","urlOrBlob":Module["mainScriptUrlOrBlob"]||_scriptDir,"wasmMemory":wasmMemory,"wasmModule":wasmModule})},allocateUnusedWorker:function(){var pthreadMainJs=locateFile("Decoder.worker.js");PThread.unusedWorkers.push(new Worker(pthreadMainJs))},getNewWorker:function(){if(PThread.unusedWorkers.length==0){PThread.allocateUnusedWorker();PThread.loadWasmModuleToWorker(PThread.unusedWorkers[0])}return PThread.unusedWorkers.pop()}};function establishStackSpace(stackTop,stackMax){_emscripten_stack_set_limits(stackTop,stackMax);stackRestore(stackTop)}Module["establishStackSpace"]=establishStackSpace;function invokeEntryPoint(ptr,arg){return wasmTable.get(ptr)(arg)}Module["invokeEntryPoint"]=invokeEntryPoint;function _AdditionDataCB(port,pstAddDataInfo,nUser){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(1,0,port,pstAddDataInfo,nUser);JSPlayM4_AdditionDataCBFun(port,pstAddDataInfo,nUser)}function _DecCB(port,pYUVPCMData,size,type,timestamp,nUser){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(2,0,port,pYUVPCMData,size,type,timestamp,nUser);var apYUVPCMData=new Uint8Array(size);apYUVPCMData.set(Module.HEAPU8.subarray(pYUVPCMData,pYUVPCMData+size));JSPlayM4_DecCallBack(port,apYUVPCMData,size,type,timestamp,nUser);apYUVPCMData=null}function _FirstFrameCB(port){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(3,0,port);JSPlayM4_FirstFrameCallBack(port)}function _RawDataCB(port,rawVideoInfo,nUser){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(4,0,port,rawVideoInfo,nUser);JSPlayM4_RawDataCallBack(port,rawVideoInfo,nUser)}function _RunTimeInfoCB(port,pstRunTimeInfo,nUser){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(5,0,port,pstRunTimeInfo,nUser);JSPlayM4_RunTimeInfoCallBack(port,pstRunTimeInfo,nUser)}function _YUVDisplayCB(port,yuvData,size,timestamp,nUser){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(6,0,port,yuvData,size,timestamp,nUser);var aYUVData=new Uint8Array(size);aYUVData.set(Module.HEAPU8.subarray(yuvData,yuvData+size));JSPlayM4_DisplayCallBack(port,aYUVData,size,timestamp,nUser);aYUVData=null}function ___assert_fail(condition,filename,line,func){abort("Assertion failed: "+UTF8ToString(condition)+", at: "+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])}var _emscripten_get_now;if(ENVIRONMENT_IS_PTHREAD){_emscripten_get_now=function(){return performance.now()-Module["__performance_now_clock_drift"]}}else _emscripten_get_now=function(){return performance.now()};var _emscripten_get_now_is_monotonic=true;function setErrNo(value){GROWABLE_HEAP_I32()[___errno_location()>>2]=value;return value}function _clock_gettime(clk_id,tp){var now;if(clk_id===0){now=Date.now()}else if((clk_id===1||clk_id===4)&&_emscripten_get_now_is_monotonic){now=_emscripten_get_now()}else{setErrNo(28);return-1}GROWABLE_HEAP_I32()[tp>>2]=now/1e3|0;GROWABLE_HEAP_I32()[tp+4>>2]=now%1e3*1e3*1e3|0;return 0}function ___clock_gettime(a0,a1){return _clock_gettime(a0,a1)}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function _atexit(func,arg){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(7,1,func,arg)}function ___cxa_thread_atexit(routine,arg){PThread.threadExitHandlers.push(function(){wasmTable.get(routine)(arg)})}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){GROWABLE_HEAP_I32()[this.ptr+4>>2]=type};this.get_type=function(){return GROWABLE_HEAP_I32()[this.ptr+4>>2]};this.set_destructor=function(destructor){GROWABLE_HEAP_I32()[this.ptr+8>>2]=destructor};this.get_destructor=function(){return GROWABLE_HEAP_I32()[this.ptr+8>>2]};this.set_refcount=function(refcount){GROWABLE_HEAP_I32()[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;GROWABLE_HEAP_I8()[this.ptr+12>>0]=caught};this.get_caught=function(){return GROWABLE_HEAP_I8()[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;GROWABLE_HEAP_I8()[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return GROWABLE_HEAP_I8()[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){Atomics.add(GROWABLE_HEAP_I32(),this.ptr+0>>2,1)};this.release_ref=function(){var prev=Atomics.sub(GROWABLE_HEAP_I32(),this.ptr+0>>2,1);return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function ___emscripten_init_main_thread_js(tb){__emscripten_thread_init(tb,!ENVIRONMENT_IS_WORKER,1);PThread.threadInit()}function _tzset_impl(){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(8,1);var currentYear=(new Date).getFullYear();var winter=new Date(currentYear,0,1);var summer=new Date(currentYear,6,1);var winterOffset=winter.getTimezoneOffset();var summerOffset=summer.getTimezoneOffset();var stdTimezoneOffset=Math.max(winterOffset,summerOffset);GROWABLE_HEAP_I32()[__get_timezone()>>2]=stdTimezoneOffset*60;GROWABLE_HEAP_I32()[__get_daylight()>>2]=Number(winterOffset!=summerOffset);function extractZone(date){var match=date.toTimeString().match(/\(([A-Za-z ]+)\)$/);return match?match[1]:"GMT"}var winterName=extractZone(winter);var summerName=extractZone(summer);var winterNamePtr=allocateUTF8(winterName);var summerNamePtr=allocateUTF8(summerName);if(summerOffset<winterOffset){GROWABLE_HEAP_I32()[__get_tzname()>>2]=winterNamePtr;GROWABLE_HEAP_I32()[__get_tzname()+4>>2]=summerNamePtr}else{GROWABLE_HEAP_I32()[__get_tzname()>>2]=summerNamePtr;GROWABLE_HEAP_I32()[__get_tzname()+4>>2]=winterNamePtr}}function _tzset(){if(_tzset.called)return;_tzset.called=true;_tzset_impl()}function _localtime_r(time,tmPtr){_tzset();var date=new Date(GROWABLE_HEAP_I32()[time>>2]*1e3);GROWABLE_HEAP_I32()[tmPtr>>2]=date.getSeconds();GROWABLE_HEAP_I32()[tmPtr+4>>2]=date.getMinutes();GROWABLE_HEAP_I32()[tmPtr+8>>2]=date.getHours();GROWABLE_HEAP_I32()[tmPtr+12>>2]=date.getDate();GROWABLE_HEAP_I32()[tmPtr+16>>2]=date.getMonth();GROWABLE_HEAP_I32()[tmPtr+20>>2]=date.getFullYear()-1900;GROWABLE_HEAP_I32()[tmPtr+24>>2]=date.getDay();var start=new Date(date.getFullYear(),0,1);var yday=(date.getTime()-start.getTime())/(1e3*60*60*24)|0;GROWABLE_HEAP_I32()[tmPtr+28>>2]=yday;GROWABLE_HEAP_I32()[tmPtr+36>>2]=-(date.getTimezoneOffset()*60);var summerOffset=new Date(date.getFullYear(),6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dst=(summerOffset!=winterOffset&&date.getTimezoneOffset()==Math.min(winterOffset,summerOffset))|0;GROWABLE_HEAP_I32()[tmPtr+32>>2]=dst;var zonePtr=GROWABLE_HEAP_I32()[__get_tzname()+(dst?4:0)>>2];GROWABLE_HEAP_I32()[tmPtr+40>>2]=zonePtr;return tmPtr}function ___localtime_r(a0,a1){return _localtime_r(a0,a1)}function spawnThread(threadParams){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! spawnThread() can only ever be called from main application thread!";var worker=PThread.getNewWorker();if(!worker){return 6}if(worker.pthread!==undefined)throw"Internal error!";if(!threadParams.pthread_ptr)throw"Internal error, no pthread ptr!";PThread.runningWorkers.push(worker);var stackHigh=threadParams.stackBase+threadParams.stackSize;var pthread=PThread.pthreads[threadParams.pthread_ptr]={worker:worker,stackBase:threadParams.stackBase,stackSize:threadParams.stackSize,allocatedOwnStack:threadParams.allocatedOwnStack,threadInfoStruct:threadParams.pthread_ptr};var tis=pthread.threadInfoStruct>>2;Atomics.store(GROWABLE_HEAP_U32(),tis+(60>>2),threadParams.detached);Atomics.store(GROWABLE_HEAP_U32(),tis+(76>>2),threadParams.stackSize);Atomics.store(GROWABLE_HEAP_U32(),tis+(72>>2),stackHigh);Atomics.store(GROWABLE_HEAP_U32(),tis+(100>>2),threadParams.stackSize);Atomics.store(GROWABLE_HEAP_U32(),tis+(100+8>>2),stackHigh);Atomics.store(GROWABLE_HEAP_U32(),tis+(100+12>>2),threadParams.detached);worker.pthread=pthread;var msg={"cmd":"run","start_routine":threadParams.startRoutine,"arg":threadParams.arg,"threadInfoStruct":threadParams.pthread_ptr,"stackBase":threadParams.stackBase,"stackSize":threadParams.stackSize};msg.moduleCanvasId=threadParams.moduleCanvasId;msg.offscreenCanvases=threadParams.offscreenCanvases;worker.runPthread=function(){msg.time=performance.now();worker.postMessage(msg,threadParams.transferList)};if(worker.loaded){worker.runPthread();delete worker.runPthread}return 0}function ___pthread_create_js(pthread_ptr,attr,start_routine,arg){if(typeof SharedArrayBuffer==="undefined"){err("Current environment does not support SharedArrayBuffer, pthreads are not available!");return 6}var transferList=[];var error=0;var transferredCanvasNames=attr?GROWABLE_HEAP_I32()[attr+36>>2]:0;if(transferredCanvasNames==-1)transferredCanvasNames="#canvas";else if(transferredCanvasNames)transferredCanvasNames=UTF8ToString(transferredCanvasNames).trim();if(transferredCanvasNames)transferredCanvasNames=transferredCanvasNames.split(",");var offscreenCanvases={};var moduleCanvasId=Module["canvas"]?Module["canvas"].id:"";for(var i=0;i<transferredCanvasNames.length;i++){var name=transferredCanvasNames[i].trim();var offscreenCanvasInfo;try{if(name=="#canvas"){if(!Module["canvas"]){err('pthread_create: could not find canvas with ID "'+name+'" to transfer to thread!');error=28;break}name=Module["canvas"].id}if(GL.offscreenCanvases[name]){offscreenCanvasInfo=GL.offscreenCanvases[name];GL.offscreenCanvases[name]=null;if(Module["canvas"]instanceof OffscreenCanvas&&name===Module["canvas"].id)Module["canvas"]=null}else if(!ENVIRONMENT_IS_PTHREAD){var canvas=Module["canvas"]&&Module["canvas"].id===name?Module["canvas"]:document.querySelector(name);if(!canvas){err('pthread_create: could not find canvas with ID "'+name+'" to transfer to thread!');error=28;break}if(canvas.controlTransferredOffscreen){err('pthread_create: cannot transfer canvas with ID "'+name+'" to thread, since the current thread does not have control over it!');error=63;break}if(canvas.transferControlToOffscreen){if(!canvas.canvasSharedPtr){canvas.canvasSharedPtr=_malloc(12);GROWABLE_HEAP_I32()[canvas.canvasSharedPtr>>2]=canvas.width;GROWABLE_HEAP_I32()[canvas.canvasSharedPtr+4>>2]=canvas.height;GROWABLE_HEAP_I32()[canvas.canvasSharedPtr+8>>2]=0}offscreenCanvasInfo={offscreenCanvas:canvas.transferControlToOffscreen(),canvasSharedPtr:canvas.canvasSharedPtr,id:canvas.id};canvas.controlTransferredOffscreen=true}else{err('pthread_create: cannot transfer control of canvas "'+name+'" to pthread, because current browser does not support OffscreenCanvas!');err("pthread_create: Build with -s OFFSCREEN_FRAMEBUFFER=1 to enable fallback proxying of GL commands from pthread to main thread.");return 52}}if(offscreenCanvasInfo){transferList.push(offscreenCanvasInfo.offscreenCanvas);offscreenCanvases[offscreenCanvasInfo.id]=offscreenCanvasInfo}}catch(e){err('pthread_create: failed to transfer control of canvas "'+name+'" to OffscreenCanvas! Error: '+e);return 28}}if(ENVIRONMENT_IS_PTHREAD&&(transferList.length===0||error)){return _emscripten_sync_run_in_main_thread_4(687865856,pthread_ptr,attr,start_routine,arg)}if(error)return error;var stackSize=0;var stackBase=0;var detached=0;if(attr&&attr!=-1){stackSize=GROWABLE_HEAP_I32()[attr>>2];stackSize+=81920;stackBase=GROWABLE_HEAP_I32()[attr+8>>2];detached=GROWABLE_HEAP_I32()[attr+12>>2]!==0}else{stackSize=2097152}var allocatedOwnStack=stackBase==0;if(allocatedOwnStack){stackBase=_memalign(16,stackSize)}else{stackBase-=stackSize;assert(stackBase>0)}for(var i in offscreenCanvases){GROWABLE_HEAP_I32()[offscreenCanvases[i].canvasSharedPtr+8>>2]=pthread_ptr}var threadParams={stackBase:stackBase,stackSize:stackSize,allocatedOwnStack:allocatedOwnStack,detached:detached,startRoutine:start_routine,pthread_ptr:pthread_ptr,arg:arg,moduleCanvasId:moduleCanvasId,offscreenCanvases:offscreenCanvases,transferList:transferList};if(ENVIRONMENT_IS_PTHREAD){threadParams.cmd="spawnThread";postMessage(threadParams,transferList);return 0}return spawnThread(threadParams)}function ___pthread_detached_exit(){postMessage({"cmd":"detachedExit"})}function ___pthread_exit_run_handlers(status){while(PThread.threadExitHandlers.length>0){PThread.threadExitHandlers.pop()()}}function _emscripten_futex_wait(addr,val,timeout){if(addr<=0||addr>GROWABLE_HEAP_I8().length||addr&3!=0)return-28;if(!ENVIRONMENT_IS_WEB){var ret=Atomics.wait(GROWABLE_HEAP_I32(),addr>>2,val,timeout);if(ret==="timed-out")return-73;if(ret==="not-equal")return-6;if(ret==="ok")return 0;throw"Atomics.wait returned an unexpected value "+ret}else{if(Atomics.load(GROWABLE_HEAP_I32(),addr>>2)!=val){return-6}var tNow=performance.now();var tEnd=tNow+timeout;var lastAddr=Atomics.exchange(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2,addr);while(1){tNow=performance.now();if(tNow>tEnd){lastAddr=Atomics.exchange(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2,0);return-73}lastAddr=Atomics.exchange(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2,0);if(lastAddr==0){break}_emscripten_main_thread_process_queued_calls();if(Atomics.load(GROWABLE_HEAP_I32(),addr>>2)!=val){return-6}lastAddr=Atomics.exchange(GROWABLE_HEAP_I32(),__emscripten_main_thread_futex>>2,addr)}return 0}}function _emscripten_check_blocking_allowed(){if(ENVIRONMENT_IS_WORKER)return;warnOnce("Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread")}function __emscripten_do_pthread_join(thread,status,block){if(!thread){err("pthread_join attempted on a null thread pointer!");return 71}if(ENVIRONMENT_IS_PTHREAD&&_pthread_self()==thread){err("PThread "+thread+" is attempting to join to itself!");return 16}else if(!ENVIRONMENT_IS_PTHREAD&&_emscripten_main_browser_thread_id()==thread){err("Main thread "+thread+" is attempting to join to itself!");return 16}var self=GROWABLE_HEAP_I32()[thread+8>>2];if(self!==thread){err("pthread_join attempted on thread "+thread+", which does not point to a valid thread, or does not exist anymore!");return 71}var detached=Atomics.load(GROWABLE_HEAP_U32(),thread+60>>2);if(detached){err("Attempted to join thread "+thread+", which was already detached!");return 28}if(block){_emscripten_check_blocking_allowed()}for(;;){var threadStatus=Atomics.load(GROWABLE_HEAP_U32(),thread+0>>2);if(threadStatus==1){if(status){var result=Atomics.load(GROWABLE_HEAP_U32(),thread+88>>2);GROWABLE_HEAP_I32()[status>>2]=result}Atomics.store(GROWABLE_HEAP_U32(),thread+60>>2,1);if(!ENVIRONMENT_IS_PTHREAD)cleanupThread(thread);else postMessage({"cmd":"cleanupThread","thread":thread});return 0}if(!block){return 10}_pthread_testcancel();if(!ENVIRONMENT_IS_PTHREAD)_emscripten_main_thread_process_queued_calls();_emscripten_futex_wait(thread+0,threadStatus,ENVIRONMENT_IS_PTHREAD?100:1)}}function ___pthread_join_js(thread,status){return __emscripten_do_pthread_join(thread,status,true)}var PATH={splitPath:function(filename){var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:function(parts,allowAboveRoot){var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:function(path){var isAbsolute=path.charAt(0)==="/",trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(function(p){return!!p}),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:function(path){var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:function(path){if(path==="/")return"/";path=PATH.normalize(path);path=path.replace(/\/$/,"");var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},extname:function(path){return PATH.splitPath(path)[3]},join:function(){var paths=Array.prototype.slice.call(arguments,0);return PATH.normalize(paths.join("/"))},join2:function(l,r){return PATH.normalize(l+"/"+r)}};function getRandomDevice(){if(typeof crypto==="object"&&typeof crypto["getRandomValues"]==="function"){var randomBuffer=new Uint8Array(1);return function(){crypto.getRandomValues(randomBuffer);return randomBuffer[0]}}else return function(){abort("randomDevice")}}var PATH_FS={resolve:function(){var resolvedPath="",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=path.charAt(0)==="/"}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(function(p){return!!p}),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:function(from,to){from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};var TTY={ttys:[],init:function(){},shutdown:function(){},register:function(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open:function(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close:function(stream){stream.tty.ops.flush(stream.tty)},flush:function(stream){stream.tty.ops.flush(stream.tty)},read:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char:function(tty){if(!tty.input.length){var result=null;if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}tty.input=intArrayFromString(result,true)}return tty.input.shift()},put_char:function(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},flush:function(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}}},default_tty1_ops:{put_char:function(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},flush:function(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};function zeroMemory(address,size){GROWABLE_HEAP_U8().fill(0,address,address+size)}function alignMemory(size,alignment){return Math.ceil(size/alignment)*alignment}function mmapAlloc(size){size=alignMemory(size,65536);var ptr=_memalign(65536,size);if(!ptr)return 0;zeroMemory(ptr,size);return ptr}var MEMFS={ops_table:null,mount:function(mount){return MEMFS.createNode(null,"/",16384|511,0)},createNode:function(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray:function(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage:function(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage:function(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr:function(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr:function(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup:function(parent,name){throw FS.genericErrors[44]},mknod:function(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename:function(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink:function(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir:function(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir:function(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink:function(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink:function(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read:function(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write:function(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===GROWABLE_HEAP_I8().buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek:function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate:function(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap:function(stream,address,length,position,prot,flags){if(address!==0){throw new FS.ErrnoError(28)}if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}GROWABLE_HEAP_I8().set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync:function(stream,buffer,offset,length,mmapFlags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(mmapFlags&2){return 0}var bytesWritten=MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};function asyncLoad(url,onload,onerror,noRunDep){var dep=!noRunDep?getUniqueRunDependency("al "+url):"";readAsync(url,function(arrayBuffer){assert(arrayBuffer,'Loading data file "'+url+'" failed (no arrayBuffer).');onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},function(event){if(onerror){onerror()}else{throw'Loading data file "'+url+'" failed.'}});if(dep)addRunDependency(dep)}var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(path,opts){path=PATH_FS.resolve(FS.cwd(),path);opts=opts||{};if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};for(var key in defaults){if(opts[key]===undefined){opts[key]=defaults[key]}}if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=PATH.normalizeArray(path.split("/").filter(function(p){return!!p}),false);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath:function(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?mount+"/"+path:mount+path}path=path?node.name+"/"+path:node.name;node=node.parent}},hashName:function(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode:function(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode:function(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode:function(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode:function(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode:function(node){FS.hashRemoveNode(node)},isRoot:function(node){return node===node.parent},isMountpoint:function(node){return!!node.mounted},isFile:function(mode){return(mode&61440)===32768},isDir:function(mode){return(mode&61440)===16384},isLink:function(mode){return(mode&61440)===40960},isChrdev:function(mode){return(mode&61440)===8192},isBlkdev:function(mode){return(mode&61440)===24576},isFIFO:function(mode){return(mode&61440)===4096},isSocket:function(mode){return(mode&49152)===49152},flagModes:{"r":0,"r+":2,"w":577,"w+":578,"a":1089,"a+":1090},modeStringToFlags:function(str){var flags=FS.flagModes[str];if(typeof flags==="undefined"){throw new Error("Unknown file open mode: "+str)}return flags},flagsToPermissionString:function(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions:function(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup:function(dir){var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate:function(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete:function(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen:function(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd:function(fd_start,fd_end){fd_start=fd_start||0;fd_end=fd_end||FS.MAX_OPEN_FDS;for(var fd=fd_start;fd<=fd_end;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStream:function(fd){return FS.streams[fd]},createStream:function(stream,fd_start,fd_end){if(!FS.FSStream){FS.FSStream=function(){};FS.FSStream.prototype={object:{get:function(){return this.node},set:function(val){this.node=val}},isRead:{get:function(){return(this.flags&2097155)!==1}},isWrite:{get:function(){return(this.flags&2097155)!==0}},isAppend:{get:function(){return this.flags&1024}}}}var newStream=new FS.FSStream;for(var p in stream){newStream[p]=stream[p]}stream=newStream;var fd=FS.nextfd(fd_start,fd_end);stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream:function(fd){FS.streams[fd]=null},chrdev_stream_ops:{open:function(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek:function(){throw new FS.ErrnoError(70)}},major:function(dev){return dev>>8},minor:function(dev){return dev&255},makedev:function(ma,mi){return ma<<8|mi},registerDevice:function(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:function(dev){return FS.devices[dev]},getMounts:function(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs:function(populate,callback){if(typeof populate==="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err("warning: "+FS.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work")}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(function(mount){if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount:function(type,opts,mountpoint){var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount:function(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(function(hash){var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup:function(parent,name){return parent.node_ops.lookup(parent,name)},mknod:function(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create:function(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir:function(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree:function(path,mode){var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev:function(path,mode,dev){if(typeof dev==="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink:function(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename:function(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir:function(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir:function(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink:function(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink:function(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat:function(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat:function(path){return FS.stat(path,true)},chmod:function(path,mode,dontFollow){var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod:function(path,mode){FS.chmod(path,mode,true)},fchmod:function(fd,mode){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}FS.chmod(stream.node,mode)},chown:function(path,uid,gid,dontFollow){var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown:function(path,uid,gid){FS.chown(path,uid,gid,true)},fchown:function(fd,uid,gid){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}FS.chown(stream.node,uid,gid)},truncate:function(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path==="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate:function(fd,len){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime:function(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open:function(path,flags,mode,fd_start,fd_end){if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags==="string"?FS.modeStringToFlags(flags):flags;mode=typeof mode==="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false},fd_start,fd_end);if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close:function(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed:function(stream){return stream.fd===null},llseek:function(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read:function(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!=="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write:function(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate:function(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap:function(stream,address,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,address,length,position,prot,flags)},msync:function(stream,buffer,offset,length,mmapFlags){if(!stream||!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:function(stream){return 0},ioctl:function(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile:function(path,opts){opts=opts||{};opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error('Invalid encoding type "'+opts.encoding+'"')}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret},writeFile:function(path,data,opts){opts=opts||{};opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:function(){return FS.currentPath},chdir:function(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories:function(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices:function(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:function(){return 0},write:function(stream,buffer,offset,length,pos){return length}});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var random_device=getRandomDevice();FS.createDevice("/dev","random",random_device);FS.createDevice("/dev","urandom",random_device);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount:function(){var node=FS.createNode(proc_self,"fd",16384|511,73);node.node_ops={lookup:function(parent,name){var fd=+name;var stream=FS.getStream(fd);if(!stream)throw new FS.ErrnoError(8);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return stream.path}}};ret.parent=ret;return ret}};return node}},{},"/proc/self/fd")},createStandardStreams:function(){if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1)},ensureErrnoError:function(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message="FS error"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(function(code){FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"})},staticInit:function(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS}},init:function(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()},quit:function(){FS.init.initialized=false;var fflush=Module["_fflush"];if(fflush)fflush(0);for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},getMode:function(canRead,canWrite){var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode},findObject:function(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(ret.exists){return ret.object}else{return null}},analyzePath:function(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath:function(parent,path,canRead,canWrite){parent=typeof parent==="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile:function(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);var mode=FS.getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile:function(parent,name,data,canRead,canWrite,canOwn){var path=name?PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name):parent;var mode=FS.getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice:function(parent,name,input,output){var path=PATH.join2(typeof parent==="string"?parent:FS.getPath(parent),name);var mode=FS.getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open:function(stream){stream.seekable=false},close:function(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read:function(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:function(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile:function(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!=="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}},createLazyFile:function(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=function(from,to){if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);if(typeof Uint8Array!="undefined")xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}else{return intArrayFromString(xhr.responseText||"",true)}};var lazyArray=this;lazyArray.setDataGetter(function(chunkNum){var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(function(key){var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});stream_ops.read=function stream_ops_read(stream,buffer,offset,length,position){FS.forceLoadFile(node);var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size};node.stream_ops=stream_ops;return node},createPreloadedFile:function(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish){Browser.init();var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency("cp "+fullname);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS.createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}var handled=false;Module["preloadPlugins"].forEach(function(plugin){if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,function(){if(onerror)onerror();removeRunDependency(dep)});handled=true}});if(!handled)finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url,function(byteArray){processData(byteArray)},onerror)}else{processData(url)}},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(paths,onload,onerror){onload=onload||function(){};onerror=onerror||function(){};var indexedDB=FS.indexedDB();try{var openRequest=indexedDB.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return onerror(e)}openRequest.onupgradeneeded=function openRequest_onupgradeneeded(){out("creating db");var db=openRequest.result;db.createObjectStore(FS.DB_STORE_NAME)};openRequest.onsuccess=function openRequest_onsuccess(){var db=openRequest.result;var transaction=db.transaction([FS.DB_STORE_NAME],"readwrite");var files=transaction.objectStore(FS.DB_STORE_NAME);var ok=0,fail=0,total=paths.length;function finish(){if(fail==0)onload();else onerror()}paths.forEach(function(path){var putRequest=files.put(FS.analyzePath(path).object.contents,path);putRequest.onsuccess=function putRequest_onsuccess(){ok++;if(ok+fail==total)finish()};putRequest.onerror=function putRequest_onerror(){fail++;if(ok+fail==total)finish()}});transaction.onerror=onerror};openRequest.onerror=onerror},loadFilesFromDB:function(paths,onload,onerror){onload=onload||function(){};onerror=onerror||function(){};var indexedDB=FS.indexedDB();try{var openRequest=indexedDB.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return onerror(e)}openRequest.onupgradeneeded=onerror;openRequest.onsuccess=function openRequest_onsuccess(){var db=openRequest.result;try{var transaction=db.transaction([FS.DB_STORE_NAME],"readonly")}catch(e){onerror(e);return}var files=transaction.objectStore(FS.DB_STORE_NAME);var ok=0,fail=0,total=paths.length;function finish(){if(fail==0)onload();else onerror()}paths.forEach(function(path){var getRequest=files.get(path);getRequest.onsuccess=function getRequest_onsuccess(){if(FS.analyzePath(path).exists){FS.unlink(path)}FS.createDataFile(PATH.dirname(path),PATH.basename(path),getRequest.result,true,true,true);ok++;if(ok+fail==total)finish()};getRequest.onerror=function getRequest_onerror(){fail++;if(ok+fail==total)finish()}});transaction.onerror=onerror};openRequest.onerror=onerror}};var SYSCALLS={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt:function(dirfd,path,allowEmpty){if(path[0]==="/"){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=FS.getStream(dirfd);if(!dirstream)throw new FS.ErrnoError(8);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat:function(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}GROWABLE_HEAP_I32()[buf>>2]=stat.dev;GROWABLE_HEAP_I32()[buf+4>>2]=0;GROWABLE_HEAP_I32()[buf+8>>2]=stat.ino;GROWABLE_HEAP_I32()[buf+12>>2]=stat.mode;GROWABLE_HEAP_I32()[buf+16>>2]=stat.nlink;GROWABLE_HEAP_I32()[buf+20>>2]=stat.uid;GROWABLE_HEAP_I32()[buf+24>>2]=stat.gid;GROWABLE_HEAP_I32()[buf+28>>2]=stat.rdev;GROWABLE_HEAP_I32()[buf+32>>2]=0;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?(Math.min(+Math.floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+40>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+44>>2]=tempI64[1];GROWABLE_HEAP_I32()[buf+48>>2]=4096;GROWABLE_HEAP_I32()[buf+52>>2]=stat.blocks;GROWABLE_HEAP_I32()[buf+56>>2]=stat.atime.getTime()/1e3|0;GROWABLE_HEAP_I32()[buf+60>>2]=0;GROWABLE_HEAP_I32()[buf+64>>2]=stat.mtime.getTime()/1e3|0;GROWABLE_HEAP_I32()[buf+68>>2]=0;GROWABLE_HEAP_I32()[buf+72>>2]=stat.ctime.getTime()/1e3|0;GROWABLE_HEAP_I32()[buf+76>>2]=0;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?(Math.min(+Math.floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+80>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+84>>2]=tempI64[1];return 0},doMsync:function(addr,stream,len,flags,offset){var buffer=GROWABLE_HEAP_U8().slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},doMkdir:function(path,mode){path=PATH.normalize(path);if(path[path.length-1]==="/")path=path.substr(0,path.length-1);FS.mkdir(path,mode,0);return 0},doMknod:function(path,mode,dev){switch(mode&61440){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}FS.mknod(path,mode,dev);return 0},doReadlink:function(path,buf,bufsize){if(bufsize<=0)return-28;var ret=FS.readlink(path);var len=Math.min(bufsize,lengthBytesUTF8(ret));var endChar=GROWABLE_HEAP_I8()[buf+len];stringToUTF8(ret,buf,bufsize+1);GROWABLE_HEAP_I8()[buf+len]=endChar;return len},doAccess:function(path,amode){if(amode&~7){return-28}var node;var lookup=FS.lookupPath(path,{follow:true});node=lookup.node;if(!node){return-44}var perms="";if(amode&4)perms+="r";if(amode&2)perms+="w";if(amode&1)perms+="x";if(perms&&FS.nodePermissions(node,perms)){return-2}return 0},doDup:function(path,flags,suggestFD){var suggest=FS.getStream(suggestFD);if(suggest)FS.close(suggest);return FS.open(path,flags,0,suggestFD,suggestFD).fd},doReadv:function(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=GROWABLE_HEAP_I32()[iov+i*8>>2];var len=GROWABLE_HEAP_I32()[iov+(i*8+4)>>2];var curr=FS.read(stream,GROWABLE_HEAP_I8(),ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break}return ret},doWritev:function(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=GROWABLE_HEAP_I32()[iov+i*8>>2];var len=GROWABLE_HEAP_I32()[iov+(i*8+4)>>2];var curr=FS.write(stream,GROWABLE_HEAP_I8(),ptr,len,offset);if(curr<0)return-1;ret+=curr}return ret},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=GROWABLE_HEAP_I32()[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD:function(fd){var stream=FS.getStream(fd);if(!stream)throw new FS.ErrnoError(8);return stream},get64:function(low,high){return low}};function ___sys_fcntl64(fd,cmd,varargs){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(9,1,fd,cmd,varargs);SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=SYSCALLS.get();if(arg<0){return-28}var newStream;newStream=FS.open(stream.path,stream.flags,0,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=SYSCALLS.get();stream.flags|=arg;return 0}case 12:{var arg=SYSCALLS.get();var offset=0;GROWABLE_HEAP_I16()[arg+offset>>1]=2;return 0}case 13:case 14:return 0;case 16:case 8:return-28;case 9:setErrNo(28);return-1;default:{return-28}}}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___sys_ioctl(fd,op,varargs){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(10,1,fd,op,varargs);SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:case 21505:{if(!stream.tty)return-59;return 0}case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:{if(!stream.tty)return-59;return 0}case 21519:{if(!stream.tty)return-59;var argp=SYSCALLS.get();GROWABLE_HEAP_I32()[argp>>2]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21531:{var argp=SYSCALLS.get();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;return 0}case 21524:{if(!stream.tty)return-59;return 0}default:abort("bad ioctl syscall "+op)}}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function ___sys_open(path,flags,varargs){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(11,1,path,flags,varargs);SYSCALLS.varargs=varargs;try{var pathname=SYSCALLS.getStr(path);var mode=varargs?SYSCALLS.get():0;var stream=FS.open(pathname,flags,mode);return stream.fd}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return-e.errno}}function __embind_register_bigint(primitiveType,name,size,minRange,maxRange){}function getShiftFromSize(size){switch(size){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+size)}}function embind_init_charCodes(){var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes}var embind_charCodes=undefined;function readLatin1String(ptr){var ret="";var c=ptr;while(GROWABLE_HEAP_U8()[c]){ret+=embind_charCodes[GROWABLE_HEAP_U8()[c++]]}return ret}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var char_0=48;var char_9=57;function makeLegalFunctionName(name){if(undefined===name){return"_unknown"}name=name.replace(/[^a-zA-Z0-9_]/g,"$");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return"_"+name}else{return name}}function createNamedFunction(name,body){name=makeLegalFunctionName(name);return new Function("body","return function "+name+"() {\n"+'    "use strict";'+"    return body.apply(this, arguments);\n"+"};\n")(body)}function extendError(baseErrorType,errorName){var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+"\n"+stack.replace(/^Error(:[^\n]*)?\n/,"")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return this.name+": "+this.message}};return errorClass}var BindingError=undefined;function throwBindingError(message){throw new BindingError(message)}var InternalError=undefined;function registerType(rawType,registeredInstance,options){options=options||{};if(!("argPackAdvance"in registeredInstance)){throw new TypeError("registerType registeredInstance requires argPackAdvance")}var name=registeredInstance.name;if(!rawType){throwBindingError('type "'+name+'" must have a positive integer typeid pointer')}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError("Cannot register type '"+name+"' twice")}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(function(cb){cb()})}}function __embind_register_bool(rawType,name,size,trueValue,falseValue){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(wt){return!!wt},"toWireType":function(destructors,o){return o?trueValue:falseValue},"argPackAdvance":8,"readValueFromPointer":function(pointer){var heap;if(size===1){heap=GROWABLE_HEAP_I8()}else if(size===2){heap=GROWABLE_HEAP_I16()}else if(size===4){heap=GROWABLE_HEAP_I32()}else{throw new TypeError("Unknown boolean type size: "+name)}return this["fromWireType"](heap[pointer>>shift])},destructorFunction:null})}var emval_free_list=[];var emval_handle_array=[{},{value:undefined},{value:null},{value:true},{value:false}];function __emval_decref(handle){if(handle>4&&0===--emval_handle_array[handle].refcount){emval_handle_array[handle]=undefined;emval_free_list.push(handle)}}function count_emval_handles(){var count=0;for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){++count}}return count}function get_first_emval(){for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){return emval_handle_array[i]}}return null}function init_emval(){Module["count_emval_handles"]=count_emval_handles;Module["get_first_emval"]=get_first_emval}function __emval_register(value){switch(value){case undefined:{return 1}case null:{return 2}case true:{return 3}case false:{return 4}default:{var handle=emval_free_list.length?emval_free_list.pop():emval_handle_array.length;emval_handle_array[handle]={refcount:1,value:value};return handle}}}function simpleReadValueFromPointer(pointer){return this["fromWireType"](GROWABLE_HEAP_U32()[pointer>>2])}function __embind_register_emval(rawType,name){name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(handle){var rv=emval_handle_array[handle].value;__emval_decref(handle);return rv},"toWireType":function(destructors,value){return __emval_register(value)},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:null})}function _embind_repr(v){if(v===null){return"null"}var t=typeof v;if(t==="object"||t==="array"||t==="function"){return v.toString()}else{return""+v}}function floatReadValueFromPointer(name,shift){switch(shift){case 2:return function(pointer){return this["fromWireType"](GROWABLE_HEAP_F32()[pointer>>2])};case 3:return function(pointer){return this["fromWireType"](GROWABLE_HEAP_F64()[pointer>>3])};default:throw new TypeError("Unknown float type: "+name)}}function __embind_register_float(rawType,name,size){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(value){return value},"toWireType":function(destructors,value){if(typeof value!=="number"&&typeof value!=="boolean"){throw new TypeError('Cannot convert "'+_embind_repr(value)+'" to '+this.name)}return value},"argPackAdvance":8,"readValueFromPointer":floatReadValueFromPointer(name,shift),destructorFunction:null})}function integerReadValueFromPointer(name,shift,signed){switch(shift){case 0:return signed?function readS8FromPointer(pointer){return GROWABLE_HEAP_I8()[pointer]}:function readU8FromPointer(pointer){return GROWABLE_HEAP_U8()[pointer]};case 1:return signed?function readS16FromPointer(pointer){return GROWABLE_HEAP_I16()[pointer>>1]}:function readU16FromPointer(pointer){return GROWABLE_HEAP_U16()[pointer>>1]};case 2:return signed?function readS32FromPointer(pointer){return GROWABLE_HEAP_I32()[pointer>>2]}:function readU32FromPointer(pointer){return GROWABLE_HEAP_U32()[pointer>>2]};default:throw new TypeError("Unknown integer type: "+name)}}function __embind_register_integer(primitiveType,name,size,minRange,maxRange){name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var shift=getShiftFromSize(size);var fromWireType=function(value){return value};if(minRange===0){var bitshift=32-8*size;fromWireType=function(value){return value<<bitshift>>>bitshift}}var isUnsignedType=name.includes("unsigned");registerType(primitiveType,{name:name,"fromWireType":fromWireType,"toWireType":function(destructors,value){if(typeof value!=="number"&&typeof value!=="boolean"){throw new TypeError('Cannot convert "'+_embind_repr(value)+'" to '+this.name)}if(value<minRange||value>maxRange){throw new TypeError('Passing a number "'+_embind_repr(value)+'" from JS side to C/C++ side to an argument of type "'+name+'", which is outside the valid range ['+minRange+", "+maxRange+"]!")}return isUnsignedType?value>>>0:value|0},"argPackAdvance":8,"readValueFromPointer":integerReadValueFromPointer(name,shift,minRange!==0),destructorFunction:null})}function __embind_register_memory_view(rawType,dataTypeIndex,name){var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){handle=handle>>2;var heap=GROWABLE_HEAP_U32();var size=heap[handle];var data=heap[handle+1];return new TA(buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":decodeMemoryView,"argPackAdvance":8,"readValueFromPointer":decodeMemoryView},{ignoreDuplicateRegistrations:true})}function __embind_register_std_string(rawType,name){name=readLatin1String(name);var stdStringIsUTF8=name==="std::string";registerType(rawType,{name:name,"fromWireType":function(value){var length=GROWABLE_HEAP_U32()[value>>2];var str;if(stdStringIsUTF8){var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i;if(i==length||GROWABLE_HEAP_U8()[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(GROWABLE_HEAP_U8()[value+4+i])}str=a.join("")}_free(value);return str},"toWireType":function(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var getLength;var valueIsOfTypeString=typeof value==="string";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){getLength=function(){return lengthBytesUTF8(value)}}else{getLength=function(){return value.length}}var length=getLength();var ptr=_malloc(4+length+1);GROWABLE_HEAP_U32()[ptr>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr+4,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}GROWABLE_HEAP_U8()[ptr+4+i]=charCode}}else{for(var i=0;i<length;++i){GROWABLE_HEAP_U8()[ptr+4+i]=value[i]}}}if(destructors!==null){destructors.push(_free,ptr)}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_std_wstring(rawType,charSize,name){name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=function(){return GROWABLE_HEAP_U16()};shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=function(){return GROWABLE_HEAP_U32()};shift=2}registerType(rawType,{name:name,"fromWireType":function(value){var length=GROWABLE_HEAP_U32()[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},"toWireType":function(destructors,value){if(!(typeof value==="string")){throwBindingError("Cannot pass non-string to C++ string type "+name)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);GROWABLE_HEAP_U32()[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_void(rawType,name){name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,"argPackAdvance":0,"fromWireType":function(){return undefined},"toWireType":function(destructors,o){return undefined}})}function __emscripten_notify_thread_queue(targetThreadId,mainThreadId){if(targetThreadId==mainThreadId){postMessage({"cmd":"processQueuedMainThreadWork"})}else if(ENVIRONMENT_IS_PTHREAD){postMessage({"targetThread":targetThreadId,"cmd":"processThreadQueue"})}else{var pthread=PThread.pthreads[targetThreadId];var worker=pthread&&pthread.worker;if(!worker){return}worker.postMessage({"cmd":"processThreadQueue"})}return 1}function __emscripten_throw_longjmp(){throw"longjmp"}function _abort(){abort()}function _emscripten_set_main_loop_timing(mode,value){Browser.mainLoop.timingMode=mode;Browser.mainLoop.timingValue=value;if(!Browser.mainLoop.func){return 1}if(!Browser.mainLoop.running){runtimeKeepalivePush();Browser.mainLoop.running=true}if(mode==0){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,Browser.mainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(Browser.mainLoop.runner,timeUntilNextTick)};Browser.mainLoop.method="timeout"}else if(mode==1){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_rAF(){Browser.requestAnimationFrame(Browser.mainLoop.runner)};Browser.mainLoop.method="rAF"}else if(mode==2){if(typeof setImmediate==="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var Browser_setImmediate_messageHandler=function(event){if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",Browser_setImmediate_messageHandler,true);setImmediate=function Browser_emulated_setImmediate(func){setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){if(Module["setImmediates"]===undefined)Module["setImmediates"]=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setImmediate(){setImmediate(Browser.mainLoop.runner)};Browser.mainLoop.method="immediate"}return 0}function runtimeKeepalivePush(){runtimeKeepaliveCounter+=1}function maybeExit(){if(!keepRuntimeAlive()){try{if(ENVIRONMENT_IS_PTHREAD)__emscripten_thread_exit(EXITSTATUS);else _exit(EXITSTATUS)}catch(e){handleException(e)}}}function setMainLoop(browserIterationFunc,fps,simulateInfiniteLoop,arg,noSetTiming){assert(!Browser.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");Browser.mainLoop.func=browserIterationFunc;Browser.mainLoop.arg=arg;var thisMainLoopId=Browser.mainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<Browser.mainLoop.currentlyRunningMainloop){runtimeKeepalivePop();maybeExit();return false}return true}Browser.mainLoop.running=false;Browser.mainLoop.runner=function Browser_mainLoop_runner(){if(ABORT)return;if(Browser.mainLoop.queue.length>0){var start=Date.now();var blocker=Browser.mainLoop.queue.shift();blocker.func(blocker.arg);if(Browser.mainLoop.remainingBlockers){var remaining=Browser.mainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){Browser.mainLoop.remainingBlockers=next}else{next=next+.5;Browser.mainLoop.remainingBlockers=(8*remaining+next)/9}}out('main loop blocker "'+blocker.name+'" took '+(Date.now()-start)+" ms");Browser.mainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(Browser.mainLoop.runner,0);return}if(!checkIsRunning())return;Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0;if(Browser.mainLoop.timingMode==1&&Browser.mainLoop.timingValue>1&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0){Browser.mainLoop.scheduler();return}else if(Browser.mainLoop.timingMode==0){Browser.mainLoop.tickStartTime=_emscripten_get_now()}if(typeof GL!=="undefined"&&GL.currentContext&&!GL.currentContextIsProxied&&!GL.currentContext.attributes.explicitSwapControl&&GL.currentContext.GLctx.commit){GL.currentContext.GLctx.commit()}Browser.mainLoop.runIter(browserIterationFunc);if(!checkIsRunning())return;if(typeof SDL==="object"&&SDL.audio&&SDL.audio.queueNewAudioData)SDL.audio.queueNewAudioData();Browser.mainLoop.scheduler()};if(!noSetTiming){if(fps&&fps>0)_emscripten_set_main_loop_timing(0,1e3/fps);else _emscripten_set_main_loop_timing(1,1);Browser.mainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}}function callUserCallback(func,synchronous){if(ABORT){return}if(synchronous){func();return}try{func();if(ENVIRONMENT_IS_PTHREAD)maybeExit()}catch(e){handleException(e)}}function runtimeKeepalivePop(){runtimeKeepaliveCounter-=1}function safeSetTimeout(func,timeout){runtimeKeepalivePush();return setTimeout(function(){runtimeKeepalivePop();callUserCallback(func)},timeout)}var Browser={mainLoop:{running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Browser.mainLoop.scheduler=null;Browser.mainLoop.currentlyRunningMainloop++},resume:function(){Browser.mainLoop.currentlyRunningMainloop++;var timingMode=Browser.mainLoop.timingMode;var timingValue=Browser.mainLoop.timingValue;var func=Browser.mainLoop.func;Browser.mainLoop.func=null;setMainLoop(func,0,false,Browser.mainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);Browser.mainLoop.scheduler()},updateStatus:function(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=Browser.mainLoop.remainingBlockers;var expected=Browser.mainLoop.expectedBlockers;if(remaining){if(remaining<expected){Module["setStatus"](message+" ("+(expected-remaining)+"/"+expected+")")}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},runIter:function(func){if(ABORT)return;if(Module["preMainLoop"]){var preRet=Module["preMainLoop"]();if(preRet===false){return}}callUserCallback(func);if(Module["postMainLoop"])Module["postMainLoop"]()}},isFullscreen:false,pointerLock:false,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(!Module["preloadPlugins"])Module["preloadPlugins"]=[];if(Browser.initted)return;Browser.initted=true;try{new Blob;Browser.hasBlobConstructor=true}catch(e){Browser.hasBlobConstructor=false;out("warning: no blob constructor, cannot create blobs with mimetypes")}Browser.BlobBuilder=typeof MozBlobBuilder!="undefined"?MozBlobBuilder:typeof WebKitBlobBuilder!="undefined"?WebKitBlobBuilder:!Browser.hasBlobConstructor?out("warning: no BlobBuilder"):null;Browser.URLObject=typeof window!="undefined"?window.URL?window.URL:window.webkitURL:undefined;if(!Module.noImageDecoding&&typeof Browser.URLObject==="undefined"){out("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available.");Module.noImageDecoding=true}var imagePlugin={};imagePlugin["canHandle"]=function imagePlugin_canHandle(name){return!Module.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(name)};imagePlugin["handle"]=function imagePlugin_handle(byteArray,name,onload,onerror){var b=null;if(Browser.hasBlobConstructor){try{b=new Blob([byteArray],{type:Browser.getMimetype(name)});if(b.size!==byteArray.length){b=new Blob([new Uint8Array(byteArray).buffer],{type:Browser.getMimetype(name)})}}catch(e){warnOnce("Blob constructor present but fails: "+e+"; falling back to blob builder")}}if(!b){var bb=new Browser.BlobBuilder;bb.append(new Uint8Array(byteArray).buffer);b=bb.getBlob()}var url=Browser.URLObject.createObjectURL(b);var img=new Image;img.onload=function img_onload(){assert(img.complete,"Image "+name+" could not be decoded");var canvas=document.createElement("canvas");canvas.width=img.width;canvas.height=img.height;var ctx=canvas.getContext("2d");ctx.drawImage(img,0,0);Module["preloadedImages"][name]=canvas;Browser.URLObject.revokeObjectURL(url);if(onload)onload(byteArray)};img.onerror=function img_onerror(event){out("Image "+url+" could not be decoded");if(onerror)onerror()};img.src=url};Module["preloadPlugins"].push(imagePlugin);var audioPlugin={};audioPlugin["canHandle"]=function audioPlugin_canHandle(name){return!Module.noAudioDecoding&&name.substr(-4)in{".ogg":1,".wav":1,".mp3":1}};audioPlugin["handle"]=function audioPlugin_handle(byteArray,name,onload,onerror){var done=false;function finish(audio){if(done)return;done=true;Module["preloadedAudios"][name]=audio;if(onload)onload(byteArray)}function fail(){if(done)return;done=true;Module["preloadedAudios"][name]=new Audio;if(onerror)onerror()}if(Browser.hasBlobConstructor){try{var b=new Blob([byteArray],{type:Browser.getMimetype(name)})}catch(e){return fail()}var url=Browser.URLObject.createObjectURL(b);var audio=new Audio;audio.addEventListener("canplaythrough",function(){finish(audio)},false);audio.onerror=function audio_onerror(event){if(done)return;out("warning: browser could not fully decode audio "+name+", trying slower base64 approach");function encode64(data){var BASE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var PAD="=";var ret="";var leftchar=0;var leftbits=0;for(var i=0;i<data.length;i++){leftchar=leftchar<<8|data[i];leftbits+=8;while(leftbits>=6){var curr=leftchar>>leftbits-6&63;leftbits-=6;ret+=BASE[curr]}}if(leftbits==2){ret+=BASE[(leftchar&3)<<4];ret+=PAD+PAD}else if(leftbits==4){ret+=BASE[(leftchar&15)<<2];ret+=PAD}return ret}audio.src="data:audio/x-"+name.substr(-3)+";base64,"+encode64(byteArray);finish(audio)};audio.src=url;safeSetTimeout(function(){finish(audio)},1e4)}else{return fail()}};Module["preloadPlugins"].push(audioPlugin);function pointerLockChange(){Browser.pointerLock=document["pointerLockElement"]===Module["canvas"]||document["mozPointerLockElement"]===Module["canvas"]||document["webkitPointerLockElement"]===Module["canvas"]||document["msPointerLockElement"]===Module["canvas"]}var canvas=Module["canvas"];if(canvas){canvas.requestPointerLock=canvas["requestPointerLock"]||canvas["mozRequestPointerLock"]||canvas["webkitRequestPointerLock"]||canvas["msRequestPointerLock"]||function(){};canvas.exitPointerLock=document["exitPointerLock"]||document["mozExitPointerLock"]||document["webkitExitPointerLock"]||document["msExitPointerLock"]||function(){};canvas.exitPointerLock=canvas.exitPointerLock.bind(document);document.addEventListener("pointerlockchange",pointerLockChange,false);document.addEventListener("mozpointerlockchange",pointerLockChange,false);document.addEventListener("webkitpointerlockchange",pointerLockChange,false);document.addEventListener("mspointerlockchange",pointerLockChange,false);if(Module["elementPointerLock"]){canvas.addEventListener("click",function(ev){if(!Browser.pointerLock&&Module["canvas"].requestPointerLock){Module["canvas"].requestPointerLock();ev.preventDefault()}},false)}}},createContext:function(canvas,useWebGL,setInModule,webGLContextAttributes){if(useWebGL&&Module.ctx&&canvas==Module.canvas)return Module.ctx;var ctx;var contextHandle;if(useWebGL){var contextAttributes={antialias:false,alpha:false,majorVersion:typeof WebGL2RenderingContext!=="undefined"?2:1};if(webGLContextAttributes){for(var attribute in webGLContextAttributes){contextAttributes[attribute]=webGLContextAttributes[attribute]}}if(typeof GL!=="undefined"){contextHandle=GL.createContext(canvas,contextAttributes);if(contextHandle){ctx=GL.getContext(contextHandle).GLctx}}}else{ctx=canvas.getContext("2d")}if(!ctx)return null;if(setInModule){if(!useWebGL)assert(typeof GLctx==="undefined","cannot set in module if GLctx is used, but we are a non-GL context that would replace it");Module.ctx=ctx;if(useWebGL)GL.makeContextCurrent(contextHandle);Module.useWebGL=useWebGL;Browser.moduleContextCreatedCallbacks.forEach(function(callback){callback()});Browser.init()}return ctx},destroyContext:function(canvas,useWebGL,setInModule){},fullscreenHandlersInstalled:false,lockPointer:undefined,resizeCanvas:undefined,requestFullscreen:function(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer==="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas==="undefined")Browser.resizeCanvas=false;var canvas=Module["canvas"];function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}if(Module["onFullScreen"])Module["onFullScreen"](Browser.isFullscreen);if(Module["onFullscreen"])Module["onFullscreen"](Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?function(){canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"])}:null)||(canvasContainer["webkitRequestFullScreen"]?function(){canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"])}:null);canvasContainer.requestFullscreen()},exitFullscreen:function(){if(!Browser.isFullscreen){return false}var CFS=document["exitFullscreen"]||document["cancelFullScreen"]||document["mozCancelFullScreen"]||document["msExitFullscreen"]||document["webkitCancelFullScreen"]||function(){};CFS.apply(document,[]);return true},nextRAF:0,fakeRequestAnimationFrame:function(func){var now=Date.now();if(Browser.nextRAF===0){Browser.nextRAF=now+1e3/60}else{while(now+2>=Browser.nextRAF){Browser.nextRAF+=1e3/60}}var delay=Math.max(Browser.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame:function(func){if(typeof requestAnimationFrame==="function"){requestAnimationFrame(func);return}var RAF=Browser.fakeRequestAnimationFrame;RAF(func)},safeSetTimeout:function(func){return safeSetTimeout(func)},safeRequestAnimationFrame:function(func){runtimeKeepalivePush();return Browser.requestAnimationFrame(function(){runtimeKeepalivePop();callUserCallback(func)})},getMimetype:function(name){return{"jpg":"image/jpeg","jpeg":"image/jpeg","png":"image/png","bmp":"image/bmp","ogg":"audio/ogg","wav":"audio/wav","mp3":"audio/mpeg"}[name.substr(name.lastIndexOf(".")+1)]},getUserMedia:function(func){if(!window.getUserMedia){window.getUserMedia=navigator["getUserMedia"]||navigator["mozGetUserMedia"]}window.getUserMedia(func)},getMovementX:function(event){return event["movementX"]||event["mozMovementX"]||event["webkitMovementX"]||0},getMovementY:function(event){return event["movementY"]||event["mozMovementY"]||event["webkitMovementY"]||0},getMouseWheelDelta:function(event){var delta=0;switch(event.type){case"DOMMouseScroll":delta=event.detail/3;break;case"mousewheel":delta=event.wheelDelta/120;break;case"wheel":delta=event.deltaY;switch(event.deltaMode){case 0:delta/=100;break;case 1:delta/=3;break;case 2:delta*=80;break;default:throw"unrecognized mouse wheel delta mode: "+event.deltaMode}break;default:throw"unrecognized mouse wheel event: "+event.type}return delta},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(event){if(Browser.pointerLock){if(event.type!="mousemove"&&"mozMovementX"in event){Browser.mouseMovementX=Browser.mouseMovementY=0}else{Browser.mouseMovementX=Browser.getMovementX(event);Browser.mouseMovementY=Browser.getMovementY(event)}if(typeof SDL!="undefined"){Browser.mouseX=SDL.mouseX+Browser.mouseMovementX;Browser.mouseY=SDL.mouseY+Browser.mouseMovementY}else{Browser.mouseX+=Browser.mouseMovementX;Browser.mouseY+=Browser.mouseMovementY}}else{var rect=Module["canvas"].getBoundingClientRect();var cw=Module["canvas"].width;var ch=Module["canvas"].height;var scrollX=typeof window.scrollX!=="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!=="undefined"?window.scrollY:window.pageYOffset;if(event.type==="touchstart"||event.type==="touchend"||event.type==="touchmove"){var touch=event.touch;if(touch===undefined){return}var adjustedX=touch.pageX-(scrollX+rect.left);var adjustedY=touch.pageY-(scrollY+rect.top);adjustedX=adjustedX*(cw/rect.width);adjustedY=adjustedY*(ch/rect.height);var coords={x:adjustedX,y:adjustedY};if(event.type==="touchstart"){Browser.lastTouches[touch.identifier]=coords;Browser.touches[touch.identifier]=coords}else if(event.type==="touchend"||event.type==="touchmove"){var last=Browser.touches[touch.identifier];if(!last)last=coords;Browser.lastTouches[touch.identifier]=last;Browser.touches[touch.identifier]=coords}return}var x=event.pageX-(scrollX+rect.left);var y=event.pageY-(scrollY+rect.top);x=x*(cw/rect.width);y=y*(ch/rect.height);Browser.mouseMovementX=x-Browser.mouseX;Browser.mouseMovementY=y-Browser.mouseY;Browser.mouseX=x;Browser.mouseY=y}},resizeListeners:[],updateResizeListeners:function(){var canvas=Module["canvas"];Browser.resizeListeners.forEach(function(listener){listener(canvas.width,canvas.height)})},setCanvasSize:function(width,height,noUpdates){var canvas=Module["canvas"];Browser.updateCanvasDimensions(canvas,width,height);if(!noUpdates)Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize:function(){if(typeof SDL!="undefined"){var flags=GROWABLE_HEAP_U32()[SDL.screen>>2];flags=flags|8388608;GROWABLE_HEAP_I32()[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL!="undefined"){var flags=GROWABLE_HEAP_U32()[SDL.screen>>2];flags=flags&~8388608;GROWABLE_HEAP_I32()[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},updateCanvasDimensions:function(canvas,wNative,hNative){if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]&&Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){if(canvas.width!=w)canvas.width=w;if(canvas.height!=h)canvas.height=h;if(typeof canvas.style!="undefined"){canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}else{if(canvas.width!=wNative)canvas.width=wNative;if(canvas.height!=hNative)canvas.height=hNative;if(typeof canvas.style!="undefined"){if(w!=wNative||h!=hNative){canvas.style.setProperty("width",w+"px","important");canvas.style.setProperty("height",h+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}}}};var AL={QUEUE_INTERVAL:25,QUEUE_LOOKAHEAD:.1,DEVICE_NAME:"Emscripten OpenAL",CAPTURE_DEVICE_NAME:"Emscripten OpenAL capture",ALC_EXTENSIONS:{ALC_SOFT_pause_device:true,ALC_SOFT_HRTF:true},AL_EXTENSIONS:{AL_EXT_float32:true,AL_SOFT_loop_points:true,AL_SOFT_source_length:true,AL_EXT_source_distance_model:true,AL_SOFT_source_spatialize:true},_alcErr:0,alcErr:0,deviceRefCounts:{},alcStringCache:{},paused:false,stringCache:{},contexts:{},currentCtx:null,buffers:{0:{id:0,refCount:0,audioBuf:null,frequency:0,bytesPerSample:2,channels:1,length:0}},paramArray:[],_nextId:1,newId:function(){return AL.freeIds.length>0?AL.freeIds.pop():AL._nextId++},freeIds:[],scheduleContextAudio:function(ctx){if(Browser.mainLoop.timingMode===1&&document["visibilityState"]!="visible"){return}for(var i in ctx.sources){AL.scheduleSourceAudio(ctx.sources[i])}},scheduleSourceAudio:function(src,lookahead){if(Browser.mainLoop.timingMode===1&&document["visibilityState"]!="visible"){return}if(src.state!==4114){return}var currentTime=AL.updateSourceTime(src);var startTime=src.bufStartTime;var startOffset=src.bufOffset;var bufCursor=src.bufsProcessed;for(var i=0;i<src.audioQueue.length;i++){var audioSrc=src.audioQueue[i];startTime=audioSrc._startTime+audioSrc._duration;startOffset=0;bufCursor+=audioSrc._skipCount+1}if(!lookahead){lookahead=AL.QUEUE_LOOKAHEAD}var lookaheadTime=currentTime+lookahead;var skipCount=0;while(startTime<lookaheadTime){if(bufCursor>=src.bufQueue.length){if(src.looping){bufCursor%=src.bufQueue.length}else{break}}var buf=src.bufQueue[bufCursor%src.bufQueue.length];if(buf.length===0){skipCount++;if(skipCount===src.bufQueue.length){break}}else{var audioSrc=src.context.audioCtx.createBufferSource();audioSrc.buffer=buf.audioBuf;audioSrc.playbackRate.value=src.playbackRate;if(buf.audioBuf._loopStart||buf.audioBuf._loopEnd){audioSrc.loopStart=buf.audioBuf._loopStart;audioSrc.loopEnd=buf.audioBuf._loopEnd}var duration=0;if(src.type===4136&&src.looping){duration=Number.POSITIVE_INFINITY;audioSrc.loop=true;if(buf.audioBuf._loopStart){audioSrc.loopStart=buf.audioBuf._loopStart}if(buf.audioBuf._loopEnd){audioSrc.loopEnd=buf.audioBuf._loopEnd}}else{duration=(buf.audioBuf.duration-startOffset)/src.playbackRate}audioSrc._startOffset=startOffset;audioSrc._duration=duration;audioSrc._skipCount=skipCount;skipCount=0;audioSrc.connect(src.gain);if(typeof audioSrc.start!=="undefined"){startTime=Math.max(startTime,src.context.audioCtx.currentTime);audioSrc.start(startTime,startOffset)}else if(typeof audioSrc.noteOn!=="undefined"){startTime=Math.max(startTime,src.context.audioCtx.currentTime);audioSrc.noteOn(startTime)}audioSrc._startTime=startTime;src.audioQueue.push(audioSrc);startTime+=duration}startOffset=0;bufCursor++}},updateSourceTime:function(src){var currentTime=src.context.audioCtx.currentTime;if(src.state!==4114){return currentTime}if(!isFinite(src.bufStartTime)){src.bufStartTime=currentTime-src.bufOffset/src.playbackRate;src.bufOffset=0}var nextStartTime=0;while(src.audioQueue.length){var audioSrc=src.audioQueue[0];src.bufsProcessed+=audioSrc._skipCount;nextStartTime=audioSrc._startTime+audioSrc._duration;if(currentTime<nextStartTime){break}src.audioQueue.shift();src.bufStartTime=nextStartTime;src.bufOffset=0;src.bufsProcessed++}if(src.bufsProcessed>=src.bufQueue.length&&!src.looping){AL.setSourceState(src,4116)}else if(src.type===4136&&src.looping){var buf=src.bufQueue[0];if(buf.length===0){src.bufOffset=0}else{var delta=(currentTime-src.bufStartTime)*src.playbackRate;var loopStart=buf.audioBuf._loopStart||0;var loopEnd=buf.audioBuf._loopEnd||buf.audioBuf.duration;if(loopEnd<=loopStart){loopEnd=buf.audioBuf.duration}if(delta<loopEnd){src.bufOffset=delta}else{src.bufOffset=loopStart+(delta-loopStart)%(loopEnd-loopStart)}}}else if(src.audioQueue[0]){src.bufOffset=(currentTime-src.audioQueue[0]._startTime)*src.playbackRate}else{if(src.type!==4136&&src.looping){var srcDuration=AL.sourceDuration(src)/src.playbackRate;if(srcDuration>0){src.bufStartTime+=Math.floor((currentTime-src.bufStartTime)/srcDuration)*srcDuration}}for(var i=0;i<src.bufQueue.length;i++){if(src.bufsProcessed>=src.bufQueue.length){if(src.looping){src.bufsProcessed%=src.bufQueue.length}else{AL.setSourceState(src,4116);break}}var buf=src.bufQueue[src.bufsProcessed];if(buf.length>0){nextStartTime=src.bufStartTime+buf.audioBuf.duration/src.playbackRate;if(currentTime<nextStartTime){src.bufOffset=(currentTime-src.bufStartTime)*src.playbackRate;break}src.bufStartTime=nextStartTime}src.bufOffset=0;src.bufsProcessed++}}return currentTime},cancelPendingSourceAudio:function(src){AL.updateSourceTime(src);for(var i=1;i<src.audioQueue.length;i++){var audioSrc=src.audioQueue[i];audioSrc.stop()}if(src.audioQueue.length>1){src.audioQueue.length=1}},stopSourceAudio:function(src){for(var i=0;i<src.audioQueue.length;i++){src.audioQueue[i].stop()}src.audioQueue.length=0},setSourceState:function(src,state){if(state===4114){if(src.state===4114||src.state==4116){src.bufsProcessed=0;src.bufOffset=0}else{}AL.stopSourceAudio(src);src.state=4114;src.bufStartTime=Number.NEGATIVE_INFINITY;AL.scheduleSourceAudio(src)}else if(state===4115){if(src.state===4114){AL.updateSourceTime(src);AL.stopSourceAudio(src);src.state=4115}}else if(state===4116){if(src.state!==4113){src.state=4116;src.bufsProcessed=src.bufQueue.length;src.bufStartTime=Number.NEGATIVE_INFINITY;src.bufOffset=0;AL.stopSourceAudio(src)}}else if(state===4113){if(src.state!==4113){src.state=4113;src.bufsProcessed=0;src.bufStartTime=Number.NEGATIVE_INFINITY;src.bufOffset=0;AL.stopSourceAudio(src)}}},initSourcePanner:function(src){if(src.type===4144){return}var templateBuf=AL.buffers[0];for(var i=0;i<src.bufQueue.length;i++){if(src.bufQueue[i].id!==0){templateBuf=src.bufQueue[i];break}}if(src.spatialize===1||src.spatialize===2&&templateBuf.channels===1){if(src.panner){return}src.panner=src.context.audioCtx.createPanner();AL.updateSourceGlobal(src);AL.updateSourceSpace(src);src.panner.connect(src.context.gain);src.gain.disconnect();src.gain.connect(src.panner)}else{if(!src.panner){return}src.panner.disconnect();src.gain.disconnect();src.gain.connect(src.context.gain);src.panner=null}},updateContextGlobal:function(ctx){for(var i in ctx.sources){AL.updateSourceGlobal(ctx.sources[i])}},updateSourceGlobal:function(src){var panner=src.panner;if(!panner){return}panner.refDistance=src.refDistance;panner.maxDistance=src.maxDistance;panner.rolloffFactor=src.rolloffFactor;panner.panningModel=src.context.hrtf?"HRTF":"equalpower";var distanceModel=src.context.sourceDistanceModel?src.distanceModel:src.context.distanceModel;switch(distanceModel){case 0:panner.distanceModel="inverse";panner.refDistance=3.40282e38;break;case 53249:case 53250:panner.distanceModel="inverse";break;case 53251:case 53252:panner.distanceModel="linear";break;case 53253:case 53254:panner.distanceModel="exponential";break}},updateListenerSpace:function(ctx){var listener=ctx.audioCtx.listener;if(listener.positionX){listener.positionX.value=ctx.listener.position[0];listener.positionY.value=ctx.listener.position[1];listener.positionZ.value=ctx.listener.position[2]}else{listener.setPosition(ctx.listener.position[0],ctx.listener.position[1],ctx.listener.position[2])}if(listener.forwardX){listener.forwardX.value=ctx.listener.direction[0];listener.forwardY.value=ctx.listener.direction[1];listener.forwardZ.value=ctx.listener.direction[2];listener.upX.value=ctx.listener.up[0];listener.upY.value=ctx.listener.up[1];listener.upZ.value=ctx.listener.up[2]}else{listener.setOrientation(ctx.listener.direction[0],ctx.listener.direction[1],ctx.listener.direction[2],ctx.listener.up[0],ctx.listener.up[1],ctx.listener.up[2])}for(var i in ctx.sources){AL.updateSourceSpace(ctx.sources[i])}},updateSourceSpace:function(src){if(!src.panner){return}var panner=src.panner;var posX=src.position[0];var posY=src.position[1];var posZ=src.position[2];var dirX=src.direction[0];var dirY=src.direction[1];var dirZ=src.direction[2];var listener=src.context.listener;var lPosX=listener.position[0];var lPosY=listener.position[1];var lPosZ=listener.position[2];if(src.relative){var lBackX=-listener.direction[0];var lBackY=-listener.direction[1];var lBackZ=-listener.direction[2];var lUpX=listener.up[0];var lUpY=listener.up[1];var lUpZ=listener.up[2];var inverseMagnitude=function(x,y,z){var length=Math.sqrt(x*x+y*y+z*z);if(length<Number.EPSILON){return 0}return 1/length};var invMag=inverseMagnitude(lBackX,lBackY,lBackZ);lBackX*=invMag;lBackY*=invMag;lBackZ*=invMag;invMag=inverseMagnitude(lUpX,lUpY,lUpZ);lUpX*=invMag;lUpY*=invMag;lUpZ*=invMag;var lRightX=lUpY*lBackZ-lUpZ*lBackY;var lRightY=lUpZ*lBackX-lUpX*lBackZ;var lRightZ=lUpX*lBackY-lUpY*lBackX;invMag=inverseMagnitude(lRightX,lRightY,lRightZ);lRightX*=invMag;lRightY*=invMag;lRightZ*=invMag;lUpX=lBackY*lRightZ-lBackZ*lRightY;lUpY=lBackZ*lRightX-lBackX*lRightZ;lUpZ=lBackX*lRightY-lBackY*lRightX;var oldX=dirX;var oldY=dirY;var oldZ=dirZ;dirX=oldX*lRightX+oldY*lUpX+oldZ*lBackX;dirY=oldX*lRightY+oldY*lUpY+oldZ*lBackY;dirZ=oldX*lRightZ+oldY*lUpZ+oldZ*lBackZ;oldX=posX;oldY=posY;oldZ=posZ;posX=oldX*lRightX+oldY*lUpX+oldZ*lBackX;posY=oldX*lRightY+oldY*lUpY+oldZ*lBackY;posZ=oldX*lRightZ+oldY*lUpZ+oldZ*lBackZ;posX+=lPosX;posY+=lPosY;posZ+=lPosZ}if(panner.positionX){panner.positionX.value=posX;panner.positionY.value=posY;panner.positionZ.value=posZ}else{panner.setPosition(posX,posY,posZ)}if(panner.orientationX){panner.orientationX.value=dirX;panner.orientationY.value=dirY;panner.orientationZ.value=dirZ}else{panner.setOrientation(dirX,dirY,dirZ)}var oldShift=src.dopplerShift;var velX=src.velocity[0];var velY=src.velocity[1];var velZ=src.velocity[2];var lVelX=listener.velocity[0];var lVelY=listener.velocity[1];var lVelZ=listener.velocity[2];if(posX===lPosX&&posY===lPosY&&posZ===lPosZ||velX===lVelX&&velY===lVelY&&velZ===lVelZ){src.dopplerShift=1}else{var speedOfSound=src.context.speedOfSound;var dopplerFactor=src.context.dopplerFactor;var slX=lPosX-posX;var slY=lPosY-posY;var slZ=lPosZ-posZ;var magSl=Math.sqrt(slX*slX+slY*slY+slZ*slZ);var vls=(slX*lVelX+slY*lVelY+slZ*lVelZ)/magSl;var vss=(slX*velX+slY*velY+slZ*velZ)/magSl;vls=Math.min(vls,speedOfSound/dopplerFactor);vss=Math.min(vss,speedOfSound/dopplerFactor);src.dopplerShift=(speedOfSound-dopplerFactor*vls)/(speedOfSound-dopplerFactor*vss)}if(src.dopplerShift!==oldShift){AL.updateSourceRate(src)}},updateSourceRate:function(src){if(src.state===4114){AL.cancelPendingSourceAudio(src);var audioSrc=src.audioQueue[0];if(!audioSrc){return}var duration;if(src.type===4136&&src.looping){duration=Number.POSITIVE_INFINITY}else{duration=(audioSrc.buffer.duration-audioSrc._startOffset)/src.playbackRate}audioSrc._duration=duration;audioSrc.playbackRate.value=src.playbackRate;AL.scheduleSourceAudio(src)}},sourceDuration:function(src){var length=0;for(var i=0;i<src.bufQueue.length;i++){var audioBuf=src.bufQueue[i].audioBuf;length+=audioBuf?audioBuf.duration:0}return length},sourceTell:function(src){AL.updateSourceTime(src);var offset=0;for(var i=0;i<src.bufsProcessed;i++){offset+=src.bufQueue[i].audioBuf.duration}offset+=src.bufOffset;return offset},sourceSeek:function(src,offset){var playing=src.state==4114;if(playing){AL.setSourceState(src,4113)}if(src.bufQueue[src.bufsProcessed].audioBuf!==null){src.bufsProcessed=0;while(offset>src.bufQueue[src.bufsProcessed].audioBuf.duration){offset-=src.bufQueue[src.bufsProcessed].audiobuf.duration;src.bufsProcessed++}src.bufOffset=offset}if(playing){AL.setSourceState(src,4114)}},getGlobalParam:function(funcname,param){if(!AL.currentCtx){return null}switch(param){case 49152:return AL.currentCtx.dopplerFactor;case 49155:return AL.currentCtx.speedOfSound;case 53248:return AL.currentCtx.distanceModel;default:AL.currentCtx.err=40962;return null}},setGlobalParam:function(funcname,param,value){if(!AL.currentCtx){return}switch(param){case 49152:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}AL.currentCtx.dopplerFactor=value;AL.updateListenerSpace(AL.currentCtx);break;case 49155:if(!Number.isFinite(value)||value<=0){AL.currentCtx.err=40963;return}AL.currentCtx.speedOfSound=value;AL.updateListenerSpace(AL.currentCtx);break;case 53248:switch(value){case 0:case 53249:case 53250:case 53251:case 53252:case 53253:case 53254:AL.currentCtx.distanceModel=value;AL.updateContextGlobal(AL.currentCtx);break;default:AL.currentCtx.err=40963;return}break;default:AL.currentCtx.err=40962;return}},getListenerParam:function(funcname,param){if(!AL.currentCtx){return null}switch(param){case 4100:return AL.currentCtx.listener.position;case 4102:return AL.currentCtx.listener.velocity;case 4111:return AL.currentCtx.listener.direction.concat(AL.currentCtx.listener.up);case 4106:return AL.currentCtx.gain.gain.value;default:AL.currentCtx.err=40962;return null}},setListenerParam:function(funcname,param,value){if(!AL.currentCtx){return}if(value===null){AL.currentCtx.err=40962;return}var listener=AL.currentCtx.listener;switch(param){case 4100:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])){AL.currentCtx.err=40963;return}listener.position[0]=value[0];listener.position[1]=value[1];listener.position[2]=value[2];AL.updateListenerSpace(AL.currentCtx);break;case 4102:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])){AL.currentCtx.err=40963;return}listener.velocity[0]=value[0];listener.velocity[1]=value[1];listener.velocity[2]=value[2];AL.updateListenerSpace(AL.currentCtx);break;case 4106:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}AL.currentCtx.gain.gain.value=value;break;case 4111:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])||!Number.isFinite(value[3])||!Number.isFinite(value[4])||!Number.isFinite(value[5])){AL.currentCtx.err=40963;return}listener.direction[0]=value[0];listener.direction[1]=value[1];listener.direction[2]=value[2];listener.up[0]=value[3];listener.up[1]=value[4];listener.up[2]=value[5];AL.updateListenerSpace(AL.currentCtx);break;default:AL.currentCtx.err=40962;return}},getBufferParam:function(funcname,bufferId,param){if(!AL.currentCtx){return}var buf=AL.buffers[bufferId];if(!buf||bufferId===0){AL.currentCtx.err=40961;return}switch(param){case 8193:return buf.frequency;case 8194:return buf.bytesPerSample*8;case 8195:return buf.channels;case 8196:return buf.length*buf.bytesPerSample*buf.channels;case 8213:if(buf.length===0){return[0,0]}else{return[(buf.audioBuf._loopStart||0)*buf.frequency,(buf.audioBuf._loopEnd||buf.length)*buf.frequency]}default:AL.currentCtx.err=40962;return null}},setBufferParam:function(funcname,bufferId,param,value){if(!AL.currentCtx){return}var buf=AL.buffers[bufferId];if(!buf||bufferId===0){AL.currentCtx.err=40961;return}if(value===null){AL.currentCtx.err=40962;return}switch(param){case 8196:if(value!==0){AL.currentCtx.err=40963;return}break;case 8213:if(value[0]<0||value[0]>buf.length||value[1]<0||value[1]>buf.Length||value[0]>=value[1]){AL.currentCtx.err=40963;return}if(buf.refCount>0){AL.currentCtx.err=40964;return}if(buf.audioBuf){buf.audioBuf._loopStart=value[0]/buf.frequency;buf.audioBuf._loopEnd=value[1]/buf.frequency}break;default:AL.currentCtx.err=40962;return}},getSourceParam:function(funcname,sourceId,param){if(!AL.currentCtx){return null}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return null}switch(param){case 514:return src.relative;case 4097:return src.coneInnerAngle;case 4098:return src.coneOuterAngle;case 4099:return src.pitch;case 4100:return src.position;case 4101:return src.direction;case 4102:return src.velocity;case 4103:return src.looping;case 4105:if(src.type===4136){return src.bufQueue[0].id}else{return 0}case 4106:return src.gain.gain.value;case 4109:return src.minGain;case 4110:return src.maxGain;case 4112:return src.state;case 4117:if(src.bufQueue.length===1&&src.bufQueue[0].id===0){return 0}else{return src.bufQueue.length}case 4118:if(src.bufQueue.length===1&&src.bufQueue[0].id===0||src.looping){return 0}else{return src.bufsProcessed}case 4128:return src.refDistance;case 4129:return src.rolloffFactor;case 4130:return src.coneOuterGain;case 4131:return src.maxDistance;case 4132:return AL.sourceTell(src);case 4133:var offset=AL.sourceTell(src);if(offset>0){offset*=src.bufQueue[0].frequency}return offset;case 4134:var offset=AL.sourceTell(src);if(offset>0){offset*=src.bufQueue[0].frequency*src.bufQueue[0].bytesPerSample}return offset;case 4135:return src.type;case 4628:return src.spatialize;case 8201:var length=0;var bytesPerFrame=0;for(var i=0;i<src.bufQueue.length;i++){length+=src.bufQueue[i].length;if(src.bufQueue[i].id!==0){bytesPerFrame=src.bufQueue[i].bytesPerSample*src.bufQueue[i].channels}}return length*bytesPerFrame;case 8202:var length=0;for(var i=0;i<src.bufQueue.length;i++){length+=src.bufQueue[i].length}return length;case 8203:return AL.sourceDuration(src);case 53248:return src.distanceModel;default:AL.currentCtx.err=40962;return null}},setSourceParam:function(funcname,sourceId,param,value){if(!AL.currentCtx){return}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return}if(value===null){AL.currentCtx.err=40962;return}switch(param){case 514:if(value===1){src.relative=true;AL.updateSourceSpace(src)}else if(value===0){src.relative=false;AL.updateSourceSpace(src)}else{AL.currentCtx.err=40963;return}break;case 4097:if(!Number.isFinite(value)){AL.currentCtx.err=40963;return}src.coneInnerAngle=value;if(src.panner){src.panner.coneInnerAngle=value%360}break;case 4098:if(!Number.isFinite(value)){AL.currentCtx.err=40963;return}src.coneOuterAngle=value;if(src.panner){src.panner.coneOuterAngle=value%360}break;case 4099:if(!Number.isFinite(value)||value<=0){AL.currentCtx.err=40963;return}if(src.pitch===value){break}src.pitch=value;AL.updateSourceRate(src);break;case 4100:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])){AL.currentCtx.err=40963;return}src.position[0]=value[0];src.position[1]=value[1];src.position[2]=value[2];AL.updateSourceSpace(src);break;case 4101:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])){AL.currentCtx.err=40963;return}src.direction[0]=value[0];src.direction[1]=value[1];src.direction[2]=value[2];AL.updateSourceSpace(src);break;case 4102:if(!Number.isFinite(value[0])||!Number.isFinite(value[1])||!Number.isFinite(value[2])){AL.currentCtx.err=40963;return}src.velocity[0]=value[0];src.velocity[1]=value[1];src.velocity[2]=value[2];AL.updateSourceSpace(src);break;case 4103:if(value===1){src.looping=true;AL.updateSourceTime(src);if(src.type===4136&&src.audioQueue.length>0){var audioSrc=src.audioQueue[0];audioSrc.loop=true;audioSrc._duration=Number.POSITIVE_INFINITY}}else if(value===0){src.looping=false;var currentTime=AL.updateSourceTime(src);if(src.type===4136&&src.audioQueue.length>0){var audioSrc=src.audioQueue[0];audioSrc.loop=false;audioSrc._duration=src.bufQueue[0].audioBuf.duration/src.playbackRate;audioSrc._startTime=currentTime-src.bufOffset/src.playbackRate}}else{AL.currentCtx.err=40963;return}break;case 4105:if(src.state===4114||src.state===4115){AL.currentCtx.err=40964;return}if(value===0){for(var i in src.bufQueue){src.bufQueue[i].refCount--}src.bufQueue.length=1;src.bufQueue[0]=AL.buffers[0];src.bufsProcessed=0;src.type=4144}else{var buf=AL.buffers[value];if(!buf){AL.currentCtx.err=40963;return}for(var i in src.bufQueue){src.bufQueue[i].refCount--}src.bufQueue.length=0;buf.refCount++;src.bufQueue=[buf];src.bufsProcessed=0;src.type=4136}AL.initSourcePanner(src);AL.scheduleSourceAudio(src);break;case 4106:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}src.gain.gain.value=value;break;case 4109:if(!Number.isFinite(value)||value<0||value>Math.min(src.maxGain,1)){AL.currentCtx.err=40963;return}src.minGain=value;break;case 4110:if(!Number.isFinite(value)||value<Math.max(0,src.minGain)||value>1){AL.currentCtx.err=40963;return}src.maxGain=value;break;case 4128:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}src.refDistance=value;if(src.panner){src.panner.refDistance=value}break;case 4129:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}src.rolloffFactor=value;if(src.panner){src.panner.rolloffFactor=value}break;case 4130:if(!Number.isFinite(value)||value<0||value>1){AL.currentCtx.err=40963;return}src.coneOuterGain=value;if(src.panner){src.panner.coneOuterGain=value}break;case 4131:if(!Number.isFinite(value)||value<0){AL.currentCtx.err=40963;return}src.maxDistance=value;if(src.panner){src.panner.maxDistance=value}break;case 4132:if(value<0||value>AL.sourceDuration(src)){AL.currentCtx.err=40963;return}AL.sourceSeek(src,value);break;case 4133:var srcLen=AL.sourceDuration(src);if(srcLen>0){var frequency;for(var bufId in src.bufQueue){if(bufId){frequency=src.bufQueue[bufId].frequency;break}}value/=frequency}if(value<0||value>srcLen){AL.currentCtx.err=40963;return}AL.sourceSeek(src,value);break;case 4134:var srcLen=AL.sourceDuration(src);if(srcLen>0){var bytesPerSec;for(var bufId in src.bufQueue){if(bufId){var buf=src.bufQueue[bufId];bytesPerSec=buf.frequency*buf.bytesPerSample*buf.channels;break}}value/=bytesPerSec}if(value<0||value>srcLen){AL.currentCtx.err=40963;return}AL.sourceSeek(src,value);break;case 4628:if(value!==0&&value!==1&&value!==2){AL.currentCtx.err=40963;return}src.spatialize=value;AL.initSourcePanner(src);break;case 8201:case 8202:case 8203:AL.currentCtx.err=40964;break;case 53248:switch(value){case 0:case 53249:case 53250:case 53251:case 53252:case 53253:case 53254:src.distanceModel=value;if(AL.currentCtx.sourceDistanceModel){AL.updateContextGlobal(AL.currentCtx)}break;default:AL.currentCtx.err=40963;return}break;default:AL.currentCtx.err=40962;return}},captures:{},sharedCaptureAudioCtx:null,requireValidCaptureDevice:function(deviceId,funcname){if(deviceId===0){AL.alcErr=40961;return null}var c=AL.captures[deviceId];if(!c){AL.alcErr=40961;return null}var err=c.mediaStreamError;if(err){AL.alcErr=40961;return null}return c}};function _alBufferData(bufferId,format,pData,size,freq){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(12,1,bufferId,format,pData,size,freq);if(!AL.currentCtx){return}var buf=AL.buffers[bufferId];if(!buf){AL.currentCtx.err=40963;return}if(freq<=0){AL.currentCtx.err=40963;return}var audioBuf=null;try{switch(format){case 4352:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(1,size,freq);var channel0=audioBuf.getChannelData(0);for(var i=0;i<size;++i){channel0[i]=GROWABLE_HEAP_U8()[pData++]*.0078125-1}}buf.bytesPerSample=1;buf.channels=1;buf.length=size;break;case 4353:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(1,size>>1,freq);var channel0=audioBuf.getChannelData(0);pData>>=1;for(var i=0;i<size>>1;++i){channel0[i]=GROWABLE_HEAP_I16()[pData++]*30517578125e-15}}buf.bytesPerSample=2;buf.channels=1;buf.length=size>>1;break;case 4354:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(2,size>>1,freq);var channel0=audioBuf.getChannelData(0);var channel1=audioBuf.getChannelData(1);for(var i=0;i<size>>1;++i){channel0[i]=GROWABLE_HEAP_U8()[pData++]*.0078125-1;channel1[i]=GROWABLE_HEAP_U8()[pData++]*.0078125-1}}buf.bytesPerSample=1;buf.channels=2;buf.length=size>>1;break;case 4355:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(2,size>>2,freq);var channel0=audioBuf.getChannelData(0);var channel1=audioBuf.getChannelData(1);pData>>=1;for(var i=0;i<size>>2;++i){channel0[i]=GROWABLE_HEAP_I16()[pData++]*30517578125e-15;channel1[i]=GROWABLE_HEAP_I16()[pData++]*30517578125e-15}}buf.bytesPerSample=2;buf.channels=2;buf.length=size>>2;break;case 65552:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(1,size>>2,freq);var channel0=audioBuf.getChannelData(0);pData>>=2;for(var i=0;i<size>>2;++i){channel0[i]=GROWABLE_HEAP_F32()[pData++]}}buf.bytesPerSample=4;buf.channels=1;buf.length=size>>2;break;case 65553:if(size>0){audioBuf=AL.currentCtx.audioCtx.createBuffer(2,size>>3,freq);var channel0=audioBuf.getChannelData(0);var channel1=audioBuf.getChannelData(1);pData>>=2;for(var i=0;i<size>>3;++i){channel0[i]=GROWABLE_HEAP_F32()[pData++];channel1[i]=GROWABLE_HEAP_F32()[pData++]}}buf.bytesPerSample=4;buf.channels=2;buf.length=size>>3;break;default:AL.currentCtx.err=40963;return}buf.frequency=freq;buf.audioBuf=audioBuf}catch(e){AL.currentCtx.err=40963;return}}function _alDeleteBuffers(count,pBufferIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(13,1,count,pBufferIds);if(!AL.currentCtx){return}for(var i=0;i<count;++i){var bufId=GROWABLE_HEAP_I32()[pBufferIds+i*4>>2];if(bufId===0){continue}if(!AL.buffers[bufId]){AL.currentCtx.err=40961;return}if(AL.buffers[bufId].refCount){AL.currentCtx.err=40964;return}}for(var i=0;i<count;++i){var bufId=GROWABLE_HEAP_I32()[pBufferIds+i*4>>2];if(bufId===0){continue}AL.deviceRefCounts[AL.buffers[bufId].deviceId]--;delete AL.buffers[bufId];AL.freeIds.push(bufId)}}function _alSourcei(sourceId,param,value){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(14,1,sourceId,param,value);switch(param){case 514:case 4097:case 4098:case 4103:case 4105:case 4128:case 4129:case 4131:case 4132:case 4133:case 4134:case 4628:case 8201:case 8202:case 53248:AL.setSourceParam("alSourcei",sourceId,param,value);break;default:AL.setSourceParam("alSourcei",sourceId,param,null);break}}function _alDeleteSources(count,pSourceIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(15,1,count,pSourceIds);if(!AL.currentCtx){return}for(var i=0;i<count;++i){var srcId=GROWABLE_HEAP_I32()[pSourceIds+i*4>>2];if(!AL.currentCtx.sources[srcId]){AL.currentCtx.err=40961;return}}for(var i=0;i<count;++i){var srcId=GROWABLE_HEAP_I32()[pSourceIds+i*4>>2];AL.setSourceState(AL.currentCtx.sources[srcId],4116);_alSourcei(srcId,4105,0);delete AL.currentCtx.sources[srcId];AL.freeIds.push(srcId)}}function _alGenBuffers(count,pBufferIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(16,1,count,pBufferIds);if(!AL.currentCtx){return}for(var i=0;i<count;++i){var buf={deviceId:AL.currentCtx.deviceId,id:AL.newId(),refCount:0,audioBuf:null,frequency:0,bytesPerSample:2,channels:1,length:0};AL.deviceRefCounts[buf.deviceId]++;AL.buffers[buf.id]=buf;GROWABLE_HEAP_I32()[pBufferIds+i*4>>2]=buf.id}}function _alGenSources(count,pSourceIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(17,1,count,pSourceIds);if(!AL.currentCtx){return}for(var i=0;i<count;++i){var gain=AL.currentCtx.audioCtx.createGain();gain.connect(AL.currentCtx.gain);var src={context:AL.currentCtx,id:AL.newId(),type:4144,state:4113,bufQueue:[AL.buffers[0]],audioQueue:[],looping:false,pitch:1,dopplerShift:1,gain:gain,minGain:0,maxGain:1,panner:null,bufsProcessed:0,bufStartTime:Number.NEGATIVE_INFINITY,bufOffset:0,relative:false,refDistance:1,maxDistance:3.40282e38,rolloffFactor:1,position:[0,0,0],velocity:[0,0,0],direction:[0,0,0],coneOuterGain:0,coneInnerAngle:360,coneOuterAngle:360,distanceModel:53250,spatialize:2,get playbackRate(){return this.pitch*this.dopplerShift}};AL.currentCtx.sources[src.id]=src;GROWABLE_HEAP_I32()[pSourceIds+i*4>>2]=src.id}}function _alGetError(){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(18,1);if(!AL.currentCtx){return 40964}else{var err=AL.currentCtx.err;AL.currentCtx.err=0;return err}}function _alGetSourcef(sourceId,param,pValue){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(19,1,sourceId,param,pValue);var val=AL.getSourceParam("alGetSourcef",sourceId,param);if(val===null){return}if(!pValue){AL.currentCtx.err=40963;return}switch(param){case 4097:case 4098:case 4099:case 4106:case 4109:case 4110:case 4128:case 4129:case 4130:case 4131:case 4132:case 4133:case 4134:case 8203:GROWABLE_HEAP_F32()[pValue>>2]=val;break;default:AL.currentCtx.err=40962;return}}function _alGetSourcei(sourceId,param,pValue){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(20,1,sourceId,param,pValue);var val=AL.getSourceParam("alGetSourcei",sourceId,param);if(val===null){return}if(!pValue){AL.currentCtx.err=40963;return}switch(param){case 514:case 4097:case 4098:case 4103:case 4105:case 4112:case 4117:case 4118:case 4128:case 4129:case 4131:case 4132:case 4133:case 4134:case 4135:case 4628:case 8201:case 8202:case 53248:GROWABLE_HEAP_I32()[pValue>>2]=val;break;default:AL.currentCtx.err=40962;return}}function _alListenerfv(param,pValues){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(21,1,param,pValues);if(!AL.currentCtx){return}if(!pValues){AL.currentCtx.err=40963;return}switch(param){case 4100:case 4102:AL.paramArray[0]=GROWABLE_HEAP_F32()[pValues>>2];AL.paramArray[1]=GROWABLE_HEAP_F32()[pValues+4>>2];AL.paramArray[2]=GROWABLE_HEAP_F32()[pValues+8>>2];AL.setListenerParam("alListenerfv",param,AL.paramArray);break;case 4111:AL.paramArray[0]=GROWABLE_HEAP_F32()[pValues>>2];AL.paramArray[1]=GROWABLE_HEAP_F32()[pValues+4>>2];AL.paramArray[2]=GROWABLE_HEAP_F32()[pValues+8>>2];AL.paramArray[3]=GROWABLE_HEAP_F32()[pValues+12>>2];AL.paramArray[4]=GROWABLE_HEAP_F32()[pValues+16>>2];AL.paramArray[5]=GROWABLE_HEAP_F32()[pValues+20>>2];AL.setListenerParam("alListenerfv",param,AL.paramArray);break;default:AL.setListenerParam("alListenerfv",param,null);break}}function _alSourcePlay(sourceId){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(22,1,sourceId);if(!AL.currentCtx){return}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return}AL.setSourceState(src,4114)}function _alSourceQueueBuffers(sourceId,count,pBufferIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(23,1,sourceId,count,pBufferIds);if(!AL.currentCtx){return}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return}if(src.type===4136){AL.currentCtx.err=40964;return}if(count===0){return}var templateBuf=AL.buffers[0];for(var i=0;i<src.bufQueue.length;i++){if(src.bufQueue[i].id!==0){templateBuf=src.bufQueue[i];break}}for(var i=0;i<count;++i){var bufId=GROWABLE_HEAP_I32()[pBufferIds+i*4>>2];var buf=AL.buffers[bufId];if(!buf){AL.currentCtx.err=40961;return}if(templateBuf.id!==0&&(buf.frequency!==templateBuf.frequency||buf.bytesPerSample!==templateBuf.bytesPerSample||buf.channels!==templateBuf.channels)){AL.currentCtx.err=40964}}if(src.bufQueue.length===1&&src.bufQueue[0].id===0){src.bufQueue.length=0}src.type=4137;for(var i=0;i<count;++i){var bufId=GROWABLE_HEAP_I32()[pBufferIds+i*4>>2];var buf=AL.buffers[bufId];buf.refCount++;src.bufQueue.push(buf)}if(src.looping){AL.cancelPendingSourceAudio(src)}AL.initSourcePanner(src);AL.scheduleSourceAudio(src)}function _alSourceStop(sourceId){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(24,1,sourceId);if(!AL.currentCtx){return}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return}AL.setSourceState(src,4116)}function _alSourceUnqueueBuffers(sourceId,count,pBufferIds){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(25,1,sourceId,count,pBufferIds);if(!AL.currentCtx){return}var src=AL.currentCtx.sources[sourceId];if(!src){AL.currentCtx.err=40961;return}if(count>(src.bufQueue.length===1&&src.bufQueue[0].id===0?0:src.bufsProcessed)){AL.currentCtx.err=40963;return}if(count===0){return}for(var i=0;i<count;i++){var buf=src.bufQueue.shift();buf.refCount--;GROWABLE_HEAP_I32()[pBufferIds+i*4>>2]=buf.id;src.bufsProcessed--}if(src.bufQueue.length===0){src.bufQueue.push(AL.buffers[0])}AL.initSourcePanner(src);AL.scheduleSourceAudio(src)}function _alSourcef(sourceId,param,value){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(26,1,sourceId,param,value);switch(param){case 4097:case 4098:case 4099:case 4106:case 4109:case 4110:case 4128:case 4129:case 4130:case 4131:case 4132:case 4133:case 4134:case 8203:AL.setSourceParam("alSourcef",sourceId,param,value);break;default:AL.setSourceParam("alSourcef",sourceId,param,null);break}}function _alSourcefv(sourceId,param,pValues){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(27,1,sourceId,param,pValues);if(!AL.currentCtx){return}if(!pValues){AL.currentCtx.err=40963;return}switch(param){case 4097:case 4098:case 4099:case 4106:case 4109:case 4110:case 4128:case 4129:case 4130:case 4131:case 4132:case 4133:case 4134:case 8203:var val=GROWABLE_HEAP_F32()[pValues>>2];AL.setSourceParam("alSourcefv",sourceId,param,val);break;case 4100:case 4101:case 4102:AL.paramArray[0]=GROWABLE_HEAP_F32()[pValues>>2];AL.paramArray[1]=GROWABLE_HEAP_F32()[pValues+4>>2];AL.paramArray[2]=GROWABLE_HEAP_F32()[pValues+8>>2];AL.setSourceParam("alSourcefv",sourceId,param,AL.paramArray);break;default:AL.setSourceParam("alSourcefv",sourceId,param,null);break}}function listenOnce(object,event,func){object.addEventListener(event,func,{"once":true})}function autoResumeAudioContext(ctx,elements){if(!elements){elements=[document,document.getElementById("canvas")]}["keydown","mousedown","touchstart"].forEach(function(event){elements.forEach(function(element){if(element){listenOnce(element,event,function(){if(ctx.state==="suspended")ctx.resume()})}})})}function _alcCreateContext(deviceId,pAttrList){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(28,1,deviceId,pAttrList);if(!(deviceId in AL.deviceRefCounts)){AL.alcErr=40961;return 0}var options=null;var attrs=[];var hrtf=null;pAttrList>>=2;if(pAttrList){var attr=0;var val=0;while(true){attr=GROWABLE_HEAP_I32()[pAttrList++];attrs.push(attr);if(attr===0){break}val=GROWABLE_HEAP_I32()[pAttrList++];attrs.push(val);switch(attr){case 4103:if(!options){options={}}options.sampleRate=val;break;case 4112:case 4113:break;case 6546:switch(val){case 0:hrtf=false;break;case 1:hrtf=true;break;case 2:break;default:AL.alcErr=40964;return 0}break;case 6550:if(val!==0){AL.alcErr=40964;return 0}break;default:AL.alcErr=40964;return 0}}}var AudioContext=window.AudioContext||window.webkitAudioContext;var ac=null;try{if(options){ac=new AudioContext(options)}else{ac=new AudioContext}}catch(e){if(e.name==="NotSupportedError"){AL.alcErr=40964}else{AL.alcErr=40961}return 0}autoResumeAudioContext(ac);if(typeof ac.createGain==="undefined"){ac.createGain=ac.createGainNode}var gain=ac.createGain();gain.connect(ac.destination);var ctx={deviceId:deviceId,id:AL.newId(),attrs:attrs,audioCtx:ac,listener:{position:[0,0,0],velocity:[0,0,0],direction:[0,0,0],up:[0,0,0]},sources:[],interval:setInterval(function(){AL.scheduleContextAudio(ctx)},AL.QUEUE_INTERVAL),gain:gain,distanceModel:53250,speedOfSound:343.3,dopplerFactor:1,sourceDistanceModel:false,hrtf:hrtf||false,_err:0,get err(){return this._err},set err(val){if(this._err===0||val===0){this._err=val}}};AL.deviceRefCounts[deviceId]++;AL.contexts[ctx.id]=ctx;if(hrtf!==null){for(var ctxId in AL.contexts){var c=AL.contexts[ctxId];if(c.deviceId===deviceId){c.hrtf=hrtf;AL.updateContextGlobal(c)}}}return ctx.id}function _alcGetCurrentContext(){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(29,1);if(AL.currentCtx!==null){return AL.currentCtx.id}else{return 0}}function _alcGetString(deviceId,param){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(30,1,deviceId,param);if(AL.alcStringCache[param]){return AL.alcStringCache[param]}var ret;switch(param){case 0:ret="No Error";break;case 40961:ret="Invalid Device";break;case 40962:ret="Invalid Context";break;case 40963:ret="Invalid Enum";break;case 40964:ret="Invalid Value";break;case 40965:ret="Out of Memory";break;case 4100:if(typeof AudioContext!=="undefined"||typeof webkitAudioContext!=="undefined"){ret=AL.DEVICE_NAME}else{return 0}break;case 4101:if(typeof AudioContext!=="undefined"||typeof webkitAudioContext!=="undefined"){ret=AL.DEVICE_NAME.concat("\0")}else{ret="\0"}break;case 785:ret=AL.CAPTURE_DEVICE_NAME;break;case 784:if(deviceId===0)ret=AL.CAPTURE_DEVICE_NAME.concat("\0");else{var c=AL.requireValidCaptureDevice(deviceId,"alcGetString");if(!c){return 0}ret=c.deviceName}break;case 4102:if(!deviceId){AL.alcErr=40961;return 0}ret="";for(var ext in AL.ALC_EXTENSIONS){ret=ret.concat(ext);ret=ret.concat(" ")}ret=ret.trim();break;default:AL.alcErr=40963;return 0}ret=allocate(intArrayFromString(ret),ALLOC_NORMAL);AL.alcStringCache[param]=ret;return ret}function _alcMakeContextCurrent(contextId){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(31,1,contextId);if(contextId===0){AL.currentCtx=null;return 0}else{AL.currentCtx=AL.contexts[contextId];return 1}}function _alcOpenDevice(pDeviceName){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(32,1,pDeviceName);if(pDeviceName){var name=UTF8ToString(pDeviceName);if(name!==AL.DEVICE_NAME){return 0}}if(typeof AudioContext!=="undefined"||typeof webkitAudioContext!=="undefined"){var deviceId=AL.newId();AL.deviceRefCounts[deviceId]=0;return deviceId}else{return 0}}var readAsmConstArgsArray=[];function readAsmConstArgs(sigPtr,buf){readAsmConstArgsArray.length=0;var ch;buf>>=2;while(ch=GROWABLE_HEAP_U8()[sigPtr++]){var double=ch<105;if(double&&buf&1)buf++;readAsmConstArgsArray.push(double?GROWABLE_HEAP_F64()[buf++>>1]:GROWABLE_HEAP_I32()[buf]);++buf}return readAsmConstArgsArray}function _emscripten_asm_const_int(code,sigPtr,argbuf){var args=readAsmConstArgs(sigPtr,argbuf);return ASM_CONSTS[code].apply(null,args)}function _emscripten_cancel_main_loop(){Browser.mainLoop.pause();Browser.mainLoop.func=null}function _emscripten_conditional_set_current_thread_status(expectedStatus,newStatus){}var JSEvents={inEventHandler:0,removeAllEventListeners:function(){for(var i=JSEvents.eventHandlers.length-1;i>=0;--i){JSEvents._removeHandler(i)}JSEvents.eventHandlers=[];JSEvents.deferredCalls=[]},registerRemoveEventListeners:function(){if(!JSEvents.removeEventListenersRegistered){__ATEXIT__.push(JSEvents.removeAllEventListeners);JSEvents.removeEventListenersRegistered=true}},deferredCalls:[],deferCall:function(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort(function(x,y){return x.precedence<y.precedence})},removeDeferredCalls:function(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests:function(){return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction.apply(null,call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(target,eventTypeString){for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler:function(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler:function(eventHandler){var jsEventHandler=function jsEventHandler(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=jsEventHandler;eventHandler.target.addEventListener(eventHandler.eventTypeString,jsEventHandler,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler);JSEvents.registerRemoveEventListeners()}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}},queueEventHandlerOnThread_iiii:function(targetThread,eventHandlerFunc,eventTypeId,eventData,userData){var stackTop=stackSave();var varargs=stackAlloc(12);GROWABLE_HEAP_I32()[varargs>>2]=eventTypeId;GROWABLE_HEAP_I32()[varargs+4>>2]=eventData;GROWABLE_HEAP_I32()[varargs+8>>2]=userData;__emscripten_call_on_thread(0,targetThread,637534208,eventHandlerFunc,eventData,varargs);stackRestore(stackTop)},getTargetThreadForEventCallback:function(targetThread){switch(targetThread){case 1:return 0;case 2:return PThread.currentProxiedOperationCallerThread;default:return targetThread}},getNodeNameForTarget:function(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target&&target.nodeName?target.nodeName:""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};function __webgl_enable_ANGLE_instanced_arrays(ctx){var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=function(index,divisor){ext["vertexAttribDivisorANGLE"](index,divisor)};ctx["drawArraysInstanced"]=function(mode,first,count,primcount){ext["drawArraysInstancedANGLE"](mode,first,count,primcount)};ctx["drawElementsInstanced"]=function(mode,count,type,indices,primcount){ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount)};return 1}}function __webgl_enable_OES_vertex_array_object(ctx){var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=function(){return ext["createVertexArrayOES"]()};ctx["deleteVertexArray"]=function(vao){ext["deleteVertexArrayOES"](vao)};ctx["bindVertexArray"]=function(vao){ext["bindVertexArrayOES"](vao)};ctx["isVertexArray"]=function(vao){return ext["isVertexArrayOES"](vao)};return 1}}function __webgl_enable_WEBGL_draw_buffers(ctx){var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=function(n,bufs){ext["drawBuffersWEBGL"](n,bufs)};return 1}}function __webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(ctx){return!!(ctx.dibvbi=ctx.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"))}function __webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(ctx){return!!(ctx.mdibvbi=ctx.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"))}function __webgl_enable_WEBGL_multi_draw(ctx){return!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"))}var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:{},offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],stringCache:{},stringiCache:{},unpackAlignment:4,recordError:function recordError(errorCode){if(!GL.lastError){GL.lastError=errorCode}},getNewId:function(table){var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},getSource:function(shader,count,string,length){var source="";for(var i=0;i<count;++i){var len=length?GROWABLE_HEAP_I32()[length+i*4>>2]:-1;source+=UTF8ToString(GROWABLE_HEAP_I32()[string+i*4>>2],len<0?undefined:len)}return source},createContext:function(canvas,webGLContextAttributes){if(Module["preinitializedWebGLContext"]){var ctx=Module["preinitializedWebGLContext"];webGLContextAttributes.majorVersion=typeof WebGL2RenderingContext!=="undefined"&&ctx instanceof WebGL2RenderingContext?2:1}else{if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;canvas.getContext=function(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}}var ctx=webGLContextAttributes.majorVersion>1?canvas.getContext("webgl2",webGLContextAttributes):canvas.getContext("webgl",webGLContextAttributes)}if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:function(ctx,webGLContextAttributes){var handle=_malloc(8);GROWABLE_HEAP_I32()[handle+4>>2]=_pthread_self();var context={handle:handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault==="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:function(contextHandle){GL.currentContext=GL.contexts[contextHandle];Module.ctx=GLctx=GL.currentContext&&GL.currentContext.GLctx;return!(contextHandle&&!GLctx)},getContext:function(contextHandle){return GL.contexts[contextHandle]},deleteContext:function(contextHandle){if(GL.currentContext===GL.contexts[contextHandle])GL.currentContext=null;if(typeof JSEvents==="object")JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas);if(GL.contexts[contextHandle]&&GL.contexts[contextHandle].GLctx.canvas)GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined;_free(GL.contexts[contextHandle].handle);GL.contexts[contextHandle]=null},initExtensions:function(context){if(!context)context=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;__webgl_enable_ANGLE_instanced_arrays(GLctx);__webgl_enable_OES_vertex_array_object(GLctx);__webgl_enable_WEBGL_draw_buffers(GLctx);__webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx);__webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx);if(context.version>=2){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query_webgl2")}if(context.version<2||!GLctx.disjointTimerQueryExt){GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}__webgl_enable_WEBGL_multi_draw(GLctx);var exts=GLctx.getSupportedExtensions()||[];exts.forEach(function(ext){if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};function maybeCStringToJsString(cString){return cString>2?UTF8ToString(cString):cString}function findCanvasEventTarget(target){target=maybeCStringToJsString(target);return GL.offscreenCanvases[target.substr(1)]||target=="canvas"&&Object.keys(GL.offscreenCanvases)[0]||typeof document!=="undefined"&&document.querySelector(target)}function _emscripten_get_canvas_element_size_calling_thread(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;if(canvas.canvasSharedPtr){var w=GROWABLE_HEAP_I32()[canvas.canvasSharedPtr>>2];var h=GROWABLE_HEAP_I32()[canvas.canvasSharedPtr+4>>2];GROWABLE_HEAP_I32()[width>>2]=w;GROWABLE_HEAP_I32()[height>>2]=h}else if(canvas.offscreenCanvas){GROWABLE_HEAP_I32()[width>>2]=canvas.offscreenCanvas.width;GROWABLE_HEAP_I32()[height>>2]=canvas.offscreenCanvas.height}else if(!canvas.controlTransferredOffscreen){GROWABLE_HEAP_I32()[width>>2]=canvas.width;GROWABLE_HEAP_I32()[height>>2]=canvas.height}else{return-4}return 0}function _emscripten_get_canvas_element_size_main_thread(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(33,1,target,width,height);return _emscripten_get_canvas_element_size_calling_thread(target,width,height)}function _emscripten_get_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(canvas){return _emscripten_get_canvas_element_size_calling_thread(target,width,height)}else{return _emscripten_get_canvas_element_size_main_thread(target,width,height)}}function _emscripten_memcpy_big(dest,src,num){GROWABLE_HEAP_U8().copyWithin(dest,src,src+num)}function _emscripten_proxy_to_main_thread_js(index,sync){var numCallArgs=arguments.length-2;var stack=stackSave();var serializedNumCallArgs=numCallArgs;var args=stackAlloc(serializedNumCallArgs*8);var b=args>>3;for(var i=0;i<numCallArgs;i++){var arg=arguments[2+i];GROWABLE_HEAP_F64()[b+i]=arg}var ret=_emscripten_run_in_main_runtime_thread_js(index,serializedNumCallArgs,args,sync);stackRestore(stack);return ret}var _emscripten_receive_on_main_thread_js_callArgs=[];function _emscripten_receive_on_main_thread_js(index,numCallArgs,args){_emscripten_receive_on_main_thread_js_callArgs.length=numCallArgs;var b=args>>3;for(var i=0;i<numCallArgs;i++){_emscripten_receive_on_main_thread_js_callArgs[i]=GROWABLE_HEAP_F64()[b+i]}var isEmAsmConst=index<0;var func=!isEmAsmConst?proxiedFunctionTable[index]:ASM_CONSTS[-index-1];return func.apply(null,_emscripten_receive_on_main_thread_js_callArgs)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=GROWABLE_HEAP_U8().length;requestedSize=requestedSize>>>0;if(requestedSize<=oldSize){return false}var maxHeapSize=1048576e3;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function stringToNewUTF8(jsString){var length=lengthBytesUTF8(jsString)+1;var cString=_malloc(length);stringToUTF8(jsString,cString,length);return cString}function _emscripten_set_offscreencanvas_size_on_target_thread_js(targetThread,targetCanvas,width,height){var stackTop=stackSave();var varargs=stackAlloc(12);var targetCanvasPtr=0;if(targetCanvas){targetCanvasPtr=stringToNewUTF8(targetCanvas)}GROWABLE_HEAP_I32()[varargs>>2]=targetCanvasPtr;GROWABLE_HEAP_I32()[varargs+4>>2]=width;GROWABLE_HEAP_I32()[varargs+8>>2]=height;__emscripten_call_on_thread(0,targetThread,657457152,0,targetCanvasPtr,varargs);stackRestore(stackTop)}function _emscripten_set_offscreencanvas_size_on_target_thread(targetThread,targetCanvas,width,height){targetCanvas=targetCanvas?UTF8ToString(targetCanvas):"";_emscripten_set_offscreencanvas_size_on_target_thread_js(targetThread,targetCanvas,width,height)}function _emscripten_set_canvas_element_size_calling_thread(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;if(canvas.canvasSharedPtr){GROWABLE_HEAP_I32()[canvas.canvasSharedPtr>>2]=width;GROWABLE_HEAP_I32()[canvas.canvasSharedPtr+4>>2]=height}if(canvas.offscreenCanvas||!canvas.controlTransferredOffscreen){if(canvas.offscreenCanvas)canvas=canvas.offscreenCanvas;var autoResizeViewport=false;if(canvas.GLctxObject&&canvas.GLctxObject.GLctx){var prevViewport=canvas.GLctxObject.GLctx.getParameter(2978);autoResizeViewport=prevViewport[0]===0&&prevViewport[1]===0&&prevViewport[2]===canvas.width&&prevViewport[3]===canvas.height}canvas.width=width;canvas.height=height;if(autoResizeViewport){canvas.GLctxObject.GLctx.viewport(0,0,width,height)}}else if(canvas.canvasSharedPtr){var targetThread=GROWABLE_HEAP_I32()[canvas.canvasSharedPtr+8>>2];_emscripten_set_offscreencanvas_size_on_target_thread(targetThread,target,width,height);return 1}else{return-4}return 0}function _emscripten_set_canvas_element_size_main_thread(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(34,1,target,width,height);return _emscripten_set_canvas_element_size_calling_thread(target,width,height)}function _emscripten_set_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(canvas){return _emscripten_set_canvas_element_size_calling_thread(target,width,height)}else{return _emscripten_set_canvas_element_size_main_thread(target,width,height)}}function _emscripten_set_current_thread_status(newStatus){}function _emscripten_set_main_loop_arg(func,arg,fps,simulateInfiniteLoop){var browserIterationFunc=function(){wasmTable.get(func)(arg)};setMainLoop(browserIterationFunc,fps,simulateInfiniteLoop,arg)}function _emscripten_set_timeout(cb,msecs,userData){runtimeKeepalivePush();return setTimeout(function(){runtimeKeepalivePop();callUserCallback(function(){wasmTable.get(cb)(userData)})},msecs)}function _emscripten_supports_offscreencanvas(){return typeof OffscreenCanvas!=="undefined"}function _emscripten_unwind_to_js_event_loop(){throw"unwind"}function _emscripten_webgl_do_commit_frame(){if(!GL.currentContext||!GL.currentContext.GLctx){return-3}if(!GL.currentContext.attributes.explicitSwapControl){return-3}return 0}function _emscripten_webgl_commit_frame(){return _emscripten_webgl_do_commit_frame()}var __emscripten_webgl_power_preferences=["default","low-power","high-performance"];function _emscripten_webgl_do_create_context(target,attributes){var a=attributes>>2;var powerPreference=GROWABLE_HEAP_I32()[a+(24>>2)];var contextAttributes={"alpha":!!GROWABLE_HEAP_I32()[a+(0>>2)],"depth":!!GROWABLE_HEAP_I32()[a+(4>>2)],"stencil":!!GROWABLE_HEAP_I32()[a+(8>>2)],"antialias":!!GROWABLE_HEAP_I32()[a+(12>>2)],"premultipliedAlpha":!!GROWABLE_HEAP_I32()[a+(16>>2)],"preserveDrawingBuffer":!!GROWABLE_HEAP_I32()[a+(20>>2)],"powerPreference":__emscripten_webgl_power_preferences[powerPreference],"failIfMajorPerformanceCaveat":!!GROWABLE_HEAP_I32()[a+(28>>2)],majorVersion:GROWABLE_HEAP_I32()[a+(32>>2)],minorVersion:GROWABLE_HEAP_I32()[a+(36>>2)],enableExtensionsByDefault:GROWABLE_HEAP_I32()[a+(40>>2)],explicitSwapControl:GROWABLE_HEAP_I32()[a+(44>>2)],proxyContextToMainThread:GROWABLE_HEAP_I32()[a+(48>>2)],renderViaOffscreenBackBuffer:GROWABLE_HEAP_I32()[a+(52>>2)]};var canvas=findCanvasEventTarget(target);if(!canvas){return 0}if(canvas.offscreenCanvas)canvas=canvas.offscreenCanvas;if(contextAttributes.explicitSwapControl){var supportsOffscreenCanvas=canvas.transferControlToOffscreen||typeof OffscreenCanvas!=="undefined"&&canvas instanceof OffscreenCanvas;if(!supportsOffscreenCanvas){return 0}if(canvas.transferControlToOffscreen){if(!canvas.controlTransferredOffscreen){GL.offscreenCanvases[canvas.id]={canvas:canvas.transferControlToOffscreen(),canvasSharedPtr:_malloc(12),id:canvas.id};canvas.controlTransferredOffscreen=true}else if(!GL.offscreenCanvases[canvas.id]){return 0}canvas=GL.offscreenCanvases[canvas.id]}}var contextHandle=GL.createContext(canvas,contextAttributes);return contextHandle}function _emscripten_webgl_create_context(a0,a1){return _emscripten_webgl_do_create_context(a0,a1)}function _emscripten_webgl_do_get_current_context(){return GL.currentContext?GL.currentContext.handle:0}function _emscripten_webgl_get_current_context(){return _emscripten_webgl_do_get_current_context()}Module["_emscripten_webgl_get_current_context"]=_emscripten_webgl_get_current_context;function _emscripten_webgl_make_context_current(contextHandle){var success=GL.makeContextCurrent(contextHandle);return success?0:-5}Module["_emscripten_webgl_make_context_current"]=_emscripten_webgl_make_context_current;function _emscripten_webgl_destroy_context_calling_thread(contextHandle){if(GL.currentContext==contextHandle)GL.currentContext=0;GL.deleteContext(contextHandle)}function _emscripten_webgl_destroy_context_main_thread(a0){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(35,1,a0);return _emscripten_webgl_destroy_context_calling_thread(a0)}function _emscripten_webgl_destroy_context_before_on_calling_thread(contextHandle){if(_emscripten_webgl_get_current_context()==contextHandle)_emscripten_webgl_make_context_current(0)}function _emscripten_webgl_destroy_context(p0){_emscripten_webgl_destroy_context_before_on_calling_thread(p0);GL.contexts[p0]?_emscripten_webgl_destroy_context_calling_thread(p0):_emscripten_webgl_destroy_context_main_thread(p0)}function _emscripten_webgl_init_context_attributes(attributes){var a=attributes>>2;for(var i=0;i<56>>2;++i){GROWABLE_HEAP_I32()[a+i]=0}GROWABLE_HEAP_I32()[a+(0>>2)]=GROWABLE_HEAP_I32()[a+(4>>2)]=GROWABLE_HEAP_I32()[a+(12>>2)]=GROWABLE_HEAP_I32()[a+(16>>2)]=GROWABLE_HEAP_I32()[a+(32>>2)]=GROWABLE_HEAP_I32()[a+(40>>2)]=1;if(ENVIRONMENT_IS_WORKER)GROWABLE_HEAP_I32()[attributes+48>>2]=1}var ENV={};function getExecutableName(){return thisProgram||"./this.program"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator==="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+"="+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}function _environ_get(__environ,environ_buf){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(36,1,__environ,environ_buf);var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;GROWABLE_HEAP_I32()[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(37,1,penviron_count,penviron_buf_size);var strings=getEnvStrings();GROWABLE_HEAP_I32()[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});GROWABLE_HEAP_I32()[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(38,1,fd);try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return e.errno}}function _fd_read(fd,iov,iovcnt,pnum){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(39,1,fd,iov,iovcnt,pnum);try{var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doReadv(stream,iov,iovcnt);GROWABLE_HEAP_I32()[pnum>>2]=num;return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return e.errno}}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(40,1,fd,offset_low,offset_high,whence,newOffset);try{var stream=SYSCALLS.getStreamFromFD(fd);var HIGH_OFFSET=4294967296;var offset=offset_high*HIGH_OFFSET+(offset_low>>>0);var DOUBLE_LIMIT=9007199254740992;if(offset<=-DOUBLE_LIMIT||offset>=DOUBLE_LIMIT){return-61}FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?(Math.min(+Math.floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[newOffset>>2]=tempI64[0],GROWABLE_HEAP_I32()[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return e.errno}}function _fd_write(fd,iov,iovcnt,pnum){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(41,1,fd,iov,iovcnt,pnum);try{var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doWritev(stream,iov,iovcnt);GROWABLE_HEAP_I32()[pnum>>2]=num;return 0}catch(e){if(typeof FS==="undefined"||!(e instanceof FS.ErrnoError))abort(e);return e.errno}}function _getTempRet0(){return getTempRet0()}function _gettimeofday(ptr){var now=Date.now();GROWABLE_HEAP_I32()[ptr>>2]=now/1e3|0;GROWABLE_HEAP_I32()[ptr+4>>2]=now%1e3*1e3|0;return 0}function _glActiveTexture(x0){GLctx["activeTexture"](x0)}function _glAttachShader(program,shader){GLctx.attachShader(GL.programs[program],GL.shaders[shader])}function _glBindBuffer(target,buffer){if(target==35051){GLctx.currentPixelPackBufferBinding=buffer}else if(target==35052){GLctx.currentPixelUnpackBufferBinding=buffer}GLctx.bindBuffer(target,GL.buffers[buffer])}function _glBindFramebuffer(target,framebuffer){GLctx.bindFramebuffer(target,GL.framebuffers[framebuffer])}function _glBindTexture(target,texture){GLctx.bindTexture(target,GL.textures[texture])}function _glBlendFunc(x0,x1){GLctx["blendFunc"](x0,x1)}function _glBufferData(target,size,data,usage){if(GL.currentContext.version>=2){if(data){GLctx.bufferData(target,GROWABLE_HEAP_U8(),usage,data,size)}else{GLctx.bufferData(target,size,usage)}}else{GLctx.bufferData(target,data?GROWABLE_HEAP_U8().subarray(data,data+size):size,usage)}}function _glClear(x0){GLctx["clear"](x0)}function _glCompileShader(shader){GLctx.compileShader(GL.shaders[shader])}function _glCreateProgram(){var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id}function _glCreateShader(shaderType){var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id}function _glDeleteBuffers(n,buffers){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[buffers+i*4>>2];var buffer=GL.buffers[id];if(!buffer)continue;GLctx.deleteBuffer(buffer);buffer.name=0;GL.buffers[id]=null;if(id==GLctx.currentPixelPackBufferBinding)GLctx.currentPixelPackBufferBinding=0;if(id==GLctx.currentPixelUnpackBufferBinding)GLctx.currentPixelUnpackBufferBinding=0}}function _glDeleteFramebuffers(n,framebuffers){for(var i=0;i<n;++i){var id=GROWABLE_HEAP_I32()[framebuffers+i*4>>2];var framebuffer=GL.framebuffers[id];if(!framebuffer)continue;GLctx.deleteFramebuffer(framebuffer);framebuffer.name=0;GL.framebuffers[id]=null}}function _glDeleteProgram(id){if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null}function _glDeleteRenderbuffers(n,renderbuffers){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[renderbuffers+i*4>>2];var renderbuffer=GL.renderbuffers[id];if(!renderbuffer)continue;GLctx.deleteRenderbuffer(renderbuffer);renderbuffer.name=0;GL.renderbuffers[id]=null}}function _glDeleteShader(id){if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null}function _glDeleteTextures(n,textures){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[textures+i*4>>2];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}}function _glDepthFunc(x0){GLctx["depthFunc"](x0)}function _glDepthMask(flag){GLctx.depthMask(!!flag)}function _glDetachShader(program,shader){GLctx.detachShader(GL.programs[program],GL.shaders[shader])}function _glDisable(x0){GLctx["disable"](x0)}function _glDrawArrays(mode,first,count){GLctx.drawArrays(mode,first,count)}function _glEnable(x0){GLctx["enable"](x0)}function _glEnableVertexAttribArray(index){GLctx.enableVertexAttribArray(index)}function _glFramebufferTexture2D(target,attachment,textarget,texture,level){GLctx.framebufferTexture2D(target,attachment,textarget,GL.textures[texture],level)}function __glGenObject(n,buffers,createFunction,objectTable){for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}GROWABLE_HEAP_I32()[buffers+i*4>>2]=id}}function _glGenBuffers(n,buffers){__glGenObject(n,buffers,"createBuffer",GL.buffers)}function _glGenFramebuffers(n,ids){__glGenObject(n,ids,"createFramebuffer",GL.framebuffers)}function _glGenTextures(n,textures){__glGenObject(n,textures,"createTexture",GL.textures)}function _glGenerateMipmap(x0){GLctx["generateMipmap"](x0)}function _glGetAttribLocation(program,name){return GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name))}function writeI53ToI64(ptr,num){GROWABLE_HEAP_U32()[ptr>>2]=num;GROWABLE_HEAP_U32()[ptr+4>>2]=(num-GROWABLE_HEAP_U32()[ptr>>2])/4294967296}function emscriptenWebGLGet(name_,p,type){if(!p){GL.recordError(1281);return}var ret=undefined;switch(name_){case 36346:ret=1;break;case 36344:if(type!=0&&type!=1){GL.recordError(1280)}return;case 34814:case 36345:ret=0;break;case 34466:var formats=GLctx.getParameter(34467);ret=formats?formats.length:0;break;case 33309:if(GL.currentContext.version<2){GL.recordError(1282);return}var exts=GLctx.getSupportedExtensions()||[];ret=2*exts.length;break;case 33307:case 33308:if(GL.currentContext.version<2){GL.recordError(1280);return}ret=name_==33307?3:0;break}if(ret===undefined){var result=GLctx.getParameter(name_);switch(typeof result){case"number":ret=result;break;case"boolean":ret=result?1:0;break;case"string":GL.recordError(1280);return;case"object":if(result===null){switch(name_){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:{ret=0;break}default:{GL.recordError(1280);return}}}else if(result instanceof Float32Array||result instanceof Uint32Array||result instanceof Int32Array||result instanceof Array){for(var i=0;i<result.length;++i){switch(type){case 0:GROWABLE_HEAP_I32()[p+i*4>>2]=result[i];break;case 2:GROWABLE_HEAP_F32()[p+i*4>>2]=result[i];break;case 4:GROWABLE_HEAP_I8()[p+i>>0]=result[i]?1:0;break}}return}else{try{ret=result.name|0}catch(e){GL.recordError(1280);err("GL_INVALID_ENUM in glGet"+type+"v: Unknown object returned from WebGL getParameter("+name_+")! (error: "+e+")");return}}break;default:GL.recordError(1280);err("GL_INVALID_ENUM in glGet"+type+"v: Native code calling glGet"+type+"v("+name_+") and it returns "+result+" of type "+typeof result+"!");return}}switch(type){case 1:writeI53ToI64(p,ret);break;case 0:GROWABLE_HEAP_I32()[p>>2]=ret;break;case 2:GROWABLE_HEAP_F32()[p>>2]=ret;break;case 4:GROWABLE_HEAP_I8()[p>>0]=ret?1:0;break}}function _glGetIntegerv(name_,p){emscriptenWebGLGet(name_,p,0)}function _glGetProgramiv(program,pname,p){if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";GROWABLE_HEAP_I32()[p>>2]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){for(var i=0;i<GLctx.getProgramParameter(program,35718);++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){for(var i=0;i<GLctx.getProgramParameter(program,35721);++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){for(var i=0;i<GLctx.getProgramParameter(program,35382);++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxUniformBlockNameLength}else{GROWABLE_HEAP_I32()[p>>2]=GLctx.getProgramParameter(program,pname)}}function _glGetShaderInfoLog(shader,maxLength,length,infoLog){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)GROWABLE_HEAP_I32()[length>>2]=numBytesWrittenExclNull}function _glGetShaderiv(shader,pname,p){if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;GROWABLE_HEAP_I32()[p>>2]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;GROWABLE_HEAP_I32()[p>>2]=sourceLength}else{GROWABLE_HEAP_I32()[p>>2]=GLctx.getShaderParameter(GL.shaders[shader],pname)}}function jstoi_q(str){return parseInt(str)}function webglGetLeftBracePos(name){return name.slice(-1)=="]"&&name.lastIndexOf("[")}function webglPrepareUniformLocationsBeforeFirstUse(program){var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};for(i=0;i<GLctx.getProgramParameter(program,35718);++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}}function _glGetUniformLocation(program,name){name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1}function _glLinkProgram(program){program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}}function _glScissor(x0,x1,x2,x3){GLctx["scissor"](x0,x1,x2,x3)}function _glShaderSource(shader,count,string,length){var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)}function computeUnpackAlignedImageSize(width,height,sizePerPixel,alignment){function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=width*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,alignment);return height*alignedRowSize}function __colorChannelsInGlTextureFormat(format){var colorChannels={5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4};return colorChannels[format-6402]||1}function heapObjectForWebGLType(type){type-=5120;if(type==0)return GROWABLE_HEAP_I8();if(type==1)return GROWABLE_HEAP_U8();if(type==2)return GROWABLE_HEAP_I16();if(type==4)return GROWABLE_HEAP_I32();if(type==6)return GROWABLE_HEAP_F32();if(type==5||type==28922||type==28520||type==30779||type==30782)return GROWABLE_HEAP_U32();return GROWABLE_HEAP_U16()}function heapAccessShiftForWebGLHeap(heap){return 31-Math.clz32(heap.BYTES_PER_ELEMENT)}function emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat){var heap=heapObjectForWebGLType(type);var shift=heapAccessShiftForWebGLHeap(heap);var byteSize=1<<shift;var sizePerPixel=__colorChannelsInGlTextureFormat(format)*byteSize;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel,GL.unpackAlignment);return heap.subarray(pixels>>shift,pixels+bytes>>shift)}function _glTexImage2D(target,level,internalFormat,width,height,border,format,type,pixels){if(GL.currentContext.version>=2){if(GLctx.currentPixelUnpackBufferBinding){GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels)}else if(pixels){var heap=heapObjectForWebGLType(type);GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,heap,pixels>>heapAccessShiftForWebGLHeap(heap))}else{GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,null)}return}GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null)}function _glTexParameteri(x0,x1,x2){GLctx["texParameteri"](x0,x1,x2)}function webglGetUniformLocation(location){var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc==="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?"["+webglLoc+"]":""))}return webglLoc}else{GL.recordError(1282)}}function _glUniform1f(location,v0){GLctx.uniform1f(webglGetUniformLocation(location),v0)}function _glUniform1i(location,v0){GLctx.uniform1i(webglGetUniformLocation(location),v0)}function _glUniform4f(location,v0,v1,v2,v3){GLctx.uniform4f(webglGetUniformLocation(location),v0,v1,v2,v3)}var miniTempWebGLFloatBuffers=[];function _glUniformMatrix4fv(location,count,transpose,value){if(GL.currentContext.version>=2){GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,GROWABLE_HEAP_F32(),value>>2,count*16);return}if(count<=18){var view=miniTempWebGLFloatBuffers[16*count-1];var heap=GROWABLE_HEAP_F32();value>>=2;for(var i=0;i<16*count;i+=16){var dst=value+i;view[i]=heap[dst];view[i+1]=heap[dst+1];view[i+2]=heap[dst+2];view[i+3]=heap[dst+3];view[i+4]=heap[dst+4];view[i+5]=heap[dst+5];view[i+6]=heap[dst+6];view[i+7]=heap[dst+7];view[i+8]=heap[dst+8];view[i+9]=heap[dst+9];view[i+10]=heap[dst+10];view[i+11]=heap[dst+11];view[i+12]=heap[dst+12];view[i+13]=heap[dst+13];view[i+14]=heap[dst+14];view[i+15]=heap[dst+15]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*64>>2)}GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,view)}function _glUseProgram(program){program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program}function _glVertexAttribPointer(index,size,type,normalized,stride,ptr){GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)}function _glViewport(x0,x1,x2,x3){GLctx["viewport"](x0,x1,x2,x3)}function _setTempRet0(val){setTempRet0(val)}function _time(ptr){var ret=Date.now()/1e3|0;if(ptr){GROWABLE_HEAP_I32()[ptr>>2]=ret}return ret}if(!ENVIRONMENT_IS_PTHREAD)PThread.initMainThreadBlock();var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.staticInit();Module["FS_createPath"]=FS.createPath;Module["FS_createDataFile"]=FS.createDataFile;Module["FS_createPreloadedFile"]=FS.createPreloadedFile;Module["FS_createLazyFile"]=FS.createLazyFile;Module["FS_createDevice"]=FS.createDevice;Module["FS_unlink"]=FS.unlink;embind_init_charCodes();BindingError=Module["BindingError"]=extendError(Error,"BindingError");InternalError=Module["InternalError"]=extendError(Error,"InternalError");init_emval();Module["requestFullscreen"]=function Module_requestFullscreen(lockPointer,resizeCanvas){Browser.requestFullscreen(lockPointer,resizeCanvas)};Module["requestAnimationFrame"]=function Module_requestAnimationFrame(func){Browser.requestAnimationFrame(func)};Module["setCanvasSize"]=function Module_setCanvasSize(width,height,noUpdates){Browser.setCanvasSize(width,height,noUpdates)};Module["pauseMainLoop"]=function Module_pauseMainLoop(){Browser.mainLoop.pause()};Module["resumeMainLoop"]=function Module_resumeMainLoop(){Browser.mainLoop.resume()};Module["getUserMedia"]=function Module_getUserMedia(){Browser.getUserMedia()};Module["createContext"]=function Module_createContext(canvas,useWebGL,setInModule,webGLContextAttributes){return Browser.createContext(canvas,useWebGL,setInModule,webGLContextAttributes)};var GLctx;var miniTempWebGLFloatBuffersStorage=new Float32Array(288);for(var i=0;i<288;++i){miniTempWebGLFloatBuffers[i]=miniTempWebGLFloatBuffersStorage.subarray(0,i+1)}var proxiedFunctionTable=[null,_AdditionDataCB,_DecCB,_FirstFrameCB,_RawDataCB,_RunTimeInfoCB,_YUVDisplayCB,_atexit,_tzset_impl,___sys_fcntl64,___sys_ioctl,___sys_open,_alBufferData,_alDeleteBuffers,_alSourcei,_alDeleteSources,_alGenBuffers,_alGenSources,_alGetError,_alGetSourcef,_alGetSourcei,_alListenerfv,_alSourcePlay,_alSourceQueueBuffers,_alSourceStop,_alSourceUnqueueBuffers,_alSourcef,_alSourcefv,_alcCreateContext,_alcGetCurrentContext,_alcGetString,_alcMakeContextCurrent,_alcOpenDevice,_emscripten_get_canvas_element_size_main_thread,_emscripten_set_canvas_element_size_main_thread,_emscripten_webgl_destroy_context_main_thread,_environ_get,_environ_sizes_get,_fd_close,_fd_read,_fd_seek,_fd_write];function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var decodeBase64=typeof atob==="function"?atob:function(input){var keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{enc1=keyStr.indexOf(input.charAt(i++));enc2=keyStr.indexOf(input.charAt(i++));enc3=keyStr.indexOf(input.charAt(i++));enc4=keyStr.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;output=output+String.fromCharCode(chr1);if(enc3!==64){output=output+String.fromCharCode(chr2)}if(enc4!==64){output=output+String.fromCharCode(chr3)}}while(i<input.length);return output};var asmLibraryArg={"Va":_AdditionDataCB,"Ya":_DecCB,"zb":_FirstFrameCB,"Wa":_RawDataCB,"Ua":_RunTimeInfoCB,"Xa":_YUVDisplayCB,"d":___assert_fail,"Db":___clock_gettime,"c":___cxa_allocate_exception,"Fb":___cxa_thread_atexit,"b":___cxa_throw,"cb":___emscripten_init_main_thread_js,"Pa":___localtime_r,"ga":___pthread_create_js,"bb":___pthread_detached_exit,"mb":___pthread_exit_run_handlers,"Ta":___pthread_join_js,"Z":___sys_fcntl64,"Ra":___sys_ioctl,"Sa":___sys_open,"ib":__embind_register_bigint,"fb":__embind_register_bool,"Na":__embind_register_emval,"S":__embind_register_float,"i":__embind_register_integer,"h":__embind_register_memory_view,"$":__embind_register_std_string,"E":__embind_register_std_wstring,"Eb":__embind_register_void,"gb":__emscripten_notify_thread_queue,"na":__emscripten_throw_longjmp,"N":_abort,"ea":_alBufferData,"vb":_alDeleteBuffers,"wb":_alDeleteSources,"sb":_alGenBuffers,"rb":_alGenSources,"pb":_alGetError,"kb":_alGetSourcef,"w":_alGetSourcei,"I":_alListenerfv,"lb":_alSourcePlay,"da":_alSourceQueueBuffers,"K":_alSourceStop,"A":_alSourceUnqueueBuffers,"J":_alSourcef,"fa":_alSourcefv,"qb":_alSourcei,"ub":_alcCreateContext,"ha":_alcGetCurrentContext,"ob":_alcGetString,"tb":_alcMakeContextCurrent,"nb":_alcOpenDevice,"s":_clock_gettime,"F":_emscripten_asm_const_int,"H":_emscripten_cancel_main_loop,"Oa":_emscripten_check_blocking_allowed,"z":_emscripten_conditional_set_current_thread_status,"q":_emscripten_futex_wait,"l":_emscripten_futex_wake,"Ka":_emscripten_get_canvas_element_size,"m":_emscripten_get_now,"Za":_emscripten_memcpy_big,"eb":_emscripten_receive_on_main_thread_js,"_a":_emscripten_resize_heap,"aa":_emscripten_set_canvas_element_size,"ba":_emscripten_set_current_thread_status,"ca":_emscripten_set_main_loop_arg,"db":_emscripten_set_timeout,"jb":_emscripten_supports_offscreencanvas,"$a":_emscripten_unwind_to_js_event_loop,"La":_emscripten_webgl_commit_frame,"G":_emscripten_webgl_create_context,"W":_emscripten_webgl_destroy_context,"V":_emscripten_webgl_get_current_context,"Ma":_emscripten_webgl_init_context_attributes,"D":_emscripten_webgl_make_context_current,"xb":_environ_get,"yb":_environ_sizes_get,"ab":_exit,"Y":_fd_close,"Qa":_fd_read,"hb":_fd_seek,"_":_fd_write,"n":_getTempRet0,"p":_gettimeofday,"qa":_glActiveTexture,"Q":_glAttachShader,"e":_glBindBuffer,"T":_glBindFramebuffer,"C":_glBindTexture,"L":_glBlendFunc,"Ba":_glBufferData,"Ab":_glClear,"va":_glCompileShader,"Aa":_glCreateProgram,"xa":_glCreateShader,"Da":_glDeleteBuffers,"Ia":_glDeleteFramebuffers,"R":_glDeleteProgram,"Ha":_glDeleteRenderbuffers,"k":_glDeleteShader,"U":_glDeleteTextures,"Cb":_glDepthFunc,"Bb":_glDepthMask,"x":_glDetachShader,"t":_glDisable,"B":_glDrawArrays,"j":_glEnable,"f":_glEnableVertexAttribArray,"Ea":_glFramebufferTexture2D,"Ca":_glGenBuffers,"Fa":_glGenFramebuffers,"Ja":_glGenTextures,"pa":_glGenerateMipmap,"oa":_glGetAttribLocation,"Ga":_glGetIntegerv,"ya":_glGetProgramiv,"ua":_glGetShaderInfoLog,"P":_glGetShaderiv,"u":_glGetUniformLocation,"za":_glLinkProgram,"ia":_glScissor,"wa":_glShaderSource,"r":_glTexImage2D,"y":_glTexParameteri,"ra":_glUniform1f,"O":_glUniform1i,"sa":_glUniform4f,"ta":_glUniformMatrix4fv,"v":_glUseProgram,"g":_glVertexAttribPointer,"M":_glViewport,"ma":invoke_iii,"ka":invoke_iiii,"la":invoke_iiiii,"ja":invoke_vi,"a":wasmMemory||Module["wasmMemory"],"o":_setTempRet0,"X":_time};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["Gb"]).apply(null,arguments)};var _DecCallBack=Module["_DecCallBack"]=function(){return(_DecCallBack=Module["_DecCallBack"]=Module["asm"]["Hb"]).apply(null,arguments)};var _DisplayCallBack=Module["_DisplayCallBack"]=function(){return(_DisplayCallBack=Module["_DisplayCallBack"]=Module["asm"]["Ib"]).apply(null,arguments)};var _RawDataCallBack=Module["_RawDataCallBack"]=function(){return(_RawDataCallBack=Module["_RawDataCallBack"]=Module["asm"]["Jb"]).apply(null,arguments)};var _FrameInfoCallBack=Module["_FrameInfoCallBack"]=function(){return(_FrameInfoCallBack=Module["_FrameInfoCallBack"]=Module["asm"]["Kb"]).apply(null,arguments)};var _getTotalMemory=Module["_getTotalMemory"]=function(){return(_getTotalMemory=Module["_getTotalMemory"]=Module["asm"]["Lb"]).apply(null,arguments)};var _getFreeMemory=Module["_getFreeMemory"]=function(){return(_getFreeMemory=Module["_getFreeMemory"]=Module["asm"]["Mb"]).apply(null,arguments)};var _AdditionDataCBFun=Module["_AdditionDataCBFun"]=function(){return(_AdditionDataCBFun=Module["_AdditionDataCBFun"]=Module["asm"]["Nb"]).apply(null,arguments)};var _RunTimeInfoCBFun=Module["_RunTimeInfoCBFun"]=function(){return(_RunTimeInfoCBFun=Module["_RunTimeInfoCBFun"]=Module["asm"]["Ob"]).apply(null,arguments)};var _JSPlayM4_GetFrameInfo=Module["_JSPlayM4_GetFrameInfo"]=function(){return(_JSPlayM4_GetFrameInfo=Module["_JSPlayM4_GetFrameInfo"]=Module["asm"]["Pb"]).apply(null,arguments)};var _JSPlayM4_GetPort=Module["_JSPlayM4_GetPort"]=function(){return(_JSPlayM4_GetPort=Module["_JSPlayM4_GetPort"]=Module["asm"]["Qb"]).apply(null,arguments)};var _JSPlayM4_SetStreamOpenMode=Module["_JSPlayM4_SetStreamOpenMode"]=function(){return(_JSPlayM4_SetStreamOpenMode=Module["_JSPlayM4_SetStreamOpenMode"]=Module["asm"]["Rb"]).apply(null,arguments)};var _JSPlayM4_OpenStream=Module["_JSPlayM4_OpenStream"]=function(){return(_JSPlayM4_OpenStream=Module["_JSPlayM4_OpenStream"]=Module["asm"]["Sb"]).apply(null,arguments)};var _JSPlayM4_SetFirstFrameCallBack=Module["_JSPlayM4_SetFirstFrameCallBack"]=function(){return(_JSPlayM4_SetFirstFrameCallBack=Module["_JSPlayM4_SetFirstFrameCallBack"]=Module["asm"]["Tb"]).apply(null,arguments)};var _JSPlayM4_SetDecCallBack=Module["_JSPlayM4_SetDecCallBack"]=function(){return(_JSPlayM4_SetDecCallBack=Module["_JSPlayM4_SetDecCallBack"]=Module["asm"]["Ub"]).apply(null,arguments)};var _JSPlayM4_SetDisplayCallBack=Module["_JSPlayM4_SetDisplayCallBack"]=function(){return(_JSPlayM4_SetDisplayCallBack=Module["_JSPlayM4_SetDisplayCallBack"]=Module["asm"]["Vb"]).apply(null,arguments)};var _JSPlayM4_SetFrameInfoCallBack=Module["_JSPlayM4_SetFrameInfoCallBack"]=function(){return(_JSPlayM4_SetFrameInfoCallBack=Module["_JSPlayM4_SetFrameInfoCallBack"]=Module["asm"]["Wb"]).apply(null,arguments)};var _JSPlayM4_SetResetCallBack=Module["_JSPlayM4_SetResetCallBack"]=function(){return(_JSPlayM4_SetResetCallBack=Module["_JSPlayM4_SetResetCallBack"]=Module["asm"]["Xb"]).apply(null,arguments)};var _JSPlayM4_SetVideoRawDataCallBack0=Module["_JSPlayM4_SetVideoRawDataCallBack0"]=function(){return(_JSPlayM4_SetVideoRawDataCallBack0=Module["_JSPlayM4_SetVideoRawDataCallBack0"]=Module["asm"]["Yb"]).apply(null,arguments)};var _JSPlayM4_SetVideoRawDataCallBack=Module["_JSPlayM4_SetVideoRawDataCallBack"]=function(){return(_JSPlayM4_SetVideoRawDataCallBack=Module["_JSPlayM4_SetVideoRawDataCallBack"]=Module["asm"]["Zb"]).apply(null,arguments)};var _JSPlayM4_SetDecCBStream=Module["_JSPlayM4_SetDecCBStream"]=function(){return(_JSPlayM4_SetDecCBStream=Module["_JSPlayM4_SetDecCBStream"]=Module["asm"]["_b"]).apply(null,arguments)};var _JSPlayM4_Play=Module["_JSPlayM4_Play"]=function(){return(_JSPlayM4_Play=Module["_JSPlayM4_Play"]=Module["asm"]["$b"]).apply(null,arguments)};var _JSPlayM4_InputData=Module["_JSPlayM4_InputData"]=function(){return(_JSPlayM4_InputData=Module["_JSPlayM4_InputData"]=Module["asm"]["ac"]).apply(null,arguments)};var _JSPlayM4_Stop=Module["_JSPlayM4_Stop"]=function(){return(_JSPlayM4_Stop=Module["_JSPlayM4_Stop"]=Module["asm"]["bc"]).apply(null,arguments)};var _JSPlayM4_CloseStream=Module["_JSPlayM4_CloseStream"]=function(){return(_JSPlayM4_CloseStream=Module["_JSPlayM4_CloseStream"]=Module["asm"]["cc"]).apply(null,arguments)};var _JSPlayM4_FreePort=Module["_JSPlayM4_FreePort"]=function(){return(_JSPlayM4_FreePort=Module["_JSPlayM4_FreePort"]=Module["asm"]["dc"]).apply(null,arguments)};var _JSPlayM4_SetDecodeFrameType=Module["_JSPlayM4_SetDecodeFrameType"]=function(){return(_JSPlayM4_SetDecodeFrameType=Module["_JSPlayM4_SetDecodeFrameType"]=Module["asm"]["ec"]).apply(null,arguments)};var _JSPlayM4_PlaySound=Module["_JSPlayM4_PlaySound"]=function(){return(_JSPlayM4_PlaySound=Module["_JSPlayM4_PlaySound"]=Module["asm"]["fc"]).apply(null,arguments)};var _JSPlayM4_SetSecretKey=Module["_JSPlayM4_SetSecretKey"]=function(){return(_JSPlayM4_SetSecretKey=Module["_JSPlayM4_SetSecretKey"]=Module["asm"]["gc"]).apply(null,arguments)};var _JSPlayM4_GetJPEG=Module["_JSPlayM4_GetJPEG"]=function(){return(_JSPlayM4_GetJPEG=Module["_JSPlayM4_GetJPEG"]=Module["asm"]["hc"]).apply(null,arguments)};var _JSPlayM4_GetBMP=Module["_JSPlayM4_GetBMP"]=function(){return(_JSPlayM4_GetBMP=Module["_JSPlayM4_GetBMP"]=Module["asm"]["ic"]).apply(null,arguments)};var _JSPlayM4_StopSound=Module["_JSPlayM4_StopSound"]=function(){return(_JSPlayM4_StopSound=Module["_JSPlayM4_StopSound"]=Module["asm"]["jc"]).apply(null,arguments)};var _JSPlayM4_GetSDKVersion=Module["_JSPlayM4_GetSDKVersion"]=function(){return(_JSPlayM4_GetSDKVersion=Module["_JSPlayM4_GetSDKVersion"]=Module["asm"]["kc"]).apply(null,arguments)};var _JSPlayM4_GetBuildDate=Module["_JSPlayM4_GetBuildDate"]=function(){return(_JSPlayM4_GetBuildDate=Module["_JSPlayM4_GetBuildDate"]=Module["asm"]["lc"]).apply(null,arguments)};var _JSPlayM4_GetLastError=Module["_JSPlayM4_GetLastError"]=function(){return(_JSPlayM4_GetLastError=Module["_JSPlayM4_GetLastError"]=Module["asm"]["mc"]).apply(null,arguments)};var _JSPlayM4_Fast=Module["_JSPlayM4_Fast"]=function(){return(_JSPlayM4_Fast=Module["_JSPlayM4_Fast"]=Module["asm"]["nc"]).apply(null,arguments)};var _JSPlayM4_Slow=Module["_JSPlayM4_Slow"]=function(){return(_JSPlayM4_Slow=Module["_JSPlayM4_Slow"]=Module["asm"]["oc"]).apply(null,arguments)};var _JSPlayM4_SetIFrameDecInterval=Module["_JSPlayM4_SetIFrameDecInterval"]=function(){return(_JSPlayM4_SetIFrameDecInterval=Module["_JSPlayM4_SetIFrameDecInterval"]=Module["asm"]["pc"]).apply(null,arguments)};var _JSPlayM4_SetDecodeThread=Module["_JSPlayM4_SetDecodeThread"]=function(){return(_JSPlayM4_SetDecodeThread=Module["_JSPlayM4_SetDecodeThread"]=Module["asm"]["qc"]).apply(null,arguments)};var _JSPlayM4_Pause=Module["_JSPlayM4_Pause"]=function(){return(_JSPlayM4_Pause=Module["_JSPlayM4_Pause"]=Module["asm"]["rc"]).apply(null,arguments)};var _JSPlayM4_OneByOne=Module["_JSPlayM4_OneByOne"]=function(){return(_JSPlayM4_OneByOne=Module["_JSPlayM4_OneByOne"]=Module["asm"]["sc"]).apply(null,arguments)};var _JSPlayM4_SetVolume=Module["_JSPlayM4_SetVolume"]=function(){return(_JSPlayM4_SetVolume=Module["_JSPlayM4_SetVolume"]=Module["asm"]["tc"]).apply(null,arguments)};var _JSPlayM4_GetVolume=Module["_JSPlayM4_GetVolume"]=function(){return(_JSPlayM4_GetVolume=Module["_JSPlayM4_GetVolume"]=Module["asm"]["uc"]).apply(null,arguments)};var _JSPlayM4_SetDisplayRegion=Module["_JSPlayM4_SetDisplayRegion"]=function(){return(_JSPlayM4_SetDisplayRegion=Module["_JSPlayM4_SetDisplayRegion"]=Module["asm"]["vc"]).apply(null,arguments)};var _JSPlayM4_SetPrintLogFlag=Module["_JSPlayM4_SetPrintLogFlag"]=function(){return(_JSPlayM4_SetPrintLogFlag=Module["_JSPlayM4_SetPrintLogFlag"]=Module["asm"]["wc"]).apply(null,arguments)};var _JSPlayM4_RenderPrivateData=Module["_JSPlayM4_RenderPrivateData"]=function(){return(_JSPlayM4_RenderPrivateData=Module["_JSPlayM4_RenderPrivateData"]=Module["asm"]["xc"]).apply(null,arguments)};var _JSPlayM4_RenderPrivateDataEx=Module["_JSPlayM4_RenderPrivateDataEx"]=function(){return(_JSPlayM4_RenderPrivateDataEx=Module["_JSPlayM4_RenderPrivateDataEx"]=Module["asm"]["yc"]).apply(null,arguments)};var _JSPlayM4_SetCanvasSize=Module["_JSPlayM4_SetCanvasSize"]=function(){return(_JSPlayM4_SetCanvasSize=Module["_JSPlayM4_SetCanvasSize"]=Module["asm"]["zc"]).apply(null,arguments)};var _JSPlayM4_SetDisplayBuf=Module["_JSPlayM4_SetDisplayBuf"]=function(){return(_JSPlayM4_SetDisplayBuf=Module["_JSPlayM4_SetDisplayBuf"]=Module["asm"]["Ac"]).apply(null,arguments)};var _JSPlayM4_SetAudioDisplayBuf=Module["_JSPlayM4_SetAudioDisplayBuf"]=function(){return(_JSPlayM4_SetAudioDisplayBuf=Module["_JSPlayM4_SetAudioDisplayBuf"]=Module["asm"]["Bc"]).apply(null,arguments)};var _JSPlayM4_GetSourceBufferRemain=Module["_JSPlayM4_GetSourceBufferRemain"]=function(){return(_JSPlayM4_GetSourceBufferRemain=Module["_JSPlayM4_GetSourceBufferRemain"]=Module["asm"]["Cc"]).apply(null,arguments)};var _JSPlayM4_GetBufferValue=Module["_JSPlayM4_GetBufferValue"]=function(){return(_JSPlayM4_GetBufferValue=Module["_JSPlayM4_GetBufferValue"]=Module["asm"]["Dc"]).apply(null,arguments)};var _JSPlayM4_ReversePlay=Module["_JSPlayM4_ReversePlay"]=function(){return(_JSPlayM4_ReversePlay=Module["_JSPlayM4_ReversePlay"]=Module["asm"]["Ec"]).apply(null,arguments)};var _JSPlayM4_SetDecodeThreadNum=Module["_JSPlayM4_SetDecodeThreadNum"]=function(){return(_JSPlayM4_SetDecodeThreadNum=Module["_JSPlayM4_SetDecodeThreadNum"]=Module["asm"]["Fc"]).apply(null,arguments)};var _JSPlayM4_GetCanvasSize=Module["_JSPlayM4_GetCanvasSize"]=function(){return(_JSPlayM4_GetCanvasSize=Module["_JSPlayM4_GetCanvasSize"]=Module["asm"]["Gc"]).apply(null,arguments)};var _JSPlayM4_DisplayLostFrames=Module["_JSPlayM4_DisplayLostFrames"]=function(){return(_JSPlayM4_DisplayLostFrames=Module["_JSPlayM4_DisplayLostFrames"]=Module["asm"]["Hc"]).apply(null,arguments)};var _JSPlayM4_SetANRParam=Module["_JSPlayM4_SetANRParam"]=function(){return(_JSPlayM4_SetANRParam=Module["_JSPlayM4_SetANRParam"]=Module["asm"]["Ic"]).apply(null,arguments)};var _JSPlayM4_SetResampleValue=Module["_JSPlayM4_SetResampleValue"]=function(){return(_JSPlayM4_SetResampleValue=Module["_JSPlayM4_SetResampleValue"]=Module["asm"]["Jc"]).apply(null,arguments)};var _JSPlayM4_SetAntialias=Module["_JSPlayM4_SetAntialias"]=function(){return(_JSPlayM4_SetAntialias=Module["_JSPlayM4_SetAntialias"]=Module["asm"]["Kc"]).apply(null,arguments)};var _JSPlayM4_SyncToAudio=Module["_JSPlayM4_SyncToAudio"]=function(){return(_JSPlayM4_SyncToAudio=Module["_JSPlayM4_SyncToAudio"]=Module["asm"]["Lc"]).apply(null,arguments)};var _JSPlayM4_SetAudioThreadModel=Module["_JSPlayM4_SetAudioThreadModel"]=function(){return(_JSPlayM4_SetAudioThreadModel=Module["_JSPlayM4_SetAudioThreadModel"]=Module["asm"]["Mc"]).apply(null,arguments)};var _JSPlayM4_SetDecodeERC=Module["_JSPlayM4_SetDecodeERC"]=function(){return(_JSPlayM4_SetDecodeERC=Module["_JSPlayM4_SetDecodeERC"]=Module["asm"]["Nc"]).apply(null,arguments)};var _JSPlayM4_SkipErrorData=Module["_JSPlayM4_SkipErrorData"]=function(){return(_JSPlayM4_SkipErrorData=Module["_JSPlayM4_SkipErrorData"]=Module["asm"]["Oc"]).apply(null,arguments)};var _JSPlayM4_SetAdditionDataCallBack=Module["_JSPlayM4_SetAdditionDataCallBack"]=function(){return(_JSPlayM4_SetAdditionDataCallBack=Module["_JSPlayM4_SetAdditionDataCallBack"]=Module["asm"]["Pc"]).apply(null,arguments)};var _JSPlayM4_GetAdditionData=Module["_JSPlayM4_GetAdditionData"]=function(){return(_JSPlayM4_GetAdditionData=Module["_JSPlayM4_GetAdditionData"]=Module["asm"]["Qc"]).apply(null,arguments)};var _JSPlayM4_SetDemuxModel=Module["_JSPlayM4_SetDemuxModel"]=function(){return(_JSPlayM4_SetDemuxModel=Module["_JSPlayM4_SetDemuxModel"]=Module["asm"]["Rc"]).apply(null,arguments)};var _JSPlayM4_SetD3DPostProcess=Module["_JSPlayM4_SetD3DPostProcess"]=function(){return(_JSPlayM4_SetD3DPostProcess=Module["_JSPlayM4_SetD3DPostProcess"]=Module["asm"]["Sc"]).apply(null,arguments)};var _JSPlayM4_SetDecodeEngine=Module["_JSPlayM4_SetDecodeEngine"]=function(){return(_JSPlayM4_SetDecodeEngine=Module["_JSPlayM4_SetDecodeEngine"]=Module["asm"]["Tc"]).apply(null,arguments)};var _JSPlayM4_SetGlobalBaseTime=Module["_JSPlayM4_SetGlobalBaseTime"]=function(){return(_JSPlayM4_SetGlobalBaseTime=Module["_JSPlayM4_SetGlobalBaseTime"]=Module["asm"]["Uc"]).apply(null,arguments)};var _JSPlayM4_GetPlayedTimeEx=Module["_JSPlayM4_GetPlayedTimeEx"]=function(){return(_JSPlayM4_GetPlayedTimeEx=Module["_JSPlayM4_GetPlayedTimeEx"]=Module["asm"]["Vc"]).apply(null,arguments)};var _JSPlayM4_GetFileTime=Module["_JSPlayM4_GetFileTime"]=function(){return(_JSPlayM4_GetFileTime=Module["_JSPlayM4_GetFileTime"]=Module["asm"]["Wc"]).apply(null,arguments)};var _JSPlayM4_GetMpOffset=Module["_JSPlayM4_GetMpOffset"]=function(){return(_JSPlayM4_GetMpOffset=Module["_JSPlayM4_GetMpOffset"]=Module["asm"]["Xc"]).apply(null,arguments)};var _JSPlayM4_ResetBuffer=Module["_JSPlayM4_ResetBuffer"]=function(){return(_JSPlayM4_ResetBuffer=Module["_JSPlayM4_ResetBuffer"]=Module["asm"]["Yc"]).apply(null,arguments)};var _JSPlayM4_ResetEnd=Module["_JSPlayM4_ResetEnd"]=function(){return(_JSPlayM4_ResetEnd=Module["_JSPlayM4_ResetEnd"]=Module["asm"]["Zc"]).apply(null,arguments)};var _JSPlayM4_IgnoreHeaderAudioDefine=Module["_JSPlayM4_IgnoreHeaderAudioDefine"]=function(){return(_JSPlayM4_IgnoreHeaderAudioDefine=Module["_JSPlayM4_IgnoreHeaderAudioDefine"]=Module["asm"]["_c"]).apply(null,arguments)};var _JSPlayM4_SetSycGroup=Module["_JSPlayM4_SetSycGroup"]=function(){return(_JSPlayM4_SetSycGroup=Module["_JSPlayM4_SetSycGroup"]=Module["asm"]["$c"]).apply(null,arguments)};var _JSPlayM4_GetRawData=Module["_JSPlayM4_GetRawData"]=function(){return(_JSPlayM4_GetRawData=Module["_JSPlayM4_GetRawData"]=Module["asm"]["ad"]).apply(null,arguments)};var _JSPlayM4_VideoDataToBMP=Module["_JSPlayM4_VideoDataToBMP"]=function(){return(_JSPlayM4_VideoDataToBMP=Module["_JSPlayM4_VideoDataToBMP"]=Module["asm"]["bd"]).apply(null,arguments)};var _JSPlayM4_VideoDataToJpeg=Module["_JSPlayM4_VideoDataToJpeg"]=function(){return(_JSPlayM4_VideoDataToJpeg=Module["_JSPlayM4_VideoDataToJpeg"]=Module["asm"]["cd"]).apply(null,arguments)};var _JSPlayM4_SetRunTimeInfoCallBackEx=Module["_JSPlayM4_SetRunTimeInfoCallBackEx"]=function(){return(_JSPlayM4_SetRunTimeInfoCallBackEx=Module["_JSPlayM4_SetRunTimeInfoCallBackEx"]=Module["asm"]["dd"]).apply(null,arguments)};var _JSPlayM4_SetPlayOrBackSwitchMode=Module["_JSPlayM4_SetPlayOrBackSwitchMode"]=function(){return(_JSPlayM4_SetPlayOrBackSwitchMode=Module["_JSPlayM4_SetPlayOrBackSwitchMode"]=Module["asm"]["ed"]).apply(null,arguments)};var _emscripten_tls_init=Module["_emscripten_tls_init"]=function(){return(_emscripten_tls_init=Module["_emscripten_tls_init"]=Module["asm"]["fd"]).apply(null,arguments)};var ___getTypeName=Module["___getTypeName"]=function(){return(___getTypeName=Module["___getTypeName"]=Module["asm"]["hd"]).apply(null,arguments)};var ___embind_register_native_and_builtin_types=Module["___embind_register_native_and_builtin_types"]=function(){return(___embind_register_native_and_builtin_types=Module["___embind_register_native_and_builtin_types"]=Module["asm"]["id"]).apply(null,arguments)};var _emscripten_current_thread_process_queued_calls=Module["_emscripten_current_thread_process_queued_calls"]=function(){return(_emscripten_current_thread_process_queued_calls=Module["_emscripten_current_thread_process_queued_calls"]=Module["asm"]["jd"]).apply(null,arguments)};var _emscripten_main_browser_thread_id=Module["_emscripten_main_browser_thread_id"]=function(){return(_emscripten_main_browser_thread_id=Module["_emscripten_main_browser_thread_id"]=Module["asm"]["kd"]).apply(null,arguments)};var _emscripten_sync_run_in_main_thread_4=Module["_emscripten_sync_run_in_main_thread_4"]=function(){return(_emscripten_sync_run_in_main_thread_4=Module["_emscripten_sync_run_in_main_thread_4"]=Module["asm"]["ld"]).apply(null,arguments)};var _emscripten_main_thread_process_queued_calls=Module["_emscripten_main_thread_process_queued_calls"]=function(){return(_emscripten_main_thread_process_queued_calls=Module["_emscripten_main_thread_process_queued_calls"]=Module["asm"]["md"]).apply(null,arguments)};var _emscripten_run_in_main_runtime_thread_js=Module["_emscripten_run_in_main_runtime_thread_js"]=function(){return(_emscripten_run_in_main_runtime_thread_js=Module["_emscripten_run_in_main_runtime_thread_js"]=Module["asm"]["nd"]).apply(null,arguments)};var __emscripten_call_on_thread=Module["__emscripten_call_on_thread"]=function(){return(__emscripten_call_on_thread=Module["__emscripten_call_on_thread"]=Module["asm"]["od"]).apply(null,arguments)};var __emscripten_thread_exit=Module["__emscripten_thread_exit"]=function(){return(__emscripten_thread_exit=Module["__emscripten_thread_exit"]=Module["asm"]["pd"]).apply(null,arguments)};var _pthread_testcancel=Module["_pthread_testcancel"]=function(){return(_pthread_testcancel=Module["_pthread_testcancel"]=Module["asm"]["qd"]).apply(null,arguments)};var __emscripten_thread_init=Module["__emscripten_thread_init"]=function(){return(__emscripten_thread_init=Module["__emscripten_thread_init"]=Module["asm"]["rd"]).apply(null,arguments)};var _pthread_self=Module["_pthread_self"]=function(){return(_pthread_self=Module["_pthread_self"]=Module["asm"]["sd"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["td"]).apply(null,arguments)};var ___errno_location=Module["___errno_location"]=function(){return(___errno_location=Module["___errno_location"]=Module["asm"]["ud"]).apply(null,arguments)};var __get_tzname=Module["__get_tzname"]=function(){return(__get_tzname=Module["__get_tzname"]=Module["asm"]["vd"]).apply(null,arguments)};var __get_daylight=Module["__get_daylight"]=function(){return(__get_daylight=Module["__get_daylight"]=Module["asm"]["wd"]).apply(null,arguments)};var __get_timezone=Module["__get_timezone"]=function(){return(__get_timezone=Module["__get_timezone"]=Module["asm"]["xd"]).apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return(stackSave=Module["stackSave"]=Module["asm"]["yd"]).apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return(stackRestore=Module["stackRestore"]=Module["asm"]["zd"]).apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return(stackAlloc=Module["stackAlloc"]=Module["asm"]["Ad"]).apply(null,arguments)};var _emscripten_stack_set_limits=Module["_emscripten_stack_set_limits"]=function(){return(_emscripten_stack_set_limits=Module["_emscripten_stack_set_limits"]=Module["asm"]["Bd"]).apply(null,arguments)};var _setThrew=Module["_setThrew"]=function(){return(_setThrew=Module["_setThrew"]=Module["asm"]["Cd"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["Dd"]).apply(null,arguments)};var _memalign=Module["_memalign"]=function(){return(_memalign=Module["_memalign"]=Module["asm"]["Ed"]).apply(null,arguments)};var __emscripten_allow_main_runtime_queued_calls=Module["__emscripten_allow_main_runtime_queued_calls"]=582528;var __emscripten_main_thread_futex=Module["__emscripten_main_thread_futex"]=603216;function invoke_iii(index,a1,a2){var sp=stackSave();try{return wasmTable.get(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return wasmTable.get(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return wasmTable.get(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{wasmTable.get(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}Module["ccall"]=ccall;Module["setValue"]=setValue;Module["getValue"]=getValue;Module["UTF8ToString"]=UTF8ToString;Module["writeArrayToMemory"]=writeArrayToMemory;Module["addRunDependency"]=addRunDependency;Module["removeRunDependency"]=removeRunDependency;Module["FS_createPath"]=FS.createPath;Module["FS_createDataFile"]=FS.createDataFile;Module["FS_createPreloadedFile"]=FS.createPreloadedFile;Module["FS_createLazyFile"]=FS.createLazyFile;Module["FS_createDevice"]=FS.createDevice;Module["FS_unlink"]=FS.unlink;Module["addFunction"]=addFunction;Module["print"]=out;Module["keepRuntimeAlive"]=keepRuntimeAlive;Module["PThread"]=PThread;Module["PThread"]=PThread;Module["wasmMemory"]=wasmMemory;Module["ExitStatus"]=ExitStatus;var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}if(ENVIRONMENT_IS_PTHREAD){readyPromiseResolve(Module);initRuntime();postMessage({"cmd":"loaded"});return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;function exit(status,implicit){EXITSTATUS=status;if(!implicit){if(ENVIRONMENT_IS_PTHREAD){postMessage({"cmd":"exitProcess","returnCode":status});throw new ExitStatus(status)}else{}}if(keepRuntimeAlive()){}else{PThread.terminateAllThreads();exitRuntime()}procExit(status)}function procExit(code){EXITSTATUS=code;if(!keepRuntimeAlive()){PThread.terminateAllThreads();if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}if(ENVIRONMENT_IS_PTHREAD){noExitRuntime=false;PThread.initWorker()}run();


  return JSPlayerModule.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = JSPlayerModule;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return JSPlayerModule; });
else if (typeof exports === 'object')
  exports["JSPlayerModule"] = JSPlayerModule;
