import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: () => import('@/views/redirect')
    }]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/screen',
    name: 'Screen',
    component: () => import('@/views/screen/Index/index'),
    hidden: true,
  },
  {
    path: '/screenAgs',
    name: 'ScreenAgs',
    component: () => import('@/views/screenAgs/Index3480/index'),
    hidden: true,
  },
  {
    path: '/mapDemo',
    name: 'MapDemo',
    component: () => import('@/views/demo/map/index'),
    hidden: true,
  },
  {
    path: '/changfangcangku',
    name: 'ChangFangCangKu',
    component: () => import('@/views/demo/changfangcangku/index'),
    hidden: true,
  },
  {
    path: '/svgDemo',
    name: 'SvgDemo',
    component: () => import('@/views/demo/svg/index'),
    hidden: true,
  },
  {
    path: '/floorInfo',
    name: 'FloorInfo',
    component: () => import('@/views/screen/FloorInfo/index'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [{
      path: 'index',
      component: () => import('@/views/index'),
      name: 'Index',
      meta: {
        title: '首页',
        icon: 'dashboard',
        affix: true
      }
    }]
  },
  {
    path: '/flood',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'hazardCheckExtend/:id(\\d+)',
        component: () => import('@/views/shcy/hazardCheckExtend/index'),
        name: 'hazardCheckExtend',
        meta: { title: '隐患排查列表', activeMenu: '/flood/floodHazardCheckList' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [{
      path: 'profile',
      component: () => import('@/views/system/user/profile/index'),
      name: 'Profile',
      meta: {
        title: '个人中心',
        icon: 'user'
      }
    }]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [{
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [{
      path: 'role/:userId(\\d+)',
      component: () => import('@/views/system/user/authRole'),
      name: 'AuthRole',
      meta: {
        title: '分配角色',
        activeMenu: '/system/user'
      }
    }]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [{
      path: 'user/:roleId(\\d+)',
      component: () => import('@/views/system/role/authUser'),
      name: 'AuthUser',
      meta: {
        title: '分配用户',
        activeMenu: '/system/role'
      }
    }]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [{
      path: 'index/:dictId(\\d+)',
      component: () => import('@/views/system/dict/data'),
      name: 'Data',
      meta: {
        title: '字典数据',
        activeMenu: '/system/dict'
      }
    }]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [{
      path: 'index',
      component: () => import('@/views/monitor/job/log'),
      name: 'JobLog',
      meta: {
        title: '调度日志',
        activeMenu: '/monitor/job'
      }
    }]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [{
      path: 'index/:tableId(\\d+)',
      component: () => import('@/views/tool/gen/editTable'),
      name: 'GenEdit',
      meta: {
        title: '修改生成配置',
        activeMenu: '/tool/gen'
      }
    }]
  },
  {
    path: '/map/area',
    component: Layout,
    hidden: true,
    permissions: ['shcy:mapArea:add'],
    children: [{
        path: 'add',
        component: () => import('@/views/shcy/mapArea/mapAreaAdd'),
        name: 'MapAreaAdd',
        meta: {
          title: '添加地图区域',
          activeMenu: '/map/mapArea'
        }
      },
      {
        path: 'edit/:areaId(\\d+)',
        component: () => import('@/views/shcy/mapArea/mapAreaEdit'),
        name: 'MapAreaEdit',
        meta: {
          title: '修改地图区域',
          activeMenu: '/map/mapArea'
        }
      }
    ]
  },
  {
    path: '/shcy/draw',
    component: Layout,
    hidden: true,
    permissions: ['shcy:draw:edit'],
    children: [
      {
        path: 'edit/:id(\\d+)',
        component: () => import('@/views/shcy/draw/drawEdit'),
        name: 'DrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/draw'
        }
      }
    ]
  },
  {
    path: '/shcy/shop',
    component: Layout,
    hidden: true,
    permissions: ['shcy:shop:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/shop/drawEdit'),
        name: 'ShopDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/shop'
        }
      }
    ]
  },
  // 小区信息绘制地图
  {
    path: '/shcy/residentials',
    component: Layout,
    hidden: true,
    permissions: ['shcy:residentials:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/residentials/drawEdit'),
        name: 'ResidentialsDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/residentials'
        }
      }
    ]
  },
  // 小区出入门口绘制地图
  {
    path: '/shcy/residentialEntrance',
    component: Layout,
    hidden: true,
    permissions: ['shcy:residentialEntrance:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/residentialEntrance/drawEdit'),
        name: 'ResidentialEntranceDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/residentialEntrance'
        }
      }
    ]
  },
  // 物业管理处绘制地图
  {
    path: '/shcy/nonResidentialProperty',
    component: Layout,
    hidden: true,
    permissions: ['shcy:nonResidentialProperty:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/nonResidentialProperty/drawEdit'),
        name: 'NonResidentialPropertyDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/nonResidentialProperty'
        }
      }
    ]
  },
  // 企事业单位绘制地图
  {
    path: '/shcy/institutions',
    component: Layout,
    hidden: true,
    permissions: ['shcy:institutions:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/institutions/drawEdit'),
        name: 'InstitutionsDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/institutions'
        }
      }
    ]
  },
  // 宾旅馆绘制地图
  {
    path: '/shcy/hotel',
    component: Layout,
    hidden: true,
    permissions: ['shcy:hotel:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/hotel/drawEdit'),
        name: 'HotelDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/hotel'
        }
      }
    ]
  },
  // 小区垃圾房绘制地图
  {
    path: '/shcy/garbageHouse',
    component: Layout,
    hidden: true,
    permissions: ['shcy:garbageHouse:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/garbageHouse/drawEdit'),
        name: 'GarbageHouseDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/garbageHouse'
        }
      }
    ]
  },
  // 小区建筑垃圾堆放点绘制地图
  {
    path: '/shcy/constructionWasteSite',
    component: Layout,
    hidden: true,
    permissions: ['shcy:constructionWasteSite:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/constructionWasteSite/drawEdit'),
        name: 'ConstructionWasteSiteDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/constructionWasteSite'
        }
      }
    ]
  },
  // 居委会绘制地图
  {
    path: '/shcy/committee',
    component: Layout,
    hidden: true,
    permissions: ['shcy:committee:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/committee/drawEdit'),
        name: 'CommitteeDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/committee'
        }
      }
    ]
  },
  // 公寓租赁房绘制地图
  {
    path: '/shcy/apartmentRental',
    component: Layout,
    hidden: true,
    permissions: ['shcy:apartmentRental:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/apartmentRental/drawEdit'),
        name: 'ApartmentRentalDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/apartmentRental'
        }
      }
    ]
  },
  // 小区大件垃圾堆放点绘制地图
  {
    path: '/shcy/largeGarbageSite',
    component: Layout,
    hidden: true,
    permissions: ['shcy:largeGarbageSite:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/largeGarbageSite/drawEdit'),
        name: 'LargeGarbageSiteDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/largeGarbageSite'
        }
      }
    ]
  },
  // 雨污管道绘制地图
  {
    path: '/shcy/RainwaterSewagePipe',
    component: Layout,
    hidden: true,
    permissions: ['shcy:RainwaterSewagePipe:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/RainwaterSewagePipe/drawEdit'),
        name: 'RainwaterSewagePipeDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/RainwaterSewagePipe'
        }
      }
    ]
  },
  // 井盖绘制地图
  {
    path: '/shcy/cover',
    component: Layout,
    hidden: true,
    permissions: ['shcy:cover:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/cover/drawEdit'),
        name: 'coverDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/cover'
        }
      }
    ]
  },
  //非住宅物业联系表绘制地图
  {
    path: '/shcy/contact',
    component: Layout,
    hidden: true,
    permissions: ['shcy:contact:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/contact/drawEdit'),
        name: 'ContactDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/contact'
        }
      }
    ]
  },
  //娱乐场所绘制地图
  {
    path: '/shcy/entainment',
    component: Layout,
    hidden: true,
    permissions: ['shcy:entainment:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/entainment/drawEdit'),
        name: 'EntainmentDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/entainment'
        }
      }
    ]
  },
  //绿化网络信息
  {
    path: '/shcy/grid',
    component: Layout,
    hidden: true,
    permissions: ['shcy:grid:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/grid/drawEdit'),
        name: 'GridDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/grid'
        }
      }
    ]
  },
  //宗地信息
  {
    path: '/shcy/parcel',
    component: Layout,
    hidden: true,
    permissions: ['shcy:parcel:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/parcel/drawEdit'),
        name: 'parcelDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/parcel'
        }
      }
    ]
  },
  //路名街长信息
  {
    path: '/shcy/chief',
    component: Layout,
    hidden: true,
    permissions: ['shcy:chief:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/chief/drawEdit'),
        name: 'chiefDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/chief'
        }
      }
    ]
  },
  //路名街长信息
  {
    path: '/shcy/chief',
    component: Layout,
    hidden: true,
    permissions: ['shcy:chief:edit'],
    children: [
      {
        path: 'drawEdit2/:id(\\d+)',
        component: () => import('@/views/shcy/chief/drawEdit2'),
        name: 'chiefDrawEdit2',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/chief'
        }
      }
    ]
  },
  {
    path: '/shcy/cameras',
    component: Layout,
    hidden: true,
    permissions: ['shcy:cameras:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/cameras/drawEdit'),
        name: 'camerasDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/cameras'
        }
      }
    ]
  },
  {
    path: '/shcy/pumpStation',
    component: Layout,
    hidden: true,
    permissions: ['shcy:pumpStation:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/pumpStation/drawEdit'),
        name: 'pumpStationDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/pumpStation'
        }
      }
    ]
  },
  // 综合网格
  {
    path: '/shcy/comprehensiveGrid',
    component: Layout,
    hidden: true,
    permissions: ['shcy:comprehensiveGrid:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/comprehensiveGrid/drawEdit'),
        name: 'ComprehensiveGridDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/comprehensiveGrid'
        }
      }
    ]
  },
  // 两类人员网格化
  {
    path: '/shcy/personnelGrid',
    component: Layout,
    hidden: true,
    permissions: ['shcy:personnelGrid:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/personnelGrid/drawEdit'),
        name: 'PersonnelGridDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/personnelGrid'
        }
      }
    ]
  },
  // 液位超限感知设备
  {
    path: '/shcy/liquidLevelDevice',
    component: Layout,
    hidden: true,
    permissions: ['shcy:liquidLevelDevice:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/liquidLevelDevice/drawEdit'),
        name: 'LiquidLevelDeviceDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/liquidLevelDevice'
        }
      }
    ]
  },
  // 大华监控设备
  {
    path: '/shcy/iccDevice',
    component: Layout,
    hidden: true,
    permissions: ['shcy:iccDevice:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/iccDevice/drawEdit'),
        name: 'IccDeviceDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/iccDevice'
        }
      }
    ]
  },
  // 防汛防台重点区域范围
  {
    path: '/shcy/floodControlKeyArea',
    component: Layout,
    hidden: true,
    permissions: ['shcy:floodControlKeyArea:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/floodControlKeyArea/drawEdit'),
        name: 'FloodControlKeyAreaDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/floodControlKeyArea'
        }
      }
    ]
  },
  // 防汛物资储备信息
  {
    path: '/shcy/floodReserveInfo',
    component: Layout,
    hidden: true,
    permissions: ['shcy:floodReserveInfo:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/floodReserveInfo/drawEdit'),
        name: 'FloodReserveInfoDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/floodReserveInfo'
        }
      }
    ]
  },
  // 防汛安置点信息
  {
    path: '/shcy/floodShelterInfo',
    component: Layout,
    hidden: true,
    permissions: ['shcy:floodShelterInfo:edit'],
    children: [
      {
        path: 'drawEdit/:id(\\d+)',
        component: () => import('@/views/shcy/floodShelterInfo/drawEdit'),
        name: 'FloodShelterInfoDrawEdit',
        meta: {
          title: '绘制地图',
          activeMenu: '/shcy/floodShelterInfo'
        }
      }
    ]
  },
  // 综合网格联系领导
  {
    path: '/shcy/comprehensiveGridLeader',
    component: Layout,
    hidden: true,
    permissions: ['shcy:comprehensiveGrid:edit'],
    children: [{
      path: 'index/:gridId(\\d+)',
      component: () => import('@/views/shcy/comprehensiveGrid/gridLeader'),
      name: 'GridLeader',
      meta: {
        title: '联系领导',
        activeMenu: '/shcy/comprehensiveGrid'
      }
    }]
  },
  // 综合网格党支部成员
  {
    path: '/shcy/comprehensiveGridPartyMember',
    component: Layout,
    hidden: true,
    permissions: ['shcy:comprehensiveGrid:edit'],
    children: [
      {
        path: 'index/:gridId(\\d+)',
        component: () => import('@/views/shcy/comprehensiveGrid/gridPartyMember'),
        name: 'GridPartyMember',
        meta: {
          title: '街区党支部成员',
          activeMenu: '/shcy/comprehensiveGrid'
        }
      }
    ]
  },
  // 综合网格工作力量
  {
    path: '/shcy/comprehensiveGridWorkforce',
    component: Layout,
    hidden: true,
    permissions: ['shcy:comprehensiveGrid:edit'],
    children: [
      {
        path: 'index/:gridId(\\d+)',
        component: () => import('@/views/shcy/comprehensiveGrid/gridWorkforce'),
        name: 'GridWorkforce',
        meta: {
          title: '工作力量',
          activeMenu: '/shcy/comprehensiveGrid'
        }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})
