<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="发现时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 220px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <!-- 诉求事项 -->
      <el-form-item label="诉求事项">
        <el-input v-model="queryParams.requestItem" placeholder="请输入诉求事项" clearable style="width: 220px"/>
      </el-form-item>

      <!-- 重复次数 -->
      <el-form-item label="重复次数">
        <el-input-number v-model="queryParams.duplicateCount" placeholder="请输入重复次数" clearable style="width: 220px"/>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dbcenterRxDuplicateList" show-summary :summary-method="getSummaries">
      <el-table-column type="selection" width="55" align="center" v-if="false"/>

      <!-- 序号 -->
      <el-table-column label="序号" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>

      <!-- 诉求事项 -->
      <el-table-column label="诉求事项" align="center" prop="requestItem"/>

      <!-- 重复工单号 -->
      <el-table-column label="重复工单号" align="center" prop="duplicateOrderNo">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleOrderClick(scope.row)" :underline="false" style="cursor: pointer;">
            {{ scope.row.duplicateOrderNo }}
          </el-link>
        </template>
      </el-table-column>

      <!-- 诉求内容 -->
      <el-table-column label="诉求内容" align="left" prop="requestContent" :show-overflow-tooltip="true" width="300"/>

      <!-- 重复次数 -->
      <el-table-column label="重复次数" align="center" prop="duplicateCount"/>

      <!-- 重复占比 -->
      <el-table-column label="重复占比" align="center" prop="duplicateRatio">
        <template slot-scope="scope">
          {{ formatPercentage(scope.row.duplicateRatio) }}
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import {listDbcenterRxDuplicate} from "@/api/shcy/dbcenterRxDuplicate";

export default {
  name: "DbcenterRxDuplicate",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 重复工单数据
      dbcenterRxDuplicateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 日期范围
      dateRange: [],
    };
  },
  created() {
  },
  methods: {
    /** 查询重复工单信息列表 */
    getList() {
      this.loading = true;
      listDbcenterRxDuplicate(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.dbcenterRxDuplicateList = response.data;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {
      }
      // this.handleQuery();
      this.dbcenterRxDuplicateList = [];
    },
    /** 格式化百分比 */
    formatPercentage(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00%';
      }
      const num = parseFloat(value);
      if (isNaN(num)) {
        return '0.00%';
      }
      return num.toFixed(2) + '%';
    },
    /** 处理重复工单号点击事件 */
    handleOrderClick(row) {
      // 获取发现时间，从dateRange中获取
      const startDate = this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : null;
      const endDate = this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : null;
      
      // 构建路由参数
      const query = {
        hotlinesn: row.duplicateOrderNo
      };
      
      // 如果有发现时间范围，添加到参数中
      if (startDate && endDate) {
        query.startDate = startDate;
        query.endDate = endDate;
      }
      
      // 跳转到目标页面
      this.$router.push({
        path: '/hotline/dbCenterRx',
        query: query
      });
    },
    /** 计算合计行 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          // 第一列显示"合计"
          sums[index] = '合计';
          return;
        }
        if (column.property === 'duplicateCount') {
          // 重复次数列计算总和
          const values = data.map(item => Number(item.duplicateCount));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = '';
          }
        } else if (column.property === 'duplicateOrderNo' || column.property === 'duplicateRatio') {
          // 重复工单号和重复占比列不显示合计
          sums[index] = '';
        } else {
          // 其他列显示空白
          sums[index] = '';
        }
      });
      return sums;
    },
  },
};
</script>

<style scoped>
::v-deep .el-table th > .cell {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-table tbody tr:hover > td {
  background-color: transparent !important;
}
</style>

<style lang="scss">
.el-tooltip__popper {
  max-width: 50%;
}
</style>
