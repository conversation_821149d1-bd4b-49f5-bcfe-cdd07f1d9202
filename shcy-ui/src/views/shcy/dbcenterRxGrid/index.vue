<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="热线12345工单编号">
        <el-input
          style="width: 220px"
          v-model="queryParams.hotlinesn"
          placeholder="请输入热线12345工单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="紧急程度">
        <el-select
          v-model="queryParams.urgentdegree"
          placeholder="请选择紧急程度"
          clearable
          style="width: 220px"
        >
          <el-option
            v-for="dict in dict.type.dbcenter_urgentdegree"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="任务号">
        <el-input
          style="width: 220px"
          v-model="queryParams.taskid"
          placeholder="请输入任务号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="发现时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 220px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="发生地址">
        <el-input
          style="width: 220px"
          v-model="queryParams.address"
          placeholder="请输入发生地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属小区">
        <el-select
          style="width: 220px"
          v-model="queryParams.community"
          placeholder="请选择所属小区"
          clearable
          filterable
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="community in filteredCommunities"
            :key="community"
            :label="community"
            :value="community"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属居委会">
        <el-select
          style="width: 220px"
          v-model="queryParams.residentialarea"
          placeholder="请选择所属居委会"
          clearable
          filterable
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="committee in filteredCommittees"
            :key="committee"
            :label="committee"
            :value="committee"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属网格">
        <el-select
          style="width: 220px"
          v-model="queryParams.streetarea"
          placeholder="请选择所属网格"
          clearable
          filterable
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="dict in filteredStreetareas"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属物业">
        <el-select
          style="width: 220px"
          v-model="queryParams.property"
          placeholder="请选择所属物业"
          clearable
          filterable
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="property in filteredProperties"
            :key="property"
            :label="property"
            :value="property"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="问题描述">
        <el-input
          style="width: 220px"
          v-model="queryParams.description"
          placeholder="请输入问题描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="三级主责部门">
        <el-select
          style="width: 220px"
          v-model="queryParams.subexecutedeptnameMh"
          placeholder="请选择三级主责部门"
          clearable
          filterable
        >
          <el-option
            v-for="(item, i) in subexecutedeptnameMhList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="重点专项">
        <el-select
          style="width: 220px"
          v-model="specialtopics"
          multiple
          placeholder="请选择重点专项"
          clearable
          filterable
        >
          <el-option
            v-for="(item, i) in specialTopicList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="满意度">
        <el-select
          style="width: 220px"
          v-model="satisfactions"
          multiple
          placeholder="请选择满意度"
          clearable
        >
          <el-option
            v-for="dict in dict.type.dbcenter_satisfaction"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否重复">
        <el-select
          style="width: 220px"
          v-model="queryParams.isduplicate"
          placeholder="请选择是否重复"
          clearable
          filterable
        >
          <el-option
            v-for="(item, i) in isduplicateList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="诉求大类">
        <el-select
          style="width: 220px"
          v-model="queryParams.parentappealclassification"
          placeholder="请选择诉求大类"
          clearable
          filterable
          @change="changeQueryParentappealclassification(queryParams.parentappealclassification)"
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="(item, i) in parentappealClassifyList"
            :key="i"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="诉求小类">
        <el-select
          style="width: 220px"
          v-model="appealclassifications"
          multiple
          placeholder="请选择诉求小类"
          clearable
          filterable
        >
          <el-option value="空缺" label="空缺"></el-option>
          <el-option
            v-for="(item, i) in queryappealClassifyList"
            :key="i"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleRelatedQuery">关联工单</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:dbcenterRxGrid:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dbcenterRxList" @selection-change="handleSelectionChange"
              :row-style="rowStyle" @row-click="rowClickEv">
      <el-table-column type="selection" width="55" align="center"/>

      <!-- 热线12345工单编号 -->
      <el-table-column label="热线12345工单编号" align="center" prop="hotlinesn" width="180"/>

      <!-- 紧急程度 -->
      <el-table-column label="紧急程度" align="center" prop="urgentdegree" :show-overflow-tooltip="true" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.urgentdegree == null"> 一般</div>
          <dict-tag :options="dict.type.dbcenter_urgentdegree" :value="scope.row.urgentdegree" v-else/>
        </template>
      </el-table-column>

      <!-- 任务号 -->
      <el-table-column label="任务号" align="center" prop="taskid" width="150"/>

      <!-- 发现时间 -->
      <el-table-column label="发现时间" align="center" prop="discovertime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.discovertime) }}</span>
        </template>
      </el-table-column>

      <!-- 业务类型 -->
      <el-table-column label="业务类型" align="center" prop="servicetypename" width="150"/>

      <!-- 发生地址 -->
      <el-table-column label="发生地址" align="left" prop="address" :show-overflow-tooltip="true" width="180"/>

      <!-- 所属小区 -->
      <el-table-column label="所属小区" align="left" prop="community" width="180"/>

      <!-- 所属居委会 -->
      <el-table-column label="所属居委会" align="left" prop="residentialarea" width="180"/>

      <!-- 所属网格 -->
      <el-table-column label="所属网格" align="left" prop="streetarea" width="180"/>

      <!-- 所属物业 -->
      <el-table-column label="所属物业" align="left" prop="property" width="180"/>

      <!-- 问题描述 -->
      <el-table-column label="问题描述" align="left" prop="description" width="180">
        <template #default="{ row }">
          <el-tooltip :content="row.description" placement="top" dark="light">
            <div class="ellipsis">{{ row.description }}</div>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 三级主责部门 -->
      <el-table-column label="三级主责部门" align="left" prop="subexecutedeptnameMh" :show-overflow-tooltip="true"
                       width="150"/>

      <!-- 反馈结论 -->
      <el-table-column label="反馈结论" align="left" prop="description12345" width="180">
        <template #default="{ row }">
          <el-tooltip :content="row.description12345" placement="top" dark="light">
            <div class="ellipsis">{{ row.description12345 }}</div>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 热线12345工单来源 -->
      <el-table-column label="工单来源" align="center" prop="infosourcename" width="150"/>

      <el-table-column label="重点专项" align="left" prop="specialtopic" min-width="150"/>

      <el-table-column label="满意度" align="left" prop="satisfaction" width="150"/>

      <el-table-column label="是否重复" align="left" prop="isduplicate" min-width="150"/>

      <!-- 诉求大类 -->
      <el-table-column label="诉求大类" align="center" prop="parentappealclassification" width="150"/>

      <!-- 诉求归类小类 -->
      <el-table-column label="诉求小类" align="center" prop="appealclassification" width="180"/>

      <!-- 备注 -->
      <el-table-column label="备注" align="left" prop="remark" width="180">
        <template #default="{ row }">
          <el-tooltip :content="row.remark" placement="top" dark="light">
            <div class="ellipsis">{{ row.remark }}</div>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 到期日期 -->
      <el-table-column label="到期日期" align="center" prop="overdueDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.overdueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {listRelatedDbcenterRx, listAppealClassify, listSpecialTopic, listProperty} from "@/api/shcy/dbcenterRx";
import {listDbcenterRxGrid} from "@/api/shcy/dbcenterRxGrid";

import dbcenterRxMixin from "@/mixin/dbcenterRxMixin";

export default {
  name: "DbcenterRxGrid",
  dicts: ['dbcenter_urgentdegree', 'dbcenter_satisfaction', 'dbcenter_residentialarea', 'dbcenter_streetarea', 'dbcenter_powerstorage', 'dbcenter_trackingtype', 'dbcenter_overdue'],
  mixins: [dbcenterRxMixin],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 12345热线案件信息表格数据
      dbcenterRxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 日期范围
      dateRange: [],
      satisfactions: [],
      syncLoading: null,
      taskids: [],
      allappealClassifyList: [],
      parentappealClassifyList: [],
      appealClassifyList: [],
      queryappealClassifyList: [],
      specialTopicList: [],
      // 诉求归类查询参数
      appealclassifications: [],
      specialtopics: [],
      satisfactions: [],
      // 诉求归类表单数据
      appealclassificationss: [],
      // 是否重复
      isduplicateList: ['是', '否'],
      selectedArrData: [], // 把选择到的当前行的id存到数组中
      communityList: [],
      propertyList: [],
      nonResidentialPropertyList: [],
      // 居委会和网格的映射关系
      residentialToStreetMap: {
        '滨一居委会': '石化街道第一综合网格',
        '滨二居委会': '石化街道第一综合网格',
        '桥园居委会': '石化街道第一综合网格',
        '山龙居委会': '石化街道第一综合网格',
        '紫卫居委会': '石化街道第一综合网格',
        '东村居委会': '石化街道第一综合网格',
        '卫清居委会': '石化街道第二综合网格',
        '辰凯居委会': '石化街道第二综合网格',
        '山鑫居委会': '石化街道第二综合网格',
        '东礁一居委会': '石化街道第二综合网格',
        '东礁二居委会': '石化街道第二综合网格',
        '东泉居委会': '石化街道第二综合网格',
        '合生居委会': '石化街道第二综合网格',
        '三村居委会': '石化街道第三综合网格',
        '合浦居委会': '石化街道第三综合网格',
        '四村居委会': '石化街道第三综合网格',
        '柳城居委会': '石化街道第三综合网格',
        '十二村居委会': '石化街道第三综合网格',
        '十三村居委会': '石化街道第四综合网格',
        '梅州居委会': '石化街道第四综合网格',
        '临蒙居委会': '石化街道第四综合网格',
        '临三居委会': '石化街道第四综合网格',
        '七村居委会': '石化街道第四综合网格',
        '九村居委会': '石化街道第四综合网格',
        '十村居委会': '石化街道第四综合网格',
        '海棠居委会': '石化街道第四综合网格'
      }
    };
  },
  created() {
    this.getList();
    this.getAppealClassifyList();
    this.getSpecialTopicList();
    this.getPropertyList();
  },
  methods: {
    /** 查询12345热线案件信息列表 */
    getList() {
      this.loading = true;

      const { appealclassifications, specialtopics, satisfactions } = this;
      this.queryParams.params = { appealclassifications, specialtopics, satisfactions };

      listDbcenterRxGrid(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.dbcenterRxList = response.rows;

        // 遍历this.dbcenterRxList将this.dbcenterRxList中的specialtopic字段值替换为逗号分隔的数组
        this.dbcenterRxList.forEach(item => {
          if(item && item.specialtopic){
            item.specialtopic = item.specialtopic.split(',');
          }
        });

        this.total = response.total;
        this.loading = false;
      });
    },
    getAppealClassifyList() {
      listAppealClassify().then(response => {
        this.allappealClassifyList = response.data;
        // 获取this.allappealClassifyList中parentId为0的数据
        this.parentappealClassifyList = this.allappealClassifyList.filter(item => item.parentId == 0);
      });
    },
    getSpecialTopicList() {
      listSpecialTopic().then(response => {
        this.specialTopicList = response.data;
      });
    },
    getPropertyList() {
      listProperty().then(response => {
        this.nonResidentialPropertyList = response.data;
        this.propertyList = [...new Set(this.nonResidentialPropertyList.map(item => item.name))];
        this.communityList = [...new Set(this.nonResidentialPropertyList.map(item => item.residential))];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        isstandard: null,
        checkimage: null,
        servicetypename: null,
        wpType: null,
        isfirstcontact: null,
        hyname: null,
        upkeepername: null,
        deptname: null,
        statusname: null,
        synctime: null,
        lastcontacttime: null,
        accepttime: null,
        cancletime: null,
        lastsolvingtime: null,
        importantsolvingtime: null,
        middlesolvingtime: null,
        allendtime: null,
        allimportanttime: null,
        allmiddletime: null,
        endtime: null,
        telasktime: null,
        solvingtime: null,
        dispatchtime: null,
        createtime: null,
        percreatetime: null,
        discovertime: null,
        executedeptname: null,
        workgridcode: null,
        communityname: null,
        streetname: null,
        workgrid: null,
        address: null,
        coordy: null,
        coordx: null,
        gridcode: null,
        communitycode: null,
        streetcode: null,
        viewinfo: null,
        casevaluation12345: null,
        notReason: null,
        description12345: null,
        appealExplain: null,
        banliresult12345: null,
        wpSource: null,
        reportdeptname: null,
        hotlinesn: null,
        banliresult: null,
        duLimit: null,
        urgeCount: null,
        callbackFlag: null,
        userevaluate: null,
        isanonymity: null,
        servicetype: null,
        similarcasesn: null,
        approach: null,
        urgentdegree: null,
        partsn: null,
        endnote: null,
        dispatchnote: null,
        reporter: null,
        infozcname: null,
        infoscname: null,
        infobcname: null,
        infotypename: null,
        infosourcename: null,
        contactinfo: null,
        hastentypecount: null,
        hasleadtypecount: null,
        huifangcount: null,
        hechacount: null,
        heshicount: null,
        contactmode: null,
        callnumber: null,
        priorityarea: null,
        checkresult: null,
        verifyresult: null,
        endresult: null,
        caseend: null,
        insertuser: null,
        keepersn: null,
        insertdeptcode: null,
        executedeptcode: null,
        deptcode: null,
        status: 0,
        description: null,
        infoatcode: null,
        infozccode: null,
        infosccode: null,
        infobccode: null,
        infotypeid: null,
        infosourceid: null,
        casesn: null,
        taskid: null
      };
      this.satisfactions = [];
      this.appealclassificationss = [];
      this.trackingtypes = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleRelatedQuery() {
      // 判断工单编号是否为空
      if (this.queryParams.hotlinesn == null || this.queryParams.hotlinesn === '') {
        this.$modal.msgError('热线12345工单编号不能为空');
        return;
      }

      this.queryParams.pageNum = 1;
      this.loading = true;
      listRelatedDbcenterRx(this.queryParams).then(response => {
        this.dbcenterRxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.selectedArrData = [];
      this.dateRange = [];
      this.satisfactions = [];
      this.appealclassifications = [];
      this.specialtopics = [];
      this.queryTrackingtypes = [];
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.taskids = selection.map(item => item.taskid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/dbcenterRxGrid/export', {
        ...this.queryParams
      }, `dbcenter_${new Date().getTime()}.xlsx`)
    },
    changeParentappealclassification(value) {
      this.appealclassificationss = [];
      // 获取this.parentappealClassifyList中name为value的项
      const parentappealClassify = this.parentappealClassifyList.find(item => item.name === value);
      if (parentappealClassify != null) {
        // 获取this.allappealClassifyList中parentId为parentappealClassify.id的项
        this.appealClassifyList = this.allappealClassifyList.filter(item => item.parentId === parentappealClassify.id);
      }
    },
    changeQueryParentappealclassification(value) {
      if (value === '空缺') {
        this.queryappealClassifyList = [];
      } else {
        this.appealclassifications = [];
        const parentappealClassify = this.parentappealClassifyList.find(item => item.name === value);
        if (parentappealClassify != null) {
          this.queryappealClassifyList = this.allappealClassifyList.filter(item => item.parentId === parentappealClassify.id);
        }
      }
    },
    // 某一行被点击行触发事件，默认形参代表一整行数据
    rowClickEv(row) {
      //点击的那行数据默认是对象{__ob__: Observer}，将其转数组
      this.selectedArrData = [row];
    },
    rowStyle({ row }) {
      const checkIdList = this.selectedArrData.map((item) => item.id);
      if (checkIdList.includes(row.id)) {
        return {
          backgroundColor: "#1890ff",
          color: "#ffffff",
        };
      }
    },
  },
  computed: {
    // 过滤后的网格列表
    filteredStreetareas() {
      const userDept = this.$store.getters.deptName;
      const streetareas = this.dict.type.dbcenter_streetarea || [];
      const matchedStreetarea = streetareas.find(item => item.label === userDept);
      return matchedStreetarea ? [matchedStreetarea] : [];
    },

    // 过滤后的居委会列表
    filteredCommittees() {
      const userDept = this.$store.getters.deptName;
      const streetareas = this.dict.type.dbcenter_streetarea || [];
      if (!streetareas.find(item => item.label === userDept)) {
        return [];
      }

      return Object.entries(this.residentialToStreetMap)
        .filter(([_, street]) => street === userDept)
        .map(([committee]) => committee);
    },

    // 过滤后的小区列表
    filteredCommunities() {
      const committees = this.filteredCommittees;
      if (committees.length === 0) {
        return [];
      }

      return [...new Set(
        this.nonResidentialPropertyList
          .filter(item => committees.includes(item.committee))
          .map(item => item.residential)
      )];
    },

    // 过滤后的物业列表
    filteredProperties() {
      const committees = this.filteredCommittees;
      if (committees.length === 0) {
        return [];
      }

      return [...new Set(
        this.nonResidentialPropertyList
          .filter(item => committees.includes(item.committee))
          .map(item => item.name)
      )];
    }
  }
};
</script>

<style scoped>
::v-deep .el-table th > .cell {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-table tbody tr:hover > td {
  /*background-color: #1890ff !important;
  color: #ffffff;*/

  background-color: transparent !important;
}
</style>

<style lang="scss">
.el-tooltip__popper {
  max-width: 50%;
}
</style>
