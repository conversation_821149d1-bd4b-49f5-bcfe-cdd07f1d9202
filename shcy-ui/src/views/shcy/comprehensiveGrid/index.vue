<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="gridName">
        <el-input
          v-model="queryParams.gridName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编号" prop="gridNumber">
        <el-input
          v-model="queryParams.gridNumber"
          placeholder="请输入编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:comprehensiveGrid:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:comprehensiveGrid:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:comprehensiveGrid:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:comprehensiveGrid:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="comprehensiveGridList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="gridName" >
        <template slot-scope="scope">
          <router-link :to="'/shcy/comprehensiveGrid/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.gridName }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="编号" align="center" prop="gridNumber" />
      <el-table-column label="覆盖范围" align="center" prop="coverageArea" />
      <el-table-column label="覆盖居民区" align="center" prop="residentialAreas" />
      <el-table-column label="面积(平方千米)" align="center" prop="areaSize" />
      <el-table-column label="人口规模(万人)" align="center" prop="population" />
      <el-table-column label="市场主体(家)" align="center" prop="marketEntities" />
      <el-table-column label="新就业群(人)" align="center" prop="newEmployment" />
      <el-table-column label="工作力量" align="center" prop="gridWorkforce">
        <template slot-scope="scope">
          <router-link :to="'/shcy/comprehensiveGridWorkforce/index/' + scope.row.id" class="link-type">
            <span>工作力量</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:comprehensiveGrid:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:comprehensiveGrid:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改综合网格对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="名称" prop="gridName">
          <el-input v-model="form.gridName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="编号" prop="gridNumber">
          <el-input v-model="form.gridNumber" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="覆盖范围" prop="coverageArea">
          <el-input v-model="form.coverageArea" placeholder="请输入覆盖范围" />
        </el-form-item>
        <el-form-item label="覆盖居民区" prop="residentialAreas">
          <el-input v-model="form.residentialAreas" placeholder="请输入覆盖居民区" />
        </el-form-item>
        <el-form-item label="面积(平方千米)" prop="areaSize">
          <el-input v-model="form.areaSize" placeholder="请输入面积(平方千米)" />
        </el-form-item>
        <el-form-item label="人口规模(万人)" prop="population">
          <el-input v-model="form.population" placeholder="请输入人口规模(万人)" />
        </el-form-item>
        <el-form-item label="市场主体(家)" prop="marketEntities">
          <el-input v-model="form.marketEntities" placeholder="请输入市场主体(家)" />
        </el-form-item>
        <el-form-item label="新就业群(人)" prop="newEmployment">
          <el-input v-model="form.newEmployment" placeholder="请输入新就业群(人)" />
        </el-form-item>
        <el-form-item label="图形类型" prop="type">
          <el-input v-model="form.type" placeholder="请输入图形类型" />
        </el-form-item>
        <el-form-item label="坐标" prop="coordinate">
          <el-input v-model="form.coordinate" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="填充颜色" prop="fillColor">
          <el-input v-model="form.fillColor" placeholder="请输入填充颜色" />
        </el-form-item>
        <el-form-item label="边框色" prop="outlineColor">
          <el-input v-model="form.outlineColor" placeholder="请输入边框色" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listComprehensiveGrid, getComprehensiveGrid, delComprehensiveGrid, addComprehensiveGrid, updateComprehensiveGrid } from "@/api/shcy/comprehensiveGrid";

export default {
  name: "ComprehensiveGrid",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 综合网格表格数据
      comprehensiveGridList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        gridName: null,
        gridNumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询综合网格列表 */
    getList() {
      this.loading = true;
      listComprehensiveGrid(this.queryParams).then(response => {
        this.comprehensiveGridList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        gridName: null,
        gridNumber: null,
        type: null,
        coordinate: null,
        fillColor: null,
        outlineColor: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加综合网格";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getComprehensiveGrid(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改综合网格";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateComprehensiveGrid(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addComprehensiveGrid(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除综合网格编号为"' + ids + '"的数据项？').then(function() {
        return delComprehensiveGrid(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/comprehensiveGrid/export', {
        ...this.queryParams
      }, `comprehensiveGrid_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
