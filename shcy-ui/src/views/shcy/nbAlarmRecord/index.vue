<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="当天报警情况" name="first">
        <el-form :model="queryParams" ref="queryForm" :rules="rules" size="small" :inline="true" v-show="showSearch" label-width="auto">
          <el-form-item label="报警时间" prop="dateTimeRange">
           <el-date-picker
             v-model="queryParams.dateTimeRange"
             style="width: 400px"
             value-format="yyyy-MM-dd HH:mm:ss"
             type="datetimerange"
             range-separator="至"
             start-placeholder="开始时间"
             end-placeholder="结束时间"
             :picker-options="pickerOptions"
             @change="handleDateTimeChange"
           ></el-date-picker>
         </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="nbAlarmRecordList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" v-if="false"/>
          <el-table-column label="序号" align="center" width="55">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="设备点位" align="center" prop="deviceName" />
          <el-table-column label="监测水体" align="center" prop="monitoredWaterBody" />
          <el-table-column label="管网类型" align="center" prop="pipelineType" />
          <el-table-column label="设备状态" align="center" prop="deviceState" />
          <el-table-column label="22:00-08:00" align="center" prop="lateNightAlarmCount" />
          <el-table-column label="08:00-12:00" align="center" prop="morningAlarmCount" />
          <el-table-column label="12:00-15:00" align="center" prop="afternoonAlarmCount" />
          <el-table-column label="15:00-18:00" align="center" prop="eveningAlarmCount" />
          <el-table-column label="18:00-22:00" align="center" prop="earlyNightAlarmCount" />
          <el-table-column label="当日报警数量" align="center" prop="todayAlarmNum" />
          <el-table-column label="历史报警数量" align="center" prop="historyAlarmNum" />
          <el-table-column label="最新报警时间" align="center" prop="alarmTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.alarmTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="历史告警详情" name="second">
        <el-form :model="queryParamsHistory" ref="queryFormHistory" size="small" :inline="true" v-show="showSearchHistory" label-width="auto">
          <el-form-item label="设备点位" prop="deviceName">
            <el-input v-model="queryParamsHistory.deviceName" placeholder="请输入设备点位" clearable />
          </el-form-item>
          <el-form-item label="管网类型" prop="pipelineType">
            <el-select v-model="queryParamsHistory.pipelineType" placeholder="请选择管网类型" clearable>
              <el-option label="市政管网" value="市政管网" />
              <el-option label="小区三级管网" value="小区三级管网" />
            </el-select>
          </el-form-item>
          <el-form-item label="监测水体" prop="monitoredWaterBody">
            <el-select v-model="queryParamsHistory.monitoredWaterBody" placeholder="请选择监测水体" clearable>
              <el-option label="雨水" value="雨水" />
              <el-option label="污水" value="污水" />
            </el-select>
          </el-form-item>
          <el-form-item label="报警时间" prop="dateRange">
           <el-date-picker
             v-model="queryParamsHistory.dateRange"
             style="width: 400px"
             value-format="yyyy-MM-dd"
             type="daterange"
             range-separator="至"
             start-placeholder="开始日期"
             end-placeholder="结束日期"
           ></el-date-picker>
         </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryHistory">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQueryHistory">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table 
          v-loading="loadingHistory" 
          :data="historyAlarmRecordList" 
          @selection-change="handleSelectionChangeHistory"
          :show-summary="true"
          :summary-method="getSummaries">
          <el-table-column type="selection" width="55" align="center" v-if="false"/>
          <el-table-column label="序号" align="center" width="55">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="设备点位" align="center" prop="deviceName" />
          <el-table-column label="监测水体" align="center" prop="monitoredWaterBody" />
          <el-table-column label="排水去向" align="center" prop="drainageDirection" />
          <el-table-column label="管网类型" align="center" prop="pipelineType" />
          <el-table-column label="报警数量" align="center" prop="alarmNum" sortable />
          <el-table-column label="历史报警数量" align="center" prop="historyAlarmNum" sortable />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { list, historyList  } from "@/api/shcy/nbAlarmRecord";

export default {
  name: "NbAlarmRecord",
  data() {
    return {
      // 活动的tab页签
      activeName: 'first',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 短信发送记录表格数据
      nbAlarmRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        dateTimeRange: []
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dateTimeRange: [
          { required: true, message: "请选择报警时间范围", trigger: "change" }
        ]
      },
      // 日期选择器配置
      pickerOptions: {
        onPick: (val) => {
          // 当选择开始时间时，限制结束时间只能选择同一天
          if (val.minDate && !val.maxDate) {
            this.pickerOptions.disabledDate = (time) => {
              const selectedDate = new Date(val.minDate);
              const currentDate = new Date(time);
              return currentDate.toDateString() !== selectedDate.toDateString();
            };
          } else if (val.maxDate) {
            // 选择完成后清除限制
            this.pickerOptions.disabledDate = null;
          }
        }
      },
      // 历史告警查询参数
      queryParamsHistory: {
        deviceName: '',
        pipelineType: '',
        monitoredWaterBody: '',
        dateRange: [],
      },
      // 历史告警表格数据
      historyAlarmRecordList: [],
      // 历史告警遮罩层
      loadingHistory: false,
      // 历史告警显示搜索条件
      showSearchHistory: true,
    };
  },
  created() {
    // 设置默认时间为当天
    this.queryParams.dateTimeRange = this.getCurrentDateTimeRange();
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // 使用时间戳范围进行查询
      list(this.addDateRange(this.queryParams, this.queryParams.dateTimeRange)).then(response => {
        this.nbAlarmRecordList = response.data;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.$refs["queryForm"].validate(valid => {
        if (valid) {
          this.getList();
        }
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.dateTimeRange = this.getCurrentDateTimeRange();
      this.handleQuery();
    },
    /** 获取当前日期时间范围 */
    getCurrentDateTimeRange() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');

      const startTime = `${year}-${month}-${day} 00:00:00`;
      const endTime = `${year}-${month}-${day} 23:59:59`;

      return [startTime, endTime];
    },
    /** 处理时间范围变化 */
    handleDateTimeChange(value) {
      if (value && value.length === 2) {
        const startDateTime = new Date(value[0]);
        const endDateTime = new Date(value[1]);

        // 检查是否是同一天
        if (startDateTime.toDateString() !== endDateTime.toDateString()) {
          this.$message.warning('开始时间和结束时间必须是同一天！');
          // 重置为当天范围
          this.queryParams.dateTimeRange = this.getCurrentDateTimeRange();
          return;
        }

        // 只有当结束时间的时分秒为00:00:00时才自动调整为23:59:59
        if (value[1].endsWith(' 00:00:00')) {
          const endDate = new Date(endDateTime);
          const year = endDate.getFullYear();
          const month = String(endDate.getMonth() + 1).padStart(2, '0');
          const day = String(endDate.getDate()).padStart(2, '0');

          const newEndTime = `${year}-${month}-${day} 23:59:59`;

          this.$nextTick(() => {
            this.queryParams.dateTimeRange = [value[0], newEndTime];
          });
        }
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.smsId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    getListHistory() {
      this.loadingHistory = true;
      // 使用时间戳范围进行查询
      historyList(this.addDateRange(this.queryParamsHistory, this.queryParamsHistory.dateRange)).then(response => {
        this.historyAlarmRecordList = response.data || [];
        this.loadingHistory = false;
      }).catch(() => {
        this.loadingHistory = false;
      });
    },
    // 表单重置
    resetHistory() {
      this.queryParamsHistory = {
        deviceName: '',
        pipelineType: '',
        monitoredWaterBody: '',
        dateRange: [],
      };
      this.resetForm("queryFormHistory");
    },
    /** 搜索按钮操作 */
    handleQueryHistory() {
      this.getListHistory();
    },
    /** 重置按钮操作 */
    resetQueryHistory() {
      this.resetForm("queryFormHistory");
      this.queryParamsHistory.dateRange = [];
      this.handleQueryHistory();
    },
    // 多选框选中数据
    handleSelectionChangeHistory(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** tab点击事件 */
    handleClick(tab, event) {
      if (tab.name === 'first') {
        // 当天报警情况
        // this.getList();
      } else if (tab.name === 'second') {
        // 历史告警详情
        // this.getListHistory();
      }
    },
    /** 计算合计 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property === 'alarmNum') {
          const values = data.map(item => Number(item[column.property]) || 0);
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        } else if (column.property === 'historyAlarmNum') {
          const values = data.map(item => Number(item[column.property]) || 0);
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
  }
};
</script>
