<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="案件状态" prop="statusname">
        <el-select
          v-model="queryParams.statusname"
          placeholder="请选择案件状态"
          clearable
          filterable
          style="width: 215px"
        >
          <el-option
            v-for="item in statusnameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="任务号" prop="taskid">
        <el-input
          v-model="queryParams.taskid"
          placeholder="请输入任务号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="立案时间" prop="createtime">
        <el-date-picker
          v-model="dateRange"
          style="width: 215px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="案件属性" prop="infotypename">
        <el-select
          v-model="queryParams.infotypename"
          placeholder="请选择案件属性"
          clearable
          filterable
          style="width: 215px"
        >
          <el-option
            v-for="item in infotypenameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="案件大类" prop="infobcname">
        <el-select
          v-model="queryParams.infobcname"
          placeholder="请选择案件大类"
          clearable
          filterable
          style="width: 215px"
          @change="changeSelect"
        >
          <el-option
            v-for="(item, i) in infobcnameList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="案件小类" prop="infoscname">
        <el-select
          v-model="queryParams.infoscname"
          placeholder="请选择案件小类"
          clearable
          filterable
          style="width: 215px"
        >
          <el-option
            v-for="(item, i) in infoscnameList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="发生地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入发生地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="监督员姓名" prop="reporter">
        <el-input
          v-model="queryParams.reporter"
          placeholder="请输入监督员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="问题描述" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入问题描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="主责部门" prop="executedeptname">
        <el-select
          v-model="queryParams.executedeptname"
          placeholder="请选择主责部门"
          clearable
          filterable
          style="width: 215px"
        >
          <el-option
            v-for="(item, i) in executedeptnameList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否超期" prop="dbcenterFlagParam">
        <el-select
          v-model="queryParams.dbcenterFlagParam"
          placeholder="请选择是否超期"
          clearable
          style="width: 215px"
        >
          <el-option
            v-for="item in [{label: '是', value: true}, {label: '否', value: false}]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否突发事件" prop="dbcenterEmergentParam">
        <el-select
          v-model="queryParams.dbcenterEmergentParam"
          placeholder="请选择是否突发事件"
          clearable
          style="width: 215px"
        >
          <el-option
            v-for="item in [{label: '是', value: true}, {label: '否', value: false}]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:dbcenter:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleImageExport"
          v-hasPermi="['shcy:dbcenter:export']"
        >导出（图片）
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-document-checked"
          size="mini"
          @click="handleSync"
          v-hasPermi="['shcy:dbcenter:sync']"
        >同步</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dbcenterList" @selection-change="handleSelectionChange"
              :row-style="rowStyle" @row-click="rowClickEv">
      <el-table-column type="selection" width="55" align="center"/>

      <!-- 案件状态 -->
      <el-table-column label="案件状态" align="center" prop="statusname" width="100"/>

      <!-- 任务号 -->
      <el-table-column label="任务号" align="center" prop="taskid" width="200">
        <template slot-scope="scope">
<!--          <el-tag type="danger" v-if="scope.row.ddFlag == '2'">超期</el-tag>-->
<!--          <el-tag type="warning" v-if="scope.row.ddFlag == '1'">超时</el-tag>-->
<!--          <span style="width:45px;height:28px;border:0px solid red;display:inline-block" v-if="scope.row.ddFlag == '0'"></span>-->
          {{scope.row.taskid}}
        </template>
      </el-table-column>

      <!-- 案件来源 -->
      <el-table-column label="案件来源" align="center" prop="infosourcename" width="150"/>

      <!-- 发现时间 -->
      <el-table-column label="立案时间" align="center" prop="createtime" width="180">
        <template slot-scope="scope">
          <span >{{ parseTime(scope.row.createtime) }}</span>
        </template>
      </el-table-column>

      <!-- 案件属性 -->
      <el-table-column label="案件属性" align="center" prop="infotypename" width="100"/>

      <!-- 案件大类 -->
      <el-table-column label="案件大类" align="center" prop="infobcname" :show-overflow-tooltip="true" width="150"/>

      <!-- 案件小类 -->
      <el-table-column label="案件小类" align="center" prop="infoscname" :show-overflow-tooltip="true" width="150"/>

      <!-- 发生地址 -->
      <el-table-column label="发生地址" align="left" prop="address" :show-overflow-tooltip="true" width="180"/>

      <!-- 案件图片 -->
      <el-table-column label="案件图片" align="center" prop="imagefilename" :show-overflow-tooltip="true" width="100">
        <template slot-scope="scope">
          <el-image style="height: 23px; margin-bottom: -7px" v-if="scope.row.imagefilenames" :src="scope.row.imagefilenames[0]" :preview-src-list="scope.row.imagefilenames"></el-image>
        </template>
      </el-table-column>

      <!-- 核查照片 -->
      <el-table-column label="核查照片" align="center" prop="checkimage" :show-overflow-tooltip="true" width="100">
        <template slot-scope="scope">
          <el-image style="height: 23px; margin-bottom: -7px" v-if="scope.row.checkimages" :src="scope.row.checkimages[0]" :preview-src-list="scope.row.checkimages"></el-image>
        </template>
      </el-table-column>

      <!-- 监督员姓名 -->
      <el-table-column label="监督员姓名" align="center" prop="reporter" :show-overflow-tooltip="true" width="150"/>

      <!-- 问题描述 -->
      <el-table-column label="问题描述" align="left" prop="description" :show-overflow-tooltip="true" width="180"/>

      <!-- 主责部门 -->
      <el-table-column label="主责部门" align="center" prop="executedeptname" width="180"/>

      <!-- 三级主责部门 -->
      <el-table-column label="三级主责部门" align="center" prop="subexecutedeptnameMh" width="180"/>

      <!-- 结案时间 -->
      <el-table-column label="结案时间" align="center" prop="endtime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endtime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改网格化案件信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="chosetitle" :visible.sync="choseOpen" width="500px" append-to-body>
      <el-form ref="formData" :model="formData" :rules="fpRules" label-width="120px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-input prop="wghId" :value="this.formData.wghId" type="hidden"/>
            <el-form-item label="选择分派人员" prop="dealBy"  >
              <el-radio-group v-model="formData.dealBy" size="medium" style="line-height:30px;">
                <el-radio v-for="(item, index) in userOptions" :key="index" :label="item.userName"
                >{{item.userName}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="chosePersonSubmitForm">确 定 选 择</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listDbcenterJsqzxdc, getDbcenter, delDbcenter, addDbcenter, updateDbcenter, getUserInfoForWgh, syncDbcenter} from "@/api/shcy/dbcenter";

import dbcenterMixin from '@/mixin/dbcenterMixin'
import {Loading} from "element-ui";
import {chosePerson,getWghByWghId} from "@/api/shcy/wgh";

export default {
  name: "Dbcenter",
  dicts: ['wgh_state'],
  mixins: [dbcenterMixin],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 网格化案件信息表格数据
      dbcenterList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      choseOpen: false,
      chosetitle:"",
      formData: {
        dealBy: null,
        wghId:null,
      },
      userOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createtime: null,
        dbcenterFlagParam: null,
        dbcenterEmergentParam: null,
        infoscname: '',
        infosourcename: '金山区专项调查'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      fpRules: {
        dealBy: [{
          required: true,
          message: '分派人不能为空',
          trigger: 'change'
        }],
      },
      // 日期范围
      dateRange: [],
      syncLoading: null,
      infosourcenameOptions: [
        {
          label: '专职监督员上报',
          value: '专职监督员上报'
        },
        {
          label: '区级网格上报',
          value: '区级网格上报'
        }
      ],
      infotypenameOptions: [
        {
          label: '事件',
          value: '事件'
        },
        {
          label: '部件',
          value: '部件'
        }
      ],
      statusnameOptions: [
        {
          label: '已作废',
          value: '已作废'
        },
        {
          label: '待受理',
          value: '待受理'
        },
        {
          label: '热线退单待审核',
          value: '热线退单待审核'
        },
        {
          label: '待立案',
          value: '待立案'
        },
        {
          label: '待结案',
          value: '待结案'
        },
        {
          label: '已结案',
          value: '已结案'
        },
        {
          label: '已下发核查',
          value: '已下发核查'
        },
        {
          label: '已退回其他平台',
          value: '已退回其他平台'
        },
        {
          label: '待下发核查',
          value: '待下发核查'
        },
        {
          label: '预立案',
          value: '预立案'
        },
        {
          label: '热线延期待审核',
          value: '热线延期待审核'
        },
        {
          label: '待督办',
          value: '待督办'
        },
        {
          label: '待派遣',
          value: '待派遣'
        },
        {
          label: '待再回访',
          value: '待再回访'
        }
      ],
      selectedArrData: [], // 把选择到的当前行的id存到数组中
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getUserInfoForWgh(){
      getUserInfoForWgh().then(response => {
        this.userOptions=response.data;
      })
    },
    handleChoseUpdate(row,title) {
      this.getUserInfoForWgh();
      this.reset();
      const id = row.id || this.ids;
      this.formData.wghId=id;
      getWghByWghId(id).then(response => {
        this.formData=response.data
        this.formData.wghId=id;
        this.choseOpen = true;
        this.chosetitle = title;
      })
    },
    /** 查询网格化案件信息列表 */
    getList() {
      this.loading = true;
      listDbcenterJsqzxdc(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.dbcenterList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.choseOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        checkimage: null,
        deptname: null,
        statusname: null,
        endtime: null,
        dispatchtime: null,
        createtime: null,
        executedeptname: null,
        address: null,
        hotlinesn: null,
        reporter: null,
        infoscname: null,
        infobcname: null,
        infotypename: null,
        infosourcename: null,
        description: null,
        casesn: null,
        taskid: null
      };
      this.formData={
        dealBy:null,
        wghId:null,
      }
      this.resetForm("form");
      this.resetForm("formData");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.selectedArrData = [];
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.choseOpen = false;
      this.title = "添加网格化案件信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDbcenter(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.choseOpen = false;
        this.title = "修改网格化案件信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDbcenter(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.choseOpen = false;
              this.getList();
            });
          } else {
            addDbcenter(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.choseOpen = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除网格化案件信息编号为"' + ids + '"的数据项？').then(function () {
        return delDbcenter(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/dbcenter/export', {
        ...this.queryParams
      }, `dbcenter_${new Date().getTime()}.xlsx`)
    },
    /** 导出（图片）按钮操作 */
    handleImageExport() {
      this.download('shcy/dbcenter/exportImage', {
        ...this.queryParams
      }, `dbcenter_${new Date().getTime()}.xlsx`)
    },
    handleSync() {
      if (this.dateRange == null || this.dateRange.length == 0) {
        this.$modal.msgError('请选择时间范围');
        return;
      }
      const that = this;
      this.$modal.confirm('是否确认同步数据？').then(function() {
        that.syncLoading = Loading.service({
          lock: true,
          text: '同步中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        return syncDbcenter(that.dateRange);
      }).then(() => {
        that.syncLoading.close();
        this.getList();
        this.$modal.msgSuccess("同步成功");
      }).catch(() => {});
    },
    changeSelect() {
      // 清空案件小类的值
      this.queryParams.infoscname = '';
      // 循环遍历父级
      for (const k in this.infobcnameList) {
        if (this.queryParams.infobcname === this.infobcnameList[k]) {
          // 核心代码在这里 进行赋值操作
          this.infoscnameList = this.typeObj[this.queryParams.infobcname];
        }
      }
    },
    chosePersonSubmitForm() {
      this.$refs["formData"].validate(valid => {
        if (valid) {
          chosePerson(this.formData).then(response => {
            this.$modal.msgSuccess("分派成功");
            this.choseOpen = false;
            this.getList();
          });
        }
      });
    },
    // 某一行被点击行触发事件，默认形参代表一整行数据
    rowClickEv(row) {
      //点击的那行数据默认是对象{__ob__: Observer}，将其转数组
      this.selectedArrData = [row];
    },
    rowStyle({ row }) {
      const checkIdList = this.selectedArrData.map((item) => item.id);
      if (checkIdList.includes(row.id)) {
        return {
          backgroundColor: "#1890ff",
          color: "#ffffff",
        };
      }
    },
  }
};
</script>

<style scoped>
::v-deep .el-table th > .cell {
  white-space: nowrap;
}

::v-deep .el-table tbody tr:hover > td {
  /*background-color: #1890ff !important;
  color: #ffffff;*/

  background-color: transparent !important;
}
</style>

<style lang="scss">
.el-tooltip__popper {
  max-width: 50%;
}
</style>
