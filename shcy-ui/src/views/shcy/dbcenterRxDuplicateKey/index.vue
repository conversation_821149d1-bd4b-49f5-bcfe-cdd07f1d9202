<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="发现时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 220px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tableData" border :span-method="objectSpanMethod">
      <el-table-column type="selection" width="55" align="center" v-if="false"/>

      <!-- 序号 -->
      <el-table-column label="序号" align="center" width="100">
        <template slot-scope="scope">
          {{ getSequenceNumber(scope.$index) }}
        </template>
      </el-table-column>

      <!-- 重点大类 -->
      <el-table-column label="重点大类" align="center" prop="parentduplicateclassification"/>

      <!-- 重复次数（大类） -->
      <el-table-column label="重复次数" align="center" prop="parentDuplicateCount"/>

      <!-- 重点小类 -->
      <el-table-column label="重点小类" align="left" prop="duplicateclassification" />

      <!-- 诉求人 -->
      <el-table-column label="诉求人" align="center" prop="requestItem"/>

      <!-- 第一次工单号 -->
      <el-table-column label="第一次工单号" align="center" prop="firstOrderNo"/>

      <!-- 最新一次工单号 -->
      <el-table-column label="最新一次工单号" align="center" prop="latestOrderNo">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleOrderClick(scope.row)" :underline="false" style="cursor: pointer;">
            {{ scope.row.latestOrderNo }}
          </el-link>
        </template>
      </el-table-column>

      <!-- 重复次数（详情） -->
      <el-table-column label="重复次数" align="center" prop="duplicateCount"/>

      <!-- 重复占比 -->
      <el-table-column label="重复占比" align="center" prop="duplicateRatio">
        <template slot-scope="scope">
          {{ formatPercentage(scope.row.duplicateRatio) }}
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import {listDbcenterRxDuplicateKey} from "@/api/shcy/dbcenterRxDuplicateKey";

export default {
  name: "DbcenterRxDuplicateKey",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 重复工单数据
      dbcenterRxDuplicateList: [],
      // 表格数据（扁平化后的数据）
      tableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 日期范围
      dateRange: [],
      // 合并单元格数组
      spanArr: {
        parentClass: [], // 重点大类合并
        subClass: []     // 重点小类合并
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询重复工单信息列表 */
    getList() {
      this.loading = true;
      listDbcenterRxDuplicateKey(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.dbcenterRxDuplicateList = response.data;
        this.processTableData();
        this.loading = false;
      });
    },
    /** 处理表格数据 */
    processTableData() {
      this.tableData = [];
      this.spanArr = {
        parentClass: [],
        subClass: []
      };

      const dataList = this.dbcenterRxDuplicateList || [];

      // 填充表格数据
      dataList.forEach(parent => {
        const parentName = parent.parentduplicateclassification;
        const parentCount = parent.duplicateCount;

        if (parent.duplicateSubCategories && parent.duplicateSubCategories.length > 0) {
          parent.duplicateSubCategories.forEach(sub => {
            const subName = sub.duplicateclassification;

            if (sub.workOrderDetails && sub.workOrderDetails.length > 0) {
              sub.workOrderDetails.forEach(detail => {
                this.tableData.push({
                  parentduplicateclassification: parentName,
                  parentDuplicateCount: parentCount,
                  duplicateclassification: subName,
                  requestItem: detail.requestItem,
                  firstOrderNo: detail.firstOrderNo,
                  latestOrderNo: detail.latestOrderNo,
                  duplicateCount: detail.duplicateCount,
                  duplicateRatio: detail.duplicateRatio
                });
              });
            } else {
              // 无工单详情数据
              this.tableData.push({
                parentduplicateclassification: parentName,
                parentDuplicateCount: parentCount,
                duplicateclassification: subName,
                requestItem: '',
                firstOrderNo: '',
                latestOrderNo: '',
                duplicateCount: '',
                duplicateRatio: ''
              });
            }
          });
        } else {
          // 无子分类数据
          this.tableData.push({
            parentduplicateclassification: parentName,
            parentDuplicateCount: parentCount,
            duplicateclassification: '',
            requestItem: '',
            firstOrderNo: '',
            latestOrderNo: '',
            duplicateCount: '',
            duplicateRatio: ''
          });
        }
      });

      // 计算合并单元格
      this.calculateSpanArr();
    },
    /** 计算合并单元格数组 */
    calculateSpanArr() {
      this.spanArr = {
        parentClass: [],
        subClass: []
      };

      const data = this.tableData;
      if (data.length === 0) return;

      // 初始值
      this.spanArr.parentClass = [1];
      this.spanArr.subClass = [1];

      // 计算各列的合并单元格
      for (let i = 1; i < data.length; i++) {
        // 计算重点大类合并
        if (data[i].parentduplicateclassification === data[i-1].parentduplicateclassification) {
          this.spanArr.parentClass[i] = 0;
          this.spanArr.parentClass[this.getLastIndex(this.spanArr.parentClass)] += 1;
        } else {
          this.spanArr.parentClass.push(1);
        }

        // 计算重点小类合并
        if (data[i].duplicateclassification === data[i-1].duplicateclassification &&
            data[i].parentduplicateclassification === data[i-1].parentduplicateclassification) {
          this.spanArr.subClass[i] = 0;
          this.spanArr.subClass[this.getLastIndex(this.spanArr.subClass)] += 1;
        } else {
          this.spanArr.subClass.push(1);
        }
      }
    },
    /** 获取最后一个非零元素的索引 */
    getLastIndex(arr) {
      for (let i = arr.length - 1; i >= 0; i--) {
        if (arr[i] !== 0) {
          return i;
        }
      }
      return -1;
    },
    /** 获取序号 */
    getSequenceNumber(index) {
      // 如果当前行的合并值大于0，说明是新的重点大类的开始
      if (this.spanArr.parentClass[index] > 0) {
        // 计算这是第几个重点大类
        let sequenceNum = 1;
        for (let i = 0; i < index; i++) {
          if (this.spanArr.parentClass[i] > 0) {
            sequenceNum++;
          }
        }
        return sequenceNum;
      }
      // 如果是合并的行，返回空字符串（不显示）
      return '';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {
      }
      // this.handleQuery();
      this.dbcenterRxDuplicateList = [];
      this.tableData = [];
    },
    /** 合并单元格方法 */
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 序号
        const _row = this.spanArr.parentClass[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 1) { // 重点大类
        const _row = this.spanArr.parentClass[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 2) { // 重复次数（大类）
        const _row = this.spanArr.parentClass[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 3) { // 重点小类
        const _row = this.spanArr.subClass[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    /** 格式化百分比 */
    formatPercentage(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00%';
      }
      const num = parseFloat(value);
      if (isNaN(num)) {
        return '0.00%';
      }
      return num.toFixed(2) + '%';
    },
    /** 处理重复工单号点击事件 */
    handleOrderClick(row) {
      // 获取发现时间，从dateRange中获取
      const startDate = this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : null;
      const endDate = this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : null;

      // 构建路由参数
      const query = {
        hotlinesn: row.latestOrderNo
      };

      // 如果有发现时间范围，添加到参数中
      if (startDate && endDate) {
        query.startDate = startDate;
        query.endDate = endDate;
      }

      // 如果有重点大类，添加到参数中
      if (row.parentduplicateclassification) {
        query.parentduplicateclassification = row.parentduplicateclassification;
      }
      // 如果有重点小类，添加到参数中
      if (row.duplicateclassification) {
        query.duplicateclassification = row.duplicateclassification;
      }

      // 跳转到目标页面
      this.$router.push({
        path: '/hotline/dbCenterRx',
        query: query
      });
    },
  },
};
</script>

<style scoped>
::v-deep .el-table th > .cell {
  white-space: nowrap;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-table tbody tr:hover > td {
  background-color: transparent !important;
}
</style>

<style lang="scss">
.el-tooltip__popper {
  max-width: 50%;
}
</style>
