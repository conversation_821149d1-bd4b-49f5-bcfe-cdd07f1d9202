<template>
  <div class="app-container">
<!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--      <el-form-item label="隐患表id" prop="checkId">-->
<!--        <el-input-->
<!--          v-model="queryParams.checkId"-->
<!--          placeholder="请输入隐患表id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="检查项目父类" prop="checkItemParent">-->
<!--        <el-input-->
<!--          v-model="queryParams.checkItemParent"-->
<!--          placeholder="请输入检查项目父类"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="检查项目子类" prop="checkItemChild">-->
<!--        <el-input-->
<!--          v-model="queryParams.checkItemChild"-->
<!--          placeholder="请输入检查项目子类"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="发现隐患数量" prop="foundHazardCount">-->
<!--        <el-input-->
<!--          v-model="queryParams.foundHazardCount"-->
<!--          placeholder="请输入发现隐患数量"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="整改隐患数量" prop="rectifiedHazardCount">-->
<!--        <el-input-->
<!--          v-model="queryParams.rectifiedHazardCount"-->
<!--          placeholder="请输入整改隐患数量"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['shcy:hazardCheckExtend:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['shcy:hazardCheckExtend:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['shcy:hazardCheckExtend:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['shcy:hazardCheckExtend:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->

    <el-table v-loading="loading" :data="hazardCheckExtendList" @selection-change="handleSelectionChange">
      <el-table-column label="检查项目父类" align="center" prop="checkItemParentName" />
      <el-table-column label="检查项目子类" align="center" prop="checkItemChildName" />
      <el-table-column label="发现隐患数量" align="center" prop="foundHazardCount" />
      <el-table-column label="整改隐患数量" align="center" prop="rectifiedHazardCount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:hazardCheckExtend:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:hazardCheckExtend:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改隐患排查子表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="隐患表id" prop="checkId">
          <el-input v-model="form.checkId" placeholder="请输入隐患表id" />
        </el-form-item>
        <el-form-item label="检查项目父类" prop="checkItemParent">
          <el-input v-model="form.checkItemParent" placeholder="请输入检查项目父类" />
        </el-form-item>
        <el-form-item label="检查项目子类" prop="checkItemChild">
          <el-input v-model="form.checkItemChild" placeholder="请输入检查项目子类" />
        </el-form-item>
        <el-form-item label="发现隐患数量" prop="foundHazardCount">
          <el-input v-model="form.foundHazardCount" placeholder="请输入发现隐患数量" />
        </el-form-item>
        <el-form-item label="整改隐患数量" prop="rectifiedHazardCount">
          <el-input v-model="form.rectifiedHazardCount" placeholder="请输入整改隐患数量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHazardCheckExtend, getHazardCheckExtend, delHazardCheckExtend, addHazardCheckExtend, updateHazardCheckExtend } from "@/api/shcy/hazardCheckExtend";

export default {
  name: "HazardCheckExtend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患排查子表表格数据
      hazardCheckExtendList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkId: null,
        checkItemParent: null,
        checkItemChild: null,
        foundHazardCount: null,
        rectifiedHazardCount: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    const id = this.$route.params && this.$route.params.id;
    this.getList(id);
  },
  methods: {
    /** 查询隐患排查子表列表 */
    getList(id) {
      this.loading = true;
      if(id)
      {
        this.queryParams.checkId=id;
      }
      listHazardCheckExtend(this.queryParams).then(response => {
        this.hazardCheckExtendList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        checkId: null,
        checkItemParent: null,
        checkItemChild: null,
        foundHazardCount: null,
        rectifiedHazardCount: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隐患排查子表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHazardCheckExtend(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隐患排查子表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHazardCheckExtend(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHazardCheckExtend(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除隐患排查子表编号为"' + ids + '"的数据项？').then(function() {
        return delHazardCheckExtend(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/hazardCheckExtend/export', {
        ...this.queryParams
      }, `hazardCheckExtend_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
