<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属网格" prop="gridArea">
        <el-select
          style="width: 220px"
          v-model="queryParams.gridArea"
          placeholder="请选择所属网格"
          clearable
          filterable
        >
          <el-option
            v-for="dict in dict.type.dbcenter_streetarea"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="处置状态" prop="circulationState">
        <el-select v-model="queryParams.circulationState" placeholder="请选择处置状态" clearable filterable>
          <el-option
            v-for="dict in dict.type.case_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:comprehensiveInspection:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:comprehensiveInspection:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="comprehensiveInspectionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="所属网格" align="center" prop="gridArea" />
      <el-table-column label="企业名称" align="center" prop="companyName" />
      <el-table-column label="企业地址" align="center" prop="companyAddress" />
      <el-table-column label="检查日期" align="center" prop="inspectionDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.inspectionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="烟道是否高空排放" align="center" prop="isHighEmission" />
      <el-table-column label="是否安装油烟净化器" align="center" prop="hasPurifier" />
      <el-table-column label="是否符合距离要求" align="center" prop="meetDistanceReq" />
      <el-table-column label="是否有隔油除渣设施" align="center" prop="hasGreaseTrap" />
      <el-table-column label="污水是否正确纳管" align="center" prop="sewageProperlyManaged" />
      <el-table-column label="检查人数" align="center" prop="inspectorCount" />
      <el-table-column label="处置状态" align="center" prop="circulationState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.case_state" :value="scope.row.circulationState"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)">处置详细
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:comprehensiveInspection:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:comprehensiveInspection:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改综合检查对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="任务名称" prop="taskName">
          <span v-if="operatorType === 'view'">{{ form.taskName }}</span>
          <el-input v-else v-model="form.taskName" readonly placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="所属网格" prop="gridArea">
          <span v-if="operatorType === 'view'">{{ form.gridArea }}</span>
          <el-select v-else v-model="form.gridArea" placeholder="请选择所属网格">
            <el-option
              v-for="dict in dict.type.dbcenter_streetarea"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业名称" prop="companyName">
          <span v-if="operatorType === 'view'">{{ form.companyName }}</span>
          <el-input v-else v-model="form.companyName" placeholder="请输入企业名称"/>
        </el-form-item>
        <el-form-item label="企业地址" prop="companyAddress">
          <span v-if="operatorType === 'view'">{{ form.companyAddress }}</span>
          <el-input v-else v-model="form.companyAddress" placeholder="请输入企业地址"/>
        </el-form-item>
        <el-form-item label="检查日期" prop="inspectionDate">
          <span v-if="operatorType === 'view'">{{ parseTime(form.inspectionDate, '{y}-{m}-{d}') }}</span>
          <el-date-picker v-else
            clearable
            v-model="form.inspectionDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择检查日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="烟道是否高空排放" prop="isHighEmission">
          <span v-if="operatorType === 'view'">{{ form.isHighEmission }}</span>
          <el-select v-else v-model="form.isHighEmission" placeholder="请选择烟道是否高空排放">
            <el-option
              v-for="dict in yesNoOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="烟道排放照片" prop="highEmissionPhoto" v-if="highEmissionPhotoList.length > 0">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in highEmissionPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="highEmissionPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="是否安装油烟净化器" prop="hasPurifier">
          <span v-if="operatorType === 'view'">{{ form.hasPurifier }}</span>
          <el-select v-else v-model="form.hasPurifier" placeholder="请选择是否安装油烟净化器">
            <el-option
              v-for="dict in yesNoOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="油烟净化器照片" prop="purifierPhoto" v-if="purifierPhotoList.length > 0">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in purifierPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="purifierPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="是否符合距离要求" prop="meetDistanceReq">
          <span v-if="operatorType === 'view'">{{ form.meetDistanceReq }}</span>
          <el-select v-else v-model="form.meetDistanceReq" placeholder="请选择是否符合距离要求">
            <el-option
              v-for="dict in yesNoOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否有隔油除渣设施" prop="hasGreaseTrap">
          <span v-if="operatorType === 'view'">{{ form.hasGreaseTrap }}</span>
          <el-checkbox-group v-else v-model="selectedGreaseTrap" @change="handleGreaseTrapChange">
            <el-checkbox v-for="item in greaseTrapOptions" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="隔油除渣设施照片" prop="greaseTrapPhoto" v-if="greaseTrapPhotoList.length > 0">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in greaseTrapPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="greaseTrapPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="污水是否正确纳管" prop="sewageProperlyManaged">
          <span v-if="operatorType === 'view'">{{ form.sewageProperlyManaged }}</span>
          <el-select v-else v-model="form.sewageProperlyManaged" placeholder="请选择污水是否正确纳管">
            <el-option
              v-for="dict in yesNoOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="其他问题" prop="otherIssues" v-if="form.otherIssues">
          <span v-if="operatorType === 'view'">{{ form.otherIssues }}</span>
          <el-input v-else v-model="form.otherIssues" placeholder="请输入其他问题" type="textarea" :rows="3"/>
        </el-form-item>
        <el-form-item label="其他问题照片" prop="otherIssuesPhoto" v-if="otherIssuesPhotoList.length > 0">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in otherIssuesPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="otherIssuesPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="检查人数" prop="inspectorCount">
          <span v-if="operatorType === 'view'">{{ form.inspectorCount }}</span>
          <el-input v-else v-model="form.inspectorCount" placeholder="请输入检查人数" type="number"/>
        </el-form-item>
        <el-form-item label="现场检查照片" prop="inspectionPhoto" v-if="inspectionPhotoList.length > 0">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in inspectionPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="inspectionPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="复查日期" prop="reviewDate" v-if="form.reviewDate && operatorType === 'view'">
          <span v-if="operatorType === 'view'">{{ parseTime(form.reviewDate, '{y}-{m}-{d}') }}</span>
          <el-date-picker v-else
            clearable
            v-model="form.reviewDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择复查日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="烟道高空排放处置内容" prop="highEmissionDisposal" v-if="form.highEmissionDisposal && operatorType === 'view'">
          <span v-if="operatorType === 'view'">{{ form.highEmissionDisposal }}</span>
          <el-input v-else v-model="form.highEmissionDisposal" placeholder="请输入烟道高空排放处置内容"/>
        </el-form-item>
        <el-form-item label="烟道处置照片" prop="highEmissionDisposalPhoto" v-if="highEmissionDisposalPhotoList.length > 0 && operatorType === 'view'">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in highEmissionDisposalPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="highEmissionDisposalPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="油烟净化器处置内容" prop="purifierDisposal" v-if="form.purifierDisposal && operatorType === 'view'">
          <span v-if="operatorType === 'view'">{{ form.purifierDisposal }}</span>
          <el-input v-else v-model="form.purifierDisposal" placeholder="请输入油烟净化器处置内容"/>
        </el-form-item>
        <el-form-item label="油烟净化器处置照片" prop="purifierDisposalPhoto" v-if="purifierDisposalPhotoList.length > 0 && operatorType === 'view'">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in purifierDisposalPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="purifierDisposalPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="隔油除渣设施处置内容" prop="greaseTrapDisposal" v-if="form.greaseTrapDisposal && operatorType === 'view'">
          <span v-if="operatorType === 'view'">{{ form.greaseTrapDisposal }}</span>
          <el-input v-else v-model="form.greaseTrapDisposal" placeholder="请输入隔油除渣设施处置内容"/>
        </el-form-item>
        <el-form-item label="隔油除渣设施处置照片" prop="greaseTrapDisposalPhoto" v-if="greaseTrapDisposalPhotoList.length > 0 && operatorType === 'view'">
          <div class="image-preview">
            <el-image
              v-for="(url, index) in greaseTrapDisposalPhotoList"
              :key="index"
              :src="url"
              lazy
              :preview-src-list="greaseTrapDisposalPhotoList"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="污水纳管处置内容" prop="sewageDisposal" v-if="form.sewageDisposal && operatorType === 'view'">
          <span v-if="operatorType === 'view'">{{ form.sewageDisposal }}</span>
          <el-input v-else v-model="form.sewageDisposal" placeholder="请输入污水纳管处置内容"/>
        </el-form-item>
        <el-form-item label="处置状态" prop="circulationState" v-if="operatorType === 'view'">
          <span v-if="operatorType === 'view'">
            <dict-tag :options="dict.type.case_state" :value="form.circulationState"/>
          </span>
          <el-select v-else v-model="form.circulationState" placeholder="请选择处置状态">
            <el-option
              v-for="dict in dict.type.case_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="operatorType == 'view'" @click="cancel">关 闭</el-button>
        <el-button v-if="operatorType == 'edit'" type="primary" @click="submitForm">确 定</el-button>
        <el-button v-if="operatorType == 'edit'" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listComprehensiveInspection, getComprehensiveInspection, delComprehensiveInspection, updateComprehensiveInspection } from "@/api/shcy/comprehensiveInspection";

export default {
  name: "ComprehensiveInspection",
  dicts: ['case_state',  'dbcenter_streetarea'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 综合检查表格数据
      comprehensiveInspectionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 操作类型
      operatorType: '',
      // 照片列表
      highEmissionPhotoList: [],
      purifierPhotoList: [],
      greaseTrapPhotoList: [],
      otherIssuesPhotoList: [],
      inspectionPhotoList: [],
      highEmissionDisposalPhotoList: [],
      purifierDisposalPhotoList: [],
      greaseTrapDisposalPhotoList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        gridArea: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      yesNoOptions: [{
        text: '是',
        value: '是'
      }, {
        text: '否',
        value: '否'
      }],
      selectedGreaseTrap: [],
      greaseTrapOptions: [{
        label: '油水分离器',
        value: '油水分离器'
      }, {
        label: '隔油池',
        value: '隔油池'
      }, {
        label: '无',
        value: '无'
      }]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询综合检查列表 */
    getList() {
      this.loading = true;
      listComprehensiveInspection(this.queryParams).then(response => {
        this.comprehensiveInspectionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskName: null,
        gridArea: null,
        companyName: null,
        companyAddress: null,
        inspectionDate: null,
        isHighEmission: null,
        highEmissionPhoto: null,
        hasPurifier: null,
        purifierPhoto: null,
        meetDistanceReq: null,
        hasGreaseTrap: null,
        greaseTrapPhoto: null,
        sewageProperlyManaged: null,
        otherIssues: null,
        otherIssuesPhoto: null,
        inspectorCount: null,
        inspectionPhoto: null,
        reviewDate: null,
        highEmissionDisposal: null,
        highEmissionDisposalPhoto: null,
        purifierDisposal: null,
        purifierDisposalPhoto: null,
        greaseTrapDisposal: null,
        greaseTrapDisposalPhoto: null,
        sewageDisposal: null,
        circulationState: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.selectedGreaseTrap = [];
      this.resetForm("form");
      // 清空照片列表
      this.highEmissionPhotoList = [];
      this.purifierPhotoList = [];
      this.greaseTrapPhotoList = [];
      this.otherIssuesPhotoList = [];
      this.inspectionPhotoList = [];
      this.highEmissionDisposalPhotoList = [];
      this.purifierDisposalPhotoList = [];
      this.greaseTrapDisposalPhotoList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
     /** 修改按钮操作 */
     handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getComprehensiveInspection(id).then(response => {
        this.form = response.data;
        // 初始化隔油除渣设施选中值
        this.selectedGreaseTrap = this.form.hasGreaseTrap ? this.form.hasGreaseTrap.split(',') : [];
        this.open = true;
        this.operatorType = 'edit';
        this.title = "修改综合检查";

        // 处理各类照片URL
        this.processPhotoUrls(response.data);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateComprehensiveInspection(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除综合检查编号为"' + ids + '"的数据项？').then(function() {
        return delComprehensiveInspection(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看详情页面 按钮操作**/
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getComprehensiveInspection(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.operatorType = 'view';
        this.title = "查看详情";

        // 处理各类照片URL
        this.processPhotoUrls(response.data);
      });
    },
    /** 处理照片URL */
    processPhotoUrls(data) {
      // 处理高空排放照片
      if (data.highEmissionPhotoUrls) {
        data.highEmissionPhotoUrls.forEach(item => {
          this.highEmissionPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理油烟净化器照片
      if (data.purifierPhotoUrls) {
        data.purifierPhotoUrls.forEach(item => {
          this.purifierPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理隔油除渣设施照片
      if (data.greaseTrapPhotoUrls) {
        data.greaseTrapPhotoUrls.forEach(item => {
          this.greaseTrapPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理其他问题照片
      if (data.otherIssuesPhotoUrls) {
        data.otherIssuesPhotoUrls.forEach(item => {
          this.otherIssuesPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理现场检查照片
      if (data.inspectionPhotoUrls) {
        data.inspectionPhotoUrls.forEach(item => {
          this.inspectionPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理高空排放处置照片
      if (data.highEmissionDisposalPhotoUrls) {
        data.highEmissionDisposalPhotoUrls.forEach(item => {
          this.highEmissionDisposalPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理油烟净化器处置照片
      if (data.purifierDisposalPhotoUrls) {
        data.purifierDisposalPhotoUrls.forEach(item => {
          this.purifierDisposalPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }

      // 处理隔油除渣设施处置照片
      if (data.greaseTrapDisposalPhotoUrls) {
        data.greaseTrapDisposalPhotoUrls.forEach(item => {
          this.greaseTrapDisposalPhotoList.push(process.env.VUE_APP_BASE_API + item);
        });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/comprehensiveInspection/export', {
        ...this.queryParams
      }, `comprehensiveInspection_${new Date().getTime()}.xlsx`)
    },
    handleGreaseTrapChange(value) {
      // 处理"无"选项的互斥逻辑
      if (value.includes('无')) {
        // 如果选中了"无"，取消其他选项
        this.selectedGreaseTrap = ['无'];
      } else if (value.length > 0) {
        // 如果选中了其他选项，移除"无"选项
        this.selectedGreaseTrap = value.filter(item => item !== '无');
      }
      // 更新form中的值
      this.form.hasGreaseTrap = this.selectedGreaseTrap.join(',');
    }
  }
};
</script>

<style scoped>
.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.preview-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-form-item {
  margin-bottom: 15px;
}
</style>
