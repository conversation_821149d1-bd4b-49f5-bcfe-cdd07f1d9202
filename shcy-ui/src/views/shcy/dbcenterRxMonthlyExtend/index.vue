<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="单位" prop="dept">
        <el-input
          v-model="queryParams.dept"
          placeholder="请输入单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="立案数" prop="las">
        <el-input
          v-model="queryParams.las"
          placeholder="请输入立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="常住人口数" prop="czrks">
        <el-input
          v-model="queryParams.czrks"
          placeholder="请输入常住人口数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准立案数" prop="bzllas">
        <el-input
          v-model="queryParams.bzllas"
          placeholder="请输入标准立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准类常住人口立案数" prop="bzlczrklas">
        <el-input
          v-model="queryParams.bzlczrklas"
          placeholder="请输入标准类常住人口立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拓展类立案数" prop="tzllas">
        <el-input
          v-model="queryParams.tzllas"
          placeholder="请输入拓展类立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拓展类常住人口立案数" prop="tzlczrklas">
        <el-input
          v-model="queryParams.tzlczrklas"
          placeholder="请输入拓展类常住人口立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="简易流程立案数" prop="jylclas">
        <el-input
          v-model="queryParams.jylclas"
          placeholder="请输入简易流程立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="简易流程常住人口立案数" prop="jylcczrklas">
        <el-input
          v-model="queryParams.jylcczrklas"
          placeholder="请输入简易流程常住人口立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="村居立案数" prop="cjlas">
        <el-input
          v-model="queryParams.cjlas"
          placeholder="请输入村居立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="村居常住人口立案数" prop="cjczrklas">
        <el-input
          v-model="queryParams.cjczrklas"
          placeholder="请输入村居常住人口立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准立案数" prop="bzlas">
        <el-input
          v-model="queryParams.bzlas"
          placeholder="请输入标准立案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结案数" prop="jas">
        <el-input
          v-model="queryParams.jas"
          placeholder="请输入结案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结案率" prop="jal">
        <el-input
          v-model="queryParams.jal"
          placeholder="请输入结案率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="未结案数" prop="wjas">
        <el-input
          v-model="queryParams.wjas"
          placeholder="请输入未结案数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="父id" prop="pid">
        <el-input
          v-model="queryParams.pid"
          placeholder="请输入父id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:dbcenterRxMonthlyExtend:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:dbcenterRxMonthlyExtend:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:dbcenterRxMonthlyExtend:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:dbcenterRxMonthlyExtend:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dbcenterRxMonthlyExtendList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="单位" align="center" prop="dept" />
      <el-table-column label="立案数" align="center" prop="las" />
      <el-table-column label="常住人口数" align="center" prop="czrks" />
      <el-table-column label="标准立案数" align="center" prop="bzllas" />
      <el-table-column label="标准类常住人口立案数" align="center" prop="bzlczrklas" />
      <el-table-column label="拓展类立案数" align="center" prop="tzllas" />
      <el-table-column label="拓展类常住人口立案数" align="center" prop="tzlczrklas" />
      <el-table-column label="简易流程立案数" align="center" prop="jylclas" />
      <el-table-column label="简易流程常住人口立案数" align="center" prop="jylcczrklas" />
      <el-table-column label="村居立案数" align="center" prop="cjlas" />
      <el-table-column label="村居常住人口立案数" align="center" prop="cjczrklas" />
      <el-table-column label="标准立案数" align="center" prop="bzlas" />
      <el-table-column label="结案数" align="center" prop="jas" />
      <el-table-column label="结案率" align="center" prop="jal" />
      <el-table-column label="未结案数" align="center" prop="wjas" />
      <el-table-column label="父id" align="center" prop="pid" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthlyExtend:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthlyExtend:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改12345热线分析拓展对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="单位" prop="dept">
          <el-input v-model="form.dept" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="立案数" prop="las">
          <el-input v-model="form.las" placeholder="请输入立案数" />
        </el-form-item>
        <el-form-item label="常住人口数" prop="czrks">
          <el-input v-model="form.czrks" placeholder="请输入常住人口数" />
        </el-form-item>
        <el-form-item label="标准立案数" prop="bzllas">
          <el-input v-model="form.bzllas" placeholder="请输入标准立案数" />
        </el-form-item>
        <el-form-item label="标准类常住人口立案数" prop="bzlczrklas">
          <el-input v-model="form.bzlczrklas" placeholder="请输入标准类常住人口立案数" />
        </el-form-item>
        <el-form-item label="拓展类立案数" prop="tzllas">
          <el-input v-model="form.tzllas" placeholder="请输入拓展类立案数" />
        </el-form-item>
        <el-form-item label="拓展类常住人口立案数" prop="tzlczrklas">
          <el-input v-model="form.tzlczrklas" placeholder="请输入拓展类常住人口立案数" />
        </el-form-item>
        <el-form-item label="简易流程立案数" prop="jylclas">
          <el-input v-model="form.jylclas" placeholder="请输入简易流程立案数" />
        </el-form-item>
        <el-form-item label="简易流程常住人口立案数" prop="jylcczrklas">
          <el-input v-model="form.jylcczrklas" placeholder="请输入简易流程常住人口立案数" />
        </el-form-item>
        <el-form-item label="村居立案数" prop="cjlas">
          <el-input v-model="form.cjlas" placeholder="请输入村居立案数" />
        </el-form-item>
        <el-form-item label="村居常住人口立案数" prop="cjczrklas">
          <el-input v-model="form.cjczrklas" placeholder="请输入村居常住人口立案数" />
        </el-form-item>
        <el-form-item label="标准立案数" prop="bzlas">
          <el-input v-model="form.bzlas" placeholder="请输入标准立案数" />
        </el-form-item>
        <el-form-item label="结案数" prop="jas">
          <el-input v-model="form.jas" placeholder="请输入结案数" />
        </el-form-item>
        <el-form-item label="结案率" prop="jal">
          <el-input v-model="form.jal" placeholder="请输入结案率" />
        </el-form-item>
        <el-form-item label="未结案数" prop="wjas">
          <el-input v-model="form.wjas" placeholder="请输入未结案数" />
        </el-form-item>
        <el-form-item label="父id" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入父id" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDbcenterRxMonthlyExtend, getDbcenterRxMonthlyExtend, delDbcenterRxMonthlyExtend, addDbcenterRxMonthlyExtend, updateDbcenterRxMonthlyExtend } from "@/api/shcy/dbcenterRxMonthlyExtend";

export default {
  name: "DbcenterRxMonthlyExtend",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 12345热线分析拓展表格数据
      dbcenterRxMonthlyExtendList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dept: null,
        las: null,
        czrks: null,
        bzllas: null,
        bzlczrklas: null,
        tzllas: null,
        tzlczrklas: null,
        jylclas: null,
        jylcczrklas: null,
        cjlas: null,
        cjczrklas: null,
        bzlas: null,
        jas: null,
        jal: null,
        wjas: null,
        pid: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询12345热线分析拓展列表 */
    getList() {
      this.loading = true;
      listDbcenterRxMonthlyExtend(this.queryParams).then(response => {
        this.dbcenterRxMonthlyExtendList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dept: null,
        las: null,
        czrks: null,
        bzllas: null,
        bzlczrklas: null,
        tzllas: null,
        tzlczrklas: null,
        jylclas: null,
        jylcczrklas: null,
        cjlas: null,
        cjczrklas: null,
        bzlas: null,
        jas: null,
        jal: null,
        wjas: null,
        createTime: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
        pid: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加12345热线分析拓展";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDbcenterRxMonthlyExtend(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改12345热线分析拓展";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDbcenterRxMonthlyExtend(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDbcenterRxMonthlyExtend(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除12345热线分析拓展编号为"' + ids + '"的数据项？').then(function() {
        return delDbcenterRxMonthlyExtend(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/dbcenterRxMonthlyExtend/export', {
        ...this.queryParams
      }, `dbcenterRxMonthlyExtend_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
