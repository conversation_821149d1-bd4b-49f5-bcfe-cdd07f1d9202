<template>
  <div class="dashboard-editor-container">
    <el-row :gutter="40" class="panel-group">
      <el-col  :xs="24" :sm="24" :lg="32"  class="card-panel-col">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="auto">
            <el-form-item label="选择查询方式"  >
              <el-select
                placeholder="请选择查询方式"  @change="handleChange"   v-model="searchType"
                clearable
                style="width: 220px"
              >
                <el-option value="按月查询" label="按月查询">
                </el-option>
                <el-option value="按时间段查询" label="按时间段查询">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年月" prop="year" v-if="showMonth">
              <el-date-picker
                style="width: 220px"
                v-model="queryParams.nianyue"
                type="month"
                placeholder="选择年月"
                clearable
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="时间" v-if="showRange">
              <el-date-picker
                style="width: 220px"
                v-model="startMonth"
                type="month"
                placeholder="选择年月"
                clearable
              ></el-date-picker>
              至
              <el-date-picker
                style="width: 220px"
                v-model="endMonth"
                type="month"
                placeholder="选择年月"
                clearable
              ></el-date-picker>

<!--              <el-date-picker-->
<!--                v-model="dateRange"-->
<!--                style="width: 220px"-->
<!--                value-format="yyyy-MM-dd"-->
<!--                type="daterange"-->
<!--                range-separator="-"-->
<!--                start-placeholder="开始日期"-->
<!--                end-placeholder="结束日期"-->
<!--              ></el-date-picker>-->
            </el-form-item>


            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button v-if="showMonth"
                         size="mini"
                type="success"
                @click="exportPDF1()"
              >导出</el-button>
              <el-button v-if="showRange"
                     size="mini"
                     type="success"
                     @click="exportPDF3()"
            >导出</el-button>
            </el-form-item>
          </el-form>
      </el-col>
    </el-row>
    <div v-if="showFlag"  >
      <div id="page2" >
        <div style="width: 100%;height: auto;margin-top: -10px">
          <p class="tag1">一、“12345”市民热线案件情况</p>
        </div>
        <el-row :gutter="40" class="panel-group">
        <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">
          <div class="card-panel" >
            <div style="width: 100%;height: auto;">
              <p class="tag3">本期主办工单数及累计数</p>
            </div>
            <div style="width: 100%;height: auto;text-align:center;">
              <span style="background:#F5F7F9;border-radius: 6px;line-height:40px;display:inline-block;padding:0px 5px;">
                本期实际受理工单数: <span style="color:#1784FE;font-size:24px">{{ total }}</span> 件
              </span>
            </div>
            <div style="color:#000000;text-align:center;font-size: 18px;">
              <div style="text-align:right;width:50%;float:left;padding-right:10%">{{ sj}}</div>
              <div style="text-align:left;width:50%;float:left;padding-left:10%">{{ sj1 }}</div>
            </div>
            <cfgd-chart1 ref="rxChart" height="180px" style="color:#666666;margin-bottom:10px;"></cfgd-chart1>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">
          <div class="card-panel" >
            <div style="width: 100%;height: auto;">
              <p class="tag3" >市民热线满意度测评情况</p>
            </div>
            <div style="width:50%;float:left;margin-top:-30px;">
              <myd-chart ref="myChart1" ></myd-chart>
              <myd-chart1 height="280px" ref="myChart3" style="margin-top:-30px;" ></myd-chart1>
            </div>
            <div style="width:50%;float:left;margin-top:-30px;">
              <myd-chart ref="myChart"></myd-chart>
              <myd-chart1 height="280px" ref="myChart2" style="margin-top:-30px;"></myd-chart1>
            </div>
            <div v-if="showMonth" style="text-align: center">
              <p class="tag3" > 较上月{{ this.ms9 }}{{this.mss9}}分，环比{{this.ms9}}{{this.mss10}}%</p>
            </div>
            <div v-if="showRange" style="float:left;width:100%">
              <mydqs-chart ref="myChart5" ></mydqs-chart>
            </div>
          </div>

        </el-col>
      </el-row>
      </div>

      <div id="page3">
        <div>
          <div style="width: 100%;height: auto;margin-top: -10px">
            <p class="tag1">二、重复工单情况</p>
          </div>
        </div>
        <el-row :gutter="40" class="panel-group">
          <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">
            <div class="card-panel" >
              <!--            <cfgd-chart  ref="cfgd"></cfgd-chart>-->
              <div class="cfgdDiv" >
                <div style="float:left;width:50%">
                  <img src="@/assets/images/cfgd_img_1.png" style="padding:20px;height:80%;"/>
                </div>
                <div class="cfgdDiv_1" >
                  本期重复工单数<br><span style="font-size:36px;color:#1F1F1F;font-weight: bold">{{ cfgdNum }}</span> 件
                </div>

              </div>
              <div class="cfgdDiv" v-if="showMonth">
                <div style="float:left;width:50%">
                  <img src="@/assets/images/cfgd_img_2.png" style="padding:20px;height:80%;"/>
                </div>
                <div class="cfgdDiv_1" >
                  <div style="float:left">较上月工单数</div>
                  <div class="cfgdDiv_2"  ><span>{{ ms2 }}</span></div>
                  <br><span style="font-size:36px;color:#1F1F1F;font-weight: bold">{{ mss2 }}</span> 件
                </div>

              </div>
              <div class="cfgdDiv">
                <div style="float:left;width:50%">
                  <img src="@/assets/images/cfgd_img_3.png" style="padding:20px;height:80%;"/>
                </div>
                <div class="cfgdDiv_1" >
                  归类工单问题<br><span style="font-size:36px;color:#1F1F1F;font-weight: bold">{{ cfgdNum2 }}</span> 件
                </div>

              </div>

            </div>

          </el-col>
          <!--        <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">-->
          <!--          <div class="card-panel">-->
          <!--            <cfgd-chart1 ref="sygd"></cfgd-chart1>-->
          <!--          </div>-->

          <!--        </el-col>-->
          <!--        <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">-->

          <!--          <div class="card-panel">-->
          <!--            <cfgd-chart  ref="glgd"></cfgd-chart>-->
          <!--          </div>-->
          <!--        </el-col>-->

        </el-row>
      </div>


      <div>
        <div style="width: 100%;height: auto;margin-top: -10px;">
          <p class="tag3">工单诉求类别分析</p>
        </div>
      </div>

      <el-row :gutter="40" class="panel-group">
        <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">

          <div class="card-panel">
            <el-table :data="cfgdList" >
              <el-table-column label="序号" align="center" width="100" >
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="主责部门" align="center"  prop="cbbm"/>
              <el-table-column label="诉求子类" align="center"  prop="sqzl"/>
              <el-table-column label="本期工单号" align="center"  prop="bqgdh"/>
              <el-table-column label="往期工单号" align="center"  prop="cfgdh"/>
              <el-table-column label="地址" align="center" prop="address" />
              <el-table-column label="诉求人" align="center" prop="sqr" />
              <el-table-column label="数量" align="center"  prop="sl" />
            </el-table>
          </div>
        </el-col>
      </el-row>
      <div id="page4" >
        <div>
          <div style="width: 100%;height: auto;margin-top: -10px">
            <p class="tag1">三、不满意工单情况</p>
          </div>
        </div>
        <el-row :gutter="40" class="panel-group">
          <el-col :xs="24" :sm="24" :lg="32"  class="card-panel-col">
            <div class="card-panel">
              <!--              <cfgd-chart  ref="bmygd"></cfgd-chart>-->
              <div class="cfgdDiv"  >
                <div style="float:left;width:50%;">
                  <img src="@/assets/images/bmy_img_1.png" style="padding:20px;height:80%;"/>
                </div>
                <div class="cfgdDiv_1" >
                  本期评测不满意工单数<br><span style="font-size:36px;color:#1F1F1F;font-weight: bold">{{ bmyNum }}</span> 件
                </div>

              </div>
              <div class="cfgdDiv" v-if="showMonth" >
                <div style="float:left;width:50%;">
                  <img src="@/assets/images/my1.png" style="padding:20px;height:80%;" v-if="ms3 =='持平'"/>
                  <img src="@/assets/images/my2.png" style="padding:20px;height:80%;" v-if="ms3 =='减少'"/>
                  <img src="@/assets/images/my3.png" style="padding:20px;height:80%;" v-if="ms3 =='增加'"/>
                </div>
                <div class="cfgdDiv_1" >
                  <div style="float:left">较上月工单数</div>
                  <div class="cfgdDiv_2"  ><span>{{ ms3 }}</span></div>
                  <br><span style="font-size:36px;color:#1F1F1F;font-weight: bold">{{ mss3 }}</span> 件
                </div>

              </div>
              <div class="cfgdDiv1" >
                <five-pie-chart1 ref="cfgdlb">></five-pie-chart1>
              </div>
            </div>

          </el-col>
          <!--          <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">-->
          <!--            <div class="card-panel">-->
          <!--              <cfgd-chart1  ref="jsybmygd"></cfgd-chart1>-->
          <!--            </div>-->

          <!--          </el-col>-->
          <!--          <el-col :xs="12" :sm="12" :lg="8" class="card-panel-col">-->

          <!--            <div class="card-panel">-->
          <!--              <cfgdlb-chart ref="cfgdlb"></cfgdlb-chart>-->
          <!--            </div>-->
          <!--          </el-col>-->

        </el-row>
      </div>

      <el-row :gutter="40" class="panel-group">
        <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">

          <div class="card-panel">
            <el-table :data="bmyList">
              <el-table-column label="序号" align="center"  width="100">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="诉求大类" align="center"   prop="parentappealclassification"/>
              <el-table-column label="诉求子类" align="center"  prop="appealclassification" />
              <el-table-column label="热线工单编号" align="center"  prop="hotlinesn" >
                <template slot-scope="scope">
                  <span v-for="(item,index) in scope.row.hotlinesn.split(',')"  @click="handleView(item)" style="cursor: pointer">
                    {{item}} <span v-if="index < scope.row.hotlinesn.split(',').length-1">,</span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="主责部门" align="center"  prop="subexecutedeptnameMh"/>
              <el-table-column label="所属居委会" align="center"  prop="residentialarea"/>
            </el-table>
          </div>
        </el-col>
      </el-row>
      <div id="page5" >
        <div>
          <div style="width: 100%;height: auto;margin-top: -10px">
            <p class="tag1">四、重点问题类投诉情况</p>
          </div>
        </div>
        <el-row :gutter="40" class="panel-group">
          <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">

            <div class="card-panel">
              <zdwtl-chart ref="zdwtl" ></zdwtl-chart>
            </div>
          </el-col>
        </el-row>
      </div>

      <div id="pdfPage1">
        <div>
          <div style="width: 100%;height: auto;margin-top: -10px;">
            <p class="tag1">五、工单诉求类别分析</p>
          </div>
        </div>
        <el-row :gutter="40" class="panel-group" >
          <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">

            <div class="card-panel">
              <el-col :xs="11" :sm="11" :lg="7" class="card-panel-col">
                  <cur-month-pie-chart   ref="curmonth"></cur-month-pie-chart>

              </el-col>
              <el-col :xs="11" :sm="11" :lg="7" class="card-panel-col">
                  <five-pie-chart  ref="five"></five-pie-chart>

              </el-col>
              <el-col :xs="14" :sm="14" :lg="10" class="card-panel-col" v-if="this.showMonth">
                  <div style="width: 100%;height: 0px;float:left;">
                    <p class="tag3" >近3个月受理热线对比</p>
                  </div>
                  <div style="width:80%;float:left;">
                    <rx-db-chart ref="rx"></rx-db-chart>
                  </div>
                  <div style="width:20%;float:left;font-size:12px">
                    <div style="width:100%;margin-top:85px;height:40px;"></div>
                    <div style="width:100%;margin-top:35px;">较上月{{ ms1 }}：<font style="color:#1784FE;font-size:16px;">{{ mss1}}</font> 件</div>
                    <div style="width:100%;margin-top:60px;">较上月{{ ms }}：<font style="color:#1784FE;font-size:16px;">{{ mss}}</font> 件</div>
                  </div>

              </el-col>
            </div>
          </el-col>


        </el-row>
        <el-row :gutter="40" class="panel-group">
          <el-col :xs="24" :sm="24" :lg="32" class="card-panel-col">

            <div class="card-panel">
              <div>
                <div style="width: 100%;height: 20px;margin-top: -10px">
                  <p class="tag3">本期工单诉求大类靠前5类别分析</p>
                </div>
              </div>
              <div class="tag3_1" v-for="item in xlList">
                <div class="tag3_1_1" >
                  <div class="tag3_1_2" >{{ item.parentappealclassification }}</div>
                  <zb-chart :num="item.num" :num1="num"></zb-chart>
                </div>
                <div  class="tag3_1_3"  >
                  <div class="tag3_1_4"   v-for="item1 in item.xlList">
                    <div class="tag3_1_5" >{{ item1.appealclassification }}
                      <el-tooltip :visible="visible" effect="light">
                        <template #content>
                          <div v-for="item2 in item1.xlAllList">
                            <span v-if="item2.residentialarea">{{item2.residentialarea}}</span>
                            <span v-if="item2.residentialarea == null">其他
                            </span>
                            <span style="margin-left:10px;">{{item2.num}}</span>件
                          </div>
                        </template>
                        <el-button @mouseenter="visible = true" @mouseleave="visible = false" style="float:right;border:0px;background:transparent;margin-top:15px;">
                          <span style="color:#3874FD">{{ item1.num }}</span>件
                        </el-button>
                      </el-tooltip>
                    </div>
                    <jmq-chart :key="componentKey" :height="item1.xlList.length == 1? '50px' : '120px'" :xlList="item1.xlList" ></jmq-chart>

                  </div>

                </div>

              </div>
            </div>
          </el-col>
        </el-row>
      </div>

    </div>
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="auto">

        <el-form-item label="工单编号" prop="hotlinesn">
          {{ form.hotlinesn }}
        </el-form-item>
        <el-form-item label="任务号" prop="hotlinesn">
          {{ form.taskid }}
        </el-form-item>
        <el-form-item label="诉求联系人" prop="reporter">
          {{ form.reporter }}
        </el-form-item>
        <el-form-item label="联系方式" prop="contactinfo">
          {{ form.contactinfo }}
        </el-form-item>
        <el-form-item label="发现时间" prop="contactinfo">
          {{ parseTime(form.discovertime) }}
        </el-form-item>
        <el-form-item label="业务类型" prop="servicetypename">
          {{ form.servicetypename}}
        </el-form-item>
        <el-form-item label="发生地址" prop="address">
          {{ form.address}}
        </el-form-item>
        <el-form-item label="所属居委会" prop="residentialarea">
          {{ form.residentialarea}}
        </el-form-item>
        <el-form-item label="问题描述" prop="description">
          {{ form.description}}
        </el-form-item>
        <el-form-item label="三级主责部门" prop="subexecutedeptnameMh">
          {{ form.subexecutedeptnameMh}}
        </el-form-item>
        <el-form-item label="反馈结论" prop="description12345">
          {{ form.description12345}}
        </el-form-item>
        <el-form-item label="工单来源" prop="infosourcename">
          {{ form.infosourcename}}
        </el-form-item>
        <el-form-item label="重点专项" prop="specialtopic">
          {{ form.specialtopic}}
        </el-form-item>
        <el-form-item label="测评月份" prop="evaluationmonth">
          {{ form.evaluationmonth}}
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>

</template>

<script>
import {getDlList,getXlList,getCfList,getBmyList,getZlList,getZdwtlList} from "@/api/shcy/dbcenterRxReport";
import CurMonthPieChart from "@/views/dashboard/CurMonthPieChart.vue";
import FivePieChart from "@/views/dashboard/FivePieChart.vue";
import RxDbChart from "@/views/dashboard/RxDbChart.vue";
import ZbChart from "@/views/dashboard/ZbChart.vue";
import JmqChart from "@/views/dashboard/JmqChart.vue";
import {formatDate} from "@/utils";
import CfgdChart from "@/views/dashboard/CfgdChart.vue";
import CfgdlbChart from "@/views/dashboard/CfgdlbChart.vue";
import ZdwtlChart from "@/views/dashboard/ZdwtlChart.vue";
import Sbjqk from "@/views/shcy/dbcenterRxMonthly/count/sbjqk.vue";
import RxCount from "@/views/shcy/dbcenterRxMonthly/count/index.vue";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import CfgdChart1 from "@/views/dashboard/CfgdChart1.vue";
import * as JsPDF from "jspdf";
import FivePieChart1 from "@/views/dashboard/FivePieChart1.vue";
import MydChart from "@/views/dashboard/mydChart.vue";
import MydChart1 from "@/views/dashboard/mydChart1.vue";
import {
  countDbcenterRx,
  getDbcenterRxByHotlinesn,
  jmqList,
  ksList,
  manyiDuPxList,
  manyiqkList,
  manyiqsList,
  myList
} from "@/api/shcy/dbcenterRx";
import {Loading} from "element-ui";
import MydqsChart from "@/views/dashboard/MydqsChart.vue";
export default {
  name: "reportDaily",
  components: {
    MydqsChart,
    MydChart1,
    MydChart,
    FivePieChart1,
    CfgdChart1,
    RxCount,
    Sbjqk,
    ZdwtlChart, CfgdlbChart, CfgdChart, JmqChart, ZbChart, RxDbChart, FivePieChart, CurMonthPieChart},
  props: {
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nianyue:null,
      },
      sj:null,
      searchType:null,
      sj1:null,
      loading: false,
      dateRange: [],
      myqkList:[],
      jdzs:null,
      jdzs1:null,
      my:{},
      myl1:null,
      myl2:null,
      ms9:null,
      mss9:null,
      visible:false,
      ms10:null,
      mss10:null,
      num:0,
      cfgdNum:0,
      cfgdNum1:0,
      cfgdNum2:0,
      lastNum:0,
      bmyNum:0,
      total:0,
      rxNum:0,
      componentKey: 0,
      rxNum2:0,
      rxNum1:0,
      rxNum3:0,
      lalastNum:0,
      ms:null,
      ms1:null,
      mss:0,
      myl:0,
      open: false,
      title: "",
      mss1:null,
      ms2:0,
      mss2:null,
      ms3:0,
      mss3:null,
      xlList:{},
      form: {},
      showMonth:false,
      showRange:false,
      showFlag:false,
      cfgdList:[],
      bmyList:[],
      printVisible: false,
      startMonth:null,
      endMonth:null,
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  created() {
  },
  computed: {
  },
  methods: {
    handleChange(value){
      this.dateRange=[];
      this.showFlag=false;
        if(value == "按月查询")
        {
            this.showMonth=true;
            this.showRange=false;
        }
        else if(value == "按时间段查询"){
          this.showMonth=false;
          this.showRange=true;
        }
        else {
          this.showMonth=false;
          this.showRange=false;
        }
    },
    async exportPDF(pdf) {
      const divsToExport = ['page3','page4', 'page5'];
      pdf.addPage();
      let position = 10;
      for (const divId of divsToExport) {
        // 使用html2canvas将div转换为canvas
        const canvas = await html2canvas(document.getElementById(divId));
        // 将canvas转换为图片
        const imgData = canvas.toDataURL('image/png');
        // 将图片添加到PDF中
        const imgProps= pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const imgWidth = imgProps.width;
        const imgHeight = imgProps.height;
        const renderHeight = imgHeight * (pdfWidth / imgWidth);
        pdf.addImage(imgData, 'PNG', 5, position, pdfWidth, renderHeight);
        position += renderHeight + 10; // 计算下一个div的位置
      }

     this.exportPDF2(pdf);
    },
    async exportPDF2(pdf) {
      const divsToExport = [ 'pdfPage1'];
      pdf.addPage();
      let position = 10; // 初始位置

      for (const divId of divsToExport) {
        // 使用html2canvas将div转换为canvas
        const canvas = await html2canvas(document.getElementById(divId));
        // 将canvas转换为图片
        const imgData = canvas.toDataURL('image/png');
        // 将图片添加到PDF中
        const imgProps= pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const imgWidth = imgProps.width;
        const imgHeight = imgProps.height;
        const renderHeight = imgHeight * (pdfWidth / imgWidth);
        pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, renderHeight);
        position += renderHeight + 10; // 计算下一个div的位置
      }

      pdf.save('报表.pdf'); // 保存PDF
      Loading.service().close();
      this.loading = false
    },
    async exportPDF1() {
      this.loading = true;
      Loading.service({
        lock: true,
        text: '导出中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const divsToExport = ['page2','page3','page4', 'page5'];
      const pdf = new jsPDF('p', 'mm', 'a4');
      let position = 10; // 初始位置

      for (const divId of divsToExport) {
        // 使用html2canvas将div转换为canvas
        const canvas = await html2canvas(document.getElementById(divId));
        // 将canvas转换为图片
        const imgData = canvas.toDataURL('image/png');
        // 将图片添加到PDF中
        const imgProps= pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const imgWidth = imgProps.width;
        const imgHeight = imgProps.height;
        const renderHeight = imgHeight * (pdfWidth / imgWidth);
        pdf.addImage(imgData, 'PNG', 5, position, pdfWidth, renderHeight);
        position += renderHeight + 10; // 计算下一个div的位置
      }
      this.exportPDF2(pdf);
    },
    async exportPDF3() {
      this.loading = true;
      Loading.service({
        lock: true,
        text: '导出中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const divsToExport = ['page2'];
      const pdf = new jsPDF('p', 'mm', 'a4');
      let position = 10; // 初始位置

      for (const divId of divsToExport) {
        // 使用html2canvas将div转换为canvas
        const canvas = await html2canvas(document.getElementById(divId));
        // 将canvas转换为图片
        const imgData = canvas.toDataURL('image/png');
        // 将图片添加到PDF中
        const imgProps= pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const imgWidth = imgProps.width;
        const imgHeight = imgProps.height;
        const renderHeight = imgHeight * (pdfWidth / imgWidth);
        pdf.addImage(imgData, 'PNG', 5, position, pdfWidth, renderHeight);
        position += renderHeight + 10; // 计算下一个div的位置
      }
      this.exportPDF(pdf);
    },
    getTotal(){

      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      countDbcenterRx( this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.total = response.data;
      })
    },
    //获取总数据
    getZlData() {
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      getZlList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.num=response.num;
        this.lastNum=response.lastNum;
        this.lalastNum=response.lalastNum;
        let sl=this.num.split("-");
        let sl1=this.lastNum.split("-");
        let sl2=this.lastNum.split("-");
        let sl3=this.lalastNum.split("-");
        if(Number(sl[1])>Number(sl1[1]))
        {
          this.ms="增加"
          this.mss=Number(sl[1])-Number(sl1[1]);
        }
        else if(Number(sl[1])<Number(sl1[1]))
        {
          this.ms="减少"
          this.mss=Number(sl1[1])-Number(sl[1]);
        }
        else {
          this.ms="持平"
          this.mss=Number(sl1[1])-Number(sl[1]);
        }
        if(Number(sl2[1])>Number(sl3[1]))
        {
          this.ms1="增加"
          this.mss1=Number(sl2[1])-Number(sl3[1]);
        }
        else  if(Number(sl2[1])<Number(sl3[1]))
        {
          this.ms1="减少"
          this.mss1=Number(sl3[1])-Number(sl2[1]);
        }
        else {
          this.ms1="持平"
          this.mss1=Number(sl3[1])-Number(sl2[1]);
        }
          setTimeout(() => {
            this.$refs.curmonth.initChart(this.num,"本期共受理热线")
            if(this.showMonth)
            {
              this.$refs.rx.initChart(this.num,this.lastNum,this.lalastNum)
            }

          },1000)
        this.getDlData();
      });
    },
    //获取大类数据
    getDlData() {
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      getDlList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        setTimeout(() => {
             this.$refs.five.initChart(response,this.num)
        },1000)
      });
    },
    getXlData() {
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      getXlList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.xlList=response;
        this.componentKey += 1;
      });
    },
    getCfData() {
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      getCfList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.cfgdList=response[0].cfgdList;
          this.cfgdNum=response[0].num;
          this.cfgdNum2=response[0].gbflNum;
          let num=response[0].num;
          let num1=response[0].lastNum;
          if(Number(num) > Number(num1))
          {
            this.ms2="增加"
            this.mss2=(Number(num)-Number(num1));
          }
          else  if(Number(num) < Number(num1))
          {
            this.ms2="减少"
            this.mss2=(Number(num1)-Number(num));
          }
          else {
            this.ms2="持平"
            this.mss2=0;
          }
          // setTimeout(() => {
          //   this.$refs.cfgd.initChart(response[0].num,null,"本月重复工单数")
          //   this.$refs.sygd.initChart(response[0].num,response[0].lastNum,"较上月工单数")
          //   this.$refs.glgd.initChart(response[0].gbflNum,null,"归类工单问题")
          // },1000)

      });
    },
    getBmyData() {
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }
      getBmyList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
       this.bmyList=response[0].xlList;
       this.bmyNum=response[0].num;
        let num=response[0].num;
        let num1=response[0].lastNum;
        if(Number(num) > Number(num1))
        {
          this.ms3="增加"
          this.mss3=(Number(num)-Number(num1));
        }
        else  if(Number(num) < Number(num1)){
          this.ms3="减少"
          this.mss3=(Number(num1)-Number(num));
        }
        else {
          this.ms3="持平"
          this.mss3=0;
        }
        setTimeout(() => {
          this.$nextTick(() => {
            // this.$refs.bmygd.initChart(response[0].num,null,"本月测评不满意工单")
            // this.$refs.jsybmygd.initChart(response[0].num,response[0].lastNum,"较上月工单数")
            this.$refs.cfgdlb.initChart(response[0].parentappealclassification)
          })
        },1000)
      });
    },
    getZdwtlData(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      getZdwtlList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        setTimeout(() => {
          this.$nextTick(() => {
            this.$refs.zdwtl.initChart(response)
          })
        },1000)
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if(this.queryParams.nianyue && this.searchType == '按月查询')
      {
        this.showFlag=true;
        const date=new Date(this.queryParams.nianyue);
        this.sj=date.getFullYear()+"."+(date.getMonth() + 1).toString().padStart(2, '0');
        let year1;
        if(date.getMonth() + 1 >= 11)
        {
           year1=date.getFullYear();
        }
        else {
           year1=date.getFullYear()-1;
        }
        this.sj1=year1+".11"+"-"+ this.sj;

      }
      else if(this.searchType == '按时间段查询' && this.startMonth && this.endMonth){
        this.showFlag=true;
        var firstDay=formatDate(this.startMonth,'yyyy-MM-dd hh:mm');
        var lastDay =formatDate(this.getLastDayOfMonth(this.endMonth,'yyyy-MM-dd hh:mm'));
        const date1=new Date(this.startMonth);
        const date2=new Date(this.endMonth);
        let year1;
        if(date1.getMonth() + 1 >= 11)
        {
          year1=date1.getFullYear();
        }
        else {
          year1=date1.getFullYear()-1;
        }
        this.sj1=year1+".11"+"-"+date2.getFullYear()+"."+(date2.getMonth() + 1).toString().padStart(2, '0');
        this.sj=date1.getFullYear()+"."+(date1.getMonth() + 1).toString().padStart(2, '0')+"-"+date2.getFullYear()+"."+(date2.getMonth() + 1).toString().padStart(2, '0');
        this.dateRange=[firstDay,lastDay];
      }
      else {
        this.showFlag=false;
      }
      this.queryParams.pageNum = 1;
       this.getTotal();
      this.getZlData();
      this.getXlData();
       this.getCfData();
      this.getBmyData();
       this.getZdwtlData();
      this.manyi();
      this.rxks();
      if(this.showRange)
      {
        this.manyiqs();
      }

    },
    rxks(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      ksList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.rxNum2=0;
        this.rxNum3=0;
        for(let i=0;i<response.data.length;i++)
        {
          this.rxNum2=this.rxNum2+response.data[i].zbNum;
          this.rxNum3=this.rxNum3+response.data[i].zbNum1;
        }
        this.rxgdJmq();
      })
    },
    rxgdJmq(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      jmqList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.rxNum=0;
        this.rxNum1=0;
        for(let i=0;i<response.data.length;i++)
        {
          this.rxNum=this.rxNum+response.data[i].zbNum;
          this.rxNum1=this.rxNum1+response.data[i].zbNum1;
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.$refs.rxChart.initChart(this.rxNum,this.rxNum1,this.rxNum2,this.rxNum3);
          })
        },1000)
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange=[];
      this.searchType=null;
      this.showMonth=false;
      this.showRange=false;
      this.showFlag=false;
      this.startMonth=null;
      this.endMonth=null;

    },
    percentage(total,value,value1,value2) {
      if (total === 0) {
        return 0;
      }
      let num=((value+value1*0.8+value2*0.6) / total) * 100;
      return (num).toFixed(2);
    },
    myLast(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.getFirstDayOfLastMonth(this.queryParams.nianyue),'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfLastMonth(this.queryParams.nianyue),'yyyy-MM-dd hh:mm');
      }
      myList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {

        this.jdzs1=response.data.my+response.data.bmy+response.data.yb+response.data.jbmy;
        this.myl2=this.percentage(this.jdzs1,response.data.my,response.data.jbmy,response.data.yb);

        if( Number(this.myl1) > Number(this.myl2))
        {
          this.ms9="增加";
          this.mss9=(Number(this.myl1)-Number(this.myl2)).toFixed(2)
          this.mss10=(Number(this.mss9)/this.myl1*100).toFixed(2)
        }
        else if(Number( this.myl1) < Number(this.myl2))
        {
          this.ms9="减少"
          this.mss9=(Number(this.myl2)-Number(this.myl1)).toFixed(2)
          this.mss10=((Number(this.myl1)-Number(this.myl2))/this.myl1*100).toFixed(2)
        }
        else {
          this.ms9="持平"
          this.mss9=0
          this.mss10=0
        }
      })
    },
    manyi(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      myList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.my=response.data;
        this.jdzs=this.my.my+this.my.bmy+this.my.yb+this.my.jbmy;
        this.myl1=this.percentage(this.jdzs,this.my.my,this.my.jbmy,this.my.yb);
        if(this.showMonth)
        {
          this.myLast();
        }
        this.manyiqk();
        this.manyiDuPx();

      })
    },
    manyiDuPx(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      manyiDuPxList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        let num=0;
        let num1=0;
        let num2=0;
        let num3=0;
        for(let i=0;i<response.data.length;i++)
        {
          num=num+Number(response.data[i].my);
          num1=num1+Number(response.data[i].jbmy);
          num2=num2+Number(response.data[i].yb);
          num3=num3+Number(response.data[i].bmy);
        }
        const total=Number(num)+Number(num1)+Number(num2)+Number(num3);
        setTimeout(() => {
          this.$nextTick(() => {
            this.$refs.myChart.initChart(num,num1,num2,num3,this.sj1);
            this.$refs.myChart2.initChart(this.percentage(total,num,num1,num2,num3));
          })
        },1000)
      })
    },
    getFirstDayOfLastMonth(time) {
      // JavaScript中的月份是从0开始的，因此需要减1
      const year = time.getFullYear();
      const month = time.getMonth(); // 注意：月份是从0开始的
      const firstDay = new Date(year, month-1, 1);

      // 将最后一天的时间设置为23:59:59.999
      firstDay.setHours(0, 0, 0, 0);
      return firstDay;
    },
    getLastDayOfLastMonth(time) {
      // JavaScript中的月份是从0开始的，因此需要减1
      const year = time.getFullYear();
      const month = time.getMonth(); // 注意：月份是从0开始的
      const lastDay = new Date(year, month, 0);

      // 将最后一天的时间设置为23:59:59.999
      lastDay.setHours(23, 59, 59, 999);
      return lastDay;
    },
    getLastDayOfMonth(time) {
      // JavaScript中的月份是从0开始的，因此需要减1
      const year = time.getFullYear();
      const month = time.getMonth(); // 注意：月份是从0开始的

      // 创建一个表示当前月份最后一天的Date对象
      const lastDay = new Date(year, month + 1, 0);

      // 将最后一天的时间设置为23:59:59.999
      lastDay.setHours(23, 59, 59, 999);
      return lastDay;
    },
    handleView(hotlinesn) {
      const hotlinesn1 = hotlinesn
      getDbcenterRxByHotlinesn(hotlinesn1).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "12345热线案件信息";
      });
    },
    manyiqk(){
      if(this.showMonth)
      {
        this.dateRange[0]=formatDate(this.queryParams.nianyue,'yyyy-MM-dd hh:mm');
        this.dateRange[1]=formatDate(this.getLastDayOfMonth(this.queryParams.nianyue,'yyyy-MM-dd hh:mm'));
      }

      manyiqkList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        let num=0;
        let num1=0;
        let num2=0;
        let num3=0;
        this.myqkList=response.data;
        for(let i=0;i<this.myqkList.length;i++)
        {
          num=num+Number(this.myqkList[i].my);
          num1=num1+Number(this.myqkList[i].jbmy);
          num2=num2+Number(this.myqkList[i].yb);
          num3=num3+Number(this.myqkList[i].bmy);
          this.myqkList[i].zNum= this.jdzs;
          if(i>0)
          {
            this.myqkList[i].zNumbl= 0;
          }
          else
          {
            this.myqkList[0].zNumbl= this.myl1;
          }

        }
        const total=Number(num)+Number(num1)+Number(num2)+Number(num3);
        setTimeout(() => {
          this.$nextTick(() => {
            this.$refs.myChart1.initChart(num,num1,num2,num3,this.sj);
            this.$refs.myChart3.initChart(this.percentage(total,num,num1,num2,num3));
          })
        },1000)
      })
    },
    manyiqs(){
      manyiqsList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        setTimeout(() => {
          this.$refs.myChart5.initChart(response.data)
        },1000)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}


.pdfPage1 {
  page-break-after: always; /* CSS分页标志，需要确保内容在此div后分页 */
}

.panel-group {
  margin-top: 0px;

  .card-panel-col {
    margin-bottom: 15px;
  }

  .card-panel {
    height:auto;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
.tag1{
  font-size: 26px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #3C3C3C;
  text-align:center;
}
#tag2{
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #3C3C3C;
}
.tag3{
  font-size: 20px;
  font-family: Source Han Sans CN;
  width:100%;
  text-align:center;
  color: #3C3C3C;
  line-height:30px;
}
.tag3_1{
  width:96%;
  margin-left:2%;
  height:310px;
  margin-top:20px;
  margin-bottom:20px;
  background: #FAFAFA;
  border: 2px solid #E1E1E0;
}
.tag3_1_1{
  width:30%;
  float:left;
  height:330px;
}
.tag3_1_2{
  font-family: Source Han Sans CN;
  text-align:center;
  font-weight: bold;
  font-size: 16px;
  color: #2E2E2E;
  line-height: 60px;
}
.tag3_1_3{
  width:70%;
  float:left;
  height:330px;
}
.tag3_1_4{
  width:28%;
  margin-right:5%;
  float:left;
  height:150px;
}
.tag3_1_5{
  font-family: Source Han Sans CN;
  text-align:left;
  font-weight: bold;
  font-size: 16px;
  color: #2E2E2E;
  line-height: 60px;
  height:50px;
}
.cfgdDiv{
  border:1px solid #E9E9E9;
  height:180px;
  background:#FAFAF8;
  width: 25%;
  margin-left:30px;
  margin-top:30px;
  margin-bottom:30px;
  float:left;
}
.cfgdDiv1{
  border:1px solid #E9E9E9;
  height:180px;
  background:#FAFAF8;
  width: 38%;
  margin-top:30px;
  margin-left:30px;
  margin-bottom:30px;
  float:left;
}
.cfgdDiv_1{
  float:left;
  line-height:60px;
  margin-top:20px;
  font-size:16px;
  width:50%;
}
.cfgdDiv_2{
  border:1px solid #15C827;
  background: #DAFCDD;
  color:#15C827;
  border-radius: 20px;
  float:left;
  padding:0px 10px;
  margin-top:18px;
  line-height:20px;
  margin-left:5px;
}
</style>

