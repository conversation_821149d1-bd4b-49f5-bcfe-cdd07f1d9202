<template>
  <div class="container">
    <div class="wrapper">
      <h3>二、事部件数据完成情况</h3>
      <p>（统计周期：{{parseTime(this.rx.sbjStartTime, '{y}.{m}.{d}') }}-{{parseTime(this.rx.sbjEndTime, '{y}.{m}.{d}') }} ）</p>
      <p>
        <el-table
          :data="dbcenterRxMonthlyExtendList" border
          style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important;"  class="ctable">
          <el-table-column :resizable="false" class-name="column-custom" prop="dept" label="表格"  width="70px;">
            <template slot="header" slot-scope="scope">
              <div class="header-div">
                <div class="header-col1">单位</div>
                <div class="header-col2">项目</div>
                <div class="header-line"></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="las" label="立案数" align="center"  width="60px;"></el-table-column>
          <el-table-column prop="czrks" label="常住/人口数/（万）" align="center" width="60px;"  :render-header="renderHeader"></el-table-column>
          <el-table-column label="标准类一般流程案件" align="center">
            <el-table-column prop="bzllas" label="立案数" align="center" width="60px;"></el-table-column>
            <el-table-column prop="bzlczrklas" label="每万名/常住人口/立案数"    align="center"  :render-header="renderHeader"></el-table-column>
          </el-table-column>
          <el-table-column label="拓展类案件" align="center">
            <el-table-column prop="tzllas" label="立案数" align="center" width="60px;"></el-table-column>
            <el-table-column prop="tzlczrklas" label="每万名/常住人口/立案数"   align="center"  :render-header="renderHeader"></el-table-column>
          </el-table-column>
          <el-table-column label="简易流程案件" align="center">
            <el-table-column prop="jylclas" label="立案数" align="center" width="60px;"></el-table-column>
            <el-table-column prop="jylcczrklas" label="每万名/常住人口/立案数"   align="center"  :render-header="renderHeader"></el-table-column>
          </el-table-column>
          <el-table-column label="村居案件" align="center">
            <el-table-column prop="cjlas" label="立案数" align="center" width="60px;"></el-table-column>
            <el-table-column prop="cjczrklas" label="每万名/常住人口/立案数"   align="center"  :render-header="renderHeader"></el-table-column>
          </el-table-column>
        </el-table>
      </p>
      <p>（一）立案情况</p>
      <p>本月共完成立案{{this.dbcenterRxMonthlyExtendList[0].las}}个，其中一般流程案件每万名常住人口立案{{this.dbcenterRxMonthlyExtendList[0].bzlczrklas}}个，
        为区平均值的{{ this.pjs }}%；拓展类案件每万名常住人口立案{{this.dbcenterRxMonthlyExtendList[0].tzlczrklas}}个，为区平均值的{{ this.pjs1 }}%；
        简易流程案件每万名常住人口立案{{this.dbcenterRxMonthlyExtendList[0].jylcczrklas}}个，为区平均值的{{ this.pjs2 }}%；
        村居工作站每万名常住人口立案{{this.dbcenterRxMonthlyExtendList[0].cjczrklas}}个，为区平均值的{{ this.pjs3 }}%。</p>
      <p>本月各居民区工作站立案总数为{{this.total}}个，详见下表。</p>
      <p>
        <el-table
          :data="laList"  show-summary class="ctable" border :summary-method="getSummaries2"
          style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important; page-break-after: always;"  >
          <el-table-column  label="序号" align="center"  width="50">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="居委" align="center"  ></el-table-column>
          <el-table-column prop="zNum" label="立案数" align="center" ></el-table-column>
        </el-table>
      </p>
      <p>（二）总体结案情况</p>
      <p>
        <el-table class="ctable" border
          :data="dbcenterRxMonthlyExtendList"
          style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important;"  >
          <el-table-column :resizable="false" class-name="column-custom" prop="dept" label="表格" width="100px;">
            <template slot="header" slot-scope="scope">
              <div class="header-div1">
                <div class="header-col1">单位</div>
                <div class="header-col2">项目</div>
                <div class="header-line1"></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="bzlas" label="标准案件数" align="center" ></el-table-column>
          <el-table-column prop="jas" label="结案数" align="center"  ></el-table-column>
          <el-table-column prop="jal" label="结案率" align="center" ></el-table-column>
          <el-table-column prop="wjas" label="未结案数" align="center"  ></el-table-column>
        </el-table>
      </p>
      <p>本月实际结案数{{ this.dbcenterRxMonthlyExtendList[0].jas }}个，结案率{{ this.dbcenterRxMonthlyExtendList[0].jal }}。</p>
      <p>截至{{parseTime(this.rq2, '{y}年{m}月{d}日') }}，有{{ this.dbcenterRxMonthlyExtendList[0].wjas }}个未结案数。</p>
    </div>
  </div>
</template>

<script>
import {getDbcenterRxMonthly} from "@/api/shcy/dbcenterRxMonthly";
import {getDbcenterRxMonthlyExtendByPid} from "@/api/shcy/dbcenterRxMonthlyExtend";
import {formatDate} from "@/utils";
import {laqkList,jasList} from "@/api/shcy/dbcenterRx";

export default {
  name: 'SbjqkJmq',
  props: {
    id: {
      type: Number,
    }
  },
  data() {
    return {
      queryParams: {},
      dbcenterRxMonthlyExtendList:[],
      rx: {},
      rq:null,
      rq1:null,
      rq2:null,
      rxList: [],
      laList:[],
      jList:[],
      num:0,
      num1:0,
      pjs:0,
      pjs1:0,
      pjs2:0,
      pjs3:0,
      total:0,
    }
  },
  created() {
    this.getList();
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    getSummaries2(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[1] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        // 只对amount这一列进行总计核算。
        if (column.property != 'deptName'
        ) {
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              }else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '---'
          }
        }
      });
      return sums;
    },
    getLastDayOfMonth(year, month) {
      // JavaScript中的月份是从0开始的，因此需要减1
      month--;
      // 创建日期对象
      var date = new Date(year, month, 1);
      // 将时间设置为当月的最后一刻
      date.setMonth(date.getMonth() + 1, 0);
      date.setHours(23, 59, 59, 999);
      return date;
    },
    renderHeader(h, { column }) {
      return h('span', {}, [
        h('span', {}, column.label.split('/')[0]),
        h('br'),
        h('span', {}, column.label.split('/')[1]),
        h('br'),
        h('span', {}, column.label.split('/')[2])
      ])
    },
    laqk(time,time1){
      this.dateRange=[time,time1];
      laqkList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.laList=response.data;
        for(let i=0;i<this.laList.length;i++)
        {
          this.total=this.laList[i].zNum+this.total;
        }

      })
    },
    getDbcenterRxMonthlyExtend(id){
      getDbcenterRxMonthlyExtendByPid(id).then(response => {
        this.dbcenterRxMonthlyExtendList = response.data;
        this.dbcenterRxMonthlyExtendList[0].bzlas=this.num;
        this.dbcenterRxMonthlyExtendList[0].jas=this.num-this.num1;
        this.dbcenterRxMonthlyExtendList[0].jal=this.formatNumber(this.dbcenterRxMonthlyExtendList[0].jas/this.num*100)+"%";
        this.dbcenterRxMonthlyExtendList[0].wjas=this.num1;
        if(this.dbcenterRxMonthlyExtendList[1].bzlczrklas == 0)
        {
          this.pjs = 0
        }
        else {
          this.pjs=this.formatNumber(this.dbcenterRxMonthlyExtendList[0].bzlczrklas/this.dbcenterRxMonthlyExtendList[1].bzlczrklas*100);
        }
        if(this.dbcenterRxMonthlyExtendList[1].tzlczrklas == 0)
        {
          this.pjs1 = 0
        }
        else {
          this.pjs1=this.formatNumber(this.dbcenterRxMonthlyExtendList[0].tzlczrklas/this.dbcenterRxMonthlyExtendList[1].tzlczrklas*100);
        }
        if(this.dbcenterRxMonthlyExtendList[1].jylcczrklas == 0)
        {
          this.pjs2 = 0
        }
        else {
          this.pjs2=this.formatNumber(this.dbcenterRxMonthlyExtendList[0].jylcczrklas/this.dbcenterRxMonthlyExtendList[1].jylcczrklas*100);
        }
        if(this.dbcenterRxMonthlyExtendList[1].cjczrklas == 0)
        {
          this.pjs3 = 0
        }
        else {
          this.pjs3=this.formatNumber(this.dbcenterRxMonthlyExtendList[0].cjczrklas/this.dbcenterRxMonthlyExtendList[1].cjczrklas*100);
        }

      });
    },
    jas(time,time1,id){
      this.dateRange=[time,time1];
      const _id=id;
      jasList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.jList=response.data;
        this.num=0;
        this.num1=0;
        for(let i=0;i<this.jList.length;i++)
        {
          this.num=this.jList[i].zbNum+this.num;
          this.num1=this.jList[i].cqNum+this.num1;
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.getDbcenterRxMonthlyExtend(_id)
          })
        }, 1000);

      })
    },
    getList() {
      getDbcenterRxMonthly(this.id).then(response => {
        this.rx = response.data;
        this.rq=this.rx.yeartj+"."+this.rx.monthtj;
        let year1;
        if(Number(this.rx.monthtj) >= 11)
        {
          year1=this.rx.yeartj;
        }
        else {
          year1=this.rx.yeartj-1;
        }
        this.rq1=year1+".11-"+this.rq;
        this.rq2=this.getLastDayOfMonth(this.rx.yeartj,this.rx.monthtj);
        this.laqk(this.rx.sbjStartTime,this.rx.sbjEndTime);
        this.jas(this.rx.sbjStartTime,this.rx.sbjEndTime,this.rx.id);
      });
    },
    formatNumber(value) {
      // 使用 toFixed 方法将数字保留两位小数
      return value.toFixed(0);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  background-color: #f3f3f4;
  display: flex;
  justify-content: center;
}

.wrapper {
  background-color: white;
  width: 800px;
  min-height: 100vh;
}

h1 {
  text-align: center;
  font-weight: bold;
}

h3 {
  font-weight: bold;
  padding: 0 20px;
}

p {
  padding: 0 20px;
}

::v-deep .el-tag {
  width: 60px;
}
.header-div {
  position: relative;
  height:100px;
  width:50px;
}
.header-div1 {
  position: relative;
  height:30px;
  width:80px;
}
.header-col1 {
  position: absolute;
  left: 0;
  bottom: 0;
  font-size: 14px;
  margin-left: 0px;
}
.header-col2 {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 14px;
  margin-right: 0px;
}
.header-line {
  padding-left: 0;
  width: 1px;
  height: 120px;
  transform: rotate(-28deg); /*这里需要自己调整，根据线的位置*/
  -webkit-transform-origin: top;
  transform-origin: top;
  background-color: #dfe6ec;
}
.header-line1 {
  padding-left: 0;
  width: 1px;
  height: 100px;
  transform: rotate(-67deg); /*这里需要自己调整，根据线的位置*/
  -webkit-transform-origin: top;
  transform-origin: top;
  background-color: #dfe6ec;
}
::v-deep .ctable th > .cell {
  font-size: 12px; /* 设置为所需的字体大小 */
}
</style>
