<template>
  <div class="container">
    <div class="wrapper">
      <p style="text-align:center;font-size:24px;font-weight:normal">石化街道城市运行管理中心<br>
        网格化业务工作{{ rx.month }}月简报<br><span style="font-size: 18px">(统计日期{{ rx.yeartj }}年{{ rx.monthtj }}月)<br>{{ rx.year }}年第{{ rx.month }}期总第{{ rx.qs }}期</span>
      </p>

      <h3>一、“12345”市民热线案件情况：</h3>
      <p>（一）本月主办工单数及累计数</p>
      <p>本月实际受理{{total1}}个，其中各居民区受理{{total}}个。</p>
      <p>
        <el-table
          :data="jmqRxList" show-summary border :summary-method="getSummaries2"
          style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important;" >
          <el-table-column  label="序号" align="center"  width="50" >
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="居民区" align="center" width="150"></el-table-column>
          <el-table-column label="主办工单（件）" align="center">
            <el-table-column prop="zbNum" :label="this.rq" align="center"></el-table-column>
            <el-table-column prop="zbNum1" :label="this.rq1"   align="center" width="120px"></el-table-column>
          </el-table-column>
          <el-table-column label="超期工单（件）" align="center">
              <el-table-column prop="cqNum" :label="this.rq" align="center"></el-table-column>
              <el-table-column prop="cqGdh" label="超期工单号" align="center" width="130px"></el-table-column>
              <el-table-column prop="cqNum1" :label="this.rq1"  align="center" width="120px"></el-table-column>
          </el-table-column>
        </el-table>
      </p>
      <p>（二）市民热线满意度测评情况</p>
      <p>统计期内，市热线办抽查到的工单如下：满意{{this.zmy}}个、基本满意{{ this.zjbmy }}个、一般{{ this.zyb }}个、不满意{{ this.zbmy}}个。</p>
      <p>
        <el-table
          :data="myqkList"  show-summary  :span-method="mergeFirstColumn" border :summary-method="getSummaries"
          style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important;"  >
          <el-table-column  label="各居民区满意度测评情况（当月）(按满意度从高到低排列)"  align="center">
            <el-table-column  :label="'统计日期：'+this.rq" align="right">
              <el-table-column label="总体情况" align="center" >
                <el-table-column prop="zNum" label="被测工单数量/（件）" align="center" width="100px" :render-header="renderHeader" >
                </el-table-column>
                <el-table-column prop="zNumbl"  label="满意率/（%）" align="center"  :render-header="renderHeader" >
                </el-table-column>
              </el-table-column>
              <el-table-column  label="具体情况" align="center" >
                <el-table-column prop="deptName" label="主办部门" align="center" width="150px"  ></el-table-column>
                <el-table-column prop="my" label="满意/（件）" align="center" width="60px" :render-header="renderHeader"></el-table-column>
                <el-table-column prop="jbmy" label="基本满意/（件）" align="center"  :render-header="renderHeader" ></el-table-column>
                <el-table-column prop="yb" label="一般/（件）" align="center" width="60px"  :render-header="renderHeader"></el-table-column>
                <el-table-column prop="bmy" label="不满意/（件）" align="center"  :render-header="renderHeader"></el-table-column>
                <el-table-column prop="hj" label="合计/（件）" align="center" width="60px"  :render-header="renderHeader"></el-table-column>
                <el-table-column prop="myl" label="满意率/（%）" align="center"  :render-header="renderHeader" ></el-table-column>
              </el-table-column>
            </el-table-column>

          </el-table-column>
        </el-table>
        <el-table :data="myPxList" style="width: 100%;border:1px solid #dfe6ec; border-collapse: collapse !important;"  border show-summary :summary-method="getSummaries1">
          <el-table-column  label="各居民区满意度测评情况（累计）(按满意度从高到低排列)"  align="center"  >
            <el-table-column  :label="'统计日期：'+this.rq1" align="right">
              <el-table-column prop="deptName" label="主办部门" align="center" width="150px"  ></el-table-column>
              <el-table-column prop="my" label="满意/（件）" align="center"  width="60px" :render-header="renderHeader"></el-table-column>
              <el-table-column prop="jbmy" label="基本满意/（件）" align="center"  :render-header="renderHeader" ></el-table-column>
              <el-table-column prop="yb" label="一般/（件）" align="center" width="60px"  :render-header="renderHeader"></el-table-column>
              <el-table-column prop="bmy" label="不满意/（件）" align="center"  :render-header="renderHeader"></el-table-column>
              <el-table-column prop="hj" label="合计/（件）" align="center" width="60px"   :render-header="renderHeader"></el-table-column>
              <el-table-column prop="myl" label="满意率/（%）" align="center"  :render-header="renderHeader" ></el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </p>
    </div>
  </div>
</template>

<script>

import {
  countDbcenterRxJmq,
  jmqList,
  ksList,
  myList,
  manyiqkListJmq,
  manyiDuPxListJmq,
  countDbcenterRx
} from '@/api/shcy/dbcenterRx'
import {getDbcenterRxMonthly} from "@/api/shcy/dbcenterRxMonthly";
import {getDbcenterRxMonthlyExtendByPid} from "@/api/shcy/dbcenterRxMonthlyExtend";
import {formatDate} from "@/utils";
export default {
  name: 'RxCountJmq',
  props: {
    id: {
      type: Number,
    }
  },
  data() {
    return {
      queryParams: {},
      rx: {},
      rq:null,
      rq1:null,
      rxList:[],
      total:0,
      total1:0,
      dateRange:[],
      jmqRxList:[],
      ksRxList:[],
      myqkList:[],
      myPxList:[],
      jdzs:null,
      zs:0,
      zmy:0,
      zjbmy:0,
      zbmy:0,
      zyb:0,
      my:{},
      myl:null,
      dbcenterRxMonthlyExtendList:[]
    }
  },
  created() {
    this.getList();
  },
  mounted() {
  },
  computed: {

  },
  methods: {
    getSummaries2(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[1] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        // 只对amount这一列进行总计核算。
        if (column.property != 'deptName' && column.property !='cqGdh'
        ) {
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              }else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '---'
          }
        }
      });
      return sums;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[2] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        // 只对amount这一列进行总计核算。
        if (column.property != 'zNum' && column.property != 'zNumbl' && column.property!='deptName'
          ) {
          if(column.property === 'myl')
          {
            sums[index]=this.myl;
          }
          else if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              }else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '---'
          }
        }
      });
      return sums;
    },
    getSummaries1(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        // 只对amount这一列进行总计核算。
        if (column.property != 'deptName' && column.property != 'myl'
        ) {
         if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              }else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '---'
          }
        }
      });
      sums[6]=((Number(sums[1])+Number(sums[2])*0.8+Number(sums[3])*0.6)/Number(sums[5])*100).toFixed(2);
      return sums;
    },
    mergeFirstColumn({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 && rowIndex < this.myqkList.length) {
        // 合并第二列的前两行和第一列的前两行
        if (rowIndex % this.myqkList.length === 0) {
          // 第一行
          return [this.myqkList.length, 1];
        } else {
          // 其他行
          return [0, 0];
        }
      }
      else if (columnIndex === 0 ) {
        // 合并第二列的前两行和第一列的前两行
        if (rowIndex % this.myqkList.length === 0) {
          // 第一行
          return [this.myqkList.length, 1];
        } else {
          // 其他行
          return [0, 0];
        }
      }
    },
    percentage(total,value,value1,value2) {
      if (total === 0) {
        return 0;
      }
      let num=((value+value1*0.8+value2*0.6) / total) * 100;
      return (num).toFixed(2);
    },
    firstDayOfMonth(year, month) {
      return new Date(year, month - 1, 1);
    },
    getLastDayOfMonth(year, month) {
      // JavaScript中的月份是从0开始的，因此需要减1
      month--;
      // 创建日期对象
      var date = new Date(year, month, 1);
      // 将时间设置为当月的最后一刻
      date.setMonth(date.getMonth() + 1, 0);
      date.setHours(23, 59, 59, 999);
      return date;
    },
    renderHeader(h, { column }) {
      return h('span', {}, [
        h('span', {}, column.label.split('/')[0]),
        h('br'),
        h('span', {}, column.label.split('/')[1])
      ])
    },
    getTotal(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      countDbcenterRxJmq( this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.total = response.data;
      })
      countDbcenterRx( this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.total1 = response.data;
      })
    },
    rxgdJmq(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      jmqList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.jmqRxList=response.data;
      })
    },
    rxks(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      ksList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.ksRxList=response.data;
      })
    },
    manyi(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      this.manyiqk(year,month);
      this.manyiDuPx(year,month);
    },
    manyiDuPx(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      manyiDuPxListJmq(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.myPxList=response.data;
      })
    },
    manyiqk(year,month){
      var firstDay= formatDate(this.firstDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      var lastDay = formatDate(this.getLastDayOfMonth(year, month), 'YYYY-MM-dd hh:mm:ss');
      this.dateRange=[firstDay,lastDay];
      manyiqkListJmq(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.myqkList=response.data;
        for(let k=0;k<this.myqkList.length;k++)
        {
          this.zs= this.zs+this.myqkList[k].my+this.myqkList[k].yb+this.myqkList[k].jbmy+this.myqkList[k].bmy;
          this.zmy=this.zmy+this.myqkList[k].my;
          this.zyb=this.zyb+this.myqkList[k].yb;
          this.zjbmy=this.zjbmy+this.myqkList[k].jbmy;
          this.zbmy=this.zbmy+this.myqkList[k].bmy;
        }

        this.myl=this.percentage(this.zs,this.zmy,this.zjbmy,this.zyb);
        for(let i=0;i<this.myqkList.length;i++)
        {
          this.myqkList[i].zNum= this.zs;
          if(i>0)
          {
            this.myqkList[i].zNumbl= 0;
          }
          else
          {
            this.myqkList[0].zNumbl= this.myl;
          }

        }
      })
    },
    getList() {
      getDbcenterRxMonthly(this.id).then(response => {
        this.rx = response.data;
        this.rq=this.rx.yeartj+"."+this.rx.monthtj;
        let year1;
        if(Number(this.rx.monthtj) >= 11)
        {
          year1=this.rx.yeartj;
        }
        else {
          year1=this.rx.yeartj-1;
        }
        this.rq1=year1+".11-"+this.rq;
        getDbcenterRxMonthlyExtendByPid(this.rx.id).then(response => {
          this.dbcenterRxMonthlyExtendList = response.data;
        });
        this.getTotal(this.rx.yeartj,this.rx.monthtj);
        this.rxgdJmq(this.rx.yeartj,this.rx.monthtj);
        this.rxks(this.rx.yeartj,this.rx.monthtj);
        this.manyi(this.rx.yeartj,this.rx.monthtj);
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  background-color: #f3f3f4;
  display: flex;
  justify-content: center;
}

.wrapper {
  background-color: white;
  width: 800px;
  min-height: 100vh;
}

h1 {
  text-align: center;
  font-weight: bold;
}

h3 {
  font-weight: bold;
  padding: 0 20px;
}

p {
  padding: 0 20px;
}

::v-deep .el-tag {
  width: 60px;
}
@media print {
  .el-table {
    page-break-after: always; /* 每个 el-table 后都进行分页 */
  }
}

::v-deep .el-table--medium .el-table__cell {
  padding: 2px 0;
}

::v-deep .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
  word-break: break-word;
  background-color: #f8f8f9;
  color: #515a6e;
  height: 30px;
  font-size: 13px;
}

::v-deep .table1 .el-table__cell {
  padding: 10px 0 !important;
}
</style>
