<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-input
          v-model="queryParams.year"
          placeholder="请输入年份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-input
          v-model="queryParams.month"
          placeholder="请输入月份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:dbcenterRxMonthly:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dbcenterRxMonthlyList" @selection-change="handleSelectionChange">
      <el-table-column label="汇报日期" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.year}}年{{ scope.row.month}}月</span>
        </template>
      </el-table-column>
      <el-table-column label="统计日期" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.yeartj}}年{{ scope.row.monthtj}}月</span>
        </template>
      </el-table-column>
      <el-table-column label="期数" align="center" prop="qs" />
      <el-table-column label="事部件开始时间" align="center" prop="sbjStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sbjStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事部件结束时间" align="center" prop="sbjEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sbjEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.status =='已完成'"
            @click="handleMonthCount(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthly:export']"
          >城运中心简报</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.status =='已完成'"
            @click="handleMonthCountGks(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthly:export']"
          >各科室简报</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="scope.row.status =='已完成'"
            @click="handleMonthCountJmq(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthly:export']"
          >各居民区简报</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthly:edit']"
          >完善信息</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:dbcenterRxMonthly:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改12345热线分析对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="汇报日期" prop="nianyue">
          <el-date-picker
            v-model="nianyue"
            type="month"
            placeholder="选择月"
            @change="changeNianyue">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="统计日期" prop="nianyue1">
          <el-date-picker
            v-model="nianyue1"
            type="month"
            placeholder="选择月"
            @change="changeNianyue1">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="期数" prop="qs">
          <el-input v-model="form.qs" placeholder="请输入期数" />
        </el-form-item>
        <el-form-item label="事部件时间范围" >
          <el-date-picker
            v-model="dateRangeSbj"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="事部件完成情况" >
        </el-form-item>
        <el-table v-loading="loading" :data="dbcenterRxMonthlyExtendList" >
          <el-table-column label="单位" align="center" prop="dept" />
          <el-table-column label="立案数" align="center" prop="las" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.las" type="number"  />
            </template>
          </el-table-column>
          <el-table-column label="常住人口数" align="center" prop="czrks" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.czrks"  type="number"/>
            </template>
          </el-table-column>
          <el-table-column label="标准立案数" align="center" prop="bzllas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.bzllas"  type="number"/>
            </template>
          </el-table-column>
          <el-table-column label="标准类常住人口立案数" align="center" prop="bzlczrklas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.bzlczrklas"  type="number"/>
            </template>
          </el-table-column>
          <el-table-column label="拓展类立案数" align="center" prop="tzllas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.tzllas" type="number" />
            </template>
          </el-table-column>
          <el-table-column label="拓展类常住人口立案数" align="center" prop="tzlczrklas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.tzlczrklas" type="number" />
            </template>
          </el-table-column>
          <el-table-column label="简易流程立案数" align="center" prop="jylclas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.jylclas" type="number" />
            </template>
          </el-table-column>
          <el-table-column label="简易流程常住人口立案数" align="center" prop="jylcczrklas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.jylcczrklas"  type="number"/>
            </template>
          </el-table-column>
          <el-table-column label="村居立案数" align="center" prop="cjlas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.cjlas" type="number" />
            </template>
          </el-table-column>
          <el-table-column label="村居常住人口立案数" align="center" prop="cjczrklas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.cjczrklas" type="number" />
            </template>
          </el-table-column>
        </el-table>
        <el-form-item label="事部件总体结案情况" >
        </el-form-item>

        <el-table v-loading="loading" :data="dbcenterRxMonthlyExtendList1" @selection-change="handleSelectionChange">
          <el-table-column label="单位" align="center" prop="dept" />
          <el-table-column label="标准立案数" align="center" prop="bzlas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.bzlas"  type="number" />
            </template>
          </el-table-column>
          <el-table-column label="结案数" align="center" prop="jas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.jas" type="number" />
            </template>
          </el-table-column>
          <el-table-column label="结案率" align="center" prop="jal" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.jal"  />
            </template>
          </el-table-column>
          <el-table-column label="未结案数" align="center" prop="wjas" >
            <template slot-scope="scope">
              <el-input v-model="scope.row.wjas" type="number" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormZc" v-if="form.status == null">暂 存</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="统计报表" :visible.sync="printVisible" width="1000px">
    <!-- startprint -->
    <div class="box">
      <div id="printMe" class="printMe" v-if="printVisible">
        <RxCount :id="id"></RxCount>
        <Sbjqk :id="id" ></Sbjqk>
      </div>
    </div>
    <!-- startprint -->
    <div style="text-align: right;">
      <el-button v-print="printObj" type="primary">直接打印</el-button>
      <el-button @click="printVisible = false">取消</el-button>
    </div>
  </el-dialog>

    <el-dialog title="统计报表" :visible.sync="printVisibleGks" width="1000px">
      <!-- startprint -->
      <div class="box">
        <div id="printMeGks" class="printMeGks" v-if="printVisibleGks">
          <RxCountGks :id="id"></RxCountGks>
          <SbjqkGks :id="id" ></SbjqkGks>
        </div>
      </div>
      <!-- startprint -->
      <div style="text-align: right;">
        <el-button v-print="printObjGks" type="primary">直接打印</el-button>
        <el-button @click="printVisibleGks = false">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="统计报表" :visible.sync="printVisibleJmq" width="1000px">
      <!-- startprint -->
      <div class="box">
        <div id="printMeJmq" class="printMeJmq" v-if="printVisibleJmq">
          <RxCountJmq :id="id"></RxCountJmq>
          <SbjqkJmq :id="id" ></SbjqkJmq>
        </div>
      </div>
      <!-- startprint -->
      <div style="text-align: right;">
        <el-button v-print="printObjJmq" type="primary">直接打印</el-button>
        <el-button @click="printVisibleJmq = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDbcenterRxMonthly, getDbcenterRxMonthly, delDbcenterRxMonthly, addDbcenterRxMonthly, updateDbcenterRxMonthly } from "@/api/shcy/dbcenterRxMonthly";
import RxCount from "@/views/shcy/dbcenterRxMonthly/count/index.vue";
import Sbjqk from "@/views/shcy/dbcenterRxMonthly/count/sbjqk.vue";
import { getDbcenterRxMonthlyExtendByPid } from "@/api/shcy/dbcenterRxMonthlyExtend";
import RxCountGks from "@/views/shcy/dbcenterRxMonthly/count/indexGks.vue";
import SbjqkGks from "@/views/shcy/dbcenterRxMonthly/count/sbjqkGks.vue";
import RxCountJmq from "@/views/shcy/dbcenterRxMonthly/count/indexJmq.vue";
import SbjqkJmq from "@/views/shcy/dbcenterRxMonthly/count/sbjqkJmq.vue";


export default {
  name: "DbcenterRxMonthly",
  components: {SbjqkJmq, RxCountJmq, SbjqkGks, RxCountGks, Sbjqk, RxCount},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 12345热线分析表格数据
      dbcenterRxMonthlyList: [],
      dbcenterRxMonthlyExtendList:[{
        dept:"石化街道",
        las:null,
        czrks:null,
        bzllas:null,
        bzlczrklas:null,
        tzllas:null,
        tzlczrklas:null,
        jylclas:null,
        jylcczrklas:null,
        cjlas:null,
        cjczrklas:null,
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      },{
        dept:"区平均值",
        las:null,
        czrks:null,
        bzllas:null,
        bzlczrklas:null,
        tzllas:null,
        tzlczrklas:null,
        jylclas:null,
        jylcczrklas:null,
        cjlas:null,
        cjczrklas:null,
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      }],
      dbcenterRxMonthlyExtendList1:[{
        dept:"区平均值",
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      }],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: null,
        month: null,
        yeartj: null,
        monthtj: null,
        qs: null,
        cqajs: null,
        sbjStartTime: null,
        sbjEndTime: null
      },
      // 表单参数
      form: {
      },
      nianyue: null,
      nianyue1: null,
      // 表单校验
      rules: {
      },
      printVisible: false,
      printVisibleGks:false,
      printVisibleJmq:false,
      printObj: {
        id: 'printMe',
        popTitle: '打印',
        openCallback: (e) => {
          this.printVisible = false
        },
        extraHead: '<meta http-equiv="Content-Language" content="zh-cn"/>'
      },
      printObjGks: {
        id: 'printMeGks',
        popTitle: '打印',
        openCallback: (e) => {
          this.printVisibleGks = false
        },
        extraHead: '<meta http-equiv="Content-Language" content="zh-cn"/>'
      },
      printObjJmq: {
        id: 'printMeJmq',
        popTitle: '打印',
        openCallback: (e) => {
          this.printVisibleJmq = false
        },
        extraHead: '<meta http-equiv="Content-Language" content="zh-cn"/>'
      },
      count: '',
      yearMonth: null,
      dateRange: [],
      dateRangeSbj:[],
      id:null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changeNianyue() {
      const date = new Date(this.nianyue);
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      this.nianyue = year + '-' + (month < 10 ? '0' + month : month);
      this.form.year = year;
      this.form.month = month;
    },
    changeNianyue1() {
      const date = new Date(this.nianyue1);
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      this.nianyue1 = year + '-' + (month < 10 ? '0' + month : month);
      this.form.yeartj = year;
      this.form.monthtj = month;
    },


    /** 查询12345热线分析列表 */
    getList() {
      this.loading = true;
      listDbcenterRxMonthly(this.queryParams).then(response => {
        this.dbcenterRxMonthlyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        year: null,
        month: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        yeartj: null,
        monthtj: null,
        qs: null,
        cqajs: null,
        sbjStartTime: null,
        sbjEndTime: null
      };
      this.nianyue=null;
      this.nianyue1=null;
      this.dateRangeSbj=[];
      this.dbcenterRxMonthlyExtendList=[{
        dept:"石化街道",
        las:null,
        czrks:null,
        bzllas:null,
        bzlczrklas:null,
        tzllas:null,
        tzlczrklas:null,
        jylclas:null,
        jylcczrklas:null,
        cjlas:null,
        cjczrklas:null,
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      },{
        dept:"区平均值",
        las:null,
        czrks:null,
        bzllas:null,
        bzlczrklas:null,
        tzllas:null,
        tzlczrklas:null,
        jylclas:null,
        jylcczrklas:null,
        cjlas:null,
        cjczrklas:null,
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      }],
        this.dbcenterRxMonthlyExtendList1=[{
        dept:"区平均值",
        bzlas:null,
        jas:null,
        jal:null,
        wjas:null,
        pid:null,
      }],
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeSbj=[];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加12345热线分析";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDbcenterRxMonthly(id).then(response => {
        this.form = response.data;
        getDbcenterRxMonthlyExtendByPid(this.form.id).then(response => {
          this.dbcenterRxMonthlyExtendList = response.data;
          this.dbcenterRxMonthlyExtendList1[0].pid=  response.data[1].pid;
          this.dbcenterRxMonthlyExtendList1[0].dept=  response.data[1].dept;
          this.dbcenterRxMonthlyExtendList1[0].bzlas=  response.data[1].bzlas;
          this.dbcenterRxMonthlyExtendList1[0].jas=  response.data[1].jas;
          this.dbcenterRxMonthlyExtendList1[0].jal=  response.data[1].jal;
          this.dbcenterRxMonthlyExtendList1[0].wjas=  response.data[1].wjas;
        });
        this.nianyue=this.form.year+"-"+this.form.month;
        this.nianyue1=this.form.yeartj+"-"+this.form.monthtj;
        if(this.form.sbjStartTime != null && this.form.sbjEndTime != null)
        {
          this.dateRangeSbj=[this.form.sbjStartTime,this.form.sbjEndTime];
        }
        else {
          this.dateRangeSbj=[];
        }
        this.open = true;
        this.title = "修改12345热线分析";
      });

    },
    submitFormZc() {
      if(this.dateRangeSbj)
      {
        this.form.sbjStartTime=this.dateRangeSbj[0];
        this.form.sbjEndTime=this.dateRangeSbj[1];
      }
      this.form.status="待细化";
      this.dbcenterRxMonthlyExtendList[1].bzlas= this.dbcenterRxMonthlyExtendList1[0].bzlas;
      this.dbcenterRxMonthlyExtendList[1].jas= this.dbcenterRxMonthlyExtendList1[0].jas;
      this.dbcenterRxMonthlyExtendList[1].jal= this.dbcenterRxMonthlyExtendList1[0].jal;
      this.dbcenterRxMonthlyExtendList[1].wjas= this.dbcenterRxMonthlyExtendList1[0].wjas;
      this.form.dbcenterRxMonthlyExtendList=this.dbcenterRxMonthlyExtendList;
      this.$refs["form"].validate(valid => {
        if (valid) {
          addDbcenterRxMonthly(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      if(this.dateRangeSbj)
      {
        this.form.sbjStartTime=this.dateRangeSbj[0];
        this.form.sbjEndTime=this.dateRangeSbj[1];
      }
      this.form.status="已完成";
      this.dbcenterRxMonthlyExtendList[1].bzlas= this.dbcenterRxMonthlyExtendList1[0].bzlas;
      this.dbcenterRxMonthlyExtendList[1].jas= this.dbcenterRxMonthlyExtendList1[0].jas;
      this.dbcenterRxMonthlyExtendList[1].jal= this.dbcenterRxMonthlyExtendList1[0].jal;
      this.dbcenterRxMonthlyExtendList[1].wjas= this.dbcenterRxMonthlyExtendList1[0].wjas;
      this.form.dbcenterRxMonthlyExtendList=this.dbcenterRxMonthlyExtendList;
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDbcenterRxMonthly(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDbcenterRxMonthly(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除12345热线分析编号为"' + ids + '"的数据项？').then(function() {
        return delDbcenterRxMonthly(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/dbcenterRxMonthly/export', {
        ...this.queryParams
      }, `dbcenterRxMonthly_${new Date().getTime()}.xlsx`)
    },
    handleMonthCount(row) {
      this.id = row.id
      this.printVisible = true;
    },
    handleMonthCountGks(row){
      this.id = row.id
      this.printVisibleGks = true;
    },
    handleMonthCountJmq(row){
      this.id = row.id
      this.printVisibleJmq = true;
    }
  }
};
</script>
