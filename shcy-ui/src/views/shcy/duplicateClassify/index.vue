<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:duplicateClassify:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:duplicateClassify:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="duplicateClassifyList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
      <el-table-column label="序号" align="center" prop="serialNumber" width="80" />
      <el-table-column label="名称" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" width="80">
        <template slot-scope="scope">
          <span v-if="!scope.row.parentId && scope.row.children && scope.row.children.length > 0">
            {{ scope.row.children.length }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:duplicateClassify:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:duplicateClassify:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 合计行 -->
    <div style="margin-top: 10px; padding: 10px; background-color: #f5f7fa; border: 1px solid #e4e7ed;">
      <el-row>
        <el-col :span="12">
          <strong>合计</strong>
        </el-col>
        <el-col :span="6">
          <strong>总数量：{{ totalSubCount }}</strong>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改重复归类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="上级类目">
          <treeselect
            v-model="form.parentId"
            :options="duplicateClassifyOptions"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择上级类目"
          />
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input type="number" v-model="form.orderNum" placeholder="请输入显示顺序" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDuplicateClassify, getDuplicateClassify, delDuplicateClassify, addDuplicateClassify, updateDuplicateClassify } from "@/api/shcy/duplicateClassify";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "DuplicateClassify",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 重复归类表格数据
      duplicateClassifyList: [],
      // 重复归类树选项
      duplicateClassifyOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: null,
        orderNum: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" }
        ],
      },
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 总小类数量
      totalSubCount: 0,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询重复归类列表 */
    getList() {
      this.loading = true;
      listDuplicateClassify(this.queryParams).then(response => {
        // this.duplicateClassifyList = response.rows;
        this.duplicateClassifyList = this.handleTree(response.data, "id");
        // 添加序号
        this.addSerialNumbers(this.duplicateClassifyList);
        // 计算总小类数量
        this.calculateTotalSubCount();

        this.loading = false;
      });
    },

    /** 为树形数据添加序号 */
    addSerialNumbers(treeData) {
      if (!treeData || !Array.isArray(treeData)) {
        return;
      }

      // 为同级节点添加序号
      treeData.forEach((item, index) => {
        item.serialNumber = index + 1;

        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          this.addSerialNumbers(item.children);
        }
      });
    },

    /** 计算总小类数量 */
    calculateTotalSubCount() {
      this.totalSubCount = 0;
      if (this.duplicateClassifyList && Array.isArray(this.duplicateClassifyList)) {
        this.duplicateClassifyList.forEach(item => {
          if (item.children && item.children.length > 0) {
            this.totalSubCount += item.children.length;
          }
        });
      }
    },

    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listDuplicateClassify().then(response => {
        this.duplicateClassifyOptions = [];
        const duplicateClassify = { id: 0, name: '主类目', children: [] };
        duplicateClassify.children = this.handleTree(response.data, "id");
        this.duplicateClassifyOptions.push(duplicateClassify);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        orderNum: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      this.open = true;
      this.title = "添加重复归类";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      const id = row.id || this.ids
      getDuplicateClassify(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改重复归类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDuplicateClassify(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDuplicateClassify(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除重复归类编号为"' + ids + '"的数据项？').then(function() {
        return delDuplicateClassify(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/duplicateClassify/export', {
        ...this.queryParams
      }, `duplicateClassify_${new Date().getTime()}.xlsx`)
    },

    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
  }
};
</script>
