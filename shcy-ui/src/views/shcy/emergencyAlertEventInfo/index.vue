<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="事件编号" prop="eventNo">
        <el-input
          v-model="queryParams.eventNo"
          placeholder="请输入事件编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="告警地点" prop="alarmLocation">
        <el-input
          v-model="queryParams.alarmLocation"
          placeholder="请输入告警地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处置人员" prop="disposalPerson">
        <el-input
          v-model="queryParams.disposalPerson"
          placeholder="请输入处置人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['shcy:emergencyAlertEventInfo:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['shcy:emergencyAlertEventInfo:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:emergencyAlertEventInfo:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['shcy:emergencyAlertEventInfo:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="emergencyAlertEventInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="事件编号" align="center" prop="eventNo" />
      <el-table-column label="告警地点" align="center" prop="alarmLocation" />
      <el-table-column label="处置人员" align="center" prop="disposalPerson" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处置截止时间" align="center" prop="disposalDeadline" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.disposalDeadline) }}</span>
        </template>
      </el-table-column><el-table-column label="事件完成时间" align="center" prop="caseFinishTime" width="180">
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.caseFinishTime) }}</span>
      </template>
    </el-table-column>
      <el-table-column label="处置状态" align="center" prop="circulationState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.case_state" :value="scope.row.circulationState"/>
        </template>
      </el-table-column>
      <el-table-column label="超时状态" align="center" prop="dealInTimeState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.case_in_time_state" :value="scope.row.dealInTimeState" />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.circulationState === '0'"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row, scope.index)"
          >处置详细</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:emergencyAlertEventInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改突发告警事件派遣信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="事件编号" prop="eventNo">
          <el-input v-model="form.eventNo" placeholder="请输入事件编号" />
        </el-form-item>
        <el-form-item label="告警地点" prop="alarmLocation">
          <el-input v-model="form.alarmLocation" placeholder="请输入告警地点" />
        </el-form-item>
        <el-form-item label="处置人员" prop="disposalPerson">
          <el-input v-model="form.disposalPerson" placeholder="请输入处置人员" />
        </el-form-item>
        <el-form-item label="处置截止时间" prop="disposalDeadline">
          <el-date-picker clearable
            v-model="form.disposalDeadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择处置截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="防汛物资情况照片" prop="floodMaterialPhoto">
          <el-input v-model="form.floodMaterialPhoto" placeholder="请输入防汛物资情况照片" />
        </el-form-item>
        <el-form-item label="防汛人员情况照片" prop="floodPersonPhoto">
          <el-input v-model="form.floodPersonPhoto" placeholder="请输入防汛人员情况照片" />
        </el-form-item>
        <el-form-item label="抢险作业情况照片" prop="rescueOperationPhoto">
          <el-input v-model="form.rescueOperationPhoto" placeholder="请输入抢险作业情况照片" />
        </el-form-item>
        <el-form-item label="临时管制措施" prop="temporaryControlMeasure">
          <el-input v-model="form.temporaryControlMeasure" placeholder="请输入临时管制措施" />
        </el-form-item>
        <el-form-item label="其他隐患" prop="otherHazard">
          <el-input v-model="form.otherHazard" placeholder="请输入其他隐患" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 处置详细 -->
    <el-dialog title="处置详细" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-form ref="form" :model="emergency" label-width="auto" size="mini">
        <el-form-item label="防汛物资情况照片" >
          <el-image style="width: 50px;margin-right: 10px" v-for="url in urls2" :key="url" :src="url" lazy :preview-src-list="urls2" fit="contain"></el-image>
        </el-form-item>
        <el-form-item label="防汛人员情况照片" >
          <el-image style="width: 50px;margin-right: 10px" v-for="url in urls1" :key="url" :src="url" lazy :preview-src-list="urls1" fit="contain"></el-image>
        </el-form-item>
        <el-form-item label="抢险作业情况照片" >
          <el-image style="width: 50px;margin-right: 10px" v-for="url in urls" :key="url" :src="url" lazy :preview-src-list="urls" fit="contain"></el-image>
        </el-form-item>
        <el-form-item label="采取临时管制措施" >
          <el-input v-model="emergency.temporaryControlMeasure" readonly/>
        </el-form-item>
        <el-form-item label="其他隐患" >
          <el-input v-model="emergency.otherHazard" readonly/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEmergencyAlertEventInfo, getEmergencyAlertEventInfo, delEmergencyAlertEventInfo, addEmergencyAlertEventInfo, updateEmergencyAlertEventInfo } from "@/api/shcy/emergencyAlertEventInfo";

export default {
  name: "EmergencyAlertEventInfo",
  dicts: ['case_state',  'case_in_time_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 突发告警事件派遣信息表格数据
      emergencyAlertEventInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: 'create_time',
        isAsc: 'descending',
        eventNo: undefined,
        alarmLocation: undefined,
        disposalPerson: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      emergency:{},
      detailOpen: false,
      urls: [],
      urls1: [],
      urls2: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询突发告警事件派遣信息列表 */
    getList() {
      this.loading = true;
      listEmergencyAlertEventInfo(this.queryParams).then(response => {
        this.emergencyAlertEventInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        eventNo: null,
        alarmLocation: null,
        disposalPerson: null,
        createTime: null,
        disposalDeadline: null,
        floodMaterialPhoto: null,
        floodPersonPhoto: null,
        rescueOperationPhoto: null,
        temporaryControlMeasure: null,
        otherHazard: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加突发告警事件派遣信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getEmergencyAlertEventInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改突发告警事件派遣信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateEmergencyAlertEventInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEmergencyAlertEventInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除突发告警事件派遣信息编号为"' + ids + '"的数据项？').then(function() {
        return delEmergencyAlertEventInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/emergencyAlertEventInfo/export', {
        ...this.queryParams
      }, `emergencyAlertEventInfo_${new Date().getTime()}.xlsx`)
    },
    /** 详细按钮操作 */
    handleView(row) {
      getEmergencyAlertEventInfo(row.id).then(response => {
        this.emergency = response.data;
        if (this.emergency.photoUrls) {
          // 将this.form.photoUrls转换为数组，并且加上前缀process.env.VUE_APP_BASE_API
          this.urls = this.emergency.photoUrls.map(item => process.env.VUE_APP_BASE_API + item);
        }
        if (this.emergency.floodPersonPhotolUrls) {
          // 将this.form.floodPersonPhotolUrls转换为数组，并且加上前缀process.env.VUE_APP_BASE_API
          this.urls1 = this.emergency.floodPersonPhotolUrls.map(item => process.env.VUE_APP_BASE_API + item);
        }
        if (this.emergency.floodMaterialPhotoUrls) {
          // 将this.form.floodMaterialPhotoUrls转换为数组，并且加上前缀process.env.VUE_APP_BASE_API
          this.urls2 = this.emergency.floodMaterialPhotoUrls.map(item => process.env.VUE_APP_BASE_API + item);
        }
        this.detailOpen = true;
      });
    }
  }
};
</script>
