<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物业名称" prop="propertyName">
        <el-input
          v-model="queryParams.propertyName"
          placeholder="请输入物业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:floodReserveInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:floodReserveInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:floodReserveInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:floodReserveInfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="floodReserveInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="物业名称" align="center" prop="propertyName">
        <template slot-scope="scope">
          <router-link :to="'/shcy/floodReserveInfo/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.propertyName }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="储备点地址" align="center" prop="reservePointAddress" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系方式" align="center" prop="contactNumber" />
      <el-table-column label="辐射区域" align="center" prop="radiationArea" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:floodReserveInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:floodReserveInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改防汛物资储备信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="物业名称" prop="propertyName">
          <el-input v-model="form.propertyName" placeholder="请输入物业名称" />
        </el-form-item>
        <el-form-item label="储备点地址" prop="reservePointAddress">
          <el-input v-model="form.reservePointAddress" placeholder="请输入储备点地址" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactNumber">
          <el-input v-model="form.contactNumber" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="辐射区域" prop="radiationArea">
          <el-input v-model="form.radiationArea" placeholder="请输入辐射区域" />
        </el-form-item>
        <el-form-item label="编织袋数量" prop="wovenBag">
          <el-input v-model="form.wovenBag" placeholder="请输入编织袋数量" />
        </el-form-item>
        <el-form-item label="草包数量" prop="strawBag">
          <el-input v-model="form.strawBag" placeholder="请输入草包数量" />
        </el-form-item>
        <el-form-item label="阻水板数量" prop="waterStopBoard">
          <el-input v-model="form.waterStopBoard" placeholder="请输入阻水板数量" />
        </el-form-item>
        <el-form-item label="防汛阻水袋数量" prop="floodStopBag">
          <el-input v-model="form.floodStopBag" placeholder="请输入防汛阻水袋数量" />
        </el-form-item>
        <el-form-item label="黄沙数量" prop="yellowSand">
          <el-input v-model="form.yellowSand" placeholder="请输入黄沙数量" />
        </el-form-item>
        <el-form-item label="水泥数量" prop="cement">
          <el-input v-model="form.cement" placeholder="请输入水泥数量" />
        </el-form-item>
        <el-form-item label="便携式工作灯数量" prop="portableWorkLight">
          <el-input v-model="form.portableWorkLight" placeholder="请输入便携式工作灯数量" />
        </el-form-item>
        <el-form-item label="水泵数量" prop="waterPump">
          <el-input v-model="form.waterPump" placeholder="请输入水泵数量" />
        </el-form-item>
        <el-form-item label="雨衣数量" prop="raincoat">
          <el-input v-model="form.raincoat" placeholder="请输入雨衣数量" />
        </el-form-item>
        <el-form-item label="雨鞋数量" prop="rainboots">
          <el-input v-model="form.rainboots" placeholder="请输入雨鞋数量" />
        </el-form-item>
        <el-form-item label="坐标" prop="coordinate">
          <el-input v-model="form.coordinate" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFloodReserveInfo, getFloodReserveInfo, delFloodReserveInfo, addFloodReserveInfo, updateFloodReserveInfo } from "@/api/shcy/floodReserveInfo";

export default {
  name: "FloodReserveInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 防汛物资储备信息表格数据
      floodReserveInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        propertyName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询防汛物资储备信息列表 */
    getList() {
      this.loading = true;
      listFloodReserveInfo(this.queryParams).then(response => {
        this.floodReserveInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        propertyName: null,
        reservePointAddress: null,
        contactPerson: null,
        contactNumber: null,
        radiationArea: null,
        wovenBag: null,
        strawBag: null,
        waterStopBoard: null,
        floodStopBag: null,
        yellowSand: null,
        cement: null,
        portableWorkLight: null,
        waterPump: null,
        raincoat: null,
        rainboots: null,
        type: null,
        coordinate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加防汛物资储备信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFloodReserveInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改防汛物资储备信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFloodReserveInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFloodReserveInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除防汛物资储备信息编号为"' + ids + '"的数据项？').then(function() {
        return delFloodReserveInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/floodReserveInfo/export', {
        ...this.queryParams
      }, `floodReserveInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
