<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="按网格统计" name="first">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
          label-width="auto">
          <el-form-item label="所属网格">
            <el-select v-model="queryParams.streetarea" placeholder="请选择所属网格" clearable filterable>
              <el-option v-for="item in girdOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间">
            <el-date-picker v-model="dateRange" style="width: 220px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tableData" border :span-method="objectSpanMethod">
          <el-table-column prop="grid" label="所属网格" align="center"></el-table-column>
          <el-table-column prop="gridQuantity" label="工单数量" align="center"></el-table-column>
          <el-table-column prop="committee" label="所属居委会" align="center"></el-table-column>
          <el-table-column prop="committeeQuantity" label="工单数量" align="center"></el-table-column>
          <el-table-column prop="category" label="诉求大类" align="center"></el-table-column>
          <el-table-column prop="categoryQuantity" label="诉求大类数量" align="center"></el-table-column>
          <el-table-column prop="subcategory" label="诉求小类" align="center"></el-table-column>
          <el-table-column prop="subcategoryQuantity" label="诉求小类数量" align="center"></el-table-column>
          <el-table-column prop="community" label="所属小区" align="center"></el-table-column>
          <el-table-column prop="communityQuantity" label="工单数量" align="center"></el-table-column>
        </el-table>

      </el-tab-pane>

      <el-tab-pane label="按小区统计" name="second">
        <el-form :model="queryParamsCommunity" ref="queryFormCommunity" size="small" :inline="true" v-show="showSearchCommunity"
          label-width="auto">
          <el-form-item label="所属小区">
            <el-select v-model="queryParamsCommunity.community" placeholder="请选择所属小区" clearable filterable>
              <el-option v-for="item in communityOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间">
            <el-date-picker v-model="dateRangeCommunity" style="width: 220px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryCommunity">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQueryCommunity">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loadingCommunity" :data="tableDataCommunity" border :span-method="communitySpanMethod">
          <el-table-column prop="community" label="所属小区" align="center"></el-table-column>
          <el-table-column prop="communityQuantity" label="工单数量" align="center"></el-table-column>
          <el-table-column prop="category" label="诉求大类" align="center"></el-table-column>
          <el-table-column prop="categoryQuantity" label="诉求大类数量" align="center"></el-table-column>
          <el-table-column prop="subcategory" label="诉求小类" align="center"></el-table-column>
          <el-table-column prop="subcategoryQuantity" label="诉求小类数量" align="center"></el-table-column>
        </el-table>

      </el-tab-pane>

      <el-tab-pane label="按物业统计" name="third">
        <el-form :model="queryParamsProperty" ref="queryFormProperty" size="small" :inline="true" v-show="showSearchProperty"
          label-width="auto">
          <el-form-item label="所属物业">
            <el-select v-model="queryParamsProperty.property" placeholder="请选择所属物业" clearable filterable>
              <el-option v-for="item in propertyOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间">
            <el-date-picker v-model="dateRangeProperty" style="width: 220px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryProperty">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQueryProperty">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loadingProperty" :data="tableDataProperty" border :span-method="propertySpanMethod">
          <el-table-column prop="property" label="所属物业" align="center"></el-table-column>
          <el-table-column prop="propertyQuantity" label="工单数量" align="center"></el-table-column>
          <el-table-column prop="community" label="所属小区" align="center"></el-table-column>
          <el-table-column prop="communityQuantity" label="工单数量" align="center"></el-table-column>
          <el-table-column prop="category" label="诉求大类" align="center"></el-table-column>
          <el-table-column prop="categoryQuantity" label="诉求大类数量" align="center"></el-table-column>
          <el-table-column prop="subcategory" label="诉求小类" align="center"></el-table-column>
          <el-table-column prop="subcategoryQuantity" label="诉求小类数量" align="center"></el-table-column>
        </el-table>

      </el-tab-pane>

      <el-tab-pane label="按满意度统计" name="fourth">
        <el-form :model="queryParamsSatisfaction" ref="queryFormSatisfaction" size="small" :inline="true" v-show="showSearchSatisfaction"
          label-width="auto">
          <el-form-item label="所属网格">
            <el-select v-model="queryParamsSatisfaction.streetarea" placeholder="请选择所属网格" clearable filterable>
              <el-option v-for="item in girdOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间">
            <el-date-picker v-model="dateRangeSatisfaction" style="width: 220px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuerySatisfaction">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuerySatisfaction">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loadingSatisfaction" :data="tableDataSatisfaction" border :span-method="satisfactionSpanMethod">
          <el-table-column prop="grid" label="所属网格" align="center" width="250px"></el-table-column>
          <el-table-column prop="committee" label="所属居委会" align="center" width="250px"></el-table-column>
          <el-table-column prop="satisfied" label="满意数量" align="center" width="250px"></el-table-column>
          <el-table-column prop="basicallySatisfied" label="基本满意数量" align="center" width="250px"></el-table-column>
          <el-table-column prop="neutral" label="一般数量" align="center" width="250px"></el-table-column>
          <el-table-column prop="dissatisfied" label="不满意数量" align="center" width="250px"></el-table-column>
          <el-table-column prop="satisfactionRate" label="满意度" align="center"></el-table-column>
        </el-table>

        <!-- 合计行 -->
        <div class="summary-row" v-if="satisfactionSummary.total > 0">
          <table class="el-table" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <td width="250px">合计</td>
              <td width="250px">&nbsp;</td>
              <td width="250px">{{satisfactionSummary.satisfied}}</td>
              <td width="250px">{{satisfactionSummary.basicallySatisfied}}</td>
              <td width="250px">{{satisfactionSummary.neutral}}</td>
              <td width="250px">{{satisfactionSummary.dissatisfied}}</td>
              <td>{{satisfactionSummary.satisfactionRate}}</td>
            </tr>
          </table>
        </div>

      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>

import { listProperty } from '@/api/shcy/dbcenterRx'
import { countGrid, countCommunity, countProperty, countSatisfaction } from '@/api/shcy/dbcenterRxCount'

export default {
  name: 'DbcenterRxCount',
  dicts: ['dbcenter_streetarea'],
  data() {
    return {
      activeName: 'first',
      loading: false,
      loadingCommunity: false,
      loadingProperty: false,
      loadingSatisfaction: false, 
      showSearch: true,
      showSearchCommunity: true,
      showSearchProperty: true,
      showSearchSatisfaction: true,
      dateRange: [],
      dateRangeCommunity: [], 
      dateRangeProperty: [],
      dateRangeSatisfaction: [],
      queryParams: {
      },
      queryParamsCommunity: {
      },
      queryParamsProperty: {
      },
      queryParamsSatisfaction: {
      },
      gridList: [],
      communityList: [],
      propertyList: [],
      satisfactionList: [],
      tableData: [], // 表格数据
      tableDataCommunity: [], // 小区表格数据
      tableDataProperty: [], // 物业表格数据
      tableDataSatisfaction: [], // 满意度表格数据
      satisfactionSummary: {
        satisfied: 0,
        basicallySatisfied: 0,
        neutral: 0,
        dissatisfied: 0,
        total: 0,
        satisfactionRate: '0.00%'
      },
      spanArr: {
        grid: [], // 网格合并
        committee: [], // 居委会合并
        category: [], // 诉求大类合并
        subcategory: [] // 诉求小类合并
      },
      spanArrCommunity: {
        community: [], // 小区合并
        category: [] // 诉求大类合并
      },
      spanArrProperty: {
        property: [], // 物业合并
        community: [], // 小区合并
        category: [] // 诉求大类合并
      },
      spanArrSatisfaction: {
        grid: []
      },
      nonResidentialPropertyList: [],
      communityOptions: [],
      propertyOptions: [],
      girdOptions: [
        '石化街道第一综合网格',
        '石化街道第二综合网格',
        '石化街道第三综合网格',
        '石化街道第四综合网格'
      ],
    }
  },
  created() {
    // this.getList();
    this.getGirdOptions();
    this.getPropertyList();
  },
  methods: {
    getList() {
      this.loading = true;
      countGrid(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.gridList = response.data;
        this.processTableData();
        this.loading = false;
      });
    },
    getGirdOptions() {
      const userDept = this.$store.getters.deptName;
      if (this.girdOptions.includes(userDept)) {
        this.girdOptions = [userDept];
      }
    },
    getListCommunity() {
      this.loadingCommunity = true;
      countCommunity(this.addDateRange(this.queryParamsCommunity, this.dateRangeCommunity)).then(response => {
        this.communityList = response.data;
        this.processTableDataCommunity();
        this.loadingCommunity = false;
      });
    },
    getListProperty() {
      this.loadingProperty = true;
      countProperty(this.addDateRange(this.queryParamsProperty, this.dateRangeProperty)).then(response => {
        this.propertyList = response.data;
        this.processTableDataProperty();
        this.loadingProperty = false;
      });
    },
    getListSatisfaction() {
      this.loadingSatisfaction = true;
      countSatisfaction(this.addDateRange(this.queryParamsSatisfaction, this.dateRangeSatisfaction)).then(response => {
        this.satisfactionList = response.data;
        this.processTableDataSatisfaction();
        this.loadingSatisfaction = false;
      });
    },
    // 处理表格数据
    processTableData() {
      this.tableData = [];
      this.spanArr = {
        grid: [],
        committee: [],
        category: [],
        subcategory: []
      };

      const gridList = this.gridList || [];
      
      // 初始化合并数组
      const gridSpan = this.spanArr.grid;
      const committeeSpan = this.spanArr.committee;
      const categorySpan = this.spanArr.category;
      const subcategorySpan = this.spanArr.subcategory;

      // 填充表格数据
      gridList.forEach(grid => {
        const gridName = grid.grid;
        const gridQuantity = grid.total_quantity;
        
        if (grid.residence_committees && grid.residence_committees.length > 0) {
          grid.residence_committees.forEach(committee => {
            const committeeName = committee.name;
            const committeeQuantity = committee.total_quantity;
            
            if (committee.appeals && committee.appeals.length > 0) {
              committee.appeals.forEach(appeal => {
                const categoryName = appeal.category;
                const categoryQuantity = appeal.quantity;
                
                if (appeal.subcategories && appeal.subcategories.length > 0) {
                  appeal.subcategories.forEach(subcategory => {
                    const subcategoryName = subcategory.name;
                    const subcategoryQuantity = subcategory.quantity;
                    
                    if (subcategory.communities && subcategory.communities.length > 0) {
                      subcategory.communities.forEach(community => {
                        this.tableData.push({
                          grid: gridName,
                          gridQuantity: gridQuantity,
                          committee: committeeName,
                          committeeQuantity: committeeQuantity,
                          category: categoryName,
                          categoryQuantity: categoryQuantity,
                          subcategory: subcategoryName,
                          subcategoryQuantity: subcategoryQuantity,
                          community: community.name,
                          communityQuantity: community.quantity
                        });
                      });
                    } else {
                      // 无小区数据
                      this.tableData.push({
                        grid: gridName,
                        gridQuantity: gridQuantity,
                        committee: committeeName,
                        committeeQuantity: committeeQuantity,
                        category: categoryName,
                        categoryQuantity: categoryQuantity,
                        subcategory: subcategoryName,
                        subcategoryQuantity: subcategoryQuantity,
                        community: '',
                        communityQuantity: ''
                      });
                    }
                  });
                } else {
                  // 无诉求小类数据
                  this.tableData.push({
                    grid: gridName,
                    gridQuantity: gridQuantity,
                    committee: committeeName,
                    committeeQuantity: committeeQuantity,
                    category: categoryName,
                    categoryQuantity: categoryQuantity,
                    subcategory: '',
                    subcategoryQuantity: '',
                    community: '',
                    communityQuantity: ''
                  });
                }
              });
            } else {
              // 无诉求大类数据
              this.tableData.push({
                grid: gridName,
                gridQuantity: gridQuantity,
                committee: committeeName,
                committeeQuantity: committeeQuantity,
                category: '',
                categoryQuantity: '',
                subcategory: '',
                subcategoryQuantity: '',
                community: '',
                communityQuantity: ''
              });
            }
          });
        } else {
          // 无居委会数据
          this.tableData.push({
            grid: gridName,
            gridQuantity: gridQuantity,
            committee: '',
            committeeQuantity: '',
            category: '',
            categoryQuantity: '',
            subcategory: '',
            subcategoryQuantity: '',
            community: '',
            communityQuantity: ''
          });
        }
      });

      // 计算合并单元格
      this.calculateSpanArr();
    },
    // 计算合并单元格数组
    calculateSpanArr() {
      this.spanArr = {
        grid: [],
        committee: [],
        category: [],
        subcategory: []
      };
      
      const data = this.tableData;
      if (data.length === 0) return;

      // 初始值
      this.spanArr.grid = [1];
      this.spanArr.committee = [1];
      this.spanArr.category = [1];
      this.spanArr.subcategory = [1];

      // 计算各列的合并单元格
      for (let i = 1; i < data.length; i++) {
        // 计算网格合并
        if (data[i].grid === data[i-1].grid) {
          this.spanArr.grid[i] = 0;
          this.spanArr.grid[this.getLastIndex(this.spanArr.grid)] += 1;
        } else {
          this.spanArr.grid.push(1);
        }
        
        // 计算居委会合并
        if (data[i].committee === data[i-1].committee && data[i].grid === data[i-1].grid) {
          this.spanArr.committee[i] = 0;
          this.spanArr.committee[this.getLastIndex(this.spanArr.committee)] += 1;
        } else {
          this.spanArr.committee.push(1);
        }
        
        // 计算诉求大类合并
        if (data[i].category === data[i-1].category && 
            data[i].committee === data[i-1].committee && 
            data[i].grid === data[i-1].grid) {
          this.spanArr.category[i] = 0;
          this.spanArr.category[this.getLastIndex(this.spanArr.category)] += 1;
        } else {
          this.spanArr.category.push(1);
        }
        
        // 计算诉求小类合并
        if (data[i].subcategory === data[i-1].subcategory && 
            data[i].category === data[i-1].category && 
            data[i].committee === data[i-1].committee && 
            data[i].grid === data[i-1].grid) {
          this.spanArr.subcategory[i] = 0;
          this.spanArr.subcategory[this.getLastIndex(this.spanArr.subcategory)] += 1;
        } else {
          this.spanArr.subcategory.push(1);
        }
      }
    },
    // 获取最后一个非零元素的索引
    getLastIndex(arr) {
      for (let i = arr.length - 1; i >= 0; i--) {
        if (arr[i] !== 0) {
          return i;
        }
      }
      return -1;
    },
    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 所属网格
        const _row = this.spanArr.grid[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 1) { // 所属网格数量
        const _row = this.spanArr.grid[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 2) { // 所属居委会
        const _row = this.spanArr.committee[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 3) { // 所属居委会数量
        const _row = this.spanArr.committee[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 4) { // 诉求大类
        const _row = this.spanArr.category[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 5) { // 诉求大类数量
        const _row = this.spanArr.category[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 6 || columnIndex === 7) { // 诉求小类和诉求小类数量
        const _row = this.spanArr.subcategory[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    handleClick(tab, event) {
      if (tab.name === 'first') {
        // this.getList();
      } else if (tab.name === 'second') {
        // this.getListCommunity();
      } else if (tab.name === 'third') {
        // this.getListProperty();
      } else if (tab.name === 'fourth') {
        // this.getListSatisfaction();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    handleQueryCommunity() {
      this.getListCommunity()
    },
    handleQueryProperty() {
      this.getListProperty()
    },
    handleQuerySatisfaction() {
      this.getListSatisfaction()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
    },
    resetQueryCommunity() {
      this.dateRangeCommunity = []
      this.resetForm('queryFormCommunity')
    },
    resetQueryProperty() {
      this.dateRangeProperty = []
      this.resetForm('queryFormProperty')
    },
    resetQuerySatisfaction() {
      this.dateRangeSatisfaction = []
      this.resetForm('queryFormSatisfaction')
    },
    getPropertyList() {
      listProperty().then(response => {
        this.nonResidentialPropertyList = response.data;
        this.propertyOptions = [...new Set(this.nonResidentialPropertyList.map(item => item.name))];
        this.communityOptions = [...new Set(this.nonResidentialPropertyList.map(item => item.residential))];
      });
    },
    // 处理小区表格数据
    processTableDataCommunity() {
      this.tableDataCommunity = [];
      this.spanArrCommunity = {
        community: [],
        category: []
      };

      const communityList = this.communityList || [];
      
      // 填充表格数据
      communityList.forEach(community => {
        const communityName = community.community;
        const communityQuantity = community.total_quantity;
        
        if (community.appeals && community.appeals.length > 0) {
          community.appeals.forEach(appeal => {
            const categoryName = appeal.category;
            const categoryQuantity = appeal.quantity;
            
            if (appeal.subcategories && appeal.subcategories.length > 0) {
              appeal.subcategories.forEach(subcategory => {
                const subcategoryName = subcategory.name;
                const subcategoryQuantity = subcategory.quantity;
                
                this.tableDataCommunity.push({
                  community: communityName,
                  communityQuantity: communityQuantity,
                  category: categoryName,
                  categoryQuantity: categoryQuantity,
                  subcategory: subcategoryName,
                  subcategoryQuantity: subcategoryQuantity
                });
              });
            } else {
              // 无诉求小类数据
              this.tableDataCommunity.push({
                community: communityName,
                communityQuantity: communityQuantity,
                category: categoryName,
                categoryQuantity: categoryQuantity,
                subcategory: '',
                subcategoryQuantity: ''
              });
            }
          });
        } else {
          // 无诉求大类数据
          this.tableDataCommunity.push({
            community: communityName,
            communityQuantity: communityQuantity,
            category: '',
            categoryQuantity: '',
            subcategory: '',
            subcategoryQuantity: ''
          });
        }
      });

      // 计算合并单元格
      this.calculateSpanArrCommunity();
    },
    // 计算小区表格合并单元格数组
    calculateSpanArrCommunity() {
      this.spanArrCommunity = {
        community: [],
        category: []
      };
      
      const data = this.tableDataCommunity;
      if (data.length === 0) return;

      // 初始值
      this.spanArrCommunity.community = [1];
      this.spanArrCommunity.category = [1];

      // 计算各列的合并单元格
      for (let i = 1; i < data.length; i++) {
        // 计算小区合并
        if (data[i].community === data[i-1].community) {
          this.spanArrCommunity.community[i] = 0;
          this.spanArrCommunity.community[this.getLastIndex(this.spanArrCommunity.community)] += 1;
        } else {
          this.spanArrCommunity.community.push(1);
        }
        
        // 计算诉求大类合并
        if (data[i].category === data[i-1].category && 
            data[i].community === data[i-1].community) {
          this.spanArrCommunity.category[i] = 0;
          this.spanArrCommunity.category[this.getLastIndex(this.spanArrCommunity.category)] += 1;
        } else {
          this.spanArrCommunity.category.push(1);
        }
      }
    },
    // 小区表格合并单元格方法
    communitySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 所属小区
        const _row = this.spanArrCommunity.community[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 1) { // 所属小区数量
        const _row = this.spanArrCommunity.community[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 2 || columnIndex === 3) { // 诉求大类和诉求大类数量
        const _row = this.spanArrCommunity.category[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    // 处理物业表格数据
    processTableDataProperty() {
      this.tableDataProperty = [];
      this.spanArrProperty = {
        property: [],
        community: [],
        category: []
      };

      const propertyList = this.propertyList || [];
      
      // 填充表格数据
      propertyList.forEach(property => {
        const propertyName = property.property_management;
        const propertyQuantity = property.total_quantity;
        
        if (property.communities && property.communities.length > 0) {
          property.communities.forEach(community => {
            const communityName = community.name;
            const communityQuantity = community.total_quantity;
            
            if (community.appeals && community.appeals.length > 0) {
              community.appeals.forEach(appeal => {
                const categoryName = appeal.category;
                const categoryQuantity = appeal.quantity;
                
                if (appeal.subcategories && appeal.subcategories.length > 0) {
                  appeal.subcategories.forEach(subcategory => {
                    const subcategoryName = subcategory.name;
                    const subcategoryQuantity = subcategory.quantity;
                    
                    this.tableDataProperty.push({
                      property: propertyName,
                      propertyQuantity: propertyQuantity,
                      community: communityName,
                      communityQuantity: communityQuantity,
                      category: categoryName,
                      categoryQuantity: categoryQuantity,
                      subcategory: subcategoryName,
                      subcategoryQuantity: subcategoryQuantity
                    });
                  });
                } else {
                  // 无诉求小类数据
                  this.tableDataProperty.push({
                    property: propertyName,
                    propertyQuantity: propertyQuantity,
                    community: communityName,
                    communityQuantity: communityQuantity,
                    category: categoryName,
                    categoryQuantity: categoryQuantity,
                    subcategory: '',
                    subcategoryQuantity: ''
                  });
                }
              });
            } else {
              // 无诉求大类数据
              this.tableDataProperty.push({
                property: propertyName,
                propertyQuantity: propertyQuantity,
                community: communityName,
                communityQuantity: communityQuantity,
                category: '',
                categoryQuantity: '',
                subcategory: '',
                subcategoryQuantity: ''
              });
            }
          });
        } else {
          // 无小区数据
          this.tableDataProperty.push({
            property: propertyName,
            propertyQuantity: propertyQuantity,
            community: '',
            communityQuantity: '',
            category: '',
            categoryQuantity: '',
            subcategory: '',
            subcategoryQuantity: ''
          });
        }
      });

      // 计算合并单元格
      this.calculateSpanArrProperty();
    },
    // 计算物业表格合并单元格数组
    calculateSpanArrProperty() {
      this.spanArrProperty = {
        property: [],
        community: [],
        category: []
      };
      
      const data = this.tableDataProperty;
      if (data.length === 0) return;

      // 初始值
      this.spanArrProperty.property = [1];
      this.spanArrProperty.community = [1];
      this.spanArrProperty.category = [1];

      // 计算各列的合并单元格
      for (let i = 1; i < data.length; i++) {
        // 计算物业合并
        if (data[i].property === data[i-1].property) {
          this.spanArrProperty.property[i] = 0;
          this.spanArrProperty.property[this.getLastIndex(this.spanArrProperty.property)] += 1;
        } else {
          this.spanArrProperty.property.push(1);
        }
        
        // 计算小区合并
        if (data[i].community === data[i-1].community && 
            data[i].property === data[i-1].property) {
          this.spanArrProperty.community[i] = 0;
          this.spanArrProperty.community[this.getLastIndex(this.spanArrProperty.community)] += 1;
        } else {
          this.spanArrProperty.community.push(1);
        }
        
        // 计算诉求大类合并
        if (data[i].category === data[i-1].category && 
            data[i].community === data[i-1].community && 
            data[i].property === data[i-1].property) {
          this.spanArrProperty.category[i] = 0;
          this.spanArrProperty.category[this.getLastIndex(this.spanArrProperty.category)] += 1;
        } else {
          this.spanArrProperty.category.push(1);
        }
      }
    },
    // 物业表格合并单元格方法
    propertySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 所属物业
        const _row = this.spanArrProperty.property[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 1) { // 所属物业数量
        const _row = this.spanArrProperty.property[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 2 || columnIndex === 3) { // 所属小区和所属小区数量
        const _row = this.spanArrProperty.community[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      } else if (columnIndex === 4 || columnIndex === 5) { // 诉求大类和诉求大类数量
        const _row = this.spanArrProperty.category[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    // 处理满意度表格数据
    processTableDataSatisfaction() {
      this.tableDataSatisfaction = [];
      this.spanArrSatisfaction = {
        grid: []
      };
      
      // 重置合计
      this.satisfactionSummary = {
        satisfied: 0,
        basicallySatisfied: 0,
        neutral: 0,
        dissatisfied: 0,
        total: 0,
        satisfactionRate: '0%'
      };

      const satisfactionList = this.satisfactionList || [];
      
      // 填充表格数据
      satisfactionList.forEach(grid => {
        const gridName = grid.grid;
        
        if (grid.residence_committees && grid.residence_committees.length > 0) {
          grid.residence_committees.forEach(committee => {
            const committeeName = committee.name;
            const satisfied = committee.satisfied || 0;
            const basicallySatisfied = committee.basically_satisfied || 0;
            const neutral = committee.neutral || 0;
            const dissatisfied = committee.dissatisfied || 0;
            const satisfactionRate = committee.satisfaction_rate || '0%';
            
            // 累加合计数据
            this.satisfactionSummary.satisfied += satisfied;
            this.satisfactionSummary.basicallySatisfied += basicallySatisfied;
            this.satisfactionSummary.neutral += neutral;
            this.satisfactionSummary.dissatisfied += dissatisfied;
            
            this.tableDataSatisfaction.push({
              grid: gridName,
              committee: committeeName,
              satisfied: satisfied,
              basicallySatisfied: basicallySatisfied,
              neutral: neutral,
              dissatisfied: dissatisfied,
              satisfactionRate: satisfactionRate
            });
          });
        } else {
          // 无居委会数据
          this.tableDataSatisfaction.push({
            grid: gridName,
            committee: '无',
            satisfied: 0,
            basicallySatisfied: 0,
            neutral: 0,
            dissatisfied: 0,
            satisfactionRate: '0%'
          });
        }
      });

      // 计算合并单元格
      this.calculateSpanArrSatisfaction();

      // 计算总满意度
      this.satisfactionSummary.total = this.satisfactionSummary.satisfied + 
                                      this.satisfactionSummary.basicallySatisfied + 
                                      this.satisfactionSummary.neutral + 
                                      this.satisfactionSummary.dissatisfied;
      
      if (this.satisfactionSummary.total > 0) {
        const rate = ((this.satisfactionSummary.satisfied + this.satisfactionSummary.basicallySatisfied * 0.8 + this.satisfactionSummary.neutral * 0.6) / 
                     this.satisfactionSummary.total * 100).toFixed(2);
        this.satisfactionSummary.satisfactionRate = rate + '%';
      }
    },
    // 计算满意度表格合并单元格数组
    calculateSpanArrSatisfaction() {
      this.spanArrSatisfaction = {
        grid: []
      };
      
      const data = this.tableDataSatisfaction;
      if (data.length === 0) return;

      // 初始值
      this.spanArrSatisfaction.grid = [1];

      // 计算各列的合并单元格
      for (let i = 1; i < data.length; i++) {
        // 计算网格合并
        if (data[i].grid === data[i-1].grid) {
          this.spanArrSatisfaction.grid[i] = 0;
          this.spanArrSatisfaction.grid[this.getLastIndex(this.spanArrSatisfaction.grid)] += 1;
        } else {
          this.spanArrSatisfaction.grid.push(1);
        }
      }
    },
    // 满意度表格合并单元格方法
    satisfactionSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 所属网格
        const _row = this.spanArrSatisfaction.grid[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
  },
}
</script>

<style scoped>
.el-table {
  width: 100%;
}
.summary-row {
  line-height: 46px;
  background-color: #f5f7fa;
  font-weight: bold;
  text-align: center;
}
.summary-row .el-table {
  margin-top: 0;
}
</style>
