<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物业公司"  prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入物业公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所属小区" prop="residential">
        <el-input
          v-model="queryParams.residential"
          placeholder="请输入所属小区"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:nonResidentialProperty:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:nonResidentialProperty:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:nonResidentialProperty:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:nonResidentialProperty:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="nonResidentialPropertyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="物业公司" align="center" prop="name">
<!--        <template slot-scope="scope">-->
<!--          <router-link :to="'/shcy/nonResidentialProperty/drawEdit/' + scope.row.id" class="link-type">-->
<!--            <span>{{ scope.row.name }}</span>-->
<!--          </router-link>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="管理处地址" align="center" prop="propertyServiceSite" >
        <template slot-scope="scope">
          <router-link :to="'/shcy/nonResidentialProperty/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.propertyServiceSite }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="居委会" align="center" prop="committee" />
      <el-table-column label="所管小区" align="center" prop="residential" />
      <el-table-column label="总经理姓名" align="center" prop="generalManage" />
      <el-table-column label="总经理手机" align="center" prop="generalPhone" />
      <el-table-column label="小区经理姓名" align="center" prop="contacts" />
      <el-table-column label="办公电话" align="center" prop="contactsPhone" />
      <el-table-column label="小区经理手机" align="center" prop="mobilePhone" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:nonResidentialProperty:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:nonResidentialProperty:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改非住宅物业对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="物业公司名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入物业公司" />
        </el-form-item>
        <el-form-item label="管理处地址" prop="propertyServiceSite">
          <el-input v-model="form.propertyServiceSite" placeholder="请输入管理处地址" />
        </el-form-item>
        <el-form-item label="居委会" prop="committee">
          <el-input v-model="form.committee" placeholder="请输入居委会" />
        </el-form-item>
        <el-form-item label="所管小区" prop="residential">
          <el-input v-model="form.residential" placeholder="请输入所管小区" />
        </el-form-item>
        <el-form-item label="总经理姓名" prop="generalManage">
          <el-input v-model="form.generalManage" placeholder="请输入总经理姓名" />
        </el-form-item>
        <el-form-item label="总经理手机" prop="generalPhone">
          <el-input v-model="form.generalPhone" placeholder="请输入总经理手机" />
        </el-form-item>
        <el-form-item label="小区经理姓名" prop="contacts">
          <el-input v-model="form.contacts" placeholder="请输入小区经理姓名" />
        </el-form-item>
        <el-form-item label="办公电话" prop="contactsPhone">
          <el-input v-model="form.contactsPhone" placeholder="请输入办公电话" />
        </el-form-item>
        <el-form-item label="小区经理手机" prop="mobilePhone">
          <el-input v-model="form.mobilePhone" placeholder="请输入小区经理手机" />
        </el-form-item>
        <el-form-item label="坐标" prop="coordinate">
          <el-input v-model="form.coordinate" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNonResidentialProperty, getNonResidentialProperty, delNonResidentialProperty, addNonResidentialProperty, updateNonResidentialProperty } from "@/api/shcy/nonResidentialProperty";
import {getCommitteeList} from "@/api/shcy/committee";
import {getResidentialsList} from "@/api/shcy/residentials";

export default {
  name: "NonResidentialProperty",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 非住宅物业表格数据
      nonResidentialPropertyList: [],
      committeeList:[],    //居委列表
      residentialList:[],  //小区列表
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        residential: null,
        residentialId: null,
        longitude: null,
        latitude: null,
        type: null,
        coordinate: null,
        propertyServiceSite: null,
        contacts: null,
        contactsPhone: null,
        committee: null,
        generalManage: null,
        generalPhone: null,
        mobilePhone: null
      },
      committeeQueryParams:{},
      residentialQueryParams:{},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();

    this.getAllResidentials();
  },
  methods: {
    /** 查询非住宅物业列表 */
    getList() {
      this.loading = true;
      listNonResidentialProperty(this.queryParams).then(response => {
        this.nonResidentialPropertyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 查询小区列表**/
    getAllResidentials(){
      getResidentialsList(this.residentialQueryParams).then(response=>{
        this.residentialList = response.data;
      })
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        residential: null,
        residentialId: null,
        longitude: null,
        latitude: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        type: null,
        coordinate: null,
        propertyServiceSite: null,
        contacts: null,
        contactsPhone: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物业管理处";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getNonResidentialProperty(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物业管理处";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            if (this.form.residentialId != null) {
              this.form.residentialId = this.form.residentialId.toString()
            }
            updateNonResidentialProperty(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNonResidentialProperty(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除物业管理处编号为"' + ids + '"的数据项？').then(function() {
        return delNonResidentialProperty(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/nonResidentialProperty/export', {
        ...this.queryParams
      }, `nonResidentialProperty_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
