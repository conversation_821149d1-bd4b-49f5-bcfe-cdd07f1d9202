<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="报警时间" prop="alarmDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:iccAlarmRecord:add']"
          v-show="false"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:iccAlarmRecord:edit']"
          v-show="false"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:iccAlarmRecord:remove']"
          v-show="false"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:iccAlarmRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" v-loading="loading" :data="iccAlarmRecordList" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="数据库 ID" align="center" prop="id" />-->
      <el-table-column label="报警时间" align="center" prop="alarmDate" sortable="custom" :sort-orders="['descending', 'ascending']" width="168px"/>
      <el-table-column label="报警地点" align="center" prop="alarmPosition" :show-overflow-tooltip="true"/>
      <el-table-column label="报警事件类型" align="center" prop="alarmTypeName" width="168px"/>
<!--      <el-table-column label="报警事件类型" align="center" prop="alarmType" />-->
<!--      <el-table-column label="所属组织" align="center" prop="orgName" />-->
<!--      <el-table-column label="报警状态" align="center" prop="alarmStat">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="alarmStatDict" :value="scope.row.alarmStat"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="处理人" align="center" prop="handleUser" />-->
<!--      <el-table-column label="处理时间" align="center" prop="handleDate" />-->
      <el-table-column label="车牌" align="center" prop="licensePlate" />
      <el-table-column label="车型" align="center" prop="vehicleType" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <template v-if="scope.row.status == '0'">已完成</template>
          <template v-else-if="scope.row.status == '1'">处理中</template>
          <template v-else-if="scope.row.status == '2'">作废</template>
          <template v-else>待处理</template>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            class="custom-icon"
            type="text"
            icon="el-icon-video-camera"
            @click="handleVideo(scope.row, scope.index)"
            v-hasPermi="['shcy:iccAlarmRecord:query']"
            v-if="scope.row.alarmType == '15591'"
            title="视频"
          ></el-button>
          <el-button
            class="custom-icon"
            type="text"
            icon="el-icon-camera-solid"
            @click="handleView(scope.row, scope.index)"
            v-hasPermi="['shcy:iccAlarmRecord:query']"
            title="图片预览"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改ICC报警记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="报警时间" prop="alarmDate">
          <el-input v-model="form.alarmDate" placeholder="请输入报警发生时间" />
        </el-form-item>
        <el-form-item label="报警地点" prop="alarmPosition">
          <el-input v-model="form.alarmPosition" placeholder="请输入报警发生地点" />
        </el-form-item>
        <el-form-item label="报警事件名称" prop="alarmTypeName">
          <el-input v-model="form.alarmTypeName" placeholder="请输入报警事件名称" />
        </el-form-item>
        <el-form-item label="报警所属组织" prop="orgName">
          <el-input v-model="form.orgName" placeholder="请输入报警所属组织" />
        </el-form-item>
        <el-form-item label="报警状态" prop="alarmStat">
          <el-input v-model="form.alarmStat" placeholder="请输入报警状态" />
        </el-form-item>
        <el-form-item label="处理状态" prop="handleStat">
          <el-input v-model="form.handleStat" placeholder="请输入处理状态" />
        </el-form-item>
        <el-form-item label="处理人" prop="handleUser">
          <el-input v-model="form.handleUser" placeholder="请输入处理人" />
        </el-form-item>
        <el-form-item label="处理时间" prop="handleDate">
          <el-input v-model="form.handleDate" placeholder="请输入处理时间" />
        </el-form-item>
        <el-form-item label="报警编号" prop="alarmCode">
          <el-input v-model="form.alarmCode" placeholder="请输入报警编号" />
        </el-form-item>
        <el-form-item label="联动能力" prop="linkNames">
          <el-input v-model="form.linkNames" placeholder="请输入联动能力" />
        </el-form-item>
        <el-form-item label="报警等级" prop="alarmGrade">
          <el-input v-model="form.alarmGrade" placeholder="请输入报警等级" />
        </el-form-item>
        <el-form-item label="报警源编码" prop="nodeCode">
          <el-input v-model="form.nodeCode" placeholder="请输入报警源编码" />
        </el-form-item>
        <el-form-item label="报警图片地址" prop="alarmPicture">
          <el-input v-model="form.alarmPicture" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="任务详情" prop="taskWebName">
          <el-input v-model="form.taskWebName" placeholder="请输入任务详情" />
        </el-form-item>
        <el-form-item label="参数" prop="alarmWebUrl">
          <el-input v-model="form.alarmWebUrl" placeholder="请输入参数" />
        </el-form-item>
        <el-form-item label="参数" prop="alarmAppUrl">
          <el-input v-model="form.alarmAppUrl" placeholder="请输入参数" />
        </el-form-item>
        <el-form-item label="参数" prop="taskWebUrl">
          <el-input v-model="form.taskWebUrl" placeholder="请输入参数" />
        </el-form-item>
        <el-form-item label="参数" prop="taskAppUrl">
          <el-input v-model="form.taskAppUrl" placeholder="请输入参数" />
        </el-form-item>
        <el-form-item label="车牌" prop="licensePlate">
          <el-input v-model="form.licensePlate" placeholder="请输入车牌" />
        </el-form-item>
        <el-form-item label="车型" prop="vehicleType">
          <el-input v-model="form.vehicleType" placeholder="请输入车型" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog title="图片预览" :visible.sync="detailOpen" width="800px" append-to-body>
      <div class="block">
        <el-image :src="iccAlarmPicture"></el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 视频 -->
    <el-dialog title="录像" :visible.sync="videoOpen" width="800px" append-to-body>
      <div class="block" style="padding-top: 0">
        <HLSPlayer :videoUrl="videoUrl" :timeout="500"></HLSPlayer>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listIccAlarmRecord, getIccAlarmRecord, delIccAlarmRecord, addIccAlarmRecord, updateIccAlarmRecord, getIccAccessToken, getVideoUrl } from "@/api/shcy/iccAlarmRecord";

import HLSPlayer from "@/components/HLSPlayer/index.vue";

export default {
  name: "IccAlarmRecord",
  components: {
    HLSPlayer
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ICC报警记录表格数据
      iccAlarmRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示图片预览
      detailOpen: false,
      videoOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        alarmDate: null,
      },
      // 默认排序
      defaultSort: {prop: 'alarmDate', order: 'descending'},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 图片url
      iccAlarmPicture: '',
      //
      alarmStatDict: [
        {
          value: '1',
          label: '发生',
          raw: {
            listClass: 'default'
          }
        },
        {
          value: '2',
          label: '消失',
          raw: {
            listClass: 'default'
          }
        }
      ],
      handleStatDict: [
        {
          value: '0',
          label: '未处理',
          raw: {
            listClass: 'danger'
          }
        },
        {
          value: '1',
          label: '处理中',
          raw: {
            listClass: 'default'
          }
        },
        {
          value: '2',
          label: '已处理',
          raw: {
            listClass: 'default'
          }
        },
        {
          value: '3',
          label: '误报',
          raw: {
            listClass: 'default'
          }
        },
        {
          value: '4',
          label: '忽略',
          raw: {
            listClass: 'default'
          }
        }
      ],
      videoUrl: ''
    };
  },
  created() {
    this.getList();
  },
  mounted() {
  },
  methods: {
    /** 查询ICC报警记录列表 */
    getList() {
      this.loading = true;
      listIccAlarmRecord(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.iccAlarmRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        alarmDate: null,
        alarmPosition: null,
        alarmTypeName: null,
        alarmType: null,
        orgName: null,
        alarmStat: null,
        handleStat: null,
        handleUser: null,
        handleDate: null,
        alarmCode: null,
        linkNames: null,
        alarmGrade: null,
        nodeCode: null,
        alarmPicture: null,
        taskWebName: null,
        alarmWebUrl: null,
        alarmAppUrl: null,
        taskWebUrl: null,
        taskAppUrl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加ICC报警记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getIccAlarmRecord(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改ICC报警记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateIccAlarmRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIccAlarmRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除ICC报警记录编号为"' + ids + '"的数据项？').then(function() {
        return delIccAlarmRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/iccAlarmRecord/export', {
        ...this.queryParams
      }, `iccAlarmRecord_${new Date().getTime()}.xlsx`)
    },
    /** 图片预览按钮操作 */
    handleView(row) {
      if (row.alarmType == '2') {
        this.iccAlarmPicture = 'https://*************/evo-apigw/evo-oss/' + row.alarmPicture
        this.detailOpen = true;
      } else {
        getIccAccessToken().then(response => {
          this.iccAlarmPicture = '';
          const accessToken = response.data;
          this.detailOpen = true;
          if (accessToken) {
            this.iccAlarmPicture = 'https://*************/evo-pic/' + row.alarmPicture + '?token=' + accessToken + '&oss_addr=*************:8925'
          }
        })
      }
    },
    /** 视频按钮操作 */
    handleVideo(row) {
        // 将row的alarmCode属性和nodeCode属性封装成对象传递给getVideoUrl
        const query = {
            alarmCode: row.alarmCode,
            nodeCode: row.nodeCode
        }
        getVideoUrl(query).then(response => {
            this.videoUrl = response.data;
            this.videoOpen = true;
        })
    },
  }
};
</script>

<style scoped>
  .custom-icon {
    font-size: 1.5rem;
  }

  ::v-deep .el-table--medium .el-table__cell {
    padding: 0 0;
  }

</style>
