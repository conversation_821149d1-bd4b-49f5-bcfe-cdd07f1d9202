<template>
  <div class="app-container">
    <el-form
      ref="form"
      :model="form"
      label-width="90px"
      :inline="true"
    >
      <el-form-item label="风险部位" prop="riskSite">
        <el-input
          style="width: 200px"
          size="mini"
          v-model="form.riskSite"
          readonly
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          size="mini"
          plain
          @click="submitForm"
        >提 交
        </el-button
        >
      </el-form-item>
    </el-form>
    <MapDrawEdit ref="child" @submitMapForm="submitMapForm" :type="mapType" :coordinate="mapCoordinate"></MapDrawEdit>
  </div>
</template>

<script>
import {getFloodControlKeyArea, updateFloodControlKeyArea} from "@/api/shcy/floodControlKeyArea"
import MapDrawEdit from "@/components/MapDrawEdit"

export default {
  name: "FloodControlKeyAreaDrawEdit",
  components: {
    MapDrawEdit
  },
  data() {
    return {
      form: {},
      mapType: "",
      mapCoordinate: ""
    }
  },
  mounted() {
    const id = this.$route.params && this.$route.params.id
    getFloodControlKeyArea(id).then(response => {
      this.form = response.data
      this.mapType = response.data.type
      this.mapCoordinate = response.data.coordinate
      this.$refs.child._initMap()
    })
  },
  methods: {
    submitForm() {
      updateFloodControlKeyArea(this.form).then((response) => {
        this.$modal.msgSuccess("修改成功")
        this.open = false
        this.close()
      })
    },
    close() {
      const obj = {path: "/baseInformation/floodControlKeyArea"}
      this.$tab.closeOpenPage(obj);
      this.$tab.refreshPage(obj);
    },
    submitMapForm(form) {
      this.form.type = form.type
      this.form.coordinate = form.coordinate
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
