<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="风险部位" prop="riskSite">
        <el-input
          v-model="queryParams.riskSite"
          placeholder="请输入风险部位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:floodControlKeyArea:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:floodControlKeyArea:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:floodControlKeyArea:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:floodControlKeyArea:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="floodControlKeyAreaList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="风险部位" align="center" prop="riskSite">
        <template slot-scope="scope">
          <router-link :to="'/shcy/floodControlKeyArea/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.riskSite }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="风险等级" align="center" prop="riskLevel" />
      <el-table-column label="风险类型" align="center" prop="riskType" />
      <el-table-column label="脆弱性区域" align="center" prop="vulnerableArea" />
      <el-table-column label="区域面积" align="center" prop="areaSize" />
      <el-table-column label="风险概况" align="center" prop="riskOverview" />
      <el-table-column label="受影响范围" align="center" prop="affectedRange" />
      <el-table-column label="防控期" align="center" prop="controlPeriod" />
      <el-table-column label="风险评级" align="center" prop="riskRating" />
<!--      <el-table-column label="视频监控" align="center" prop="cameras" />-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:floodControlKeyArea:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:floodControlKeyArea:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改防汛防台重点区域对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="风险部位" prop="riskSite">
          <el-input v-model="form.riskSite" placeholder="请输入风险部位" />
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-input v-model="form.riskLevel" placeholder="请输入风险等级" />
        </el-form-item>
        <el-form-item label="脆弱性区域" prop="vulnerableArea">
          <el-input v-model="form.vulnerableArea" placeholder="请输入脆弱性区域" />
        </el-form-item>
        <el-form-item label="区域面积" prop="areaSize">
          <el-input v-model="form.areaSize" placeholder="请输入区域面积" />
        </el-form-item>
        <el-form-item label="风险概况" prop="riskOverview">
          <el-input v-model="form.riskOverview" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="受影响范围" prop="affectedRange">
          <el-input v-model="form.affectedRange" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="防控期" prop="controlPeriod">
          <el-input v-model="form.controlPeriod" placeholder="请输入防控期" />
        </el-form-item>
        <el-form-item label="风险评级" prop="riskRating">
          <el-input v-model="form.riskRating" placeholder="请输入风险评级" />
        </el-form-item>
        <el-form-item label="应对措施1" prop="countermeasure1">
          <el-input v-model="form.countermeasure1" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="启动条件" prop="startCondition1">
          <el-input v-model="form.startCondition1" placeholder="请输入启动条件" />
        </el-form-item>
        <el-form-item label="责任部门" prop="responsibleDepartment1">
          <el-input v-model="form.responsibleDepartment1" placeholder="请输入责任部门" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson1">
          <el-input v-model="form.contactPerson1" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone1">
          <el-input v-model="form.contactPhone1" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="应对措施2" prop="countermeasure2">
          <el-input v-model="form.countermeasure2" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="启动条件" prop="startCondition2">
          <el-input v-model="form.startCondition2" placeholder="请输入启动条件" />
        </el-form-item>
        <el-form-item label="责任部门" prop="responsibleDepartment2">
          <el-input v-model="form.responsibleDepartment2" placeholder="请输入责任部门" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson2">
          <el-input v-model="form.contactPerson2" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone2">
          <el-input v-model="form.contactPhone2" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="填充色" prop="fillColor">
          <el-input v-model="form.fillColor" placeholder="请输入填充色" />
        </el-form-item>
        <el-form-item label="边框色" prop="outlineColor">
          <el-input v-model="form.outlineColor" placeholder="请输入边框色" />
        </el-form-item>
        <el-form-item label="视频监控" prop="cameras">
          <el-input v-model="form.cameras" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFloodControlKeyArea, getFloodControlKeyArea, delFloodControlKeyArea, addFloodControlKeyArea, updateFloodControlKeyArea } from "@/api/shcy/floodControlKeyArea";

export default {
  name: "FloodControlKeyArea",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 防汛防台重点区域表格数据
      floodControlKeyAreaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskSite: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询防汛防台重点区域列表 */
    getList() {
      this.loading = true;
      listFloodControlKeyArea(this.queryParams).then(response => {
        this.floodControlKeyAreaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        riskSite: null,
        riskLevel: null,
        riskType: null,
        vulnerableArea: null,
        areaSize: null,
        riskOverview: null,
        affectedRange: null,
        controlPeriod: null,
        riskRating: null,
        countermeasure1: null,
        startCondition1: null,
        responsibleDepartment1: null,
        contactPerson1: null,
        contactPhone1: null,
        countermeasure2: null,
        startCondition2: null,
        responsibleDepartment2: null,
        contactPerson2: null,
        contactPhone2: null,
        remark: null,
        type: null,
        coordinate: null,
        fillColor: null,
        outlineColor: null,
        cameras: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加防汛防台重点区域";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFloodControlKeyArea(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改防汛防台重点区域";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFloodControlKeyArea(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFloodControlKeyArea(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除防汛防台重点区域编号为"' + ids + '"的数据项？').then(function() {
        return delFloodControlKeyArea(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/floodControlKeyArea/export', {
        ...this.queryParams
      }, `floodControlKeyArea_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
