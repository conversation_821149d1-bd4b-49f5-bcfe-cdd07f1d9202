<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 设备状态 -->
      <el-form-item label="设备状态" prop="deviceState">
        <el-select v-model="queryParams.deviceState" placeholder="请选择设备状态" clearable>
          <el-option
            v-for="item in deviceStateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:liquidLevelDevice:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:liquidLevelDevice:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:liquidLevelDevice:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:liquidLevelDevice:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="liquidLevelDeviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="设备名称" align="left" prop="deviceName">
        <template slot-scope="scope">
          <router-link :to="'/shcy/liquidLevelDevice/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.deviceName }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="设备imei" align="center" prop="deviceImei" width="150"/>
      <el-table-column label="地址" align="left" prop="address"/>
      <el-table-column label="监测水体" align="center" prop="monitoredWaterBody" />
      <el-table-column label="管网类型" align="center" prop="pipelineType" />
      <el-table-column label="排水去向" align="center" prop="drainageDirection" />
      <el-table-column label="负责单位" align="center" prop="responsibleUnit" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactNumber" />
      <el-table-column label="设备状态" align="center" prop="deviceState" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:liquidLevelDevice:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:liquidLevelDevice:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改液位超限感知设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备imei" prop="deviceName">
          <el-input v-model="form.deviceImei" placeholder="请输入设备imei" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="监测水体" prop="monitoredWaterBody">
          <el-input v-model="form.monitoredWaterBody" placeholder="请输入监测水体" />
        </el-form-item>
        <el-form-item label="管网类型" prop="pipelineType">
          <el-input v-model="form.pipelineType" placeholder="请输入管网类型" />
        </el-form-item>
        <el-form-item label="排水去向" prop="drainageDirection">
          <el-input v-model="form.drainageDirection" placeholder="请输入排水去向" />
        </el-form-item>
        <el-form-item label="负责单位" prop="responsibleUnit">
          <el-input v-model="form.responsibleUnit" placeholder="请输入负责单位" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactNumber">
          <el-input v-model="form.contactNumber" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="关联视频" prop="cameras">
          <el-input v-model="form.cameras" type="textarea" placeholder="请输入关联视频" />
        </el-form-item>
        <el-form-item label="泵站区域" prop="pumpStationArea">
          <el-input v-model="form.pumpStationArea" type="textarea" placeholder="请输入泵站区域" />
        </el-form-item>
        <el-form-item label="设备状态" prop="deviceState">
          <el-select v-model="form.deviceState" placeholder="请选择设备状态" clearable>
            <el-option
              v-for="item in deviceStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="图片">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="requestUpload"
            :file-list="fileList"
            list-type="picture">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLiquidLevelDevice, getLiquidLevelDevice, delLiquidLevelDevice, addLiquidLevelDevice, updateLiquidLevelDevice } from "@/api/shcy/liquidLevelDevice";
import {uploadAttachment} from "@/api/shcy/hjzzCase";

export default {
  name: "LiquidLevelDevice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 液位超限感知设备表格数据
      liquidLevelDeviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        deviceState: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      fileList: [],
      deviceStateList: [
        {value: '在线', label: '在线'},
        {value: '离线', label: '离线'},
      ],
    };
  },
  created() {
    // 处理从其他页面跳转过来的参数
    this.handleRouteParams();
    // this.getList();
  },
  methods: {
    /** 查询液位超限感知设备列表 */
    getList() {
      this.loading = true;
      listLiquidLevelDevice(this.queryParams).then(response => {
        this.liquidLevelDeviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceName: null,
        monitoredWaterBody: null,
        pipelineType: null,
        drainageDirection: null,
        responsibleUnit: null,
        contactPerson: null,
        contactNumber: null,
        type: null,
        coordinate: null,
        address: null,
        cameras: null,
        pumpStationArea: null,
        images: null,
        deviceState: null
      };
      this.resetForm("form");
      this.fileList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加液位超限感知设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLiquidLevelDevice(id).then(response => {
        this.form = response.data;
        // 判断是否有附件
        if (this.form.images) {
          this.fileList.push({
            name: '',
            url: process.env.VUE_APP_BASE_API + this.form.images
          });
        }
        this.open = true;
        this.title = "修改液位超限感知设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLiquidLevelDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLiquidLevelDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除液位超限感知设备编号为"' + ids + '"的数据项？').then(function() {
        return delLiquidLevelDevice(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/liquidLevelDevice/export', {
        ...this.queryParams
      }, `liquidLevelDevice_${new Date().getTime()}.xlsx`)
    },

    requestUpload(item) {
      var formData = new FormData();
      var that = this;
      formData.append("file", item.file);
      uploadAttachment(formData).then(response => {
        that.form.images = response.fileName;
      })
    },

    /** 处理从其他页面跳转过来的参数 */
    handleRouteParams() {
      const query = this.$route.query
       // 如果有路由参数，先处理参数再查询
      if (Object.keys(query).length > 0) {
        const deviceState = query.deviceState;
        this.queryParams.deviceState = deviceState;

        // 清除URL参数，避免持续影响用户操作
        this.$router.replace({
          path: this.$route.path
        })
        // 延迟执行查询，确保所有参数都设置完成
        this.$nextTick(() => {
          // 再执行查询
          this.getList()
        })
      } else {
        // 没有路由参数，直接查询
        this.getList()
      }
    }
  }
};
</script>
