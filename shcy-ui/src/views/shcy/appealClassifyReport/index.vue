<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="发现时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 220px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="三级主责部门">
        <el-select
          style="width: 220px"
          v-model="queryParams.subexecutedeptnameMh"
          placeholder="请选择三级主责部门"
          clearable
          filterable
        >
          <el-option
            v-for="(item, i) in subexecutedeptnameMhList"
            :key="i"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="诉求大类">
        <el-select
          style="width: 220px"
          v-model="queryParams.parentappealclassification"
          placeholder="请选择诉求大类"
          clearable
          filterable
          @change="changeQueryParentappealclassification(queryParams.parentappealclassification)"
        >
          <el-option
            v-for="(item, i) in parentappealClassifyList"
            :key="i"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="诉求小类">
        <el-select
          style="width: 220px"
          v-model="queryParams.appealclassification"
          placeholder="请选择诉求小类"
          clearable
          filterable
        >
          <el-option
            v-for="(item, i) in queryappealClassifyList"
            :key="i"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="appealClassifyReportList" show-summary>
      <el-table-column label="序号" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="三级主责部门" align="center" prop="subexecutedeptnameMh">
      </el-table-column>
      <el-table-column label="诉求大类" align="center" prop="parentappealclassification">
      </el-table-column>
      <el-table-column label="诉求小类" align="center" prop="appealclassification">
      </el-table-column>
      <el-table-column label="数量" align="center" prop="count">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            @click="handleCountClick(scope.row)"
            style="color: #409EFF; cursor: pointer;"
          >
            {{ scope.row.count }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dbcenterRxMixin from '@/mixin/dbcenterRxMixin'
import { listAppealClassify } from '@/api/shcy/dbcenterRx'
import { listAppealClassifyReport } from '@/api/shcy/appealClassifyReport'

export default {
  name: 'AppealClassifyReport',
  mixins: [dbcenterRxMixin],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dateRange: [],
      // 查询参数
      queryParams: {
        subexecutedeptnameMh: null,
        parentappealclassification: null,
        appealclassification: null,
      },
      parentappealClassifyList: [],
      appealClassifyReportList: [],
      queryappealClassifyList: [],
    }
  },
  created() {
    this.getAppealClassifyList()
  },
  methods: {
    getList() {
      listAppealClassifyReport(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.appealClassifyReportList = response.data;
      });
    },
    getAppealClassifyList() {
      listAppealClassify().then((response) => {
        this.allappealClassifyList = response.data
        // 获取this.allappealClassifyList中parentId为0的数据
        this.parentappealClassifyList = this.allappealClassifyList.filter(
          (item) => item.parentId == 0
        )
      })
    },
    changeQueryParentappealclassification(value) {
      const parentappealClassify = this.parentappealClassifyList.find(item => item.name === value);
      if (parentappealClassify != null) {
        this.queryappealClassifyList = this.allappealClassifyList.filter(item => item.parentId === parentappealClassify.id);
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 处理数量点击事件 */
    handleCountClick(row) {
      // 构建跳转参数
      const query = {}
      
      // 传递发现时间参数
      if (this.dateRange && this.dateRange.length === 2) {
        query.startDate = this.dateRange[0]
        query.endDate = this.dateRange[1]
      }
      
      // 传递三级主责部门
      if (row.subexecutedeptnameMh) {
        query.subexecutedeptnameMh = row.subexecutedeptnameMh
      }
      
      // 传递诉求大类
      if (row.parentappealclassification) {
        query.parentappealclassification = row.parentappealclassification
      }
      
      // 传递诉求小类
      if (row.appealclassification) {
        query.appealclassification = row.appealclassification
      }
      
      // 跳转到dbcenterRx页面
      this.$router.push({
        path: '/hotline/dbCenterRx',
        query: query
      })
    },
  },
}
</script>
