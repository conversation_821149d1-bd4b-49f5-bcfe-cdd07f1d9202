<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="小区id" prop="residentialId">-->
<!--        <el-input-->
<!--          v-model="queryParams.residentialId"-->
<!--          placeholder="请输入小区id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="名称"  prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属居委会" label-width="100px" prop="committee">
        <el-input
          v-model="queryParams.committee"
          placeholder="请输入所属居委会"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="居委会id" prop="committeeId">-->
<!--        <el-input-->
<!--          v-model="queryParams.committeeId"-->
<!--          placeholder="请输入居委会id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="所属小区" prop="residential">
        <el-input
          v-model="queryParams.residential"
          placeholder="请输入所属小区"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经度" prop="longitude">
        <el-input
          v-model="queryParams.longitude"
          placeholder="请输入经度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input
          v-model="queryParams.latitude"
          placeholder="请输入纬度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['shcy:residentialEntrance:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['shcy:residentialEntrance:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['shcy:residentialEntrance:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['shcy:residentialEntrance:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="residentialEntranceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name">
        <template slot-scope="scope">
          <router-link :to="'/shcy/residentialEntrance/drawEdit/' + scope.row.id" class="link-type">
            <span>{{ scope.row.name }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="具体位置" align="center" prop="detailSite" />
      <el-table-column label="所属居委会" align="center" prop="committee" />
      <el-table-column label="所属小区" align="center" prop="residential" />
      <el-table-column label="是否有门卫" align="center" prop="withGuard" />
      <el-table-column label="机动车通行" align="center" prop="motorVehiclePassing" />
      <el-table-column label="出入口类型" align="center" prop="entranceType" />
      <el-table-column label="非机动车通行" align="center" prop="nonMotorVehicleTraffic" />
      <el-table-column label="行人" align="center" prop="passenger" />
      <el-table-column label="开放时间" align="center" prop="openTime" />
      <el-table-column label="属性" align="center" prop="entranceProperty">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.entrance_property" :value="scope.row.entranceProperty"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
<!--      <el-table-column label="类型" align="center" prop="type" />-->
<!--      <el-table-column label="坐标" align="center" prop="coordinate" />-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['shcy:residentialEntrance:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['shcy:residentialEntrance:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小区出入门口对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="出入口名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入出入口名称" />
        </el-form-item>
        <el-form-item label="具体位置" prop="detailSite">
          <el-input v-model="form.detailSite" placeholder="请输入具体位置" />
        </el-form-item>
        <el-form-item label="小区" prop="residentialId">
          <el-select v-model="form.residentialId" placeholder="请选择小区" clearable>
            <el-option
              v-for="item in residentialList"
              :key="item.id"
              :label="item.residential"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="居委会" prop="committeeId">
          <el-select v-model="form.committeeId" placeholder="请选择居委会" clearable>
            <el-option
              v-for="item in committeeList"
              :key="item.id"
              :label="item.committeeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否有门卫" prop="withGuard">
          <el-input v-model="form.withGuard" placeholder="请输入是否有门卫" />
        </el-form-item>
        <el-form-item label="机动车通行" prop="motorVehiclePassing">
          <el-input v-model="form.motorVehiclePassing" placeholder="请输入机动车通行" />
        </el-form-item>
        <el-form-item  label="出入口类型" prop="entranceType">
          <el-input v-model="form.entranceType" placeholder="请输入出入口类型"/>
        </el-form-item>
        <el-form-item label="非机动车通行" prop="nonMotorVehicleTraffic">
          <el-input v-model="form.nonMotorVehicleTraffic" placeholder="请输入非机动车通行" />
        </el-form-item>
        <el-form-item label="行人" prop="passenger">
          <el-input v-model="form.passenger" placeholder="请输入行人" />
        </el-form-item>
        <el-form-item label="开放时间" prop="openTime">
          <el-input v-model="form.openTime" placeholder="请输入开放时间" />
        </el-form-item>
        <el-form-item label="属性" prop="entranceProperty">
          <el-select v-model="form.entranceProperty" placeholder="请选择属性">
            <el-option
              v-for="dict in dict.type.entrance_property"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="坐标" prop="coordinate">
          <el-input v-model="form.coordinate" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listResidentialEntrance, getResidentialEntrance, delResidentialEntrance, addResidentialEntrance, updateResidentialEntrance } from "@/api/shcy/residentialEntrance";
import {getCommitteeList} from "@/api/shcy/committee";
import {getResidentialsList} from "@/api/shcy/residentials";

export default {
  name: "ResidentialEntrance",
  dicts: ['entrance_property'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小区出入门口表格数据
      residentialEntranceList: [],
      committeeList:[],    //居委列表
      residentialList:[],  //小区列表
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        residentialId: null,
        committee: null,
        committeeId: null,
        residential: null,
        detailSite: null,
        type: null,
        coordinate: null,
        withGuard: null,
        motorVehiclePassing: null,
        entranceType: null,
        nonMotorVehicleTraffic: null,
        passenger: null,
        openTime: null,
        entranceProperty:null,
      },
      committeeQueryParams:{},
      residentialQueryParams:{},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getAllCommittees();
    this.getAllResidentials();
  },
  methods: {
    /** 查询小区出入门口列表 */
    getList() {
      this.loading = true;
      listResidentialEntrance(this.queryParams).then(response => {
        this.residentialEntranceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询居委会列表**/
    getAllCommittees(){
      getCommitteeList(this.committeeQueryParams).then(response=>{
        this.committeeList = response.data;
      })
    },

    /** 查询小区列表**/
    getAllResidentials(){
      getResidentialsList(this.residentialQueryParams).then(response=>{
        this.residentialList = response.data;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        residentialId: null,
        committee: null,
        committeeId: null,
        residential: null,
        detailSite: null,
        type: null,
        coordinate: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        withGuard: null,
        motorVehiclePassing: null,
        entranceType: null,
        nonMotorVehicleTraffic: null,
        passenger: null,
        openTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加小区出入门口";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getResidentialEntrance(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改小区出入门口";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateResidentialEntrance(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addResidentialEntrance(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除小区出入门口编号为"' + ids + '"的数据项？').then(function() {
        return delResidentialEntrance(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('shcy/residentialEntrance/export', {
        ...this.queryParams
      }, `residentialEntrance_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
