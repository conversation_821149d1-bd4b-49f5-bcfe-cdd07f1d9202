<template>
<!--  <div class="dashboard-editor-container">-->
<!--    总店铺数、总人员数 -->
<!--    <el-row :gutter="40" class="panel-group">-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="shop" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              商铺总数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.shopTotal}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('messages')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="worker" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              从业人员总数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.employeesTotal}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">-->
<!--        <div style="margin-left: 50px;margin-top: 20px">-->
<!--          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--          <el-form-item label="检查日期" prop="curDate">-->
<!--            <el-date-picker-->
<!--              v-model="queryParams.curDate"-->
<!--              type="date"-->
<!--              size="medium"-->
<!--              value-format="yyyy-MM-dd"-->
<!--              placeholder="选择日期">-->
<!--            </el-date-picker>-->
<!--          </el-form-item>-->
<!--            <el-form-item>-->
<!--              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--            </el-form-item>-->
<!--          </el-form>-->
<!--          <div style="margin-left: 500px;margin-top: -52px">-->
<!--            <el-row :gutter="5" class="mb6">-->
<!--              <el-col :span="1.5">-->
<!--                <el-button-->
<!--                  type="warning"-->
<!--                  plain-->
<!--                  icon="el-icon-download"-->
<!--                  size="mini"-->
<!--                  @click="dailyDataExport"-->
<!--                >导出日报</el-button>-->
<!--              </el-col>-->
<!--            </el-row>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->
<!--    <div>-->
<!--      <div style="width: 100%;height: 20px;margin-top: -10px">-->
<!--        <p id="tag1">商铺数据</p>-->
<!--      </div>-->
<!--    </div>-->
<!--&lt;!&ndash;    今日巡查店铺数据&ndash;&gt;-->
<!--    <el-row :gutter="40" class="panel-group">-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="shop" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日巡查商铺数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.checkShopTotal}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('messages')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="shop" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日正常商铺数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.shopNormalNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('purchases')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="shop" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日歇业商铺数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.shopRestNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('shoppings')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="shop" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日关停商铺数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.shopCloseNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->
<!--&lt;!&ndash;    <panel-group />&ndash;&gt;-->
<!--&lt;!&ndash;    店铺巡检与未巡检    巡检正常与不正常  重点监管、谈话、督办、黄牌商铺&ndash;&gt;-->
<!--    <el-row :gutter="32">-->
<!--      <el-col :xs="24" :sm="24" :lg="8">-->
<!--        <div class="chart-wrapper">-->
<!--&lt;!&ndash;          <raddar-chart />&ndash;&gt;-->
<!--          <div :id=idName1 :style="{height:height,width:width}" />-->

<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="24" :sm="24" :lg="8">-->
<!--        <div class="chart-wrapper">-->
<!--&lt;!&ndash;          <pie-chart />&ndash;&gt;-->
<!--          <div :id=idName2 :style="{height:height,width:width}" />-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="24" :sm="24" :lg="8">-->
<!--        <div class="chart-wrapper">-->
<!--          &lt;!&ndash;          <pie-chart />&ndash;&gt;-->
<!--          <div :id=idName5 :style="{height:height,width:width}" />-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->
<!--    <div>-->
<!--      <div style="width: 100%;height: 20px;margin-top: -10px">-->
<!--        <p id="tag2">从业人员数据</p>-->
<!--      </div>-->
<!--    </div>-->
<!--&lt;!&ndash;    今日巡查员工数据 &ndash;&gt;-->
<!--    <el-row :gutter="40" class="panel-group">-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="peoples" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日巡查从业人员数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.checkEmployeesTotal}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('messages')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="peoples" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日核酸正常人员数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.checkNoramEmployeesNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('purchases')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="peoples" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日核酸异常人员数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.checkAbnormalEmployeesNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">-->
<!--        <div class="card-panel" @click="handleSetLineChartData('shoppings')">-->
<!--          <div class="card-panel-icon-wrapper icon-people">-->
<!--            <svg-icon icon-class="peoples" class-name="card-panel-icon" />-->
<!--          </div>-->
<!--          <div class="card-panel-description">-->
<!--            <div class="card-panel-text">-->
<!--              今日离岗人员数-->
<!--            </div>-->
<!--            <div class="card-panel-num" >{{shopDatailList.checkRetiredEmployeesNum}}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->
<!--&lt;!&ndash;    <NucleicAcidLog/>&ndash;&gt;-->
<!--    <el-row :gutter="32">-->
<!--      <el-col :xs="24" :sm="24" :lg="12">-->
<!--        <div class="chart-wrapper">-->
<!--&lt;!&ndash;          <EmployeePieOne/>&ndash;&gt;-->
<!--          <div :id="idName3" :style="{height:height,width:width}" />-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :xs="24" :sm="24" :lg="12">-->
<!--        <div class="chart-wrapper">-->
<!--&lt;!&ndash;          <employee-pie-two/>&ndash;&gt;-->
<!--          <div :id="idName4" :style="{height:height,width:width}" />-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->

<!--    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;"  :gutter="32">-->
<!--      <el-col :xs="24" :sm="24" :lg="32">-->
<!--        <div class="chart-wrapper">-->
<!--&lt;!&ndash;          <bar-chart />&ndash;&gt;-->
<!--          <div :id="idName6" :style="{height:height,width:width}" />-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->
<!--    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;"  :gutter="32">-->
<!--        <el-col :xs="24" :sm="24" :lg="32">-->
<!--          <div class="chart-wrapper">-->
<!--&lt;!&ndash;            <Category />&ndash;&gt;-->
<!--            <div :id="idName7" :style="{height:height,width:width}"/>-->
<!--          </div>-->
<!--        </el-col>-->
<!--&lt;!&ndash;      <line-chart :chart-data="lineChartData" />&ndash;&gt;-->
<!--    </el-row>-->



<!--  </div>-->
</template>

<script>
import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import RaddarChart from './dashboard/RaddarChart'
import PieChart from './dashboard/PieChart'
import BarChart from './dashboard/BarChart'
import NucleicAcidLog from "@/views/dashboard/NucleicAcidLog";
import EmployeePieOne from "@/views/dashboard/EmployeePieOne";
import EmployeePieTwo from "@/views/dashboard/EmployeePieTwo";
import Category from "@/views/dashboard/Category";

import CountTo from 'vue-count-to'
import {shopData, committeeShopData, dailyData, categoryShopData} from "@/api/shcy/shop";
import {getEmployeeNucleicAcidDetail} from "@/api/shcy/nucleicAcidLog";

import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from'.././views/dashboard/mixins/resize'
const animationDuration = 3000

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

export default {
  name: 'Index',
  components: {
    CountTo,
    // PanelGroup,
    // LineChart,
    // RaddarChart,
    // PieChart,
    // BarChart,
    // NucleicAcidLog,
    // EmployeePieOne,
    // EmployeePieTwo,
    // Category,
  },

  mixins: [resize],
  props: {
    idName1: {
      type: String,
      default: 'shopData1'
    },
    width:{
      type:String,
      default:'100%'
    },
    height:{
      type:String,
      default:'300px'
    },

    idName2: {
      type: String,
      default: 'shopData2'
    },
    // width2: {
    //   type: String,
    //   default: '100%'
    // },
    // height2: {
    //   type: String,
    //   default: '300px'
    // },
    idName3: {
      type: String,
      default: 'employeeData1'
    },
    // width3: {
    //   type: String,
    //   default: '100%'
    // },
    // height3: {
    //   type: String,
    //   default: '300px'
    // },
    idName4: {
      type: String,
      default: 'employeeData2'
    },
    // width4: {
    //   type: String,
    //   default: '100%'
    // },
    // height4: {
    //   type: String,
    //   default: '300px'
    // },
    idName5: {
      type: String,
      default: 'shopData3'
    },
    // width5: {
    //   type: String,
    //   default: '100%'
    // },
    // height5: {
    //   type: String,
    //   default: '300px'
    // },
    idName6: {
      type: String,
      default: 'committeeData'
    },
    // width6: {
    //   type: String,
    //   default: '100%'
    // },
    // height6: {
    //   type: String,
    //   default: '300px'
    // },
    idName7: {
      type: String,
      default: 'categoryData'
    },
    // width7: {
    //   type: String,
    //   default: '100%'
    // },
    // height7: {
    //   type: String,
    //   default: '300px'
    // },
  },
    data() {
      return {
        lineChartData: lineChartData.newVisitis,
        shopDatailList: [],            //店铺详细列表数据

        shopData1:null,
        shopData2:null,
        shopData3:null,
        employeeData1:null,
        employeeData2:null,
        committeeData:null,
        categoryData:null,

        checkShopTotal:null,           //   巡查店铺数量
        noCheckShopTotal:null ,        //   未巡查店铺数量

        shopNormalNum: null,          //  巡查正常店铺数量
        shopRestNum: null,            //  巡查歇业店铺数量

        checkEmployeesTotal:null,     //巡查从业人员数量
        noCheckEmployeesTotal:null,   //为巡查从业人员数量

        checkNoramEmployeesNum:  null,     //今日核酸正常人员数量
        checkAbnormalEmployeesNum:null,    //今日核酸异常人员数量

        shopSupervisorNum:null,    //重点监管商铺数量
        shopTalkNum:null,          //谈话商铺数量
        shopHandleNum:null,        //督办商铺数量
        shopYellowCardNum:null,    //黄牌商铺数量


        committeeShopList:[],    //居委会管理店铺信息
        committeeArr1: [],  //居委会
        committeeArr2:[],   //居委会管理店铺数
        committeeArr3:[],   //居委会巡查店铺数
        committeeArr4:[],   //居委会巡查比例


        categoryShoList:[],       //行业管理店铺信息
        categoryArr1:[],   //行业
        categoryArr2:[],   //行业管理店铺数
        categoryArr3:[],   //行业巡查店铺数
        categoryArr4:[],   //行业巡查比例


        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: {
          curDate: null,
        },
      }
    },

    mounted() {
      // setTimeout(() => {
      //   this.$nextTick(() => {
      //     this.initChart()
      //   })
      // }, 2000);


    },
    beforeDestroy() {
      if (!this.shopData1) {
        return
      }
      this.shopData1.dispose()
      this.shopData1 = null

      if (!this.shopData2) {
        return
      }
      this.shopData2.dispose()
      this.shopData2 = null
      if (!this.shopData3) {
        return
      }
      this.shopData3.dispose()
      this.shopData3 = null


      if (!this.employeeData1) {
        return
      }
      this.employeeData1.dispose()
      this.employeeData1 = null

      if (!this.employeeData2) {
        return
      }
      this.employeeData2.dispose()
      this.employeeData2 = null

      if (!this.committeeData) {
        return
      }
      this.committeeData.dispose()
      this.committeeData = null

      if(!this.categoryData){
        return
      }
      this.categoryData.dispose()
      this.categoryData = null
    },

    created() {
      // setTimeout(() => {
      //   this.shopDetail();
      // }, 1000);
      //
      // setTimeout(() => {
      //   this.committeeShopDetail();
      // }, 1000);
      //
      // setTimeout(() => {
      //   this.categoryShopDetail();
      // }, 1000);
      //
      //
      // this.getDate();
      // this.shopDetail();
      // this.committeeShopDetail();
      // this.categoryShopDetail();



      //废弃
      // this.employeeNucleicAcidDetail()
      // this.committeeShopDetail()
    },
    computed: {

      //已检查店铺数量  和 未检查店铺数量
      shopOption1() {
        return {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                normal: {
                  position: 'inner',
                  show: true,
                  formatter: '{d}%'
                }

              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '25',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {value: this.checkShopTotal, name: '已巡查商铺'},
                {value: this.noCheckShopTotal, name: '未巡查商铺'},
              ],

            }
          ]
        }
      },
      //巡检店铺正常数量 和 不正常数量
      shopOption2(){
        return {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                normal:{
                  position: 'inner',
                  show:true,
                  formatter:'{d}%'
                }

              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '25',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.shopNormalNum, name: '正常商铺' },
                { value: this.shopRestNum,  name:  '歇业商铺' },
              ],

            }
          ]
        }

      },
      shopOption3(){
        return {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                normal:{
                  position: 'inner',
                  show:true,
                  formatter:'{d}%'
                }

              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '25',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.shopSupervisorNum, name: '被重点监管商铺' },
                { value: this.shopTalkNum,  name:  '被谈话商铺' },
                { value: this.shopHandleNum, name: '被督办商铺' },
                { value: this.shopYellowCardNum,  name:  '被黄牌商铺' },
              ],

            }
          ]
        }

      },
      //巡查从业人员数量与未巡查从业人员数量
      employeeOption1(){
        return {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                normal:{
                  position: 'inner',
                  show:true,
                  formatter:'{d}%'
                }

              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '25',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.checkEmployeesTotal,    name:  '已巡查从业人员' },
                { value: this.noCheckEmployeesTotal,  name:  '未巡查从业人员' },
              ],

            }
          ]
        }

      },
      //今日核酸正常人员数量与核酸异常人员数量
      employeeOption2(){
        return {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center'
          },
          series: [

            {
              name: '',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                normal:{
                  position: 'inner',
                  show:true,
                  formatter:'{d}%'
                }

              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '25',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.checkNoramEmployeesNum,     name: '今日核酸正常人员' },
                { value: this.checkAbnormalEmployeesNum,  name:  '今日核酸异常人员' },
              ],

            }
          ]
        }

      },
      //居委会管理下商铺的数据
      committeeOption(){
        return{
          title:{
            text:"居委会巡查商铺数据",
            textStyle: {
              align: 'center',
              color: 'black',
              fontSize: 20,
            },
            top: '1%',
            left: 'center',

          },
          tooltip: {
            formatter:null,
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          grid: {
            top: 10,
            left: '2%',
            right: '2%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: this.committeeArr1,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel:{interval: 0,rotate:40}
          }],
          yAxis: [{
            type: 'value',
            name:"店铺数量",
            axisTick: {
              show: false
            }
          },
            {
              type: 'value',
              name: '居委巡查商铺比例',
              nameTextStyle: {
                color: '#999999',
              },
              // splitLine:{show: false},//去除网格线
              axisLine: {show: false},
              axisTick: {show: false},
              axisLabel: {
                margin: 2,
                interval: 15,
                color: '#999999',
              }
            }],
          series: [{
            name: '居委会管理店铺数',
            type: 'bar',
            stack: 'vistors',
            barWidth: '60%',
            data: this.committeeArr2,
            animationDuration
          }, {
            name: '今日居委会巡查店铺数',
            type: 'bar',
            stack: 'vistors',
            barWidth: '60%',
            data:this.committeeArr3,
            // data: [30,50,23,8,36,
            //   3,4,5,1,10,
            //   6,4,3,1,5,
            // 90,17,6,108,6,
            // 24,21,116,8,114,5],
            animationDuration},
            {
              name: '巡查比例',
              type: 'line', // 这个就是折线图的类型，设置成这个就是折线图了。
              smooth: true,
              color:['#1D1815'],  //折线条的颜色
              yAxisIndex: 1,
              itemStyle: {
                shadowBlur: 2,
              },
              data:this.committeeArr4,
              // data: [0.29,0.7,0.56,0.41,0.59,
              //   0.18,0.1,0.1,0.1,0.1,
              //   0.1,0.1,0.23,0.31,0.1,
              //   0.44,0.22,0.14,0.6,0.1,
              //   0.29,0.37,0.8,0.1,0.9,0.3],
              animationDuration,
            },
          ]
        }
      },
      //行业目录下商铺的数据
      categoryOption(){
        return{
          title:{
            text:"各个行业巡查商铺数据",
            textStyle: {
              align: 'center',
              color: 'black',
              fontSize: 20,
            },
            top: '1%',
            left: 'center',

          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          grid: {
            top: 10,
            left: '2%',
            right: '2%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: this.categoryArr1,
            axisTick: {
              alignWithLabel: true
            }
          }],
          yAxis: [{
            type: 'value',
            name:"店铺数量",
            axisTick: {
              show: false
            }
          },
            {
              type: 'value',
              name: '行业巡查商铺比例',
              nameTextStyle: {
                color: '#999999',
              },
              // splitLine:{show: false},//去除网格线
              axisLine: {show: false},
              axisTick: {show: false},
              axisLabel: {
                margin: 2,
                interval: 15,
                color: '#999999',
                // formatter: '{value}%'
              }
            }],
          series: [{
            name: '管理店铺数',
            type: 'bar',
            stack: 'vistors',
            barWidth: '60%',
            data: this.categoryArr2,
            animationDuration
          }, {
            name: '今日巡查店铺数',
            type: 'bar',
            stack: 'vistors',
            barWidth: '60%',
            // data: [40,2,9,44,2,2,0,0,6,0,2,0,3],
            data:this.categoryArr3,
            animationDuration},
            {
              name: '巡查比例',
              type: 'line', // 这个就是折线图的类型，设置成这个就是折线图了。
              smooth: true,
              color:['#1D1815'],  //折线条的颜色
              yAxisIndex: 1,
              itemStyle: {
                shadowBlur: 2,
              },
              // data: [0.30,0.11,0.17,0.17,0.40,0.17,0.17,0.17,0.17,0.17,0.17,0.17,0.17],
              data:this.categoryArr4,
              animationDuration,
            },
          ]
        }
      }

    },
    methods: {
      handleSetLineChartData(type) {
        this.lineChartData = lineChartData[type]
      },
      //店铺详细数据
      shopDetail() {
        // if (this.queryParams.curDate === null) {
        //   this.queryParams.curDate = this.getDate()
        // }
        const curDate = this.queryParams.curDate == null ? this.getDate() : this.queryParams.curDate
        dailyData(curDate).then(response => {
          if (response.code === 200) {
            console.log(response.data)
            this.shopDatailList = response.data
            //已检查店铺数量  和 未检查店铺数量
            this.checkShopTotal = parseInt(this.shopDatailList['checkShopTotal'])
            this.noCheckShopTotal = parseInt(this.shopDatailList['shopTotal'])-parseInt(this.shopDatailList['checkShopTotal'])
            //巡检店铺正常数量 和 不正常数量
            this.shopNormalNum = parseInt(this.shopDatailList['shopNormalNum'])
            this.shopRestNum = parseInt(this.shopDatailList['shopRestNum'])
            //巡查从业人员数量与未巡查从业人员数量
            this.checkEmployeesTotal = parseInt(this.shopDatailList['checkEmployeesTotal']);
            this. noCheckEmployeesTotal = parseInt(this.shopDatailList['employeesTotal'])-parseInt(this.shopDatailList['checkEmployeesTotal'])
            //今日核酸正常人员数量与核酸异常人员数量
            this.checkNoramEmployeesNum = parseInt(this.shopDatailList['checkNoramEmployeesNum'])
            this.checkAbnormalEmployeesNum = parseInt(this.shopDatailList['checkAbnormalEmployeesNum'])
            //今日重点监管、谈话、督办、黄牌 店铺数量
            this.shopSupervisorNum = parseInt(this.shopDatailList['shopSupervisorNum'])
            this.shopTalkNum = parseInt(this.shopDatailList['shopTalkNum'])
            this.shopHandleNum = parseInt(this.shopDatailList['shopHandleNum'])
            this.shopYellowCardNum = parseInt(this.shopDatailList['shopYellowCardNum'])
          }
          console.log("获取详细列表数据时间"+new Date().getTime());
        })
      },
      //居委会管理的店铺数据
      committeeShopDetail(){
        // if (this.queryParams.curDate === null) {
        //   this.queryParams.curDate = this.getDate()
        // }
        const curDate = this.queryParams.curDate == null ? this.getDate() : this.queryParams.curDate
        committeeShopData(curDate).then(response=>{
          if(response.code===200){
            this.committeeShopList= response.data
            for(let i=0;i<this.committeeShopList.length;i++){
              this.committeeArr1.push(this.committeeShopList[i].committee)
              this.committeeArr2.push(parseInt(this.committeeShopList[i].committeeCheckShopTotal))
              this.committeeArr3.push(parseInt(this.committeeShopList[i].committeeCheckedShopNum))
              this.committeeArr4.push((parseFloat(this.committeeShopList[i].committeeCheckedRatio)/100).toFixed(2))

            }
            console.log("居委会管理的店铺数据")
            console.log(this.committeeShopList)
            console.log("获取居委会管理详细列表数据时间"+new Date().getTime());
          }
        })
      },
      //各个行业管理的店铺信息
      categoryShopDetail(){
        // if (this.queryParams.curDate === null) {
        //   this.queryParams.curDate = this.getDate()
        // }
        const curDate = this.queryParams.curDate == null ? this.getDate() : this.queryParams.curDate
        categoryShopData(curDate).then(response=>{
          if(response.code===200){
            this.categoryShoList = response.data
            for(let i =0;i<this.categoryShoList.length;i++){
              this.categoryArr1.push(this.categoryShoList[i].category)
              this.categoryArr2.push(parseInt(this.categoryShoList[i].categoryCheckShopTotal))
              this.categoryArr3.push(parseInt(this.categoryShoList[i].categoryCheckedShopNum))
              this.categoryArr4.push((parseFloat(this.categoryShoList[i].categoryCheckedRatio)/100).toFixed(3))
            }
            console.log("行业管理数据")
            console.log(this.categoryShoList)
            console.log("获取行业管理详细列表数据时间"+new Date().getTime());

          }
        })
      },

      //获取当前日期
      getDate() {
        var now = new Date()
        var year = now.getFullYear() // 得到年份
        var month = now.getMonth() // 得到月份
        var date = now.getDate() // 得到日期
        month = month + 1
        month = month.toString().padStart(2, '0')
        date = date.toString().padStart(2, '0')
        var defaultDate = `${year}-${month}-${date}`
        this.$set(this.queryParams, 'curDate', defaultDate)
        console.log("当前日期"+new Date().getTime())
        return defaultDate;
      },
      // // 表单重置
      reset() {
        this.form = {
          curDate: null,
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        if (this.queryParams.curDate === null) {
          this.queryParams.curDate = this.getDate()
        }
        this.shopDetail();
        this.committeeShopDetail();
        this.categoryShopDetail();

      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },


      /** 导出按钮操作 */
      dailyDataExport(){
        if (this.queryParams.curDate === null) {
          this.queryParams.curDate = this.getDate()
        }
        const curDate = this.queryParams.curDate
        console.log("导出日报数据")
        this.download('shcy/shop/dailyExport/'+curDate,
          {...this.queryParams},
          `日报_${curDate}.xlsx`)

        console.log("导出居委会数据")
        this.download('shcy/shop/committeeExport/'+curDate,
          {...this.queryParams},
          `居委会巡查商铺日报_${curDate}.xlsx`)

        console.log("导出行业分类数据")
        this.download('shcy/shop/categoryExport/'+curDate,
          {...this.queryParams},
          `行业分类下巡查商铺日报_${curDate}.xlsx`)

      },

      //渲染数据图
      initChart() {
        this.shopData1 = echarts.init(document.getElementById("shopData1"), 'macarons')
        this.shopData1.setOption(
          this.shopOption1
        )

        this.shopData2 = echarts.init(document.getElementById("shopData2"), 'macarons')
        this.shopData2.setOption(
          this.shopOption2
        )
        this.shopData3 = echarts.init(document.getElementById("shopData3"), 'macarons')
        this.shopData3.setOption(
          this.shopOption3
        )


        this.employeeData1 = echarts.init(document.getElementById("employeeData1"), 'macarons')
        this.employeeData1.setOption(
          this.employeeOption1
        )

        this.employeeData2 = echarts.init(document.getElementById("employeeData2"), 'macarons')
        this.employeeData2.setOption(
          this.employeeOption2
        )

        this.committeeData = echarts.init(document.getElementById("committeeData"), 'macarons')
        this.committeeData.setOption(
          this.committeeOption
        )
        this.categoryData = echarts.init(document.getElementById("categoryData"), 'macarons')
        this.categoryData.setOption(
          this.categoryOption
        )


      },

    }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}




.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
#tag1{
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #3C3C3C;
}
#tag2{
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #3C3C3C;
}
</style>
