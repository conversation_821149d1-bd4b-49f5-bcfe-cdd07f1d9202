<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { shopData,committeeShopData } from "@/api/shcy/shop";
import {getEmployeeNucleicAcidDetail} from "@/api/shcy/nucleicAcidLog";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      shopDatailList:[],            //店铺详细列表数据
      employeesNucleicAcidList:[],  //从业人员核酸信息
      committeeShopList:[],         //居委会管理店铺信息

      shopNormalNum: null,  // 巡查正常店铺数量
      shopRestNum: null,    //巡查歇业店铺数量

      checkNoramEmployeesNum:  null,     //今日核酸正常人员数量
      checkAbnormalEmployeesNum:null,    //今日核酸异常人员数量
    }
  },
  mounted() {

    setTimeout(() => {
      this.$nextTick(() => {
        this.initChart()
      })
    }, 2000);

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();
    this.employeeNucleicAcidDetail();
    // this.committeeShopDetail();
  },
  computed:{
    option(){
      return {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal:{
                position: 'inner',
                show:true,
                formatter:'{d}%'
              }

            },
            emphasis: {
              label: {
                show: true,
                fontSize: '25',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.checkNoramEmployeesNum,     name: '今日核酸正常人员数量' },
              { value: this.checkAbnormalEmployeesNum,  name:  '今日核酸异常人员数量' },
            ],

          }
        ]
      }

    }
  },
  methods: {
    //店铺详细数据
    shopDetail(){
      shopData().then(response=>{
        if(response.code===200){
          console.log("JIEGUO", response.data)
          this.shopDatailList = response.data
          this.shopNormalNum = parseInt(this.shopDatailList['shopNormalNum'])
          this.shopRestNum = parseInt(this.shopDatailList['shopRestNum'])

        }
      })
    },
    //从业人员核酸数据
    employeeNucleicAcidDetail(){
      getEmployeeNucleicAcidDetail().then(response=>{
        if(response.code ===200){
          this.employeesNucleicAcidList = response.data
          this.checkNoramEmployeesNum = parseInt(this.employeesNucleicAcidList['checkNoramEmployeesNum'])
          this.checkAbnormalEmployeesNum = parseInt(this.employeesNucleicAcidList['checkAbnormalEmployeesNum'])
        }
      })
    },

    // //居委会管理的店铺数据
    // committeeShopDetail(){
    //   committeeShopData().then(response=>{
    //     if(response.code===200){
    //       this.committeeShopList= response.data
    //       // console.log(this.committeeShopList)
    //     }
    //   })
    // },

    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(
        this.option
      )
    }
  }
}
</script>
