<template>
  <div class="container">
    <div id="main07" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      data: ["部件", "事件", "自发自处", "一般流程"],
      num: [629, 119, 317, 47],
    };
  },
  computed: {
    option() {
      return {
        backgroundColor: "transparent",
        grid: {
          left: 65,
          bottom: -10,
          right: 70,
          top: 30,
        },
        yAxis: [
          {
            inverse: false,
            data: this.num,
            axisLabel: {
              show: true,
              inside: true,
              textStyle: {
                color: "#FFFFFF",
                fontSize: 30,
                align: "right",
              },
              formatter: "{value} 件" + "\n{a|占位}\n{a|占位}",
              rich: {
                a: {
                  color: "transparent",
                  lineHeight: 50,
                  fontFamily: "digital",
                },
              },
            },
            // 数字-偏移量
            offset: -342,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
          },
          {
            inverse: true,
            data: this.data,
            axisLabel: {
              show: true,
              textStyle: {
                color: "#FFFFFF",
                fontSize: 30,
                align: "left",
              },
              formatter: "{value}\n{a|占位}\n{a|占位}",
              rich: {
                a: {
                  color: "transparent",
                  lineHeight: 50,
                },
              },
            },
            // 文字-偏移量
            offset: -358,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
          },
        ],
        xAxis: {
          max: 800,
          show: false,
        },
        series: [
          {
            // 辅助系列
            type: "bar",
            barGap: "-100%",
            silent: true,
            itemStyle: {
              color: "rgba(255, 255, 254, 0.2)",
              borderRadius: 200,
            },
            barWidth: 15,
            data: [800, 800, 800, 800],
          },
          {
            type: "bar",
            data: this.num,
            barWidth: 15,
            label: {
              position: [10, 10],
              normal: {
                position: [100, -24],
                show: false,
                textStyle: {
                  color: "#8db0ff",
                  fontSize: 16,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#00F9EC",
                borderRadius: 200,
              },
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main07"));
      // 绘制图表
      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (!this.timer) {
      return;
    }
  },
};
</script>

<style lang="scss" scoped>
</style>