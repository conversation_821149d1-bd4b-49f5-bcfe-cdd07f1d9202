<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {

  },
  computed:{
  },
  methods: {
    initChart(list) {
        if(list.length>0)
        {
          this.chart = null
          this.option = null
          const colorList = ["#9E87FF", '#73DDFF', '#fe9a8b', '#F56948', '#9E87FF']
          var xData = function() {
            var data = [];
            for(var i=0;i<list.length;i++)
            {
              data.push(list[i].nianyue);
            }
            return data;
          }();
          var data1 = function() {
            var data = [];
            for(var i=0;i<list.length;i++)
            {
              const total=Number(list[i].my)+Number(list[i].bmy)+Number(list[i].jbmy)+Number(list[i].yb);
              if (total === 0) {
                data.push(0)
              }
              else {
                let num=((Number(list[i].my)+(Number(list[i].jbmy)*0.8)+(Number(list[i].yb)*0.6))/total)*100;
                data.push(num.toFixed(2));
              }

            }
            return data;
          }();
          this.chart = echarts.init(this.$el, 'macarons')
          this.option = {
            backgroundColor: '#fff',
            title: {
              text: '满意度趋势对比图',
              textStyle: {
                fontSize:20,
                fontWeight: 400,
                color:'#333'
              },
              left: 'center',
              top: '0'
            },
            legend: {
              icon: 'circle',
              top: '5%',
              right: '5%',
              itemWidth: 6,
              itemGap: 20,
              textStyle: {
                color: '#556677'
              }
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                label: {
                  show: true,
                  backgroundColor: '#fff',
                  color: '#556677',
                  borderColor: 'rgba(0,0,0,0)',
                  shadowColor: 'rgba(0,0,0,0)',
                  shadowOffsetY: 0
                },
                lineStyle: {
                  width: 0
                }
              },
              backgroundColor: '#fff',
              textStyle: {
                color: '#5c6c7c'
              },
              padding: [10, 10],
              extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)'
            },
            grid: {
              show:false,
            },
            xAxis: [{
              type: 'category',
              data: xData,
              axisLine: {
                lineStyle: {
                  color: '#DCE2E8'
                }
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#556677'
                },
                // 默认x轴字体大小
                fontSize: 12,
                // margin:文字到x轴的距离
                margin: 15
              }
            }],
            yAxis: [{
              type: 'value',
              axisTick: {
                show: false
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#DCE2E8'
                }
              },
              splitArea:{
                show:false
              },
              axisLabel: {
                textStyle: {
                  color: '#556677'
                }
              },
              splitLine: {
                show: false
              }
            }],
            series: [ {
              name: '总分',
              type: 'line',
              data: data1,
              symbolSize: 1,
              symbol: 'circle',
              smooth: true,
              yAxisIndex: 0,
              showSymbol: false,
              lineStyle: {
                width: 5,
                color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [{
                  offset: 0,
                  color: '#73DD39'
                },
                  {
                    offset: 1,
                    color: '#73DDFF'
                  }
                ]),
                shadowColor: 'rgba(115,221,255, 0.3)',
                shadowBlur: 10,
                shadowOffsetY: 20
              },
              itemStyle: {
                normal: {
                  color: colorList[1],
                  borderColor: colorList[1]
                }
              }
            }
            ]
          };
          if (this.option && typeof this.option === 'object') {
            this.chart.clear();
            this.chart.setOption(this.option, true)
          }
        }

      }
  }
}
</script>
