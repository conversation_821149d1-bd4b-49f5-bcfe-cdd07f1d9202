<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {

  },
  computed:{
  },
  methods: {

    initChart(num) {
      if(num)
      {
        this.chart = null
        this.option = null
        this.chart = echarts.init(this.$el, 'macarons')
        this.option = {
          backgroundColor: "#fff",
          animationDuration:5000,
          series: [
            {
              name: "",
              type: 'gauge',
              min: 0,
              max: 100,
              radius: "148%",
              splitNumber: 1, //刻度数量
              startAngle: 180,
              endAngle: 0,
              axisLine: {
                show: false,
                lineStyle: {
                  width: 1,
                  color: [
                    [1, 'rgba(0,0,0,0)']
                  ]
                }
              },
              //分隔线样式。
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: true,
                color: "#9b9b9b",
                formatter: "{value}",
                distance: -35,
                padding: [60, 0, 0, 0],
              },
              axisTick: {
                show: false
              },
              pointer: {
                show: false,
                width:4,
                length:"100%"
              },
              itemStyle:{
                color:"red"
              },
              detail: {
                show: false,
              },
              title: {
                show: false,
              },
              data: [{
                value: num
              }]

            },
            {
              type: "pie",
              radius: ["140%", "150%"],
              top:'60px',
              hoverAnimation: false,
              startAngle:180,
              animation: false,
              label: {
                show:false
              },
              labelLine: {
                show: false
              },
              data: [
                {
                  value: 100,
                  itemStyle:{
                    color:'#e8e8e8'
                  }
                },{
                  value: 100,
                  itemStyle: {
                    opacity:0
                  },
                }
              ]

            },
            {
              type: "pie",
              radius: ["135%", "138%"],
              top:'60px',
              hoverAnimation: false,
              startAngle:180,
              animation: false,
              label: {
                show:false,
              },
              labelLine: {
                show: false
              },
              data: ["","20","","40","","60","","80",""].map(function(e){
                if(e){
                  return {
                    name:e,
                    value: 1,
                    itemStyle:{
                      color:'#0799f3',
                    }
                  }
                }else{
                  return {
                    value: 19,
                    itemStyle:{
                      opacity:0
                    }
                  }
                }
                return
              }).concat({
                value: 100,
                itemStyle:{
                  opacity:0
                }
              })

            },
            {
              type: "pie",
              radius: ["129%", "132%"],
              top:'65px',
              hoverAnimation: false,
              startAngle:180,
              animation: false,
              label: {
                show:true,
                position:"inside",
                color:'#0799f3'
              },
              labelLine: {
                show: false
              },
              data: ["","20","","40","","60","","80",""].map(function(e){
                if(e){
                  return {
                    name:e,
                    value: 0,
                    itemStyle:{
                      color:'#fff',
                    }
                  }
                }else{
                  return {
                    value: 20,
                    itemStyle:{
                      opacity:0
                    }
                  }
                }
                return
              }).concat({
                value: 100,
                itemStyle:{
                  opacity:0
                }
              })

            },
            {
              type: "pie",
              radius: ["129%", "132%"],
              top:'65px',
              hoverAnimation: false,
              startAngle:180,
              animation: false,
              label: {
                show:true,
                position:"inside",
                color:'#0799f3'
              },
              labelLine: {
                show: false
              },
              data: ["","20","","40","","60","","80",""].map(function(e){
                if(e){
                  return {
                    name:e,
                    value: 0,
                    itemStyle:{
                      color:'#fff',
                    }
                  }
                }else{
                  return {
                    value: 20,
                    itemStyle:{
                      opacity:0
                    }
                  }
                }
                return
              }).concat({
                value: 100,
                itemStyle:{
                  opacity:0
                }
              })

            },
            {
              type: "pie",
              radius: ["0", "130%"],
              hoverAnimation: false,
              top:'60px',
              animation: false,
              startAngle:180,
              label: {
                show:true,
                position:"center",
                formatter: function(params){
                  return ["{a|满意率}","{b|"+params.value+"%}","{d|}"].join("\n")
                },
                rich: {
                  a: {
                    color: "#000000",
                    fontWeight: 1000,
                    fontSize: 16,
                    lineHeight: 30
                  },
                  b: {
                    color: "#000000",
                    fontWeight: 1000,
                    fontSize: 20,
                    lineHeight: 30
                  },
                  c: {
                    color: "#9b9b9b",
                    lineHeight: 20
                  },
                  d: {
                    color: "#9b9b9b",
                    fontSize: 40,
                    lineHeight: 70
                  }
                }
              },
              labelLine: {
                show: false
              },
              data: [{
                value: num,
                itemStyle: {
                  color:"#fff"
                },
              }
              ]

            },
            {
              type: "pie",
              radius: ["140%", "150%"],
              top:'60px',
              // animationDuration:((1200-300)*2)/(960-300)*5000,
              hoverAnimation: false,
              startAngle:180,
              label: {
                show:false
              },
              labelLine: {
                show: false
              },
              data: [
                {
                  name:"你的得分",
                  value: num,
                  itemStyle:{
                    color: new echarts.graphic.LinearGradient(
                      0, 0, 1, 0, [{
                        offset: 0,
                        color: '#0799f3'
                      },
                        {
                          offset: 1,
                          color: '#74edd7'
                        }
                      ]
                    )
                  }
                }, {
                  value: 100-Number(num)+100,
                  itemStyle: {
                    opacity:0
                  },
                }
              ]

            },
          ]
        };
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }

    }
  }
}
</script>
