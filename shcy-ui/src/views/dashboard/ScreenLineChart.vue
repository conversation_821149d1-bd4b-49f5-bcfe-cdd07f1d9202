<template>
  <div class="container">
    <div id="main05" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      data: [],
    };
  },
  computed: {
    option() {
      return {
        backgroundColor: "#030315",
        color: ["#27d391"],
        legend: {
          top: 10,
          left: "center",
          textStyle: {
            color: "#97b8d8",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        xAxis: [
          {
            gridIndex: 0,
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            type: "category",
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            gridIndex: 0,
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            splitNumber: 1,
            nameLocation: "center",
            nameTextStyle: {
              color: "#97b8d8",
              fontSize: 14,
            },
            nameRotate: 360,
          },
        ],
        grid: [
          {
            top: "10%",
            left: "1%",
            right: "1%",
            bottom: "0%",
            height: "80%",
          },
        ],
        series: [
          {
            type: "line",
            data: [
              50, 74.1, 20.2, 79.5, 46.4, 40, 46.4, 74.1, 90.2, 79.5, 46.4, 40,
            ],
            showSymbol: false,
            xAxisIndex: 0,
            yAxisIndex: 0,
            smooth: true,
            lineStyle: {
              color: "#6f68bf",
            },
            itemStyle: {
              normal: {
                color: "#6f68bf",
                borderColor: "#6f68bf",
              },
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#6f68bfb3",
                },
                {
                  offset: 1,
                  color: "#6f68bf03",
                },
              ]),
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main05"));
      // 绘制图表
      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (!this.timer) {
      return;
    }
  },
};
</script>

<style lang="scss" scoped>
#main05 {
  background-color: rgba(129, 129, 129, 0.363);
}
</style>