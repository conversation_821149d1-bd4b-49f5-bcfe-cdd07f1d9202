<template>
  <div class="container">
    <div id="main09" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      data: [],
    };
  },
  computed: {
    option() {
      return {
        backgroundColor: '#030315',
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: [
          {
            top: "0%",
            left: "0%",
            right: "0%",
            bottom: "0%",
            height: "90%",
          },
        ],
        xAxis: {
          show: false,
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 20,
          },
          data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15',]
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          data: [320, 200, 350, 280, 270, 410, 230, 320, 200, 150, 280, 270, 410, 230, 510],
          type: 'bar',
          stack: 'one',
          color: "#9936FA",
          itemStyle: {
            borderWidth: 10, //用border设置两个柱形图之间的间距
            borderColor: 'rgba(3,3,21, 1)', //同背景色一样
            barBorderRadius: 120
          },

        },
        {
          data: [220, 130, 250, 380, 170, 210, 330, 350, 180, 370, 310, 330, 320, 400, 150],
          type: 'bar',
          stack: 'one', //堆叠
          barWidth: 30,
          color: "#02F0E3",
          itemStyle: {
            borderWidth: 10, //用border设置两个柱形图之间的间距
            borderColor: 'rgba(3,3,21, 1)', //同背景色一样
            barBorderRadius: 120
          },
        }]
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main09"));
      // 绘制图表
      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (!this.timer) {
      return;
    }
  },
};
</script>

<style lang="scss" scoped>
#main09 {
  background-color: rgba(129, 129, 129, 0.363);
}
</style>