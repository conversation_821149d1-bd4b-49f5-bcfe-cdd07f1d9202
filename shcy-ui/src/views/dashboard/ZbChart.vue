<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    num:String,
    num1:String,
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    setTimeout(() => {
      this.$nextTick(() => {
        this.initChart()
      })
    }, 2000);

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();

  },
  computed:{
  },
  methods: {
    initChart() {
      let data1=this.num1.split("-");
      var num=Number(this.num);
      var num1=Number(data1[1]);
      var bl=(num / num1) * 100;
      var data = {
        'name': '',
        'value': [bl.toFixed(2)],
      };
      this.chart = echarts.init(this.$el, 'macarons')
      this.option= {
        backgroundColor: 'transparent',
        title: {
          text: data.value[0] + '%',
          textStyle: {
            color: "#2E2E2E",
            fontSize:24
          },
          // subtext: data.name + '占比',
          // subtextStyle: {
          //     color: '#aaaaaa',
          //     fontSize: 30
          // },
          itemGap: 20,
          left: 'center',
          top: '35%'
        },
        graphic: [{
          type: 'text',
          z: 100,
          left: 'center',
          top: '90%',
          style: {
            fill: '#fff',
            text: data.name,
            // text: [
            //     '横轴表示温度，单位是°C',
            //     '纵轴表示高度，单位是km',
            //     '右上角有一个图片做的水印',
            //     '这个文本块可以放在图中各',
            //     '种位置'
            // ].join('\n'),
            font: '30px Microsoft YaHei'
          }
        }],
        tooltip: {
          formatter: function(params) {
            return '<span style="color: #fff;">占比：' + params.value + '%</span>';
          }
        },
        angleAxis: {
          max: 100,
          clockwise: true, // 逆时针
          // 隐藏刻度线
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,

          },
          axisTick: {
            show: false
          },
        },
        polar: [{
          center: ['50%', '40%'], //中心点位置
          radius: '110%' //图形大小
        }],
        series: [ {
          type: 'bar',
          z: 10,
          data: data.value,
          showBackground: false,
          backgroundStyle: {
            color:"#FF9963",
            borderWidth: 10,
            width:20
          },
          coordinateSystem: 'polar',
          roundCap: true,
          barWidth:35,
          itemStyle: {
            normal: {
              opacity: 1,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                offset: 0,
                color:"#FF9963"
              }, {
                offset: 1,
                color: "#FE7A33"
              }]),
              shadowBlur: 5,
              shadowColor: '#FF9963',
            }
          },
        },
          {
            type: 'pie',
            name: '内层细圆环',
            center: ['50%', '40%'],
            radius: ['60%', '50%'],
            hoverAnimation: false,
            clockWise: true,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                  offset: 0,
                  color:"#4DC1FF"
                }, {
                  offset: 1,
                  color:"#3362FC"
                }]),
              }
            },
            tooltip: {
              show: false,
            },
            label: {
              show: false
            },
            data: [100]
          }
        ]
      }
      if (this.option && typeof this.option === 'object') {
        this.chart.clear();
        this.chart.setOption(this.option, true)
      }
    }
  }
}
</script>
