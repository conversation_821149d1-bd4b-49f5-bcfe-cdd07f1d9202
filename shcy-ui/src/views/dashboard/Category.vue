<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { shopData,committeeShopData,categoryShopData } from "@/api/shcy/shop";

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      arr1:[],  //居委会
      arr2:[],   //行业管理店铺数
      arr3:[],   //行业巡查店铺数
      arr4:[],   //行业巡查比例
      committeeShopList:[],     //居委会管理店铺信息
      categoryShoList:[],       //行业管理店铺信息
    }
  },
  mounted() {
    setTimeout(() => {
      this.$nextTick(() => {
        this.initChart()
      })

    }, 3000);
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    this.committeeShopDetail();
    this.categoryShopDetail();
  },
  computed:{
    option(){
      return{
        title:{
          text:"各个行业巡查店铺数据",
          textStyle: {
            align: 'center',
            color: 'black',
            fontSize: 20,
          },
          top: '1%',
          left: 'center',

        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 10,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.arr1,
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          name:"店铺数量",
          axisTick: {
            show: false
          }
        },
          {
            type: 'value',
            name: '行业巡查商铺比例',
            nameTextStyle: {
              color: '#999999',
            },
            // splitLine:{show: false},//去除网格线
            axisLine: {show: false},
            axisTick: {show: false},
            axisLabel: {
              margin: 2,
              interval: 15,
              color: '#999999',
              // formatter: '{value}%'
            }
        }],
        series: [{
          name: '管理店铺数',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: this.arr2,
          animationDuration
        }, {
          name: '今日巡查店铺数',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          // data: [40,2,9,44,2,2,0,0,6,0,2,0,3],
          data:this.arr3,
          animationDuration},
          {
            name: '巡查比例',
            type: 'line', // 这个就是折线图的类型，设置成这个就是折线图了。
            smooth: true,
            color:['#1D1815'],  //折线条的颜色
            yAxisIndex: 1,
            itemStyle: {
              shadowBlur: 2,
            },
            // data: [0.30,0.11,0.17,0.17,0.40,0.17,0.17,0.17,0.17,0.17,0.17,0.17,0.17],
            data:this.arr4,
            animationDuration,
          },
        ]
      }
    }
  },
  methods: {

    //居委会管理的店铺数据
    committeeShopDetail(){
      committeeShopData().then(response=>{
        if(response.code===200){
          this.committeeShopList= response.data
          for(let i=0;i<this.committeeShopList.length;i++){
            // this.arr1.push(this.committeeShopList[i].committee)
            // this.arr2.push(this.committeeShopList[i].committeeCheckShopTotal)
            // this.arr3.push(this.committeeShopList[i].committeeCheckedShopNum)
            // this.arr4.push(Number(this.committeeShopList[i].committeeCheckedRatio))
            // console.log(this.arr4)
          }
        }
      })
    },
    //各个行业管理的店铺信息
    categoryShopDetail(){
      categoryShopData().then(response=>{
          if(response.code===200){
            this.categoryShoList = response.data
            console.log("行业管理数据")
            console.log(this.categoryShoList)
            for(let i =0;i<this.categoryShoList.length;i++){
              this.arr1.push(this.categoryShoList[i].category)
              this.arr2.push(parseInt(this.categoryShoList[i].categoryCheckShopTotal))
              this.arr3.push(parseInt(this.categoryShoList[i].categoryCheckedShopNum))
              this.arr4.push((parseFloat(this.categoryShoList[i].categoryCheckedRatio)/100).toFixed(3))
            }
          }
      })
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption(
        this.option
      )
    }
  }
}
</script>
