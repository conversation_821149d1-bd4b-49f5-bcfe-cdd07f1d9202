<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {

  },
  computed:{
  },
  methods: {

    initChart(num,num1,num2,num3) {
      if(num)
      {
        this.chart = null
        this.option = null
        var xAxis = ['居民区工单情况', '科室部门'];
        this.chart = echarts.init(this.$el, 'macarons')
        this.option = {
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: [{
            show: false,
            left: '9%',
            top: 0,
            bottom: 0,
            containLabel: true,
            width: '31%'
          },
            {
              show: false,
              left: '50%',
              top: 0,
              bottom: 0,
              width: '0%'
            }, {
              show: false,
              right: '9%',
              top: 0,
              bottom: 0,
              containLabel: true,
              width: '31%'
            }
          ],
          xAxis: [{
            type: 'value',
            inverse: true,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            splitArea:{show:false},

          }, {
            gridIndex: 1,
            show: false
          }, {
            gridIndex: 2,
            type: 'value',

            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            splitArea:{show:false},
          }],
          yAxis: [{
            type: 'category',
            inverse: true,
            position: 'right',
            data: xAxis,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
          }, {
            gridIndex: 1,
            type: 'category',
            inverse: true,
            data: xAxis.map(function(value) {
              return {
                value: value,
                textStyle: {
                  align:"center",
                  color:'#666666',
                  fontSize:'16'
                }
              }
            }),
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          }, {
            gridIndex: 2,
            type: 'category',
            inverse: true,
            position: 'left',
            data: xAxis,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }

          }],
          series: [{
            name: '',
            type: 'bar',
            barWidth: 40,
            itemStyle: {
              normal: {
                barBorderRadius: 10,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#1784FE'
                }, {
                  offset: 1,
                  color: '#6BB1FF'
                }]),
              },
            },
            label: {
              show: true,
              position: 'left'
            },
            data: [num, num2]
          }, {
            xAxisIndex: 2,
            yAxisIndex: 2,
            name: '',
            itemStyle: {
              normal: {
                barBorderRadius: 10,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#FFBE94'
                }, {
                  offset: 1,
                  color: '#F28B48'
                }]),
              },
            },
            type: 'bar',
            barWidth: 40,
            label: {
              show: true,
              position: 'right'
            },
            data: [num1, num3]

          }]
        };
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }

    }
  }
}
</script>
