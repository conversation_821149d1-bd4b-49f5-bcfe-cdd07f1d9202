<template>
  <div class="container">
    <div id="main04" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      getname: [
        "榕树",
        "香樟",
        "麦冬",
        "悬铃木",
        "枫杨",
        "大叶黄杨",
        "雪松",
        "银杏",
        "测试",
        "测试",
        "测试",
        "测试",
        "测试",
        "测试",
      ],
      getvalue: [60, 20, 1, 1, 16, 16, 8, 2, 0, 0, 0, 0, 0, 0],
      data: [],
      colorList: [
        {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: "#271eb8", // 0% 处的颜色
            },
            {
              offset: 1,
              color: "#ef6565", // 100% 处的颜色
            },
          ],
        },
        {
          type: "radial",
          x: 0.65,
          y: 0.65,
          r: 0.5,
          colorStops: [
            {
              offset: 1,
              color: "#9843F3", // 0% 处的颜色
            },
            {
              offset: 0,
              color: "#0000ff", // 100% 处的颜色
            },
          ],
        },
      ],
      opacityList: [1],
      offsetData: [
        [50, 50],
        [60, 80],
        [60, 20],
        [10, 37],
        [20, 60],
        [30, 20],
        [78, 50],
        [95, 35],
        [40, 73],
        [67, 62],
        [30, 42],
        [5, 65],
        [100, 18],
        [0, 50],
      ],
      datas: [],
    };
  },
  computed: {
    sumvalue() {
      return this.sum(this.getvalue);
    },
    option() {
      return {
        grid: {
          show: false,
          top: 10,
          bottom: 10,
        },
        xAxis: [
          {
            gridIndex: 0,
            type: "value",
            show: false,
            min: 0,
            max: 100,
            nameLocation: "middle",
            nameGap: 5,
          },
        ],
        yAxis: [
          {
            gridIndex: 0,
            min: 0,
            show: false,
            max: 100,
            nameLocation: "middle",
            nameGap: 30,
          },
        ],

        series: [
          {
            type: "scatter",
            symbol: "circle",
            symbolSize: 12,
            label: {
              normal: {
                show: true,
                formatter: "{b}",
                color: "#fff",
                textStyle: {
                  fontSize: "60",
                },
              },
            },

            itemStyle: {
              color: "#00acea",
              shadowColor: "rgba(0, 0, 0, 0.5)",
              shadowBlur: 10,
            },
            data: this.datas,
          },
        ],
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      for (var i = 0; i < this.getname.length; i++) {
        this.data.push({
          name: this.getname[i],
          value: this.getvalue[i],
        });
      }

      for (var i = 0; i < this.data.length; i++) {
        var item = this.data[i];
        if (item.name === "测试") {
          var colortxt = "transparent";
          var size = 65;
        } else {
          var colortxt = "#fff";
          var size = 140;
        }
        this.datas.push({
          name: "{name|" + this.wordLength(item.name) + "}",
          value: this.offsetData[i],
          symbolSize: (item.value / this.sumvalue) * 200 + size,
          label: {
            normal: {
              textStyle: {
                fontSize: 14,
                lineHeight: 20,
              },
              rich: {
                name: {
                  color: colortxt,
                  fontSize: 35,
                  fontWeight: "400",
                  lineHeight: 20,
                  align: "center",
                },
                value: {
                  color: colortxt,
                  fontSize: 60,
                  fontWeight: "bold",
                  lineHeight: 30,
                  align: "center",
                },
              },
            },
          },
          itemStyle: {
            normal: {
              color: this.selStyle(i),
              opacity: this.opacityList[i],
            },
          },
        });
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main04"));
      // 绘制图表
      this.myChart.setOption(this.option);
    },
    sum(getvalue) {
      var s = 0;
      for (var i = getvalue.length - 1; i >= 0; i--) {
        s += getvalue[i];
      }
      return s;
    },
    wordLength(value) {
      var ret = ""; //拼接加\n返回的类目项
      var maxLength = 4; //每项显示文字个数
      var valLength = value.length; //X轴类目项的文字个数
      var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
      if (rowN > 1) {
        //如果类目项的文字大于3,
        for (var i = 0; i < rowN; i++) {
          var temp = ""; //每次截取的字符串
          var start = i * maxLength; //开始截取的位置
          var end = start + maxLength; //结束截取的位置
          if (end >= valLength) {
            temp = value.substring(start, end);
          } else {
            temp = value.substring(start, end) + "\n";
          }
          ret += temp; //凭借最终的字符串
        }
        return ret;
      } else {
        return value;
      }
    },
    selStyle(index) {
      if (index <= 10) {
        return this.colorList[0];
      } else {
        return this.colorList[1];
      }
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (!this.timer) {
      return;
    }
  },
};
</script>

<style lang="scss" scoped>
</style>