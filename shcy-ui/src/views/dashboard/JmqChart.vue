<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    xlList:Array,
  },
  watch: {
    xlList(newVal, oldVal) {
      this.initChart(newVal)
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    setTimeout(() => {
      this.$nextTick(() => {
        this.initChart()
      })
    }, 2000);

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();

  },
  computed:{
  },
  methods: {
    initChart(newVal) {
      if(newVal)
      {
        this.xlList=newVal;
      }
      let tdata=[];
      let tdata1=[];
      let t=0;
      for(let i=0;i<this.xlList.length;i++)
      {
        t=t+Number(this.xlList[i].num);
      }
      for(let i=0;i<this.xlList.length;i++)
      {
        var form={};
        if(this.xlList[i].residentialarea == null)
        {
          this.xlList[i].residentialarea="其他"
        }
        form.code=this.xlList[i].residentialarea;
        form.value=this.xlList[i].num;
        form.fundPost=this.xlList[i].num;
        tdata.push(form);
        tdata1.push(t);
      }
      var attackSourcesColor = [
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: '#6BB1FF' },
          { offset: 1, color: '#1784FE' },
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: '#FFBE94' },
          { offset: 1, color: '#F28B48' },
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: '#6BB1FF' },
          { offset: 1, color: '#1784FE' },
        ]),

      ];
      var attaData = [];
      var attaName = [];
      var topName = [];
      tdata.forEach((it, index) => {
        attaData[index] = it.fundPost;
        attaName[index] = it.code;
        topName[index] = `${it.code} ${it.stock}`;
      });
      function attackSourcesDataFmt(sData) {
        var sss = [];
        sData.forEach(function (item, i) {
          let itemStyle = {
            color: i > 3 ? attackSourcesColor[3] : attackSourcesColor[i],
          };
          sss.push({
            value: item,
            itemStyle: itemStyle,
          });
        });
        return sss;
      }
      this.chart = echarts.init(this.$el, 'macarons')
      this.option={
        grid: {
          show:'true',
          borderWidth:'0',
          left:"95",
          top:"0",
          height:"70%",
          width:"70%",
          x:"12%",
          // y:"20%",
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: "{b0}: {c0}"
          /*formatter: function(params) {
              var result = '';
              params.forEach(function (item) {
                  result += item.marker + " " + item.seriesName + " : " + item.value +"</br>";
              });
              return result;
          }*/
        },
        backgroundColor: 'transparent',        //背景色
        xAxis: {
          show: false,                //是否显示x轴
          type: 'value',
        },
        yAxis: {
          type: 'category',
          inverse:true,               //让y轴数据逆向
          axisLabel: {
            show: true,
            verticalAlign:'top',
            textStyle: {
              color: '#666',       //y轴字体颜色
              padding:[5,0,0,0],
            },
            formatter: function(value, index) {
              return [
                '{title|' + value + '} '
              ].join('\n');
            },
            //定义富文本标签
            rich: {
              lg: {
                fontWeight: 'bold',
                fontSize: 12,       //字体默认12
                color: '#08C',
                padding: [0, 0, 0, 0]
              },
              title: {
                color: '#000',
                fontWeight: 'bold',
                // borderWidth: 1,
                // borderColor: '#08c'
                // textareaBorderColor: '#08c',
              }
            }
          },
          splitLine: {show: false},   //横向的线
          axisTick: {show: false},    //y轴的端点
          axisLine: {show: false},    //y轴的线
          data:  attackSourcesDataFmt(attaName),
        },
        title: [{
          text: '',
        }],
        series: [
          {
            name: '数据内框',
            type: 'bar',
            barGap: "-100%",
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: '#00b5eb',
              }
            },
            label: {
              normal: {
                show: true,
                position: 'right',
                color: '#000',
                fontSize: 14,
                formatter:
                  function(param) {
                    // return 'xx';
                  },
              }
            },
            barWidth: 20,
            data: attackSourcesDataFmt(attaData),
          },
          {
            name: '外框',
            type: 'bar',
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: 'rgba(255, 255, 255, 0.14)' //rgba设置透明度0.14
              }
            },
            barGap: '-100%',
            z: 0,
            barWidth: 0,
            data: tdata1
          }
        ]
      }
      if (this.option && typeof this.option === 'object') {
        this.chart.clear();
        this.chart.setOption(this.option, true)
      }
    }
  }
}
</script>
