<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import {shopData} from "@/api/shcy/shop";

const animationDuration = 3000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      shopDatailList:[],            //店铺详细列表数据

      checkShopTotal:null,          //   巡查店铺数量
      noCheckShopTotal:null ,       //   未巡查店铺数量
    }
  },
  mounted() {
    setTimeout(() => {
    this.$nextTick(() => {
      this.initChart()
    })
    }, 2000);
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    this.shopDetail();

  },
  computed:{
    option(){
      return {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal:{
                position: 'inner',
                show:true,
                formatter:'{d}%'
              }

            },
            emphasis: {
              label: {
                show: true,
                fontSize: '25',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.checkShopTotal, name: '巡查店铺数量' },
              { value: this.noCheckShopTotal,  name:  '未巡查店铺数量' },
            ],

          }
        ]
      }
    }
  },
  methods: {
    //店铺详细数据
    shopDetail(){
      shopData().then(response=>{
        if(response.code===200){
          console.log("JIEGUO", response.data)
          this.shopDatailList = response.data
          this.checkShopTotal = parseInt(this.shopDatailList['checkShopTotal'])
          this.noCheckShopTotal = parseInt(this.shopDatailList['shopTotal'])-parseInt(this.shopDatailList['checkShopTotal'])

        }
      })
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption(
        this.option
      )
    }
  }
}
</script>
