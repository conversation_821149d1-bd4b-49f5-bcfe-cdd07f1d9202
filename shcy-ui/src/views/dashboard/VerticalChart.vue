<template>
  <div class="container">
    <div id="main06" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      targetOneData: [
        [20, 50, 80, 58, 83],
        [50, 70, 60, 61, 75],
        [70, 48, 73, 68, 53],
      ],
      targetTwoData: [
        [50, 70, 60, 61, 75],
        [20, 50, 80, 58, 83],
        [70, 48, 73, 68, 53],
      ],
      targetThreeData: [
        [70, 48, 73, 68, 53],
        [50, 70, 60, 61, 75],
        [20, 50, 80, 58, 83],
      ],
      targetOne: [],
      targetTwo: [],
      targetThree: [],
      timer: null,
    };
  },
  computed: {
    option() {
      return {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          show: true,
          data: ["指标一", "指标二", "指标三"],
          align: "right",
          top: 30,
          right: 165,
          icon: "circle",
          textStyle: {
            color: "#fff",
            fontSize: 30,
          },
          itemWidth: 30,
          itemHeight: 30,
          itemGap: 35,
        },
        grid: {
          left: "3%",
          right: "14%",
          bottom: "13%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月"],
            textStyle: {
              fontSize: 30,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#667283",
                width: 1,
                type: "solid",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#B6B6BD",
              },
              fontSize: 30,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: true,
              lineStyle: {
                color: "#063374",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#667283",
                width: 1,
                type: "solid",
              },
            },
            axisLabel: {
              show: true,
              fontSize: 30,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "指标一",
            type: "bar",
            data: this.targetOne,
            barWidth: 20, // 柱子宽度
            barGap: 1, // 柱子之间间距
            itemStyle: {
              normal: {
                borderRadius: [5, 5, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#06D6E0",
                  },
                  {
                    offset: 1,
                    color: "#083554",
                  },
                ]),
                opacity: 1,
              },
            },
          },
          {
            name: "指标二",
            type: "bar",
            data: this.targetTwo,
            barWidth: 20,
            barGap: 1,
            itemStyle: {
              normal: {
                borderRadius: [5, 5, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#D9A562",
                  },
                  {
                    offset: 1,
                    color: "#171E3A",
                  },
                ]),
                opacity: 1,
              },
            },
          },
          {
            name: "指标三",
            type: "bar",
            data: this.targetThree,
            barWidth: 20,
            barGap: 1,
            itemStyle: {
              normal: {
                borderRadius: [5, 5, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#8C2AF2",
                  },
                  {
                    offset: 1,
                    color: "#1C1753",
                  },
                ]),
                opacity: 1,
              },
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.targetOne = this.targetOneData[0];
    this.targetTwo = this.targetTwoData[0];
    this.targetThree = this.targetThreeData[0];
    this.$nextTick(() => {
      this.initChart();
    });

    let i = 0;
    this.timer = setInterval(() => {
      if (i >= this.targetOneData.length) {
        i = 0;
      }
      this.targetOne = this.targetOneData[i];
      this.targetTwo = this.targetTwoData[i];
      this.targetThree = this.targetThreeData[i];

      this.initChart();

      i++;
    }, 6000);
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main06"));
      // 绘制图表
      this.myChart.setOption(this.option);
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (!this.timer) {
      return;
    }
  },
};
</script>

<style lang="scss" scoped>
</style>