<template>
  <div  :style="{height:height,width:width}"/>

</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: "Czl<PERSON><PERSON>",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    chartData: {
      type: Array,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      chartData1:{},
      xData2:[],
      data1:[],
      data2: [],
      data3: [],
      xLabel:[],
    }
  },
  watch:{
    chartData(newData, oldData)
    {
      this.init(newData);
    }
  },
  computed: {
    option() {
      return  {
        title: {
          text: '处置率报表',
          textStyle: {
            align: 'center',
            color: '#333',
            fontSize: 20,
          },
          top: '3%',
          left: '10%',
        },
        backgroundColor: 'transparent',
        grid: {
          top: "25%",
          bottom: "10%"//也可设置left和right设置距离来控制图表的大小
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: {
              show: true
            }
          }
        },
        legend: {
          data: ["结案率","派单数"],
          top: "5%",
          right:"15%",
          textStyle: {
            color: "#333",
            fontSize: 20,
          }
        },
        xAxis: {
          data:  this.xLabel,
          axisLine: {
            show: true, //隐藏X轴轴线
            lineStyle: {
              color: '#01FCE3'
            }
          },
          axisTick: {
            show: true //隐藏X轴刻度
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#333", //X轴文字颜色
              fontSize: 16,
            }
          },

        },
        yAxis: [{
          type: "value",
          name: "件",
          nameTextStyle: {
            color: "#333"
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#333",
              fontSize: 16,
            }
          },

        },
          {
            type: "value",
            name: "结案率",
            nameTextStyle: {
              color: "#333"
            },
            position: "right",
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              formatter: "{value} %", //右侧Y轴文字显示
              textStyle: {
                color: "#333",
                fontSize: 16,
              }
            }
          },
          {
            type: "value",
            gridIndex: 0,
            min: 50,
            max: 100,
            splitNumber: 8,
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitArea: {
              show: true,
              areaStyle: {
                color: ["rgba(250,250,250,0.0)", "rgba(250,250,250,0.05)"]
              }
            }
          }
        ],
        series: [{
          name: "结案率",
          type: "line",
          yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
          smooth: true, //平滑曲线显示
          showAllSymbol: true, //显示所有图形。
          symbol: "circle", //标记的图形为实心圆
          symbolSize: 10, //标记的大小
          itemStyle: {
            normal: {
              color: "#058cff",
              label: {
                show: true,
                position: "top",
                fontSize:16,
                formatter: function(p) {
                  return p.value+"%";
                }
              }
            }
          },
          lineStyle: {
            color: "#058cff"
          },
          areaStyle:{
            color: "rgba(5,140,255, 0.2)"
          },
          data:this.data3
        },
          {
            name: "派单数",
            type: "bar",
            barWidth: 30,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "#00FFE3"
                },
                  {
                    offset: 1,
                    color: "#4693EC"
                  }
                ])
              }
            },
            data:this.data2
          }
        ]
      }
    }
  },
  methods: {
    init(value) {
      if(value) {
        this.chartData1 = value;
        this.initChartData()
      }
    },
    initChartData() {
      this.data2=[];
      this.data1=[];
      this.data3=[];
      this.xLabel=[];
        for(let i=0;i<this.chartData1[1].length;i++) {
          const month=this.chartData1[1][i];
          const li=JSON.stringify(this.chartData1[0],[month]);
          const jsonArray=JSON.parse(li);
          if(li.length>2)
          {
            for (const key in jsonArray)
            {

              this.data2.push(jsonArray[key]);
            }
          }
          else {
            this.data2.push(0);
          }
          this.xLabel.push(this.chartData1[1][i]);
        }
        for(let i=0;i<this.chartData1[1].length;i++) {
          const month=this.chartData1[1][i];
          const li=JSON.stringify(this.chartData1[2],[month]);
          const jsonArray=JSON.parse(li);
          if(li.length>2)
          {
            for (const key in jsonArray)
            {
              this.data1.push(jsonArray[key]);
            }
          }
          else {
            this.data1.push(0);
          }
        }
        for(let m=0;m<this.data1.length;m++)
        {
          if(Number(this.data2[m]) == 0)
          {
            this.data3.push(0);
          }
          else {
            //this.data3.push(((Number(this.data1[m]))/Number(this.data2[m])*100).toFixed(2));
            this.data3.push(this.data1[m]);
          }
        }
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })


      }
  }
}
</script>
