<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {
        this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();

  },
  computed:{
  },
  methods: {
    initChart(num) {
      if(num)
      {
        let data=num.split(",");
        let echartData=[];
        for(var i=0;i<data.length;i++)
        {
          let data1=data[i].split("-");
          var form={};
          form.name=data1[0];
          form.value=data1[1];
          echartData.push(form);
        }
        let bgColor = 'transparent';
        let color = ['#1784FE', '#F28B48', '#FFD338', '#19CA2B', '#4DE3E9', '#9080FF'];

        let item=echartData[echartData.length-1].value;
        let formatNumber = function(num) {
          let reg = /(?=(\B)(\d{3})+$)/g;
          return num.toString().replace(reg, ',');
        }
        let total = echartData.reduce((a, b) => {
          return a + b.value * 1
        }, 0);
        this.chart = echarts.init(this.$el, 'macarons')
        this.option={
          backgroundColor: bgColor,
          color: color,
          tooltip: {
            trigger: 'item'
          },
          title: [{
            text: '{val|' + formatNumber(total) + '}{name| 件}',
            top: '25%',
            left: '16%',
            textStyle: {
              rich: {
                name: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  color: '#666666',
                  padding: [10, 0]
                },
                val: {
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#1784FE',
                }
              }
            }
          },{
            text: '主要涉及问题',
            top: 20,
            left: 250,
            textStyle: {
              fontSize: 18,
              color:'#333',
            }
          }],
          legend: {
            orient: 'horizontal',
            icon: 'rect',
            x: '50%',
            y: '20%',
            itemWidth: 12,
            itemHeight: 12,
            align: 'left',
            textStyle: {
              rich: {
                name: {
                  fontSize: 12
                },
                value: {
                  fontSize: 16,
                  padding: [0, 5, 0, 15]
                },
                unit: {
                  fontSize: 12
                }
              }
            },
            formatter: function(name) {
              let res = echartData.filter(v => v.name === name);
              res = res[0] || {};
              let unit = res.unit || '';
              return '{name|' + name + '}  {value|' + res.value + '}{unit|件}'
            }
            // data: legendName
          },
          series: [{
            type: 'pie',
            radius: ['35%', '50%'],
            center: ['20%', '30%'],
            data: echartData,
            hoverAnimation: true,
            itemStyle: {
              normal: {
                borderColor: bgColor,
                borderWidth: 0,
              }
            },
            labelLine: {
              normal: {
                show: false,
                length: 20,
                length2: 120,
                lineStyle: {
                  color: '#e6e6e6'
                }
              }
            },
            label: {
              normal: {
                show: false,
                formatter: params => {
                  return (
                    '{icon|●}{name|' + params.name + '}{value|' +
                    formatNumber(params.value) + '}'
                  );
                },
                padding: [0 , -100, 25, -100],
                rich: {
                  icon: {
                    fontSize: 16
                  },
                  name: {
                    fontSize: 14,
                    padding: [0, 10, 0, 4],
                    color: '#666666'
                  },
                  value: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#333333'
                  }
                }
              }
            },
          }]
        }
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }
      }

  }
}
</script>
