<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { shopData,committeeShopData } from "@/api/shcy/shop";

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      arr1: [],  //居委会
      arr2:[],   //居委会管理店铺数
      arr3:[],   //居委会巡查店铺数
      arr4:[],   //居委会巡查比例
      committeeShopList:[],     //居委会管理店铺信息
    }
  },
  mounted() {
    setTimeout(() => {
      this.$nextTick(() => {
        this.initChart()
      })

    }, 3000);
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    this.committeeShopDetail();
  },
  computed:{
    option(){
      return{
        title:{
          text:"居委会巡查店铺数据",
          textStyle: {
            align: 'center',
            color: 'black',
            fontSize: 20,
          },
          top: '1%',
          left: 'center',

        },
        tooltip: {
          formatter:null,
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 10,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: this.arr1,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel:{interval: 0,rotate:40}
        }],
        yAxis: [{
          type: 'value',
          name:"店铺数量",
          axisTick: {
            show: false
          }
        },
          {
            type: 'value',
            name: '居委巡查商铺比例',
            nameTextStyle: {
              color: '#999999',
            },
            // splitLine:{show: false},//去除网格线
            axisLine: {show: false},
            axisTick: {show: false},
            axisLabel: {
              margin: 2,
              interval: 15,
              color: '#999999',
            }
        }],
        series: [{
          name: '居委会管理店铺数',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data: this.arr2,
          animationDuration
        }, {
          name: '今日居委会巡查店铺数',
          type: 'bar',
          stack: 'vistors',
          barWidth: '60%',
          data:this.arr3,
          // data: [30,50,23,8,36,
          //   3,4,5,1,10,
          //   6,4,3,1,5,
          // 90,17,6,108,6,
          // 24,21,116,8,114,5],
          animationDuration},
          {
            name: '巡查比例',
            type: 'line', // 这个就是折线图的类型，设置成这个就是折线图了。
            smooth: true,
            color:['#1D1815'],  //折线条的颜色
            yAxisIndex: 1,
            itemStyle: {
              shadowBlur: 2,
            },
            data:this.arr4,
            // data: [0.29,0.7,0.56,0.41,0.59,
            //   0.18,0.1,0.1,0.1,0.1,
            //   0.1,0.1,0.23,0.31,0.1,
            //   0.44,0.22,0.14,0.6,0.1,
            //   0.29,0.37,0.8,0.1,0.9,0.3],
            animationDuration,
          },
        ]
      }
    }
  },
  methods: {

    //居委会管理的店铺数据
    committeeShopDetail(){
      committeeShopData().then(response=>{
        if(response.code===200){
          this.committeeShopList= response.data
          for(let i=0;i<this.committeeShopList.length;i++){
            this.arr1.push(this.committeeShopList[i].committee)
            this.arr2.push(parseInt(this.committeeShopList[i].committeeCheckShopTotal))
            this.arr3.push(parseInt(this.committeeShopList[i].committeeCheckedShopNum))
            this.arr4.push((parseFloat(this.committeeShopList[i].committeeCheckedRatio)/100).toFixed(2))
            console.log(this.committeeShopList[i].committeeCheckedRatio)

          }
          // console.log(this.arr4)
        }
      })
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(
        this.option
      )
    }
  }
}
</script>
