<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {
        this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();

  },
  computed:{
  },
  methods: {
    initChart(list,num) {
      if(num)
      {
        let data=num.split("-");
        let qi=Number(data[1]);
        let echartData=[];
        for(var i=0;i<list.length;i++)
        {
          if(i == list.length)
          {
            var form={};
            form.name="其他";
            form.value=qi;
            echartData.push(form);
          }
          else {
            qi=qi-Number(list[i].num);
            var form={};
            form.name=list[i].parentappealclassification;
            form.value=list[i].num;
            echartData.push(form);
          }
        }
        let bgColor = 'transparent';
        let color = ['#1784FE', '#F28B48', '#FFD338', '#19CA2B', '#4DE3E9', '#9080FF'];

        let item=echartData[echartData.length-1].value;
        let formatNumber = function(num) {
          let reg = /(?=(\B)(\d{3})+$)/g;
          return num.toString().replace(reg, ',');
        }
        let total = echartData.reduce((a, b) => {
          return a + b.value * 1
        }, 0);
        this.chart = echarts.init(this.$el, 'macarons')
        this.option={
          backgroundColor: bgColor,
          color: color,
          tooltip: {
            trigger: 'item'
          },
          title: [{
            text: '{val|' + formatNumber(total-item) + '}{name| 件}',
            top: '55%',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  color: '#666666',
                  padding: [10, 0]
                },
                val: {
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#1784FE',
                }
              }
            }
          },{
            text: '本期工单诉求大类前5类占比',
            top: 20,
            left: 'center',
            textStyle: {
              fontSize: 20,
              color: '#333',
            }
          }],
          series: [{
            type: 'pie',
            radius: ['45%', '60%'],
            center: ['50%', '60%'],
            data: echartData,
            hoverAnimation: true,
            itemStyle: {
              normal: {
                borderColor: bgColor,
                borderWidth: 0,
              }
            },
            labelLine: {
              normal: {
                show: true,
                length: 20,
                length2: 120,
                lineStyle: {
                  color: '#e6e6e6'
                }
              }
            },
            label: {
              normal: {
                show: true,
                formatter: params => {
                  return (
                    '{icon|●}{name|' + params.name + '}{value|' +
                    formatNumber(params.value) + '}'
                  );
                },
                padding: [0 , -100, 25, -100],
                rich: {
                  icon: {
                    fontSize: 14
                  },
                  name: {
                    fontSize: 12,
                    padding: [0, 10, 0, 4],
                    color: '#666666'
                  },
                  value: {
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#333333'
                  }
                }
              }
            },
          }]
        }
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }
      }

  }
}
</script>
