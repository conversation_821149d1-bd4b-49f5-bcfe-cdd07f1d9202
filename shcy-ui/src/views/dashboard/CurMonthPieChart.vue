<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

        this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {
    // this.shopDetail();

  },
  computed:{
  },
  methods: {
    initChart(num,title) {

      if(num)
      {
        let data=num.split("-");
        this.chart = null
        this.option = null
        let bgColor = 'transparent';
        let color = ['#0E7CE2', '#FF8352', '#E271DE', '#F8456B', '#00FFFF', '#4AEAB0'];
        let echartData = [{
          name: "",
          value: data[1]
        }
        ];

        let formatNumber = function(num) {
          let reg = /(?=(\B)(\d{3})+$)/g;
          return num.toString().replace(reg, ',');
        }
        let total = echartData.reduce((a, b) => {
          return a + b.value * 1
        }, 0);
        this.chart = echarts.init(this.$el, 'macarons')
        this.option ={
          backgroundColor: bgColor,
          color: color,
          // tooltip: {
          //     trigger: 'item'
          // },
          title: [{
            text: '{val|' + formatNumber(total) + '}{name| 件}',
            top: '55%',
            left: 'center',
            textStyle: {
              rich: {
                name: {
                  fontSize: 12,
                  fontWeight: 'normal',
                  color: '#666666',
                  padding: [10, 0]
                },
                val: {
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#1784FE',
                }
              }
            }
          },{
            text: title,
            top: 20,
            left: 'center',
            textStyle: {
              fontSize: 20,
              color: '#333',
            }
          }],
          series: [{
            type: 'pie',
            radius: ['45%', '60%'],
            center: ['50%', '60%'],
            data: echartData,
            hoverAnimation: true,
            itemStyle: {
              normal: {
                borderColor: bgColor,
                borderWidth: 0
              }
            },
            labelLine: {
              normal: {
                show: false,
                length: 20,
                length2: 120,
                lineStyle: {
                  color: '#e6e6e6'
                }
              }
            },
            label: {
              normal: {
                show: false,
                formatter: params => {
                  return (
                    '{icon|●}{name|' + params.name + '}{value|' +
                    formatNumber(params.value) + '}'
                  );
                },
                padding: [0 , -100, 25, -100],
                rich: {
                  icon: {
                    fontSize: 16
                  },
                  name: {
                    fontSize: 14,
                    padding: [0, 10, 0, 4],
                    color: '#666666'
                  },
                  value: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#333333'
                  }
                }
              }
            },
          }]
        };
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }

    }
  }
}
</script>
