<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
    }
  },
  mounted() {

    this.initChart()

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  created() {

  },
  computed:{
  },
  methods: {
    percentage(total,value,sj) {
      if (total === 0) {
        return 0;
      }
      let num=(value/ total) * 100;
      return (num).toFixed(2);
    },
    initChart(num,num1,num2,num3,sj) {
      if(num)
      {
        this.chart = null
        this.option = null
        const total=Number(num)+Number(num1)+Number(num2)+Number(num3);
        const n=num;
        const n1=num1;
        const n2=num2;
        const n3=num3;
        this.chart = echarts.init(this.$el, 'macarons')
        this.option = {
          backgroundColor:'transparent',
          title:{
            show: true,
            text:sj,
            textStyle:{
              color:'#1F1F1F'
            },
            left:'10%',
            top: '30'

          },
          tooltip: {
            show: false,
            formatter: "{b} <br> {c}%"

          },
          legend: {
            icon: "circle",
            bottom: '30%',
            left:'10%',
            itemWidth: 7,
            itemHeight: 7,
            itemGap: 40,
            textStyle:{
              fontSize:'16',
              color:'#89A7AF',
            },
            data:[{
              name :'满意'
            },
              {
                name :'基本满意'
              },
              {
                name :'一般'
              },
              {
                name :'不满意'
              }
            ]
          },
          xAxis: [{
            type :'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false,
            },
            splitArea:{show:false},
          }],
          yAxis: [{
            //type: 'category',
            data: [''],
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
              }
            }

          }],
          series: [
            {
              name:'满意',
              type:'bar',
              barWidth:30,
              stack: '危货种类占比',
              label: {
                normal: {
                  borderWidth: 10,
                  distance: 20,
                  align: 'center',
                  verticalAlign: 'middle',
                  borderRadius: 1,
                  borderColor: '#15C827',
                  backgroundColor: '#15C827',
                  show: true,
                  position: 'top',
                  formatter: '{c}件',
                  color: '#000'
                }
              },
              itemStyle: {
                color: '#15C827'
              },
              data:[{
                value:n,
                itemStyle: {
                  normal: {
                    color: {
                      type: 'bar',
                      colorStops: [{
                        offset: 0,
                        color: '#15C827' // 0% 处的颜色
                      }, {
                        offset: 1,
                        color: '#15C827' // 100% 处的颜色
                      }],
                      globalCoord: false, // 缺省为 false

                    }
                  }
                }
              }]
            },
            {
              name:'满意三角形',
              type:'line',
              barWidth:0,
              markPoint: {
                symbol:'triangle',
                symbolRotate:'180',
                itemStyle:{
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0, color: '#15C827' // 0% 处的颜色
                    }, {
                      offset: 1, color: '#15C827' // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                },
                symbolSize:[6,5],// 容器大小
                symbolOffset:[0,-15],//位置偏移
                data:[{
                  coord: [53.11/2]
                }],
                label: {
                  normal: {
                    show: false
                  },
                  offset: [0, 0],
                }
              }
            },
            {
              name:'基本满意',
              type:'bar',
              barWidth:30,
              stack: '危货种类占比',
              itemStyle: {
                color: '#1784FE'
              },
              label: {
                normal: {
                  borderWidth: 10,
                  distance: 20,
                  align: 'center',
                  verticalAlign: 'middle',
                  borderRadius: 1,
                  borderColor: '#1784FE',
                  backgroundColor: '#1784FE',
                  show: true,
                  position: 'top',
                  formatter: '{c}件',
                  color: '#000'
                }
              },
              data:[{
                value:n1,
                itemStyle: {
                  normal: {
                    color: {
                      type: 'bar',
                      colorStops: [{
                        offset: 0,
                        color: '#1784FE' // 0% 处的颜色
                      }, {
                        offset: 1,
                        color: '#1784FE' // 100% 处的颜色
                      }],
                      globalCoord: false, // 缺省为 false

                    }
                  }
                }
              }]
            },
            {
              name:'基本满意三角形',
              type:'line',
              barWidth:0,
              markPoint: {
                symbol:'triangle',
                symbolRotate:'180',
                itemStyle:{
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0, color: '#1784FE' // 0% 处的颜色
                    }, {
                      offset: 1, color: '#1784FE' // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                },
                symbolSize:[6,5],// 容器大小
                symbolOffset:[0,-15],//位置偏移
                data:[{
                  coord: [53.11 + 23/2]
                }],
                label: {
                  normal: {
                    show: false
                  },
                  offset: [0, 0],
                }
              }
            },
            {
              name:'一般',
              type:'bar',
              barWidth:30,
              stack: '危货种类占比',
              itemStyle: {
                color: '#FF9D43'
              },
              label: {
                normal: {
                  borderWidth: 10,
                  distance: 20,
                  align: 'center',
                  verticalAlign: 'middle',
                  borderRadius: 1,
                  borderColor: '#FF9D43',
                  backgroundColor: '#FF9D43',
                  show: true,
                  position: 'top',
                  formatter: '{c}件',
                  color: '#000'
                }
              },
              data:[{
                value:n2,
                itemStyle: {
                  normal: {
                    color: {
                      type: 'bar',
                      colorStops: [{
                        offset: 0,
                        color: '#FF9D43' // 0% 处的颜色
                      }, {
                        offset: 1,
                        color: '#FF9D43' // 100% 处的颜色
                      }],
                      globalCoord: false, // 缺省为 false

                    }
                  }
                }
              }]
            },
            {
              name:'一般三角形',
              type:'line',
              barWidth:0,
              markPoint: {
                symbol:'triangle',
                symbolRotate:'180',
                itemStyle:{
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0, color: '#FF9D43' // 0% 处的颜色
                    }, {
                      offset: 1, color: '#FF9D43' // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                },
                symbolSize:[6,5],// 容器大小
                symbolOffset:[0,-15],//位置偏移
                data:[{
                  coord: [53.11 + 23 + 1/2]
                }],
                label: {
                  normal: {
                    show: false
                  },
                  offset: [0, 0],
                }
              }
            },
            {
              name:'不满意',
              type:'bar',
              barWidth:30,
              stack: '危货种类占比',
              itemStyle: {
                color: '#F23318'
              },
              label: {
                normal: {
                  borderWidth: 10,
                  distance: 20,
                  align: 'center',
                  verticalAlign: 'middle',
                  borderRadius: 1,
                  borderColor: '#F23318',
                  backgroundColor: '#F23318',
                  show: true,
                  position: 'top',
                  formatter: '{c}件',
                  color: '#000'
                }
              },
              data:[{
                value:n3,
                itemStyle: {
                  normal: {
                    color: {
                      type: 'bar',
                      colorStops: [{
                        offset: 0,
                        color: '#F23318' // 0% 处的颜色
                      }, {
                        offset: 1,
                        color: '#F23318' // 100% 处的颜色
                      }],
                      globalCoord: false, // 缺省为 false

                    }
                  }
                }
              }]
            },
            {
              name:'不满意三角形',
              type:'line',
              barWidth:0,
              markPoint: {
                symbol:'triangle',
                symbolRotate:'180',
                itemStyle:{
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0, color: '#F23318' // 0% 处的颜色
                    }, {
                      offset: 1, color: '#F23318' // 100% 处的颜色
                    }],
                    globalCoord: false // 缺省为 false
                  }
                },
                symbolSize:[6,5],// 容器大小
                symbolOffset:[0,-15],//位置偏移
                data:[{
                  coord: [53.11 + 23 + 1/2]
                }],
                label: {
                  normal: {
                    show: false
                  },
                  offset: [0, 0],
                }
              }
            }
          ]
        };
        if (this.option && typeof this.option === 'object') {
          this.chart.clear();
          this.chart.setOption(this.option, true)
        }
      }

    }
  }
}
</script>
