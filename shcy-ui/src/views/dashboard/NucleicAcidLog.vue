<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            今日巡查从业人员数
          </div>
          <div class="card-panel-num" >{{employeesNucleicAcidList.checkEmployeesTotal}}</div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('messages')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            今日核酸正常人员数
          </div>
          <div class="card-panel-num" >{{employeesNucleicAcidList.checkNoramEmployeesNum}}</div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('purchases')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
           <div class="card-panel-text">
          今日核酸异常人员数
          </div>
          <div class="card-panel-num" >{{employeesNucleicAcidList.checkAbnormalEmployeesNum}}</div>
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('shoppings')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            今日离岗人员数
          </div>
          <div class="card-panel-num" >{{employeesNucleicAcidList.checkRetiredEmployeesNum}}</div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
import { shopData,committeeShopData } from "@/api/shcy/shop";
import {getEmployeeNucleicAcidDetail} from "@/api/shcy/nucleicAcidLog";

export default {
  components: {
    CountTo
  },
  data() {
    return{
      shopDatailList:[],            //店铺详细列表数据
      employeesNucleicAcidList:[],  //从业人员核酸信息
      committeeShopList:[],         //居委会管理店铺信息
    }
  },
  created() {
    // this.shopDetail();
    this.employeeNucleicAcidDetail()
    // this.committeeShopDetail()
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    // //店铺详细数据
    // shopDetail(){
    //   shopData().then(response=>{
    //     if(response.code===200){
    //       console.log(response.data)
    //       this.shopDatailList = response.data
    //
    //     }
    //   })
    // },
    //从业人员核酸数据
    employeeNucleicAcidDetail(){
      getEmployeeNucleicAcidDetail().then(response=>{
        if(response.code === 200){
          this.employeesNucleicAcidList = response.data
          console.log("从业人员核酸信息")
          console.log(this.employeesNucleicAcidList)
        }
      })
    },

    // committeeShopDetail(){
    //   committeeShopData().then(response=>{
    //     if(response.code===200){
    //       this.committeeShopList= response.data
    //       console.log(this.committeeShopList)
    //     }
    //   })
    // },
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
