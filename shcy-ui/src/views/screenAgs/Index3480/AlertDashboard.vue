<template>
  <div class="alert-dashboard">
    <div class="dashboard-container">
      <!-- 左侧按钮区域 -->
      <div class="left-panel">
        <div class="time-buttons">
          <button
            v-for="period in timePeriods"
            :key="period.key"
            :class="['time-btn', { active: activePeriod === period.key }]"
            @click="switchPeriod(period.key)"
          >
            {{ period.label }}
          </button>
        </div>
      </div>

      <!-- 中间饼图区域 -->
      <div class="center-panel">
        <div class="chart-container">
          <div ref="pieChart" class="pie-chart"></div>
          <!-- 添加图例说明 -->
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-dot rainwater"></span>
              <span class="legend-text">雨水报警事件总数</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot sewage"></span>
              <span class="legend-text">污水报警事件总数</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧TOP5区域 -->
      <div class="right-panel">
        <div class="top5-header">
          <span class="indicator"></span>
          <span class="title">事件报警TOP5</span>
        </div>
        <div class="top5-list">
          <div
            v-for="(item, index) in currentData.top5"
            :key="index"
            class="top5-item"
          >
            <span class="device-name">{{ item.name }}</span>
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ 
                  width: (item.value / currentData.maxValue * 100) + '%',
                  background: getTop5Color(item)
                }"
              ></div>
            </div>
            <span class="device-count">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {getAlertData} from "@/api/shcy/screenFxft";
import yuan from "@/assets/images/yuan.png"

export default {
  name: 'AlertDashboard',
  data() {
    return {
      activePeriod: 'today',
      pieChart: null,
      img: yuan,
      timePeriods: [
        { key: 'today', label: '今日统计' },
        { key: 'month', label: '本月统计' },
        { key: 'year', label: '本年统计' }
      ],
      alertData: {
        today: {
          total: 0,
          top5: [],
          maxValue: 0,
        },
        month: {
          total: 0,
          top5: [],
          maxValue: 0,
        },
        year: {
          total: 0,
          top5: [],
          maxValue: 0,
        }
      }
    }
  },
  computed: {
    currentData() {
      return this.alertData[this.activePeriod]
    }
  },
  mounted() {
    this.loadAlertData()
    // window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // if (this.pieChart) {
    //   this.pieChart.dispose()
    // }
    // window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    switchPeriod(period) {
      this.activePeriod = period
      this.updateCharts()
    },
    loadAlertData() {
      getAlertData().then(res => {
        if (res.code === 200 && res.data) {
          this.processAlertData(res.data)
          this.initCharts()
        }
      }).catch(err => {
        console.error('加载报警数据失败:', err)
        this.initCharts()
      })
    },
    processAlertData(data) {
      // 处理今日数据
      this.alertData.today = this.formatPeriodData(
        data.todayTotalCount || 0,
        data.todayTop5 || [],
        data.todayRainwaterCount || 0,
        data.todaySewageCount || 0
      )

      // 处理本月数据
      this.alertData.month = this.formatPeriodData(
        data.monthlyTotalCount || 0,
        data.monthlyTop5 || [],
        data.monthlyRainwaterCount || 0,
        data.monthlySewageCount || 0
      )

      // 处理本年数据
      this.alertData.year = this.formatPeriodData(
        data.yearlyTotalCount || 0,
        data.yearlyTop5 || [],
        data.yearlyRainwaterCount || 0,
        data.yearlySewageCount || 0
      )
    },
    formatPeriodData(total, top5List, rainwaterCount, sewageCount) {
      const formattedTop5 = top5List.map(item => ({
        name: item.deviceName,
        value: item.count,
        monitoredWaterBody: item.monitoredWaterBody
      }))

      const maxValue = formattedTop5.length > 0 ? Math.max(...formattedTop5.map(item => item.value)) : 0

      return {
        total,
        top5: formattedTop5,
        maxValue,
        rainwaterCount,
        sewageCount
      }
    },
    initCharts() {
      this.initPieChart()
    },
    initPieChart() {
      this.pieChart = echarts.init(this.$refs.pieChart)
      this.updatePieChart()
    },
    updateCharts() {
      this.updatePieChart()
    },
    updatePieChart() {
      const currentData = this.currentData
      const totalCount = currentData.rainwaterCount + currentData.sewageCount
      
      const option = {
        color: [
          '#3478FB',  // 雨水颜色
          '#DB5128'   // 污水颜色
        ],
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        title: {
          text: '{a|报警事件总数\n' + totalCount  + '}',
          x: 'center',
          y: 'center',
          textStyle: {
            rich: {
              a: {
                fontSize: 14,
                color: '#fff',
                lineHeight: 18
              },
            },
          },
        },
        graphic: {
          elements: [
            {
              type: 'image',
              z: 3,
              style: {
                image: this.img,
                width: 100,
                height: 100,
              },
              left: 'center',
              top: 'center',
            },
          ],
        },
        series: [
          {
            type: 'pie',
            hoverAnimation: true,
            hoverOffset: 5,
            startAngle: 180, //起始角度
            clockwise: false, //是否顺时针
            radius: ['60%', '75%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: currentData.rainwaterCount,
                name: '雨水报警事件总数',
              },
              {
                value: currentData.sewageCount,
                name: '污水报警事件总数',
              }
            ],
            itemStyle: {
              normal: {
                shadowColor: '#37B4CD',
                shadowBlur: 30,
                shadowOffsetY: 10
              },
            },
            zlevel: 30,
          },
        ],
      }
      this.$nextTick(() => {
        this.pieChart.setOption(option)
      })
    },
    handleResize() {
      if (this.pieChart) {
        this.pieChart.resize()
      }
    },
    getTop5Color(item) {
      // 根据监测水体类型返回对应颜色
      if (item.monitoredWaterBody && item.monitoredWaterBody.includes('雨水')) {
        return '#3478FB'  // 雨水颜色
      } else if (item.monitoredWaterBody && item.monitoredWaterBody.includes('污水')) {
        return '#DB5128'  // 污水颜色
      }
      // 默认颜色（如果类型未知）
      return '#a0aec0'
    }
  }
}
</script>

<style scoped>
.alert-dashboard {
  padding: 10px;
}

.dashboard-container {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  height: 250px;
}

.left-panel {
  flex: 0 0 150px;
}

.time-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.time-btn {
  padding: 10px 20px;
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid #4a5568;
  border-radius: 8px;
  color: #a0aec0;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.time-btn:hover {
  border-color: #00d4ff;
  color: #00d4ff;
}

.time-btn.active {
  background: linear-gradient(135deg, #245DEF 0%, #12217B 100%);
  border-color: #00d4ff;
  color: white;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.center-panel {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.pie-chart {
  width: 300px;
  height: 300px;
}

.chart-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-number {
  font-size: 48px;
  font-weight: bold;
  color: #00d4ff;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  margin-bottom: 5px;
}

.chart-label {
  font-size: 14px;
  color: #a0aec0;
}

.right-panel {
  flex: 0 0 300px;
}

.top5-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.indicator {
  width: 8px;
  height: 8px;
  background: #00d4ff;
  border-radius: 50%;
  margin-right: 10px;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

.title {
  color: #00d4ff;
  font-size: 18px;
  font-weight: bold;
}

.top5-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.top5-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.device-name {
  flex: 0 0 80px;
  color: #a0aec0;
  font-size: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(74, 85, 104, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.device-count {
  flex: 0 0 30px;
  color: #00d4ff;
  font-size: 14px;
  text-align: right;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.legend-dot.rainwater {
  background-color: #3478FB;
}

.legend-dot.sewage {
  background-color: #DB5128;
}

.legend-text {
  color: #a0aec0;
  font-size: 14px;
}

</style>
