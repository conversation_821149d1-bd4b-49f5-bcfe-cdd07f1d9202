<template>
  <div>
    <!-- 设备在线情况等4块内容开始 -->
    <div class="label_bg">
      <div class="label_item_bg" @mouseover="setLabelPosition(0)">
        <div class="label_icon_sbzxqk"></div>
        <div class="label_title">设备在线情况</div>
      </div>
      <div class="label_item_bg" @mouseover="setLabelPosition(1)">
        <div class="label_icon_lhsj"></div>
        <div class="label_title">绿化数据</div>
      </div>
      <div class="label_item_bg" @mouseover="setLabelPosition(2)">
        <div class="label_icon_hdsj"></div>
        <div class="label_title">河道数据</div>
      </div>
      <div class="label_item_bg" @mouseover="setLabelPosition(3)">
        <div class="label_icon_xzsyztqk"></div>
        <div class="label_title">现状使用主体情况</div>
      </div>
      <div class="item_item_bg_press1" v-show="labelPosition === 0"></div>
      <div class="item_item_bg_press2" v-show="labelPosition === 1"></div>
      <div class="item_item_bg_press3" v-show="labelPosition === 2"></div>
      <div class="item_item_bg_press4" v-show="labelPosition === 3"></div>
    </div>
    <div class="content-label" v-show="labelPosition === 0">
      <div class="content-label-sbzxqk-bg">
        <div class="content-label-fxft-bg" @click="openLiquidLevelDevicePage">
          <div class="content-label-fxft-bg-1">
            <span style="font-size: 60px;font-weight: bold;text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">{{deviceStatusNum.fxftOnlineNum}}</span>
            <span style="font-size: 24px;margin: 0 10px">/</span>
            <span style="font-size: 24px;text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">{{deviceStatusNum.fxftTotalNum}}</span>
          </div>
        </div>
        <div class="content-label-hjzz-bg">
          <div class="content-label-fxft-bg-2">
            <span style="font-size: 60px;font-weight: bold;text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">{{deviceStatusNum.hjzzOnlineNum}}</span>
            <span style="font-size: 24px;margin: 0 10px">/</span>
            <span style="font-size: 24px;text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">{{deviceStatusNum.hjzzTotalNum}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content-label" v-show="labelPosition === 1">
      <div class="content-label-item-bg">
        <div class="content-label-item">
          <div class="content-lebel-item-icon-lhmj"></div>
          <div class="label-item-title">绿化面积</div>
          <div class="label-item-data">5.325</div>
          <div class="label-item-data-dw">平方公里</div>
        </div>
      </div>
      <div class="content-label-item-bg">
        <div class="content-label-item">
          <div class="content-lebel-item-icon-lhmj"></div>
          <div class="label-item-title">绿化覆盖率</div>
          <div class="label-item-data">27.85</div>
          <div class="label-item-data-dw">%</div>
        </div>
      </div>
      <div class="content-label-item-bg" @click="showGreeningInMap('绿化信息', ['商品房绿化', '售后公房绿化'])">
        <div class="content-label-item">
          <div class="content-lebel-item-icon-lhfgl"></div>
          <div class="label-item-title">居住绿化面积</div>
          <div class="label-item-data">0.889</div>
          <div class="label-item-data-dw">平方公里</div>
        </div>
      </div>
      <div class="content-label-item-bg" @click="showGreeningInMap('绿化信息', ['单位绿地'])">
        <div class="content-label-item">
          <div class="content-lebel-item-icon-jzlhmj"></div>
          <div class="label-item-title">单位绿化面积</div>
          <div class="label-item-data">2.305</div>
          <div class="label-item-data-dw">平方公里</div>
        </div>
      </div>
      <div class="content-label-item-bg" @click="showGreeningInMap('绿化信息', ['公共绿地', '行道树'])">
        <div class="content-label-item">
          <div class="content-lebel-item-icon-dwlhmj"></div>
          <div class="label-item-title">公共绿化面积</div>
          <div class="label-item-data">1.893</div>
          <div class="label-item-data-dw">平方公里</div>
        </div>
      </div>
    </div>
    <div class="content-label content-label-river" v-show="labelPosition === 2">
      <div class="content-label-river-title" style="left:141px;top:6px;" title="河道总数" @click="showInMapData('河道总数')">
        <div>河道总数</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">20</span>条</div>
      </div>
      <div class="content-label-river-title" style="left:37px;top:80px;" title="区管河道" @click="showInMapData('区管河道')">
        <div>区管河道</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">5</span>条</div>
      </div>
      <div class="content-label-river-title" style="left:141px;top:155px;" title="镇管河道" @click="showInMapData('镇管河道')">
        <div>镇管河道</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">1</span>条</div>
      </div>
      <div class="content-label-river-title" style="left:612px;top:6px;" title="村管河道" @click="showInMapData('村管河道')">
        <div>村管河道</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">1</span>条</div>
      </div>
      <div class="content-label-river-title" style="left:715px;top:80px;" title="其它河道" @click="showInMapData('其它河道')">
        <div>其它河道</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">9</span>条</div>
      </div>
      <div class="content-label-river-title" style="left:606px;top:155px;" title="股份公司河道" @click="showInMapData('股份公司河道')">
        <div>股份公司河道</div>
        <div style="margin-top: 25px;"><span style="font-size: 24px;margin-right: 5px;">4</span>条</div>
      </div>
      <div class="content-label-river-center-title">河道长度</div>
      <div class="content-label-river-center-data">14.12</div>
      <div class="content-label-river-center-dw">公里</div>
    </div>
    <div class="content-label" v-show="labelPosition === 3">
      <div class="content-label-xzsyztqk-item" style="width: 206px;background: #01152A;">
        <div class="qy-icon"></div>
        <div class="qy-content" style="width:160px;">
          <div class="qy-content-item" @click="showInMapData('生产型企业')">生产型企业</div>
          <div class="qy-content-item" style="margin-top: 9px;" @click="showInMapData('生产型企业-二工区')">生产型企业-二工区</div>
        </div>
      </div>
      <div class="content-label-xzsyztqk-item" style="width: 374px;background: #05231B;">
        <div class="school-icon"></div>
        <div class="school-content">
          <div class="school-cotent-item" @click="showInMapData('幼儿园')">幼儿园</div>
          <div class="school-cotent-item" style="margin-left: 10px;" @click="showInMapData('高中')">高中</div>
          <div class="school-cotent-item" style="margin-top: 10px;" @click="showInMapData('小学')">小学</div>
          <div class="school-cotent-item" style="margin-top: 10px;margin-left: 10px;" @click="showInMapData('功能型学校')">功能型学校</div>
          <div class="school-cotent-item" style="margin-top: 10px;" @click="showInMapData('初中')">初中</div>
          <div class="school-cotent-item" style="margin-top: 10px;margin-left: 10px;" @click="showInMapData('辅读学校')">辅读学校</div>
          <div class="school-cotent-item" style="margin-top: 10px;" @click="showInMapData('中职')">中职</div>
        </div>
      </div>
      <div class="content-label-xzsyztqk-item" style="width: 162px;background: #093335;">
        <div class="zc-icon"></div>
        <div class="zc-content" style="width:120px;">
          <div class="zc-content-item" @click="showInMapData('国有资产')">
            <div>国有资产</div>
            <div style="margin-top: 10px;"><span style="font-size:24px;font-weight:bold;margin-right: 5px;">76</span>家</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 设备在线情况等4块内容结束 -->
  </div>
</template>

<script>
export default {
  name: "DeviceStatusTabs",
  props: {
    labelPosition: {
      type: Number,
      default: 0
    },
    deviceStatusNum: {
      type: Object,
      default: () => ({
        fxftOnlineNum: 0,
        fxftTotalNum: 0,
        hjzzOnlineNum: 0,
        hjzzTotalNum: 0
      })
    }
  },
  methods: {
    setLabelPosition(pos) {
      this.$emit('set-label-position', pos);
    },
    showGreeningInMap(type, value) {
      this.$emit('show-greening-in-map', type, value);
    },
    showInMapData(type) {
      this.$emit('show-in-map-data', type);
    },
    openLiquidLevelDevicePage() {
      this.$emit('open-liquid-level-device-page');
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style> 