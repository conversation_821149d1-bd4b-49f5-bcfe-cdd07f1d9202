<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import * as echarts from 'echarts'
export default {
  name: "SqlbChart",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    chartData: {
      type: Array,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      xData2:[],
      chartData1:[],
      data1:[],
      data2: [],
      barWidth : 30,
      total:0,
    }
  },
  computed: {
    option() {
      function contains(arr, dst) {
        var i = arr.length;
        while ((i -= 1)) {
          if (arr[i] == dst) {
            return i;
          }
        }
        return false;
      }


      var attackSourcesColor = [
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: "#EB3B5A" },
          { offset: 1, color: "#FE9C5A" }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: "#FA8231" },
          { offset: 1, color: "#FFD14C" }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: "#F7B731" },
          { offset: 1, color: "#FFEE96" }
        ]),
        new echarts.graphic.LinearGradient(0, 1, 1, 1, [
          { offset: 0, color: "#395CFE" },
          { offset: 1, color: "#2EC7CF" }
        ])
      ];
      var attackSourcesColor1 = [
        "#EB3B5A",
        "#FA8231",
        "#F7B731",
        "#3860FC",
        "#1089E7",
        "#F57474",
        "#56D0E3",
        "#1089E7",
        "#F57474",
        "#1089E7",
        "#F57474",
        "#F57474"
      ];
      var attaData = [];
      var attaName = [];
      var topName=[]
      var opt = {
        index: 0
      }
      this.data1.forEach((it, index) => {
        attaData[index] = parseFloat(it.fundPost).toFixed(2);
        attaName[index] = it.fundPost;
        topName[4-index] = `${it.code}`
      });
      var salvProMax = []; //背景按最大值
      for (let i = 0; i < attaData.length; i++) {
        salvProMax.push(this.total);
      }
      function attackSourcesDataFmt(sData) {
        var sss = [];
        sData.forEach(function(item, i) {
          let itemStyle = {
            color: i > 3 ? attackSourcesColor[3] : attackSourcesColor[i]
          };
          sss.push({
            value: item,
            itemStyle: itemStyle
          });
        });
        return sss;
      }
      return  {
        backgroundColor: "transparent",
        tooltip: {
          show: false,
          backgroundColor: "rgba(3,169,244, 0.5)", //背景颜色（此时为默认色）
          textStyle: {
            fontSize: 16
          }
        },
        color: ["#F7B731"],
        legend: {
          pageIconSize: [12, 12],
          itemWidth: 20,
          itemHeight: 10,
          textStyle: {
            //图例文字的样式
            fontSize: 10,
            color: "#fff"
          },
          selectedMode: false,
          data: [""]
        },
        grid: {
          left: "-50",
          right: "0",
          width:"120%",
          bottom: "0%",
          top: "0%",
          containLabel: true
        },
        xAxis: {
          type: "value",

          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        yAxis: [
          {
            type: "category",
            inverse: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisPointer: {
              label: {
                show: true,
                //margin: 30
              }
            },
            padding: [5, 0, 0, 0],
            postion: "right",
            data: attaName,
            axisLabel: {
              margin: 20,
              fontSize: 10,
              align: "left",
              padding: [2, 0, 0, 0],
              color: "#000",
              rich: {
                nt0: {
                  color: "#fff",
                  backgroundColor: attackSourcesColor1[0],
                  width: 13,
                  height: 13,
                  fontSize: 10,
                  align: "center",
                  borderRadius: 100,
                  lineHeight: "5",
                  padding: [0, 1, 2, 1]
                  // padding:[0,0,2,0],
                },
                nt1: {
                  color: "#fff",
                  backgroundColor: attackSourcesColor1[1],
                  width: 13,
                  height: 13,
                  fontSize: 10,
                  align: "center",
                  borderRadius: 100,
                  padding: [0, 1, 2, 1]
                },
                nt2: {
                  color: "#fff",
                  backgroundColor: attackSourcesColor1[2],
                  width: 13,
                  height: 13,
                  fontSize: 10,
                  align: "center",
                  borderRadius: 100,
                  padding: [0, 1, 2, 1]
                },
                nt: {
                  color: "#fff",
                  backgroundColor: attackSourcesColor1[3],
                  width: 13,
                  height: 13,
                  fontSize: 10,
                  align: "center",
                  borderRadius: 100,
                  padding: [0, 1, 2, 1],
                  lineHeight: 5
                }
              },
              formatter: function(value, index) {
                if (opt.index === 0 && index < 3) {
                  return ["{nt" + index + "|" + (1 + index) + "}"].join("\n");
                } else if (opt.index !== 0 && (index + opt.index) < 9) {
                  return ["{nt|0" + (1 + index + opt.index) + "}"].join("\n");
                } else {
                  return ["{nt|" + (1 + index + opt.index) + "} "].join("\n");
                }
                // index = contains(attaName, value) + 1;
                // if (index - 1 < 3) {
                //   return ["{nt" + index + "|" + index + "}"].join("\n");
                // } else {
                //   return ["{nt|" + index + "}"].join("\n");
                // }
              }
            }
          },
          {
            type: "category",
            inverse: true,
            axisTick: "none",
            axisLine: "none",
            show: true,
            axisLabel: {
              textStyle: {
                color: "#fff",
                fontSize: "10"
              }
            },
            data: attackSourcesDataFmt(attaName)
          },
          {//名称
            type: 'category',
            offset: -10,
            position: "left",
            axisLine: {
              show: false
            },
            inverse: false,
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0,
              color: ["#fff"],
              align: "left",
              verticalAlign: "bottom",
              lineHeight: 32,
              fontSize: 10
            },
            data: topName
          },
        ],
        series: [
          {
            zlevel: 1,
            name: "",
            type: "bar",
            margin:0,
            barWidth: "15px",
            animationDuration: 1500,
            data: attackSourcesDataFmt(attaData),
            align: "center",
            itemStyle: {
              normal: {
                barBorderRadius: 10
              }
            },
            label: {
              show: false,
              fontSize: 10,
              color: "#fff",
              textBorderWidth: 2,
              padding: [2, 0, 0, 0]
            }
          },
          {
            name: "",
            type: "bar",
            barWidth: 15,
            barGap: "-100%",
            margin: "0",
            data: salvProMax,
            textStyle: {
              //图例文字的样式
              fontSize: 10,
              color: "#fff"
            },
            itemStyle: {
              normal: {
                color: "#05325F",
                //width:"100%",
                fontSize: 10,
                barBorderRadius: 30
              },
            }
          }
        ]
      };

    },
  },
  mounted() {
    this.init()
  },
  watch:{
    chartData(newData, oldData)
    {
      this.init(newData);
    }

  },
  methods: {
    init(value) {
     if(value)
      {
        this.chartData1=value;
        this.initChartData()
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })

        this.chart.on('click',params=> {
          this.$emit('chart-value-selected', params.dataIndex);
        })
      }

    },
    initChartData() {
      if(this.chartData1.length>0)
      {
        this.total=this.chartData1[0].sqdlCount;
        for(let i=0;i<5;i++) {
          let form={};
          form.code=this.chartData1[i].sqdl;
          form.fundPost=this.chartData1[i].sqdlCount;
          this.data1.push(form);
        }
      }

    }
  }
}
</script>
