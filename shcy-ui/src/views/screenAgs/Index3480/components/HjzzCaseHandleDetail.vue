<template>
  <div class="detail-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ title }}</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;display: flex;justify-content: center;">
      <el-image style="width: 750px;" v-if="caseInfo.photoUrls != null && caseInfo.photoUrls.length > 0" :src="caseInfo.photoUrls[0]" :preview-src-list="caseInfo.photoUrls" fit="contain"></el-image>
    </div>
  </div>
</template>

<script>

export default {
  name: "HjzzCaseHandleWindow",
  props: {
    caseInfo:{
      type: Object,
      default: {}
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      display: false,
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.detail-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 800px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("~@/assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 750px;
    max-height: 650px;
  }

}

</style>
