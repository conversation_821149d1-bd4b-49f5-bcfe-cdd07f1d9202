<template>
  <div :style="{height:height,width:width}"/>
</template>

<script>
import echarts from 'echarts'

import {getDumpingGarbageTypeCount, getIsFilingCount} from "@/api/shcy/screen";

export default {
  name: "HjzzEventStatistic",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      backgroundColor: '',
      // 颜色系列
      color: ['#37FFC9', '#FFE777', '#19D6FF', '#32A1FF', '#cccccc', '#ff1111'],
      radius: ['60%', '64%'],
      radius2: [],
      radius3: [],
      // 数据
      chartData: [
        // {
        //   name: '生活垃圾',
        //   value: 0
        // },
        // {
        //   name: '建筑垃圾、渣土',
        //   value: 0
        // },
        // {
        //   name: '偷倒垃圾',
        //   value: 8
        // },
        // {
        //   name: '偷倒水泥废料',
        //   value: 2
        // }
      ],
      sum: 0,
      data1: [],
      color1: [],
      color2: [],
      color3: []
    }
  },
  computed: {
    option() {
      return{
        backgroundColor: this.backgroundColor,
        grid: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
          containLabel: false
        },
        tooltip: {
          formatter: (params) => {
            if (params.name !== '') {
              return (
                params.name +
                ' : ' +
                params.value +
                '\n' +
                '(' +
                params.percent +
                '%)'
              );
            }
          }
        },
        legend: {
          top: '375px',
          orient: 'vertical',
          textStyle: {
            color: '#B6B6BD',
            fontSize: '12px'
          },
          icon: "circle",
          itemWidth: 30,  // 设置宽度
          itemHeight: 12, // 设置高度
          itemGap: 4 // 设置间距
        },
        graphic:{
          elements:[
            {
              type: 'text',
              left: 'center',
              top: '45%',
              z: 2,
              zlevel: 100,
              style: {
                text: '处置量',
                fill: '#B6B6BD',
                font: '12px Microsoft YaHei'
              }
            },
            {
              type: 'text',
              left: 'center',
              top: '50%',
              z: 2,
              zlevel: 100,
              style: {
                text: this.sum,
                  fill: '#FFFFFF',
                  font: '24px Microsoft YaHei'
              }
            }
          ]
        },
        series: [
          // 最外层圆环
          {
            type: 'pie',
            radius: this.radius3,
            center: ['50%', '50%'],
            hoverAnimation: false,
            startAngle: 90,
            selectedMode: 'single',
            selectedOffset: 0,
            itemStyle: {
              normal: {
                color: (params) => {
                  return this.color1[params.dataIndex];
                }
              }
            },
            label: {
              show: false,
              position: 'outside',
              formatter: (params) => {
                let zzb = 0;
                this.data1.forEach((item, index) => {
                  // 当前值一半加上前面的值是否大于50%（判断label朝向）
                  if (index <= params.dataIndex && item.name != '') {
                    if (index == params.dataIndex) {
                      zzb += Number(item.value) / 2;
                    } else {
                      zzb += Number(item.value);
                    }
                  }
                });
                if (params.name != '') {
                  // 若当前值一半加上前面的值的占比大于0.5三角形朝右，相反朝左
                  if (zzb / this.sum > 0.5) {
                    return (
                      '{txt|' +
                      ((params.value * 100) / this.sum).toFixed(0) +
                      '%}' +
                      '\n{san|▶}'
                    );
                  } else {
                    return (
                      '{txt|' +
                      ((params.value * 100) / this.sum).toFixed(0) +
                      '%}' +
                      '\n{san|◀}'
                    );
                  }
                }
              },
              align: 'left',
              padding: [0, 0, 0, 0],
              rich: {
                txt: {
                  color: '#fff',
                  width: 10,
                  height: 10,
                  padding: [10, 10, 0, 24]
                },
                san: {
                  padding: [12, -3, -20, 10]
                }
              }
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 60,
              lineStyle: {
                color: '#fff',
                width: 2
              }
            },
            data: this.data1,
            z: 666
          },
          // 中间层圆环
          {
            type: 'pie',
            radius: this.radius2,
            center: ['50%', '50%'],
            hoverAnimation: false,
            startAngle: 90,
            selectedMode: 'single',
            selectedOffset: 0,
            itemStyle: {
              normal: {
                color: (params) => {
                  return this.color2[params.dataIndex];
                }
              }
            },
            label: {
              show: false,
              formatter: '{b}' + ' ' + '{c}'
            },
            data: this.data1,
            z: 666
          },
          // 最内层圆环
          {
            type: 'pie',
            radius: this.radius,
            center: ['50%', '50%'],
            hoverAnimation: false,
            startAngle: 90,
            selectedMode: 'single',
            selectedOffset: 0,
            itemStyle: {
              normal: {
                color: (params) => {
                  return this.color3[params.dataIndex];
                }
              }
            },
            label: {
              show: false,
              formatter: '{b}' + ' ' + '{c}'
            },
            data: this.data1,
            z: 666
          }
        ]
      }
    },
  },
  mounted() {
    this.init()
  },
  methods:{
    init() {
      getIsFilingCount().then(res=>{
        this.chartData = JSON.parse(JSON.stringify(res.data))
        this.initChartData()
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    },
    initChartData(){
      // 设置每层圆环颜色和添加间隔的透明色
      this.color.forEach((item) => {
        this.color1.push(item, 'transparent');
        this.color2.push(this.hexToRgba(item, 0.7), 'transparent');
        this.color3.push(this.hexToRgba(item, 0.4), 'transparent');
      });
      // 根据总值设置间隔值大小
      this.chartData.forEach((item) => {
        this.sum += Number(item.value);
      });
      // 给每个数据后添加特定的透明的数据形成间隔
      this.chartData.forEach((item, index) => {
        if (item.value !== 0) {
          this.data1.push(item, {
            name: '',
            // value: this.sum / 70,
            value: 0,
            labelLine: {
              show: false,
              lineStyle: {
                color: 'transparent'
              }
            },
            itemStyle: {
              color: 'transparent'
            }
          });
        }
      });
      // 每层圆环大小
      this.radius2 = [
        Number(this.radius[1].split('%')[0]) + 0 + '%',
        Number(this.radius[1].split('%')[0]) + 4 + '%'
      ],
      this.radius3 = [
        Number(this.radius[1].split('%')[0]) + 4 + '%',
        Number(this.radius[1].split('%')[0]) + 8 + '%'
      ]
    },
    //颜色16进制换算rgba,添加透明度
    hexToRgba(hex, opacity) {
      return (
        'rgba(' +
        parseInt('0x' + hex.slice(1, 3)) +
        ',' +
        parseInt('0x' + hex.slice(3, 5)) +
        ',' +
        parseInt('0x' + hex.slice(5, 7)) +
        ',' +
        opacity +
        ')'
      );
    }
  }
}
</script>

<style scoped>

</style>
