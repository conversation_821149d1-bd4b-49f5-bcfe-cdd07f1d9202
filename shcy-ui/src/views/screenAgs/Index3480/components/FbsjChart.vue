<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'
import {getCaseNumberBySubexecutedeptnameMh} from "@/api/shcy/screenAsjgl";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      chartData:[],
      m2R2Data:[],
      total:0,
    }
  },
  mounted() {

    this.init()

  },
  created() {

  },
  computed:{
    option() {
      return{
        title: [
          {
            text: '标题',
            textStyle: {
              fontSize: 16,
              color: "black"
            },
            left: "2%"
          },
          {
            text: '合计',
            subtext: this.total+'个',
            textStyle:{
              fontSize:14,
              color:"#fff"
            },
            subtextStyle: {
              fontSize: 14,
              color: '#fff'
            },
            textAlign:"center",
            x: '22.5%',
            y: '35%',
          }],
        tooltip: {
          trigger: 'item',
          formatter:function (parms){
            var str=  parms.seriesName+"</br>"+
              parms.marker+""+parms.data.legendname+"</br>"+
              "数量："+ parms.data.value+"</br>"+
              "占比："+ parms.percent+"%";
            return  str ;
          }
        },
        legend: {
          type:"scroll",
          orient: 'vertical',
          left:'50%',
          align:'left',
          top:'middle',
          textStyle: {
            color:'#fff'
          },
          height:200,
        },
        series: [
          {
            name:'',
            type:'pie',
            center: ['25%', '45%'],
            radius: ['60%', '75%'],
            clockwise: false, //饼图的扇区是否是顺时针排布
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'outter',
                formatter:function (parms){
                  return parms.data.legendname
                }
              }
            },
            labelLine: {
              normal: {
                length:5,
                length2:3,
                smooth:true,
              }
            },
            data:this.m2R2Data
          }
        ]
      }
    }
  },
  methods: {
    init() {
      getCaseNumberBySubexecutedeptnameMh().then(res=>{
        this.chartData = JSON.parse(JSON.stringify(res.data))
         this.initChartData()
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    },
    initChartData() {
      const color = [{
        r: 141,
        g: 127,
        b: 236
      }, {
        r: 45,
        g: 238,
        b: 177
      }, {
        r: 54,
        g: 90,
        b: 206
      }, {
        r: 226,
        g: 80,
        b: 163
      }, {
        r: 242,
        g: 171,
        b: 68
      }, {
        r: 245,
        g: 206,
        b: 69
      }, {
        r: 147,
        g: 233,
        b: 0
      }, {
        r: 212,
        g: 189,
        b: 184
      }, {
        r: 141,
        g: 127,
        b: 236
      }, {
        r: 80,
        g: 133,
        b: 242
      }, {
        r: 231,
        g: 95,
        b: 195
      }, {
        r: 248,
        g: 123,
        b: 226
      }, {
        r: 242,
        g: 113,
        b: 154
      }, {
        r: 252,
        g: 164,
        b: 187
      }, {
        r: 245,
        g: 154,
        b: 143
      }, {
        r: 253,
        g: 179,
        b: 1
      }, {
        r: 87,
        g: 231,
        b: 236
      },
        {
          r: 207,
          g: 158,
          b: 241
        }, {
          r: 87,
          g: 231,
          b: 236
        }, {
          r: 207,
          g: 158,
          b: 241
        }];
      const startAlpha = 1;
      const endAlpha = 1;
      for (let i = 0; i < this.chartData.subexecutedeptnameMhList.length; i++) {
        const subexecutedeptnameMh = this.chartData.subexecutedeptnameMhList[i];
        const li = JSON.stringify(this.chartData.caseNumberBySubexecutedeptnameMhList, [subexecutedeptnameMh]);
        const jsonArray = JSON.parse(li);
        if (li.length > 2) {
          for (const key in jsonArray) {
            this.total=this.total+Number(jsonArray[key]);
            var form = {};
            form.legendname = subexecutedeptnameMh;
            form.value = jsonArray[key];
            form.name = subexecutedeptnameMh + " " + jsonArray[key];
            var form1 = {
              color: {
                type: "linear",
                global: false,
                // 这里给了 线性渐变的起止颜色
                colorStops: [{
                  offset: 0,
                  color: `rgba(${color[i].r},${color[i].g},${color[i].b},${startAlpha})`
                }, {
                  offset: 1,
                  color: `rgba(${color[i].r},${color[i].g},${color[i].b},${endAlpha})`
                }]
              }
            };
            form.itemStyle = form1;
            this.m2R2Data.push(form);
          }
        } else {
          var form = {};
          form.legendname = subexecutedeptnameMh;
          form.value = 0;
          form.name = subexecutedeptnameMh + "  0";
          var form1 = {
            color: {
              type: "linear",
              global: false,
              // 这里给了 线性渐变的起止颜色
              colorStops: [{
                offset: 0,
                color: `rgba(${color[i].r},${color[i].g},${color[i].b},${startAlpha})`
              }, {
                offset: 1,
                color: `rgba(${color[i].r},${color[i].g},${color[i].b},${endAlpha})`
              }]
            }
          };
          form.itemStyle = form1;
          this.m2R2Data.push(form);
        }
      }
    }
  }
}
</script>
