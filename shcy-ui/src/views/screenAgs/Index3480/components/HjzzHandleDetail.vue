<template>
  <div class="container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">环境整治处置详情</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-content">
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="发现时间" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ alarmRecord.alarmDate }}
        </el-descriptions-item>
        <el-descriptions-item label="事件地点" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ hjzzCase.address }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌信息" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ hjzzCase.licensePlate }}
        </el-descriptions-item>
        <el-descriptions-item label="偷倒垃圾类型" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ hjzzCase.dumpingGarbageType }}
        </el-descriptions-item>
        <el-descriptions-item label="抓拍图片" :label-style="this.labelStyle" :content-style="this.contentStyle"
                              :span="2">
          <el-image style="width: 200px; margin-right: 10px;"
                    v-if="hjzzCase.photoUrls != null && hjzzCase.photoUrls.length > 0"
                    v-for="(photoUrl, index) in hjzzCase.photoUrls" :key="index" lazy :src="photoUrl"
                    :preview-src-list="hjzzCase.photoUrls" fit="contain"></el-image>
        </el-descriptions-item>
        <template v-if="hjzzCase.isPersonalBehavior === '是'">
          <el-descriptions-item label="是否个人行为" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.isPersonalBehavior }}
          </el-descriptions-item>
          <el-descriptions-item label="是否立案" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.isFiling }}
          </el-descriptions-item>
          <el-descriptions-item label="姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.personnelInfo }}
          </el-descriptions-item>
          <el-descriptions-item label="身份证号码" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ desensitizedIdCard }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ desensitizedContactInfo }}
          </el-descriptions-item>
          <el-descriptions-item v-if="hjzzCase.isFiling !== '否'" label="处罚案件号" :label-style="this.labelStyle" :content-style="this.contentStyle"
                                :span="2">
            {{ hjzzCase.penaltyCaseNo }}
          </el-descriptions-item>
        </template>
        <template v-if="hjzzCase.isPersonalBehavior === '否'">
          <el-descriptions-item label="是否个人行为" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.isPersonalBehavior }}
          </el-descriptions-item>
          <el-descriptions-item label="是否立案" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.isFiling }}
          </el-descriptions-item>
          <el-descriptions-item label="公司名称" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.companyName }}
          </el-descriptions-item>
          <el-descriptions-item label="公司地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ hjzzCase.companyAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="法人姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ desensitizedLegalName }}
          </el-descriptions-item>
          <el-descriptions-item label="身份证号码" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ desensitizedIdCard }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
            {{ desensitizedContactInfo }}
          </el-descriptions-item>
          <el-descriptions-item v-if="hjzzCase.isFiling !== '否'" label="处罚案件号" :label-style="this.labelStyle" :content-style="this.contentStyle"
                                :span="2">
            {{ hjzzCase.penaltyCaseNo }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
  </div>
</template>

<script>

export default {
  name: "HjzzHandleWindow",
  props: {
    hjzzCase: {
      type: Object,
      default: {}
    },
    alarmRecord: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  computed: {
    desensitizedIdCard() {
      if (this.hjzzCase.idCardNo != null && this.hjzzCase.idCardNo.length > 0) {
        return this.hjzzCase.idCardNo.substring(0, 6) + '********' + this.hjzzCase.idCardNo.substring(14, 18);
      }
      return '';
    },
    // desensitizedPersonnelInfo() {
    //   if (this.hjzzCase.personnelInfo != null && this.hjzzCase.personnelInfo.length > 0) {
    //     return this.hjzzCase.personnelInfo.substr(0, 1) + '*'.repeat(this.hjzzCase.personnelInfo.length - 1);
    //   }
    //   return '';
    // },
    desensitizedContactInfo() {
      if (this.hjzzCase.contactInfo != null && this.hjzzCase.contactInfo.length > 0) {
        return this.hjzzCase.contactInfo.substr(0, 3) + '****' + this.hjzzCase.contactInfo.substr(-4);
      }
      return '';
    },
    desensitizedLegalName() {
      if (this.hjzzCase.legalName != null && this.hjzzCase.legalName.length > 0) {
        return this.hjzzCase.legalName.substr(0, 1) + '*'.repeat(this.hjzzCase.legalName.length - 1);
      }
      return '';
    }
  },
  mounted() {},
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  //left: 950px;
  //transform: translate(0%, -50%);
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 800px;
  height: 600px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("~@/assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-content {
    width: 750px;
    max-height: 515px;
    overflow-y: scroll;
    margin-top:16px;
  }

  .table-content::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}

</style>
