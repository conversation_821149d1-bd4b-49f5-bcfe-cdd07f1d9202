<template>
  <div style="height: 200px;width: 280px"></div>
</template>

<script>
import echarts from 'echarts'

import {getFxftzhxqylData} from '@/api/shcy/screen'

export default {
  name: "FxftRainChart",
  data() {
    return {
      chart: null,
      fxftzhxqylData: [],
      // 颜色
      lightBlue: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: 'rgba(41, 121, 255, 1)' // 0% 处的颜色
        }, {
          offset: 1,
          color: 'rgba(0, 192, 255, 1)' // 100% 处的颜色
        }],
        globalCoord: false // 缺省为 false
      }
    }
  },
  computed: {
    option() {
      return {
        tooltip: {
          show: false
        },
        grid: {
          top: '20%',
          left: '5%',
          right: '5%',
          bottom: '20%',
        },
        xAxis: {
          data: ['1h', '3h', '12h', '24h', '48h'],
          offset: 15,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            fontSize: 14
          }
        },
        yAxis: {
          min: 0,
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: [{
          type: 'bar',
          label: {
            show: true,
            position: 'top',
            padding: 0,
            color: '#4DEEFA',
            fontSize: 12,
            formatter: '{c}'
          },
          itemStyle: {
            color: this.lightBlue
          },
          barWidth: '30%',
          data: this.fxftzhxqylData,
          z: 10
        }],
        backgroundColor: "transparent",
      }
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    init() {
      getFxftzhxqylData().then(response => {
        this.fxftzhxqylData = response.data
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    }
  }
}
</script>
