<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import echarts from 'echarts'
import {hotlineCaseNumber} from "@/api/shcy/screenCsyxk";
export default {
  name: "Hotline<PERSON><PERSON>",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      xData2:[],
      data1:[],
      data2: [],
      barWidth : 30,
    }
  },
  computed: {
    option() {
      return {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer:{
            type:'none',
          }
        },
        grid: {
          left: 30,
          bottom: 100,
          right:0,
        },
        xAxis: {
          data: this.xData2,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: '#fff',
              fontSize: 12,
              lineHeight: 18
            },
            margin: 26, //刻度标签与轴线之间的距离。
          },
        },
        yAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
            textStyle: {
              color: '#fff',
              fontSize: 12,
            },
          }
        },
        series: [{ // 上半截柱子
          name: '热线工单数',
          type: 'bar',
          barWidth: this.barWidth,
          barGap: '-100%',
          z: 0,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
              [{
                offset: 0,
                color: "rgba(54,127,223,1)"
              },
                {
                  offset: 1,
                  color: "rgba(94,162,254,1)"
                }
              ],
              false
            ),
            opacity: .7,
          },
          data: this.data2
        },
          { //下半截柱子
            name: '不满意案件数',
            type: 'bar',
            barWidth: this.barWidth,
            barGap: '-100%',
            itemStyle: { //lenged文本
              opacity: .7,
              color: function (params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#78FCFC' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#46BBFE' // 100% 处的颜色
                }], false)
              }
            },
            data: this.data1
          },
          { //上半截柱子顶部圆片
            "name": "热线工单数",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, -5],
            "z": 12,
            "symbolPosition": "end",
            itemStyle: {
              "normal": {
                "color": {
                  "x": 0,
                  "y": 0,
                  "x2": 0,
                  "y2": 1,
                  "type": "linear",
                  "global": false,
                  "colorStops": [{
                    offset: 0,
                    color: "rgba(54,127,223,1)"
                  },
                    {
                      offset: 1,
                      color: "rgba(94,162,254,1)"
                    }],
                }
              }
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12,
              color: '#fff',
            },
            tooltip:{
              show:false
            },
            "data": this.data2
          },
          { //下半截柱子顶部圆片
            "name": "不满意案件数",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, -10],
            "z": 12,
            itemStyle: {
              opacity: 1,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(89,211,255,1)"
              },
                {
                  offset: 1,
                  color: "rgba(23,237,194,1)"
                }
              ])
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12,
              color: '#fff',
              // formatter:(item)=>{
              //     console.log(item)
              //     return 'ssss'
              // }
            },
            tooltip:{
              show:false
            },
            "symbolPosition": "end",
            "data": this.data1
          },
          { //下半截柱子底部圆片
            "name": "",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, 5],
            "z": 12,
            label:{
              show:false
            },
            itemStyle: {
              opacity: 1,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(89,211,255,1)"
              },
                {
                  offset: 1,
                  color: "rgba(23,237,194,1)"
                }
              ])
            },
            tooltip:{
              show:false
            },
            "data": [1, 1, 1, 1, 1,1,1,1,1,1,1,1]
          }
        ]
      }
    }
    // option() {
    //   return {
    //     title: {
    //       text: '12345热线工单数量统计',
    //       x: 'center',
    //       show: false
    //     },
    //     grid: {
    //       top: '6',
    //       left: '10',
    //       right: '10',
    //       bottom: '0',
    //       containLabel: true
    //     },
    //     backgroundColor: '',
    //     tooltip: {
    //       trigger: 'item'
    //     },
    //     xAxis: {
    //       data: ["22.12月", "23.1月", "23.2月", "23.3月", "23.4月", "23.5月", "23.6月", "23.7月", "23.8月", "23.9月", "23.10月", "23.11月"],
    //       axisTick: {
    //         show: false
    //       },
    //       axisLine: {
    //         show: false
    //       },
    //       axisLabel: {
    //         textStyle: {
    //           color: '#888'
    //         },
    //         margin: 20, //刻度标签与轴线之间的距离。
    //       }
    //     },
    //     yAxis: {
    //       splitLine: {
    //         show: false,
    //         lineStyle: {
    //           color: '#eee',
    //           type: 'solid'
    //         }
    //       },
    //       axisTick: {
    //         show: false
    //       },
    //       axisLine: {
    //         show: false
    //       },
    //       axisLabel: {
    //         textStyle: {
    //           color: '#888'
    //         }
    //       },
    //       show: false
    //     },
    //     series: [
    //       {
    //         //最低下的圆片
    //         name: '',
    //         type: 'pictorialBar',
    //         symbolSize: [25, 12],
    //         symbolOffset: [0, 6],
    //         z: 12,
    //         data: [
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '1',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                   {
    //                     offset: 0,
    //                     color: 'rgba(89,211,255,1)'
    //                   },
    //                   {
    //                     offset: 1,
    //                     color: 'rgba(23,237,194,1)'
    //                   }
    //                 ])
    //               }
    //             }
    //           }
    //         ]
    //       },
    //
    //       //下半截柱状图
    //       {
    //         name: '',
    //         type: 'bar',
    //         barWidth: 25,
    //         barGap: '-100%',
    //         itemStyle: {
    //           //lenged文本
    //           color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //             {
    //               offset: 0,
    //               color: 'rgba(55,255,249,1)'
    //             },
    //             {
    //               offset: 1,
    //               color: 'rgba(0,222,215,0.2)'
    //             }
    //           ])
    //         },
    //         data: [
    //           {
    //             name: '',
    //             value: '5',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第一节下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第二个柱状图下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第三个柱状图下半截
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第一节下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第二个柱状图下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第三个柱状图下半截
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第一节下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '1',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第二个柱状图下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第三个柱状图下半截
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第一节下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第二个柱状图下面
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '8',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       //第三个柱状图下半截
    //                       offset: 0,
    //                       color: 'rgba(0,255,245,0.5)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: '#43bafe'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           }
    //         ]
    //       },
    //
    //       // 替代柱状图 默认不显示颜色，是下半截柱图的value值
    //       {
    //         type: 'bar',
    //         barWidth: 25,
    //         barGap: '-100%',
    //         stack: '广告',
    //         itemStyle: {
    //           color: 'transparent'
    //         },
    //         data: [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
    //       },
    //
    //       //中间十二个圆片
    //       {
    //         name: '',
    //         type: 'pictorialBar',
    //         symbolSize: [25, 12],
    //         symbolOffset: [0, -6],
    //         z: 12,
    //         data: [
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '1',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(89,211,255,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(23,237,194,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           }
    //         ]
    //       },
    //       //十二根上半截柱子
    //       {
    //         name: '',
    //         type: 'bar',
    //         barWidth: 50,
    //         barGap: '-100%',
    //         stack: '广告',
    //         itemStyle: {
    //           // barBorderRadius: 20,
    //           color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //             {
    //               offset: 1,
    //               color: 'rgba(0,255,100,0.5)'
    //             },
    //             {
    //               offset: 1,
    //               color: 'rgba(0,255,100,0.5)'
    //             }
    //           ])
    //         },
    //         //上班截开始
    //         data: [
    //           {
    //             name: '',
    //             value: '10',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '9',
    //             trueVal: '0',
    //             itemStyle: {
    //               normal: {
    //                 color: {
    //                   x: 0,
    //                   y: 0,
    //                   x2: 0,
    //                   y2: 1,
    //                   type: 'linear',
    //                   global: false,
    //                   colorStops: [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ]
    //                 }
    //               }
    //             }
    //           }
    //         ]
    //       },
    //       //头部十二个圆片
    //       {
    //         name: '',
    //         type: 'pictorialBar',
    //         symbolSize: [25, 12],
    //         symbolOffset: [0, -6],
    //         z: 12,
    //         data: [
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '1',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           },
    //           {
    //             name: '',
    //             value: '0',
    //             symbolPosition: 'end',
    //             itemStyle: {
    //               normal: {
    //                 color: new echarts.graphic.LinearGradient(
    //                   0,
    //                   0,
    //                   0,
    //                   1,
    //                   [
    //                     {
    //                       offset: 0,
    //                       color: 'rgba(54,127,223,1)'
    //                     },
    //                     {
    //                       offset: 1,
    //                       color: 'rgba(94,162,254,1)'
    //                     }
    //                   ],
    //                   false
    //                 )
    //               }
    //             }
    //           }
    //         ]
    //       },
    //     ]
    //   };
    // }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      hotlineCaseNumber().then(res=>{
        for(let i=0;i<res.data.monthList.length;i++)
        {
          const month=res.data.monthList[i];
          const li=JSON.stringify(res.data.allData,[month]);
          const jsonArray=JSON.parse(li);
          if(li.length>2)
          {
            for (const key in jsonArray)
            {
              this.data2.push(jsonArray[key]);
            }
          }
          else {
            this.data2.push("0");
          }
          const li1=JSON.stringify(res.data.satisfactionData,[month]);
          const jsonArray1=JSON.parse(li1);
          if(li1.length>2)
          {
            for (const key in jsonArray1)
            {
              this.data1.push(jsonArray1[key]);
            }
          }
          else {
            this.data1.push("0");
          }
          const year=month.split('-')[0];
          const m=month.split('-')[1];
          this.xData2.push(year+"\n"+m+"月");
        }

        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })

    }
  }
}
</script>
