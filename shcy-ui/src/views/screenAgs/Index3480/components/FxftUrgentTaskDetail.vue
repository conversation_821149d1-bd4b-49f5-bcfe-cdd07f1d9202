<template>
  <div class="urgentTask-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">紧急任务处置详情</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="content">
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="处置人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ urgentTask.caseDealBy }}
        </el-descriptions-item>
        <el-descriptions-item label="处置地点" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ urgentTask.alarmLocation }}
        </el-descriptions-item>
        <el-descriptions-item label="处置时间" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ urgentTask.caseFinishTime }}
        </el-descriptions-item>
        <el-descriptions-item label="是否存在其他隐患" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ urgentTask.otherHazardStatus }}
        </el-descriptions-item>
        <el-descriptions-item v-if="urgentTask.otherHazardStatus === '是'" label="备注" :label-style="this.labelStyle" :content-style="this.contentStyle" :span="2">
          {{ urgentTask.otherHazard }}
        </el-descriptions-item>
        <el-descriptions-item label="处置照片" :label-style="this.labelStyle" :content-style="this.contentStyle"
                              :span="2">
          <el-image style="width: 200px; margin-right: 10px;"
                    v-if="urgentTask.photoUrls != null && urgentTask.photoUrls.length > 0"
                    v-for="photoUrl in urgentTask.photoUrls" :key="photoUrl" lazy :src="photoUrl"
                    :preview-src-list="urgentTask.photoUrls" fit="contain"></el-image>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>

export default {
  name: "FxftUrgentTaskWindow",
  props: {
    urgentTask: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    }
  },
};
</script>

<style lang="scss" scoped>
// 容器
.urgentTask-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 950px;
  transform: translate(0%, -50%);
  padding: 25px;
  width: 800px;
  height: 600px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("~@/assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .content {
    width: 750px;
    max-height: 515px;
    overflow-y: scroll;
    margin-top:16px;
  }

  .content::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}
</style>
