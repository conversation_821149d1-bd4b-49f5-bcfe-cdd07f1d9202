<template>
  <div :style="{height:height,width:width}"/>
</template>

<script>
import echarts from 'echarts'
export default {
  name: "TrafficChart",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data(){
    return {
      chart: null,
      angle: 0,//角度，用来做简单的动画效果的
      color: ['#0086FF','#FFD300','#FB5252','#00CAFF','#FDE056','#4ED33C','#FF8A26','#FF5252','#9689FF','#CB00FF']
    }
  },
  computed: {
    option() {
      let initData = this.data;
      return {
        backgroundColor:"",
        color : this.color,
        legend:{
          orient: 'vertical',
          bottom:'-2%',
          icon:'roundRect',
          itemWidth:5,
          itemHeight:33,
          textStyle:{
            color:'#B6B6BD',
            rich: {
              a:{
                fontSize: 13
              },
              b: {
                fontSize: 6
              }
            },
          },
          itemGap: 40,
          formatter(params) {
            switch (params) {
              case '正常':
                return params + '{b|} \n\n' + '{a|' + Math.round((initData[0].value / (initData[0].value + initData[1].value + initData[2].value))*100) + '%}';
                break;
              case '歇业':
                return params + '{b|} \n\n' + '{a|' + Math.round((initData[1].value / (initData[0].value + initData[1].value + initData[2].value))*100) + '%}';
                break;
              case '关停':
                return params + '{b|} \n\n' + '{a|' + Math.round((initData[2].value / (initData[0].value + initData[1].value + initData[2].value))*100) + '%}';
                break;
              case '离线':
                return params + '{b|} \n\n' + '{a|' + Math.round((initData[0].value / (initData[0].value + initData[1].value))*100) + '%}';
                break;
              case '在线':
                return params + '{b|} \n\n' + '{a|' + Math.round((initData[1].value / (initData[0].value + initData[1].value))*100) + '%}';
                break;
              default:
                return "";
            }
          }
        },
        series: [
          {//外线1
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem1,
            data: [0]
          },
          {//内线1
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem2,
            data: [0]
          },
          {//外线2
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem3,
            data: [0]
          },
          {//外线2
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem4,
            data: [0]
          },
          {//绿点1
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem5,
            data: [0]
          },
          {//绿点2
            name: "",  //绿点
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem6,
            data: [0]
          },
          {//绿点3
            name: "",
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem7,
            data: [0]
          },
          {//绿点4
            name: "",  //绿点
            type: 'custom',
            coordinateSystem: "none",
            renderItem: this.renderItem8,
            data: [0]
          },
          {
            name: '',
            type: 'pie',
            clockWise: false,
            radius: ['115%', '113%'],
            hoverAnimation: false,
            center:['50%','33%'],
            top:"center",
            itemStyle: {
              normal:{
                label: {
                  show:false
                }
              }
            },
            data:this.data1
          },
          {
            type: 'pie',
            top:"center",
            startAngle:90,
            clockwise:false,
            center:['50%','33%'],
            legendHoverLink:false,
            hoverAnimation: false,
            radius: ['113%', '65%'],
            itemStyle: {
              opacity:0.15
            },
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data:this.data2
          },
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center:['50%','33%'],
            radius: ['42%', '41%'],
            hoverAnimation: false,
            top:"center",
            itemStyle: {
              normal:{
                label: {
                  show:false
                }
              }
            },
            data:this.data1
          },
        ]
      }
    },
    data1: function(){
      let chartData = []
      let spec = 0
      this.data.forEach(item => {
        spec = spec + item.value;
      })
      for (let i = 0; i < this.data.length; i++) {
        chartData.push({
          value: this.data[i].value,
          name: this.data[i].name,
          itemStyle: {
            normal: {
              borderWidth: 8,
              shadowBlur: 20,
              borderRadius: 20,
              borderColor: this.color[i],
              shadowColor: this.color[i]
            }
          }
        }, {
          value: spec / 16,
          name: '',
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              color: 'rgba(0, 0, 0, 0)',
              borderColor: 'rgba(0, 0, 0, 0)',
              borderWidth: 0
            }
          }
        });
      }
      return chartData
    },
    data2: function (){
      let chartData = []
      let spec = 0
      this.data.forEach(item => {
        spec = spec + item.value;
      })
      for (let i = 0; i < this.data.length; i++) {
        chartData.push({
            value: this.data[i].value,
            name: this.data[i].name,
          },
          {
            value: spec / 16,
            name: '',
            itemStyle: {
              normal: {
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0,
                opacity: 0.2
              }
            }
          })
      }
      return chartData
    }
  },
  mounted() {
    this.chart = echarts.init(this.$el)
    this.$nextTick(() => {
      this.angle = this.angle+3
      this.chart.setOption(this.option, true)
    })
    // setInterval(this.draw, 100)
  },
  methods:{
    //获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
    getCirlPoint(x0, y0, r, angle) {
      let x1 = x0 + r * Math.cos(angle * Math.PI / 180)
      let y1 = y0 + r * Math.sin(angle * Math.PI / 180)
      return {
        x: x1,
        y: y1
      }
    },
    draw(){
      this.angle = this.angle+3
      this.chart.setOption(this.option, true)
      //window.requestAnimationFrame(draw);
    },
    renderItem1(params, api){
      let oldAngle = this.angle
      return {
        type: 'arc',
        shape: {
          cx: api.getWidth() / 2,
          cy: api.getHeight() / 2.4,
          r: Math.min(api.getWidth(), api.getHeight()) / 2 * 0.72,
          startAngle: (0 + oldAngle) * Math.PI / 180,
          endAngle: (90 + oldAngle) * Math.PI / 180
        },
        style: {
          stroke: '#4EE9E6',
          fill: "transparent",
          lineWidth: 1.5
        },
        silent: true
      }
    },
    renderItem2(params, api) {
      let oldAngle = this.angle
      return {
        type: 'arc',
        shape: {
          cx: api.getWidth() / 2,
          cy: api.getHeight() / 2.4,
          r: Math.min(api.getWidth(), api.getHeight()) / 2 * 0.72,
          startAngle: (180+oldAngle) * Math.PI / 180,
          endAngle: (270+oldAngle) * Math.PI / 180
        },
        style: {
          stroke: "#4EE9E6",
          fill: "transparent",
          lineWidth: 1.5
        },
        silent: true
      };
    },
    renderItem3(params, api){
      let oldAngle = this.angle
      return {
        type: 'arc',
        shape: {
          cx: api.getWidth() / 2,
          cy: api.getHeight() / 2.4,
          r: Math.min(api.getWidth(), api.getHeight()) / 2 * 0.78,
          startAngle: (270+-oldAngle) * Math.PI / 180,
          endAngle: (40+-oldAngle) * Math.PI / 180
        },
        style: {
          stroke: "#4EE9E6",
          fill: "transparent",
          lineWidth: 1.5
        },
        silent: true
      };
    },
    renderItem4(params, api){
      let oldAngle = this.angle
      return {
        type: 'arc',
        shape: {
          cx: api.getWidth() / 2,
          cy: api.getHeight() / 2.4,
          r: Math.min(api.getWidth(), api.getHeight()) / 2 * 0.78,
          startAngle: (90+-oldAngle) * Math.PI / 180,
          endAngle: (220+-oldAngle) * Math.PI / 180
        },
        style: {
          stroke: "#4EE9E6",
          fill: "transparent",
          lineWidth: 1.5
        },
        silent: true
      };
    },
    renderItem5(params, api){
      let x0 = api.getWidth() / 2;
      let y0 = api.getHeight() / 2.4;
      let r = Math.min(api.getWidth(), api.getHeight()) / 2 * 0.78;
      let point = this.getCirlPoint(x0, y0, r, (90+-this.angle))
      return {
        type: 'circle',
        shape: {
          cx: point.x,
          cy: point.y,
          r: 4
        },
        style: {
          stroke: "#66FFFF",//粉
          fill: "#66FFFF"
        },
        silent: true
      };
    },
    renderItem6(params, api){
      let x0 = api.getWidth() / 2;
      let y0 = api.getHeight() / 2.4;
      let r = Math.min(api.getWidth(), api.getHeight()) / 2 * 0.78;
      let point = this.getCirlPoint(x0, y0, r, (270+-this.angle))
      return {
        type: 'circle',
        shape: {
          cx: point.x,
          cy: point.y,
          r: 4
        },
        style: {
          stroke: "#66FFFF",//粉
          fill: "#66FFFF"
        },
        silent: true
      };
    },
    renderItem7(params, api){
      let x0 = api.getWidth() / 2;
      let y0 = api.getHeight() / 2.4;
      let r = Math.min(api.getWidth(), api.getHeight()) / 2 * 0.72;
      let point = this.getCirlPoint(x0, y0, r, (90+this.angle))
      return {
        type: 'circle',
        shape: {
          cx: point.x,
          cy: point.y,
          r: 4
        },
        style: {
          stroke: "#66FFFF",//粉
          fill: "#66FFFF"
        },
        silent: true
      };
    },
    renderItem8(params, api){
      let x0 = api.getWidth() / 2;
      let y0 = api.getHeight() / 2.4 ;
      let r = Math.min(api.getWidth(), api.getHeight()) / 2 * 0.72;
      let point = this.getCirlPoint(x0, y0, r, (270+this.angle))
      return {
        type: 'circle',
        shape: {
          cx: point.x,
          cy: point.y,
          r: 4
        },
        style: {
          stroke: "#66FFFF",//粉
          fill: "#66FFFF"
        },
        silent: true
      };
    }
  }
}
</script>

<style scoped>

</style>
