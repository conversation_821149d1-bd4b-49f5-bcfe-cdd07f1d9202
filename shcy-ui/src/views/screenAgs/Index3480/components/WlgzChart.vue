<template>
  <div :style="{height:height,width:width}" ref="chart"></div>
</template>

<script>
import echarts from "echarts";
import {iotEvent} from "@/api/shcy/screenFxft";

export default {
  name: "Wlgz<PERSON><PERSON>",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
  },
  data() {
    return {
      chart: null,
      xData2: [],
      data1: [],
      data2: [],
      barWidth : 30,
    }
  },
  computed: {
    option() {
      return {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer:{
            type:'none',
          }
        },
        grid: {
          left: 10,
          bottom: 100,
          right:20,
        },
        xAxis: {
          data: this.xData2,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: '#fff',
              fontSize: 12,
              lineHeight: 18
            },
            margin: 26, //刻度标签与轴线之间的距离。
          },
        },
        yAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
            textStyle: {
              color: '#fff',
              fontSize: 12,
            },
          }
        },
        series: [{ // 上半截柱子
          name: '设备报警总数',
          type: 'bar',
          barWidth: this.barWidth,
          barGap: '-100%',
          z: 0,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
              [{
                offset: 0,
                color: "rgba(54,127,223,1)"
              },
                {
                  offset: 1,
                  color: "rgba(94,162,254,1)"
                }
              ],
              false
            ),
            opacity: .7,
          },
          data: this.data2
        },
          { //下半截柱子
            name: '已处理报警数',
            type: 'bar',
            barWidth: this.barWidth,
            barGap: '-100%',
            itemStyle: { //lenged文本
              opacity: .7,
              color: function (params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#78FCFC' // 0% 处的颜色
                }, {
                  offset: 1,
                  color: '#46BBFE' // 100% 处的颜色
                }], false)
              }
            },
            data: this.data1
          },
          { //上半截柱子顶部圆片
            "name": "设备报警总数",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, -5],
            "z": 12,
            "symbolPosition": "end",
            itemStyle: {
              "normal": {
                "color": {
                  "x": 0,
                  "y": 0,
                  "x2": 0,
                  "y2": 1,
                  "type": "linear",
                  "global": false,
                  "colorStops": [{
                    offset: 0,
                    color: "rgba(54,127,223,1)"
                  },
                    {
                      offset: 1,
                      color: "rgba(94,162,254,1)"
                    }],
                }
              }
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12,
              color: '#fff',
            },
            tooltip:{
              show:false
            },
            "data": this.data2
          },
          { //下半截柱子顶部圆片
            "name": "已处理报警数",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, -10],
            "z": 12,
            itemStyle: {
              opacity: 1,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(89,211,255,1)"
              },
                {
                  offset: 1,
                  color: "rgba(23,237,194,1)"
                }
              ])
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12,
              color: '#fff',
              // formatter:(item)=>{
              //     console.log(item)
              //     return 'ssss'
              // }
            },
            tooltip:{
              show:false
            },
            "symbolPosition": "end",
            "data": this.data1
          },
          { //下半截柱子底部圆片
            "name": "",
            "type": "pictorialBar",
            "symbolSize": [this.barWidth, 15],
            "symbolOffset": [0, 5],
            "z": 12,
            label:{
              show:false
            },
            itemStyle: {
              opacity: 1,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(89,211,255,1)"
              },
                {
                  offset: 1,
                  color: "rgba(23,237,194,1)"
                }
              ])
            },
            tooltip:{
              show:false
            },
            "data": [1, 1, 1, 1, 1,1,1,1,1,1,1,1]
          }
        ]
      }
      // return {
      //   backgroundColor: '',
      //   tooltip: {
      //     trigger: 'item',
      //   },
      //   grid: {
      //     left: 0,
      //     bottom: 100
      //   },
      //   xAxis: {
      //     data: this.xData2,
      //     axisTick: {
      //       show: false
      //     },
      //     axisLine: {
      //       show: false
      //     },
      //     axisLabel: {
      //       interval: 0,
      //       textStyle: {
      //         color: '#fff',
      //         fontSize: 12,
      //       },
      //       margin: 20, //刻度标签与轴线之间的距离。
      //     },
      //
      //   },
      //   yAxis: {
      //     splitLine: {
      //       show: false,
      //     },
      //     axisTick: {
      //       show: false
      //     },
      //     axisLine: {
      //       show: false
      //     },
      //     axisLabel: {
      //       textStyle: {
      //         color: '#fff',
      //         fontSize: 12,
      //       },
      //     },
      //     show: false
      //   },
      //   series: [
      //     {//三个最低下的圆片
      //       "name": "",
      //       "type": "pictorialBar",
      //       "symbolSize": [20, 15],
      //       "symbolOffset": [0, 10],
      //       "z": 12,
      //       itemStyle: {
      //         opacity: 1,
      //         color: function (params) {
      //           return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      //             offset: 0,
      //             color: '#25cea1' // 0% 处的颜色
      //           }, {
      //             offset: 1,
      //             color:  '#20d9a4'// 100% 处的颜色
      //           }], false)
      //         },
      //       },
      //       "data": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
      //     },
      //
      //
      //     //下半截柱状图
      //     {
      //       name: '2020',
      //       type: 'bar',
      //       barWidth: 20,
      //       barGap: '-100%',
      //       itemStyle: {//lenged文本
      //         opacity: .7,
      //         color: function (params) {
      //           return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      //             offset: 0,
      //             color: '#25cea1' // 0% 处的颜色
      //           }, {
      //             offset: 1,
      //             color:  '#20d9a4'// 100% 处的颜色
      //           }], false)
      //         }
      //       },
      //
      //       data: this.data1
      //     },
      //
      //     { // 替代柱状图 默认不显示颜色，是最下方柱图（邮件营销）的value值 - 20
      //       type: 'bar',
      //       barWidth: 20,
      //       barGap: '-100%',
      //       stack: '广告',
      //       itemStyle: {
      //         color: 'transparent'
      //       },
      //       data: this.data1
      //     },
      //
      //     {
      //       "name": "", //头部
      //       "type": "pictorialBar",
      //       "symbolSize": [20, 15],
      //       "symbolOffset": [0, -10],
      //       "z": 12,
      //       "symbolPosition": "end",
      //       itemStyle: {
      //         color: '#1153e1',
      //         opacity: 1,
      //       },
      //       "data": this.data2
      //     },
      //
      //     {
      //       "name": "",
      //       "type": "pictorialBar",
      //       "symbolSize": [20, 15],
      //       "symbolOffset": [0, -10],
      //       "z": 12,
      //       itemStyle: {
      //         opacity: 1,
      //         color: function (params) {
      //           return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      //             offset: 0,
      //             color: '#25cea1' // 0% 处的颜色
      //           }, {
      //             offset: 1,
      //             color:  '#20d9a4'// 100% 处的颜色
      //           }], false)
      //         }
      //       },
      //       "symbolPosition": "end",
      //       "data": this.data1
      //     },
      //
      //     {
      //       name: '',
      //       type: 'bar',
      //       barWidth: 20,
      //       barGap: '-100%',
      //       z: 0,
      //       itemStyle: {
      //         color: '#0247aa',
      //         opacity: .8,
      //       },
      //
      //       data: this.data2
      //     }
      //
      //
      //   ]
      // }
    },
  },
  mounted() {
    this.createChart()
  },
  methods: {
    createChart() {
      iotEvent().then(res=> {
        for (let i = 0; i < res.data.monthList.length; i++) {
          const month = res.data.monthList[i];
          const li = JSON.stringify(res.data.allList, [month]);
          const jsonArray = JSON.parse(li);
          if (li.length > 2) {
            for (const key in jsonArray) {
              this.data2.push(jsonArray[key]);
            }
          } else {
            this.data2.push("0");
          }
          const li1 = JSON.stringify(res.data.processedList, [month]);
          const jsonArray1 = JSON.parse(li1);
          if (li1.length > 2) {
            for (const key in jsonArray1) {
              this.data1.push(jsonArray1[key]);
            }
          } else {
            this.data1.push("0");
          }
          const year = month.split('-')[0];
          const m = month.split('-')[1];
          this.xData2.push(year + "\n" + m + "月");
        }
        this.chart = echarts.init(this.$refs.chart)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    },
  },
}
</script>

<style scoped>

</style>
