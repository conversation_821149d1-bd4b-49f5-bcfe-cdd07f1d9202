<template>
  <div class="container">
    <div class="module_title">
      <span>防汛防台综合数据</span>
      <div class="small-square-box">
        <span class="square01"></span>
        <span class="square02"></span>
        <span class="square03"></span>
      </div>
    </div>

    <div class="content1">
      <!-- 泵闸数据 -->
      <div class="content2">
        <div class="content1-1">
          <span class="yuanquan"></span> <span class="ziti">泵闸数据</span>
        </div>

        <div class="content2-1">
          <div class="content2-1-1">
            <div class="content2-1-1-1">内水位</div>
            <div class="content2-1-1-2">外水位</div>
            <div class="content2-1-1-3">{{lqgsz.inWater}}</div>
            <div class="content2-1-1-4">{{lqgsz.outWater}}</div>
            <div class="content2-1-1-5">龙泉港水闸</div>
            <div class="content2-1-1-6">{{lqgsz.dateTime}}</div>
          </div>
          <div class="content2-1-1">
            <div class="content2-1-1-1">内水位</div>
            <div class="content2-1-1-2">外水位</div>
            <div class="content2-1-1-3">{{zjhsz.inWater}}</div>
            <div class="content2-1-1-4">{{zjhsz.outWater}}</div>
            <div class="content2-1-1-5">张泾河枢纽</div>
            <div class="content2-1-1-6">{{zjhsz.dateTime}}</div>
          </div>
        </div>
      </div>
      <!-- 雨量数据 -->
      <div class="content3">
        <div class="content1-1">
          <span class="yuanquan"></span><span class="ziti">雨量数据 - 鹦鹉洲气象观测站</span>
        </div>
        <div class="content-rain-chart">
          <FxftRainChart/>
        </div>
      </div>
    </div>

    <div class="content1">
      <!-- 风速风向 -->
      <div class="content2">
        <div class="content1-1">
          <span class="yuanquan"></span> <span class="ziti">风速风向</span>
        </div>

        <div class="label_bg" style="position: relative;">
          <div class="label_item_bg" :class="isActive == 'jsz' ? 'label_item_bg_press' : '' " @click="changeBg('jsz')">
            <div class="label_title">金山嘴</div>
          </div>
          <!--<div class="label_item_bg" :class="isActive == 'shhg' ? 'label_item_bg_press' : '' " @click="changeBg('shhg')">
            <div class="label_title">上海化工</div>
          </div>
          <div class="label_item_bg" :class="isActive == 'gjqxz' ? 'label_item_bg_press' : '' " @click="changeBg('gjqxz')">
            <div class="label_title">金山国家气象站</div>
          </div>-->
        </div>

        <div class="content-fsfx">
          <div class="content-fsfx-1">{{fsfxData.fengli}}</div>
          <div class="content-fsfx-2">{{fsfxData.windway}}</div>
          <div class="content-fsfx-3">{{fsfxData.winddirection}}</div>
          <div class="content-fsfx-4">{{fsfxData.windspeed}}</div>
        </div>

      </div>
      <!-- 汛情水位 -->
      <div class="content3">
        <div class="content1-1">
          <span class="yuanquan"></span><span class="ziti">汛情水位 - 金山嘴</span>
        </div>
        <div class="content2-1">
          <div class="content-xqsw">
            <span class="xqsw-title">当前水位</span>
            <div class="xqsw">{{fxftzhxqswData.outWater}}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="content1">
      <!-- 下立交水位 -->
      <div class="content2" style="width: 50%">
        <div class="content1-1">
          <span class="yuanquan"></span> <span class="ziti">下立交水位</span>
          <span style="float: right;font-size:12px;color: #409EFF;border:1px solid #409EFF;border-radius: 50px;margin-right: 20px"><span style="margin: 5px">当前无暴雨预警</span></span>
        </div>

        <div class="content2-1">
          <div class="content-sw">
            <span class="xljsw-title">蒙山路下立交</span>
            <div class="xljsw">{{fxftzhxqjsData.mslWaterValue}}</div>
          </div>

          <div class="content-shuichi">
            <div class="shuichi1">25</div>
            <div class="shuichi2">20</div>
          </div>

          <div class="content-sw">
            <span class="xljsw-title">卫零路下立交</span>
            <div class="xljsw">{{fxftzhxqjsData.wllWaterValue}}</div>
          </div>

        </div>

      </div>
      <!-- 气象数据 -->
      <div class="content3" style="width: 50%">
        <div class="content1-1">
          <span class="yuanquan"></span> <span class="ziti">气象数据</span>
        </div>

        <div class="label_bg" style="position: relative;">
          <div class="label_item_bg label_item_bg_press">
            <div class="label_title">山鑫阳光城</div>
          </div>
        </div>

        <div class="content-qxsj">
          <div class="content-qxsj-1"></div>
          <div class="content-qxsj-2">{{fxftxbData.yl}}</div>
          <div class="content-qxsj-3">{{fxftxbData.wsd}}</div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>

import { getFxftzhxqjsData, getFxftzhxqswData, getFxftzhxqfsfxData, getFxftzhxqbzData, getFxftxbData } from '@/api/shcy/screen'

import FxftRainChart from "@/views/screenAgs/Index3480/components/FxftRainChart.vue";

export default {
  name: "FxftZhsj",
  components: {
    FxftRainChart
  },
  data() {
    return {
      isActive: 'jsz',
      lqgsz: {
        inWater: undefined,
        outWater: undefined,
        dateTime: undefined,
      },
      zjhsz: {
        inWater: undefined,
        outWater: undefined,
        dateTime: undefined,
      },
      fxftzhxqjsData: {
        mslWaterValue: undefined,
        wllWaterValue: undefined,
      },
      fxftzhxqswData: {
        outWater: undefined,
      },
      jszData: {},
      shhgData: {},
      gjqxzData: {},
      fsfxData: {
        winddirection: undefined,
        windway: undefined,
        windspeed: undefined,
        fengli: undefined,
      },
      // 气象数据
      fxftxbData: {
        wsd: undefined,
        yl: undefined,
      },
    }
  },
  created() {
    this.getFxftzhxqbzData()
    this.getFxftzhxqjsData()
    this.getFxftzhxqswData()
    this.getFxftzhxqfsfxData()
    this.getFxftxbData()
  },
  methods: {
    getFxftzhxqjsData() {
      getFxftzhxqjsData().then(response => {
        this.fxftzhxqjsData = response.data
      })
    },
    getFxftzhxqswData() {
      getFxftzhxqswData().then(response => {
        this.fxftzhxqswData = response.data
      })
    },
    getFxftzhxqfsfxData() {
      getFxftzhxqfsfxData().then(response => {
        this.jszData = response.data.jsz
        this.shhgData = response.data.shhg
        this.gjqxzData = response.data.gjqxz
        this.fsfxData = this.jszData
      })
    },
    getFxftzhxqbzData() {
      getFxftzhxqbzData().then(response => {
        this.lqgsz = response.data.lqgsz
        this.zjhsz = response.data.zjhsz
      })
    },
    getFxftxbData() {
      getFxftxbData().then(response => {
        this.fxftxbData = response.data
      })
    },
    changeBg(type) {
      switch (type) {
        case 'jsz':
          this.isActive = 'jsz'
          this.fsfxData = this.jszData
          break
        case 'shhg':
          this.isActive = 'shhg'
          this.fsfxData = this.shhgData
          break
        case 'gjqxz':
          this.isActive = 'gjqxz'
          this.fsfxData = this.gjqxzData
          break
      }
    },
  }


}
</script>

<style scoped lang="scss">

.module_title{
  height: 40px;
  line-height: 40px;
  background: linear-gradient(90deg, #283498 0%, rgba(40,52,152,0.2) 100%);
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  padding-left: 33px;
}

.small-square-box {
  display: flex;
  flex-flow: row wrap;
  // justify-content: space-around;
  justify-content: flex-end;
  // align-items: stretch;
  position: relative;
  top: 8px;
  right: 3px;
  width: 18px;
  height: 18px;

  span {
    display: block;
    margin: 4px 4px 0 0;
    width: 5px;
    height: 5px;
  }

  .square01 {
    background-color: #283498;
  }

  .square03 {
    background-color: #283498;
  }

  .square02 {
    background-color: #28dfae;
  }
}

.content1 {

  display: flex;

  height: 287px;
  width: 100%;
  //border: 1px solid red;
}

.content2 {
  margin: 10px;
  width: 60%;
  //border: 1px solid pink;
}

.content2-1 {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 40px;
}

.content2-1-1 {
  position: relative;

  margin: 10px;
  width: 100%;
  //height: 60%;

  //background-image: url("https://s21.ax1x.com/2024/06/27/pkyLvin.jpg");
  background-image: url("~@/assets/screen_display_img/bzsj.png");
  background-size: contain;
  background-repeat: no-repeat;

  //border: 1px solid aqua;

  font-weight: 500;
  font-size: 13px;
  color: #B6B6BD;
  line-height: 24px;
}

.content2-1-1-1 {
  position: absolute;
  top: 10px;
  left: 30px;
}

.content2-1-1-2 {
  position: absolute;
  top: 10px;
  left: 120px;
}

.content2-1-1-3 {
  position: absolute;
  top: 50px;
  left: 30px;

  font-weight: bold;
  font-size: 20px;
  color: #4DEEFA;
  line-height: 24px;
}

.content2-1-1-4 {
  position: absolute;
  top: 50px;
  left: 120px;

  font-weight: bold;
  font-size: 20px;
  color: #4DEEFA;
  line-height: 24px;
}

.content2-1-1-5 {
  position: absolute;
  top: 110px;
  left: 30px;
}

.content2-1-1-6 {
  position: absolute;
  top: 130px;
  left: 30px;
}

.content-rain-chart {
  margin: 10px;
}

.content-sw {

  position: relative;

  //margin: 15px;
  width: 100%;
  height: 80%;

  background-image: url("~@/assets/screen_display_img/xljsw.png");
  background-size: contain;
  background-repeat: no-repeat;
}

.content-shuichi {

  position: absolute;
  left: 175px;
  width: 50px;
  height: 178px;

  background-image: url("~@/assets/screen_display_img/shuichi.png");
  background-size: contain;
  background-repeat: no-repeat;
}

.shuichi1 {
  position: absolute;
  top: 0px;
  left: 20px;

  font-size: 14px;
  color: #ee4a17;
}

.shuichi2 {
  position: absolute;
  top: 58px;
  left: 20px;

  font-size: 14px;
  color: #ffc81f;
}

.content-xqsw {
  position: relative;
  margin: 10px;
  width: 100%;
  height: 80%;
  background-image: url("~@/assets/screen_display_img/xqsw.png");
  background-size: contain;
  background-repeat: no-repeat;
}

.content3 {
  margin: 10px;
  width: 40%;
  //border: 1px solid pink;
}

.content1-1{
  color: white;

  width: 100%;
  //border: 1px solid orange;
}

/*
空心圆
 */
.yuanquan {
  display: inline-block;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  border: 2px solid #28DFAE;
  margin-right: 10px;
}

.ziti {
  width: 64px;
  height: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 8px;
}

.xljsw-title {
  position: absolute;
  top: 60px;
  //left: 40px;
  left: 50%;
  transform: translate(-50%, -50%);

  font-weight: 400;
  font-size: 13px;
  color: #FFFFFF;
  line-height: 16px;
}

.xqsw-title {
  position: absolute;
  top: 30%;
  left: 35%;
  transform: translate(-50%, -50%);

  font-weight: 400;
  font-size: 13px;
  color: #FFFFFF;
  line-height: 16px;
}

.xqsw {
  position: absolute;
  top: 50%;
  left: 35%;
  transform: translate(-50%, -50%);

  font-weight: bold;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 16px;
  text-shadow: 0 3px 10px #252525;

}

.xljsw {
  position: absolute;
  top: 100px;
  //left: 50px;
  left: 50%;
  transform: translate(-50%, -50%);

  font-weight: bold;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 16px;
  text-shadow: 0 3px 10px #252525;

}

.label_bg{
  display: flex;
  display: -webkit-flex;

  margin: 10px;
  width: 220px;
  height: 40px;
  line-height:40px;
  background: #2F2F41;
  border: 1px solid #677BB5;
  opacity: 0.8;
  border-radius: 4px;
  position: absolute;
}

.label_item_bg{
  width: 220px;
  height:38px;
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  cursor: pointer;

}

.label_title{
  margin-left: 0;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}

.label_item_bg_press {
  //background-image: url("~@/assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  background-image: url("https://s21.ax1x.com/2024/06/28/pk6N91g.md.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.content-fsfx {

  position: relative;
  height: 100%;

  background-image: url("~@/assets/screen_display_img/fsfx.png");
  background-size: contain;
  background-repeat: no-repeat;

  font-weight: bold;
  font-size: 18px;
  color: #4DEDF9;
  line-height: 24px;

  //border: 1px solid orange;
}

.content-fsfx-1 {
  position: absolute;

  top: 10%;
  left: 50%;
}

.content-fsfx-2 {
  position: absolute;

  top: 10%;
  left: 90%;
}

.content-fsfx-3 {
  position: absolute;

  top: 30%;
  left: 50%;
}

.content-fsfx-4 {
  position: absolute;

  top: 30%;
  left: 90%;
}

.content-qxsj {

  position: relative;
  width: 443px;
  height: 149px;

  background-image: url("~@/assets/screen_display_img/qxsj1.png");
  background-size: contain;
  background-repeat: no-repeat;

  font-weight: bold;
  font-size: 18px;
  color: #4DEDF9;
  line-height: 24px;
}

.content-qxsj-2 {
  position: absolute;
  top: 42px;
  left: 90%;
}

.content-qxsj-3 {
  position: absolute;
  top: 92px;
  left: 60%;
}

.content-tqyb {
  margin: 30px;
  height: 100px;
  background-image: url("~@/assets/screen_display_img/tqyb.png");
  background-size: contain;
  background-repeat: no-repeat;
}

</style>
