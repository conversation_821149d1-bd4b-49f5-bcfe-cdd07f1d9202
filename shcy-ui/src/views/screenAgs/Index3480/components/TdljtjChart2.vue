<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import echarts from 'echarts'
import {wgscrqCaseNumber} from "@/api/shcy/screenHjzz";
export default {
  name: "TdljtjChart2",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    address: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      chart: null,
      ztcCount:null,
      hntjbcCount:null,
      img: 'data:image/png;base64,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',
    }
  },
  computed: {
    option() {
      return{
          color: [
            '#00d2ff',
            '#22e5d1',
            '#f4d64e',
            '#0072ff'
          ],
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'item',
          },
          title: {
            text: '{a|渣土车/混凝土\n搅拌车\n'+this.ztcCount+'/'+this.hntjbcCount+'}',
            x: 'center',
            y: 'center',
            textStyle: {
              rich: {
                a: {
                  fontSize: 14,
                  color: '#fff',
                  lineHeight:18
                },
              },
            },
          },
          graphic: {
            elements: [
              {
                type: 'image',
                z: 3,
                style: {
                  image: this.img,
                  width: 100,
                  height: 100,
                },
                left: 'center',
                top: 'center',
              },
            ],
          },
          series: [
            {
              type: 'pie',
              hoverAnimation: true,
              hoverOffset: 5,
              startAngle: 180, //起始角度
              clockwise: false, //是否顺时针
              radius: ['60%', '75%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: this.ztcCount,
                  name: '渣土车数量',
                },
                {
                  value: this.hntjbcCount,
                  name: '混凝土搅拌车数量',
                }
              ],
              itemStyle: {
                normal: {
                  shadowColor: '#37B4CD',
                  shadowBlur: 30,
                  shadowOffsetY:10
                },
              },
              zlevel: 30,
            },
          ],
        };
    }

  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      wgscrqCaseNumber({address: this.address}).then(res => {
        this.ztcCount=res.data.ztcCount;
        this.hntjbcCount=res.data.hntjbcCount;
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })

    }
  }
}
</script>
