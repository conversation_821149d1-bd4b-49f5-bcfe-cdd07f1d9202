<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import echarts from 'echarts'
import {getYearMonthHotlineCaseNumber} from "@/api/shcy/screenAsjgl";
export default {
  name: "GdslChart",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      chartData:[],
      xLabel:[],
      data2: [],
      data1: [],
      barWidth : 30,
    }
  },
  computed: {
    option() {
      return {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'transparent',
          axisPointer: {
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(126,199,255,0)' // 0% 处的颜色
                }, {
                  offset: 0.5,
                  color: 'rgba(126,199,255,1)' // 100% 处的颜色
                }, {
                  offset: 1,
                  color: 'rgba(126,199,255,0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
          },
        },
        legend: {
          align: "left",
          right: '10%',
          top: '10%',
          type: 'plain',
          textStyle: {
            color: '#7ec7ff',
            fontSize: 16
          },
          // icon:'rect',
          itemGap: 25,
          itemWidth: 18,
          icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',

          data: [
            {
              name: '去年'
            },
            {
              name: '今年'
            }
          ]
        },
        grid: {
          top: '15%',
          left: '10%',
          right: '10%',
          bottom: '15%',
          // containLabel: true
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLine: { //坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#CEDFEF'
            },
          },
          axisLabel: { //坐标轴刻度标签的相关设置
            textStyle: {
              color: '#CEDFEF',
              padding: 14,
              fontSize: 12
            },
            formatter: function (data) {
              return data
            }
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#192a44'
            },
          },
          axisTick: {
            show: false,
          },
          data: this.xLabel
        }],
        yAxis: [{
          name: '',
          nameTextStyle: {
            color: "#7ec7ff",
            fontSize: 16,
            padding: 10
          },
          min: 0,
          splitLine: {
            show: false,
            lineStyle: {
              color: '#CEDFEF'
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#CEDFEF"
            }

          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#CEDFEF',
              padding: 16
            },
            formatter: function (value) {
              if (value === 0) {
                return value
              }
              return value
            }
          },
          axisTick: {
            show: false,
          },
        }],
        series: [{
          name: '今年',
          type: 'line',
          symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
          showAllSymbol: true,
          symbolSize: 0,
          smooth: true,
          lineStyle: {
            normal: {
              width: 5,
              color: "#2BE493", // 线条颜色
            },
            borderColor: 'rgba(43,228,147, 0.3)',
          },
          itemStyle: {
            color: "rgba(43,228,147,1)",
            borderColor: "#2BE493",
            borderWidth: 2

          },
          tooltip: {
            show: true
          },
          areaStyle: { //区域填充样式
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "rgba(43,228,147,.3)"
              },
                {
                  offset: 1,
                  color: "rgba(43,228,147, 0)"
                }
              ], false),
              shadowColor: 'rgba(43,228,147, 0.5)', //阴影颜色
              shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            }
          },
          data: this.data2
        },
          {
            name: '去年',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbolSize: 0,
            smooth: true,
            lineStyle: {
              normal: {
                width: 5,
                color: "#1F95F9", // 线条颜色
              },
              borderColor: 'rgba(31,149,249, 0.3)',
            },
            itemStyle: {
              color: "rgba(31,149,249,1)",
              borderColor: "#1F95F9",
              borderWidth: 2

            },
            tooltip: {
              show: true
            },
            areaStyle: { //区域填充样式
              normal: {
                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: "rgba(31,149,249,.3)"
                },
                  {
                    offset: 1,
                    color: "rgba(31,149,249, 0)"
                  }
                ], false),
                shadowColor: 'rgba(31,149,249, 0.5)', //阴影颜色
                shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
              }
            },
            data: this.data1
          }]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getYearMonthHotlineCaseNumber().then(res=>{
        this.chartData = JSON.parse(JSON.stringify(res.data))
         this.initChartData()
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    },
    initChartData() {

      for(let i=0;i<this.chartData.monthList.length;i++) {
        const month=this.chartData.monthList[i];
        const li=JSON.stringify(this.chartData.yearMonthCaseNumberList,[month]);
        const jsonArray=JSON.parse(li);
        if(li.length>2)
        {
          for (const key in jsonArray)
          {
            this.data2.push(jsonArray[key]);
          }
        }
        else {
          this.data2.push(0);
        }
        const y=this.chartData.monthList[i].split("-");
        this.xLabel.push(y[1]+"月");
      }

      for(let i=0;i<this.chartData.previousMonthList.length;i++) {
        const month=this.chartData.previousMonthList[i];
        const li=JSON.stringify(this.chartData.previousYearCaseNumberList,[month]);
        const jsonArray=JSON.parse(li);
        if(li.length>2)
        {
          for (const key in jsonArray)
          {
            this.data1.push(jsonArray[key]);
          }
        }
        else {
          this.data1.push(0);
        }
      }
    }
  }
}
</script>
