<template>
  <div :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'

import centerBgImage from '@/assets/images/chart-center-bg.png'

export default {
  name: 'WlgzsjtjChart',
  props: {
    width: {
      type: String,
      default: '100%',
      validator: (value) => /^\d+(%|px|rem|em|vh|vw)$/.test(value)
    },
    height: {
      type: String,
      default: '300px',
      validator: (value) => /^\d+(%|px|rem|em|vh|vw)$/.test(value)
    },
    total: {
      type: Number,
      default: 0,
    },
    processed: {
      type: Number,
      default: 0,
    },
    processedRate: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      chart: null,
      img: centerBgImage,
    }
  },
  computed: {
    chartData() {
      return [
        {
          value: this.processed,
          name: '已处理数'
        },
        {
          value: this.total - this.processed,
          name: '未处理数'
        }
      ]
    },
    option() {
      return {
        color: ['#00d2ff', '#22e5d1', '#f4d64e', '#0072ff'],
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
        },
        title: [
          {
            text: '事件处理率',  // 新增的标题
            textStyle: {
              fontSize: 16,
              color: '#fff',
            },
            top: '5%',
            left: 'center'
          },
          {
            text:
              '{a|处理情况\n' +
              this.formatNumber(this.processedRate * 100) +
              '%}',
            x: 'center',
            y: 'center',
            textStyle: {
              rich: {
                a: {
                  fontSize: 14,
                  color: '#fff',
                  lineHeight: 18,
                },
              },
            },
          }
        ],
        graphic: {
          elements: [
            {
              type: 'image',
              z: 3,
              style: {
                image: this.img,
                width: 100,
                height: 100,
              },
              left: 'center',
              top: 'center',
            },
          ],
        },
        series: [
          {
            type: 'pie',
            hoverAnimation: true,
            hoverOffset: 5,
            startAngle: 180, //起始角度
            clockwise: false, //是否顺时针
            radius: ['60%', '75%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: this.chartData,
            itemStyle: {
              normal: {
                shadowColor: '#37B4CD',
                shadowBlur: 30,
                shadowOffsetY: 10,
                borderWidth: 2,
                borderColor: 'rgba(255, 255, 255, 0.2)'
              }
            },
            zlevel: 30,
          },
        ],
      }
    },
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.chart?.dispose()
  },
  watch: {
    '$props': {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$el)
      }
      this.updateChart()
    },
    updateChart() {
      this.$nextTick(() => {
        this.chart?.setOption(this.option, true)
      })
    },
    handleResize() {
      this.chart?.resize()
    },
    formatNumber(value) {
      return Number(value).toFixed(2)
    }
  }
}
</script>
