<template>
  <div :style="{height:height,width:width}"/>
</template>

<script>
import echarts from 'echarts'
export default {
  name: "FxftAlarmHandleStatistic",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
    }
  },
  computed: {
    option() {
      return {
        title: {
          text: '设备报警事件统计',
          x: 'center',
          show: false
        },
        grid: {
          top: '6',
          left: '10',
          right: '10',
          bottom: '0',
          containLabel: true
        },
        backgroundColor: '',
        tooltip: {
          trigger: 'item'
        },
        xAxis: {
          data: ["22.12月", "23.1月", "23.2月", "23.3月", "23.4月", "23.5月", "23.6月", "23.7月", "23.8月", "23.9月", "23.10月", "23.11月"],
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#888'
            },
            margin: 20, //刻度标签与轴线之间的距离。
          }
        },
        yAxis: {
          splitLine: {
            show: false,
            lineStyle: {
              color: '#eee',
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#888'
            }
          },
          show: false
        },
        series: [
          {
            //最低下的圆片
            name: '',
            type: 'pictorialBar',
            symbolSize: [25, 12],
            symbolOffset: [0, 6],
            z: 12,
            data: [
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '1',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(89,211,255,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(23,237,194,1)'
                      }
                    ])
                  }
                }
              }
            ]
          },

          //下半截柱状图
          {
            name: '',
            type: 'bar',
            barWidth: 25,
            barGap: '-100%',
            itemStyle: {
              //lenged文本
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(55,255,249,1)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,222,215,0.2)'
                }
              ])
            },
            data: [
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第一节下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第二个柱状图下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第三个柱状图下半截
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第一节下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第二个柱状图下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第三个柱状图下半截
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第一节下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '1',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第二个柱状图下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第三个柱状图下半截
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第一节下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第二个柱状图下面
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          //第三个柱状图下半截
                          offset: 0,
                          color: 'rgba(0,255,245,0.5)'
                        },
                        {
                          offset: 1,
                          color: '#43bafe'
                        }
                      ]
                    }
                  }
                }
              }
            ]
          },

          // 替代柱状图 默认不显示颜色，是下半截柱图的value值
          {
            type: 'bar',
            barWidth: 25,
            barGap: '-100%',
            stack: '广告',
            itemStyle: {
              color: 'transparent'
            },
            data: [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
          },

          //中间十二个圆片
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [25, 12],
            symbolOffset: [0, -6],
            z: 12,
            data: [
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '1',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(89,211,255,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(23,237,194,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              }
            ]
          },
          //十二根上半截柱子
          {
            name: '',
            type: 'bar',
            barWidth: 50,
            barGap: '-100%',
            stack: '广告',
            itemStyle: {
              // barBorderRadius: 20,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 1,
                  color: 'rgba(0,255,100,0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,255,100,0.5)'
                }
              ])
            },
            //上班截开始
            data: [
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              },
              {
                name: '',
                value: '0',
                trueVal: '0',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      type: 'linear',
                      global: false,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ]
                    }
                  }
                }
              }
            ]
          },
          //头部十二个圆片
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [25, 12],
            symbolOffset: [0, -6],
            z: 12,
            data: [
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '1',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              },
              {
                name: '',
                value: '0',
                symbolPosition: 'end',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      0,
                      1,
                      [
                        {
                          offset: 0,
                          color: 'rgba(54,127,223,1)'
                        },
                        {
                          offset: 1,
                          color: 'rgba(94,162,254,1)'
                        }
                      ],
                      false
                    )
                  }
                }
              }
            ]
          },
        ]
      };
    }
  },
  mounted() {
    this.chart = echarts.init(this.$el)
    this.$nextTick(() => {
      this.chart.setOption(this.option, true)
    })
    // setInterval(this.draw, 100)
  },
  methods:{
    draw(){
      this.chart.setOption(this.option, true)
      //window.requestAnimationFrame(draw);
    }
  }
}
</script>

<style scoped>

</style>
