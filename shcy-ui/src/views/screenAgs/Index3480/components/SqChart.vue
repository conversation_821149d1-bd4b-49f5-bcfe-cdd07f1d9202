<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import echarts from 'echarts'
import {getCaseNumberByDemandType} from "@/api/shcy/screenAsjgl";
export default {
  name: "SqC<PERSON>",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    schartData: {
      type: Array,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      chart: null,
      xData2:[],
      data1:[],
      data2: [],
      barWidth : 30,
      chartData:{},
      total:0,
    }
  },
  watch: {
    schartData(newData, oldData)
    {
      this.init(newData);
    }
  },
 computed: {
    option() {
      return {
        title: [
          {
            text: '工单总数',
            subtext: this.total,
            textStyle:{
              fontSize:16,
              color:"#fff"
            },
            subtextStyle: {
              fontSize: 20,
              color: '#fff'
            },
            textAlign:"center",
            x: '48.5%',
            y: '44%',
          }],
        dataset: {
          source:JSON.parse(this.data1)
        },
        color: ['#EC5571', '#0092FF', '#eba954', '#21b6b9', '#60a900', '#01949b', '#f17677'],
        grid: {
          left: '50%',
          right: '5%',
          bottom:'5%',
          containLabel: true
        },
        xAxis: [{
          show: false,
        },
          {
            show: false,
          }
        ],
        yAxis: {
          type: 'category',
          inverse: true,
          show: false
        },

        series: [{
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['40%', '50%'],
          avoidLabelOverlap: false,

          label: {
            normal: {
              formatter: '{b}\n{d}%'
            },

          },
          labelLine: {
            normal: {
              show: true
            }
          },
          encode: {
            itemName: 'product',
            value: 'nums'
          }
        }
        ]
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(value) {
      if(value)
      {
        this.chartData=value;
        this.initChartData()
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      }

    },
    initChartData() {
      this.data1=[];
      this.data1.push(['product', 'nums']);
      let nu=0;
      this.total=0;
      if(this.chartData.length >3)
      {
        for(let i=0;i<3;i++) {
          const newList=[];
          newList[0]=this.chartData[i].sqdl;
          newList[1]=this.chartData[i].sqdlCount;
          this.total=this.total+Number(this.chartData[i].sqdlCount);
          this.data1.push(newList);
        }
        for(let i=3;i<this.chartData.length;i++) {
          nu=nu+Number(this.chartData[i].sqdlCount);
          this.total=this.total+Number(this.chartData[i].sqdlCount);
        }
        const newList1=[];
        newList1[0]="其他";
        newList1[1]=nu;
        this.data1.push(newList1);
      }
      else {
        for(let i=0;i<this.chartData.length;i++) {
          const newList=[];
          newList[0]=this.chartData[i].sqdl;
          newList[1]=this.chartData[i].sqdlCount;
          this.total=this.total+Number(this.chartData[i].sqdlCount);
          this.data1.push(newList);
        }
      }
      this.data1=JSON.stringify(this.data1);

    }
  }
}
</script>
