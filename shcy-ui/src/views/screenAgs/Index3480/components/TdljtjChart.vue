<template>
  <div :style="{height:height,width:width}"/>

</template>

<script>
import echarts from 'echarts'
import {tdljCaseNumber} from "@/api/shcy/screenHjzz";
import yuan from "@/assets/images/yuan.png"

export default {
  name: "TdljtjChart",
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    address: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      chart: null,
      monthCompleted:null,
      monthInProcess:null,
      monthClosureRate:null,
      img: yuan,
    }
  },
  computed: {
    option() {
      return{
          color: [
            '#00d2ff',
            '#22e5d1',
            '#f4d64e',
            '#0072ff'
          ],
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'item',
          },
          title: {
            text: '{a|完成情况\n'+this.formatNumber(this.monthClosureRate*100)+'%}',
            x: 'center',
            y: 'center',
            textStyle: {
              rich: {
                a: {
                  fontSize: 14,
                  color: '#fff',
                  lineHeight:18
                },
              },
            },
          },
          graphic: {
            elements: [
              {
                type: 'image',
                z: 3,
                style: {
                  image: this.img,
                  width: 100,
                  height: 100,
                },
                left: 'center',
                top: 'center',
              },
            ],
          },
          series: [
            {
              type: 'pie',
              hoverAnimation: true,
              hoverOffset: 5,
              startAngle: 180, //起始角度
              clockwise: false, //是否顺时针
              radius: ['60%', '75%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: this.monthCompleted,
                  name: '已完成数',
                },
                {
                  value: this.monthInProcess,
                  name: '正在处理数',
                },
              ],
              itemStyle: {
                normal: {
                  shadowColor: '#37B4CD',
                  shadowBlur: 30,
                  shadowOffsetY:10
                },
              },
              zlevel: 30,
            },
          ],
        };
    }

  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      tdljCaseNumber({address: this.address}).then(res => {
        this.monthCompleted=res.data.monthCompleted;
        this.monthInProcess=res.data.monthInProcess;
        this.monthClosureRate=res.data.monthClosureRate;
        this.chart = echarts.init(this.$el)
        this.$nextTick(() => {
          this.chart.setOption(this.option, true)
        })
      })
    },
    formatNumber(value) {
      // 使用 toFixed 方法将数字保留两位小数
      return value.toFixed(2);
    },
  }
}
</script>
