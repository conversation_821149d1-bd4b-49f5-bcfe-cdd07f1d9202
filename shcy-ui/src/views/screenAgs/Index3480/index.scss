.screen-container {
  position: relative;
  width: 3840px;
  height: 1080px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  font-family: Source Han Sans CN;

  // 地图容器
  .map-container {
    margin: 0 auto;
    width: 2090px;
    height: 1080px;

    .layer-buttons {
      position: absolute;
      bottom: 130px;
      left: 50%;
      transform: translateX(-50%);
      width: 322px;
      height: 62px;
      background-image: url('~@/assets/svg/layer_btn_bg.png');
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0;

      button {
        width: 170px;
        height: 60px;
        border: none;
        background-color: transparent;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        font-size: 16px;
        font-family: 'Source Han Sans CN', sans-serif;
        transition: all 0.3s ease;
        padding: 0;
        position: relative;

        &.active {
          background-image: url('~@/assets/svg/layer_btn.png');
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          color: #ffffff;
          font-weight: bold;
        }
      }
    }
  }

  // 上背景样式
  .background-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 3840px;
    height: 149px;
    background: url("../../../assets/screen_display_img/top_bg_yinying.png") no-repeat;
    background-size: contain;

    // 标题盒子
    .title-container {
      position: relative;
      margin: 0 auto;
      margin-top: 2px;
      width: 2004px;
      height: 143px;
      background: url("../../../assets/screen_display_img/top_bg_title.png") no-repeat;
      background-size: contain;


      .title-box {
        -moz-user-select: none;
        -khtml-user-select: none;
        user-select: none;
        text-align: center;


        .title-chinese {
          font-size: 46px;
          font-family: Hiragino Sans GB;
          font-weight: 900;
          letter-spacing: 14px;
          color: #FEFEFE;
          background: linear-gradient(0deg, #A5C8FD 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .title-pinyin {
          font-size: 10px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #AFC5FD;
          text-transform: uppercase;
          letter-spacing: 6px;
        }

      }

      // 顶部按钮
      .btn-container {
        display: flex;
        position: absolute;
        top: 126px;
        //left: 395px;
        left: 530px;
        width: 1414px;
        height: 47px;
        text-align: center;
        z-index: 999;

        .btn-top-item {
          position: relative;
          margin-right: 20px;
          width: 216px;
          height: 73px;
          line-height: 73px;
          background: url("https://pic.rmb.bdstatic.com/bjh/618794ab53e6235d892beff133bd0921.png") no-repeat;
          background-size: contain;
          cursor: pointer;

          .btn-icon {
            position: absolute;
            top: 9px;
            left: 22px;
            display: inline-block;
            width: 31px;
            height: 30px;
            background-size: contain;
          }

          .btn-title {
            display: inline-block;
            margin-left: 60px;
            width: 110px;
            height: 47px;
            font-size: 15px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            letter-spacing: 2px;
            text-align: center;
            color: #FFFFFF;
            line-height: 47px;
            -moz-user-select: none;
            -khtml-user-select: none;
            user-select: none;
          }
        }

        .btn-top-item:nth-child(1).btn-top-active1 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_csyxk_press.png") no-repeat;
        }

        .btn-top-item:nth-child(2).btn-top-active2 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_jzz_press.png") no-repeat;
        }

        .btn-top-item:nth-child(3).btn-top-active3 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_zdry_press.png") no-repeat;
        }

        .btn-top-item:nth-child(4).btn-top-active4 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_fxft_press.png") no-repeat;
        }

        .btn-top-item:nth-child(5).btn-top-active5 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_wlgz_press.png") no-repeat;
        }

        .btn-top-item:nth-child(6).btn-top-active6 {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_asjgl_press.png") no-repeat;
        }

        .party-logo {
          background: url('https://pic.rmb.bdstatic.com/bjh/7a633460d90bd60f4a5d00a38cd49d06.png') no-repeat !important;
          background-size: contain;
        }

        .btn-top-item:hover::after {
          position: absolute;
          content: '';
          display: block;
          // top: 50px;
          left: 50px;
          width: 130px;
          height: 19px;
          background: url("../../../assets/screen_display_img/home_nav_hover.png") no-repeat;
          background-size: contain;
        }

        .btn-top-active::after {
          position: absolute;
          content: '';
          display: block;
          // top: 50px;
          left: 50px;
          width: 130px;
          height: 19px;
          background: url("../../../assets/screen_display_img/home_nav_hover.png") no-repeat;
          background-size: contain;
        }

        .btn-top-item:hover {
          background: url("https://pic.rmb.bdstatic.com/bjh/623c1d959b20eb4709d6059e12c3f5a7.png") no-repeat;
          background-size: contain;
        }

        .btn-top-item:nth-child(1) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_csyxk_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(1):hover {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_csyxk_press.png") no-repeat;
        }

        .btn-top-item:nth-child(2) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_jzz_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(2):hover {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_jzz_press.png") no-repeat;
        }

        .btn-top-item:nth-child(3) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_zdry_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(3):hover {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_zdry_press.png") no-repeat;
        }

        .btn-top-item:nth-child(4) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_fxft_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(4):hover {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_fxft_press.png") no-repeat;
        }

        .btn-top-item:nth-child(5) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_wlgz_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(5):hover {
          background: url('http://12.112.123.249/images/mapTopButton/home_nav_wlgz_press.png') no-repeat;
        }

        .btn-top-item:nth-child(6) {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_asjgl_normal.png") no-repeat;
        }

        .btn-top-item:nth-child(6):hover {
          background: url("http://12.112.123.249/images/mapTopButton/home_nav_asjgl_press.png") no-repeat;
        }
      }
    }
  }

  // 下方背景样式
  .background-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3840px;
    height: 149px;
    background: url("../../../assets/screen_display_img/bottom_bg_yinying.png") no-repeat;
    background-size: contain;

    // 下方按钮容器
    .btn-bottom-container {
      position: relative;
      margin: 63px auto 0;
      width: 1752px;
      height: 75px;
      background: url("../../../assets/screen_display_img/bottom_bg_tab.png") no-repeat;
      background-size: contain;
      z-index: 999;

      // 按钮
      .btn-bottom-box {
        position: relative;
        display: flex;
        margin: 0 auto;
        width: 1104px;
        height: 61px;

        // 小区基础信息弹窗
        .basicInfoWindow {
          position: absolute;
          bottom: 500px;
          right: -166px;
          width: 239px;
          height: 246px;
          background: url("../../../assets/screen_display_img/basicInfoWindow.png") no-repeat;
          background-size: contain;
          z-index: 20;
        }

        .btn-bottom-item {
          margin: 13px 12px 0;
          width: 160px;
          height: 61px;
          background: url("../../../assets/screen_display_img/bottom_img_tab_press.png") no-repeat;
          background-size: contain;
          font-size: 12px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 61px;
          text-align: center;
          letter-spacing: 2px;
          cursor: pointer;
          opacity: 0.6;
          -moz-user-select: none;
          -khtml-user-select: none;
          user-select: none;
        }

        // 按钮透明度
        .active {
          opacity: 1;
        }
      }

      // 搜索框、下拉列表
      .search-option-container {
        display: flex;
        position: absolute;
        top: -100px;
        left: 584px;
        margin: 0 auto;
        width: 583px;
        height: 41px;

        // 搜索框
        .search-box {
          display: flex;
          width: 254px;
          height: 41px;

          .input-search {
            padding: 0 0 0 50px;
            width: 213px;
            height: 41px;
            outline: none;
            border: none;
            background-color: #36365E;
            font-size: 12px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #8F8FA7;
            line-height: 41px;
          }

          .input-search-icon {
            position: relative;
            display: inline-block;
            width: 41px;
            height: 41px;
            background-color: #474768;
            cursor: pointer;

            .search-icon {
              position: absolute;
              top: 13px;
              left: 13px;
              display: block;
              width: 12px;
              height: 14px;
              background: url("../../../assets/screen_display_img/bottom_icon_search.png") no-repeat;
              background-size: contain;
            }
          }
        }

        // 下拉框
        .sel-option-box {
          margin-left: 74px;
          // width: 764px;
          // height: 124px;
        }
      }
    }

    // 下方按钮容器
    .btn-bottom-party-container {
      position: relative;
      margin: 63px auto 0;
      width: 1752px;
      height: 75px;
      background: url("../../../assets/screen_display_img/bottom_bg_tab.png") no-repeat;
      background-size: contain;
      z-index: 999;

      // 按钮
      .btn-bottom-box {
        position: relative;
        display: flex;
        margin: 0 auto;
        width: 1104px;
        height: 61px;

        // 小区基础信息弹窗
        .basicInfoWindow {
          position: absolute;
          bottom: 500px;
          right: -166px;
          width: 239px;
          height: 246px;
          background: url("../../../assets/screen_display_img/basicInfoWindow.png") no-repeat;
          background-size: contain;
          z-index: 20;
        }

        .btn-bottom-item {
          margin: 13px 12px 0;
          width: 160px;
          height: 61px;
          background: url("../../../assets/screen_display_img/bottom_img_tab_press.png") no-repeat;
          background-size: contain;
          font-size: 12px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 61px;
          text-align: center;
          letter-spacing: 2px;
          cursor: pointer;
          opacity: 0.6;
          -moz-user-select: none;
          -khtml-user-select: none;
          user-select: none;
        }

        // 按钮透明度
        .active {
          opacity: 1;
        }
      }
    }

  }

  // 左边背景
  .background-left {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    width: 1315px;
    height: 1080px;
    background: url("../../../assets/screen_display_img/L_bg_yinying.png") no-repeat;
    background-size: contain;

    .background-left-bg{
      width:1141px;
      height:1080px;
      background-image: url("../../../assets/screen_display_img/home_wg_l_bg.png");
      position: absolute;
      z-index: 1;
    }

    // 左边内容容器
    .chart-left-container {
      width: 958px;
      height: 1080px;

      // 内容盒子
      .chart-left-box {
        display: flex;
        width: 914px;
        height: 1016px;
        margin: 35px 0 0 18px;
        position: absolute;
        z-index: 2;

        // 边框样式
        .border-left-style {
          width: 26px;
          height: 1016px;
          background: url("../../../assets/screen_display_img/L_img_biankuuang.png") no-repeat;
          background-size: contain;
        }

        // 内容展示区
        .content-data-box {
          margin-left: 84px;
          width: 803px;
          height: 100%;

          // 日期-时间容器
          .date-time-container {
            position: relative;
            margin: 18px 0 18px 0;
            width: 100%;
            height: 25px;

            span {
              font-size: 20px;
              // line-height: 75px;
              font-weight: 500;
              font-family: Source Han Sans CN;
              color: #c0d1f7;
            }

            .date {
              margin-right: 40px;
            }

            .week {
              margin-right: 46px;
            }

            .time {
              margin-right: 53px;
            }

            .today-title {
              font-size: 12px;
              font-weight: 400;
              color: #b6b6bd;
              vertical-align: super;
            }
          }

          // 今日执勤
          .beOnDuty {
            height: 33px;
            margin: 6px 0 8px 0;
            font-size: 15px;
            font-family: Source Han Sans CN;
            // letter-spacing: 8px;

            .title {
              color: #c0d1f7;
            }

            .people {
              color: #fff;
            }

          }

          .data-change-box {
            width: 803px;
            height: 895px;

            // 绿化情况容器
            .greening-situation-container {
              margin-bottom: 66px;
              width: 803px;
              height: 345px;

              .greening-situation-box {
                width: 803px;
              }

              // 绿化情况 标题
              .greening-situation-title {
                position: relative;
                width: 803px;
                height: 40px;
                font-size: 20px;
                font-family: Source Han Sans CN;
                font-weight: bold;
                color: #ffffff;
                line-height: 40px;
                background: linear-gradient(90deg,
                    #283498 0%,
                    rgba(40, 52, 152, 0.2) 100%);

                .greening-title {
                  padding-left: 32px;
                }
              }

              // 绿化情况 内容
              .greening-situation-content {
                display: flex;
                width: 803px;
                height: 305px;

                // 左边内容盒子
                .greening-left-data-box {
                  width: 436px;
                  height: 100%;

                  .greening-inner-box {
                    display: flex;
                    // flex-flow: row wrap;
                    flex-wrap: wrap;
                    margin: 32px 0 0 75px;
                    width: 411px;
                    height: 273px;

                    .greening-item {
                      display: flex;
                      margin-right: 3px;
                      margin-bottom: 24px;
                      width: 191px;
                      height: 50px;

                      .greening-icon {
                        width: 50px;
                        height: 50px !important;
                        background-size: contain;
                      }

                      .greening-detail-data {
                        margin-left: 20px;
                        width: 121px;
                        height: 50px;

                        .greening-detail-title {
                          font-size: 12px;
                          font-family: Source Han Sans CN;
                          font-weight: 400;
                          color: #b6b6bd;
                          line-height: 20px;
                        }

                        .greening-detail-number {
                          font-family: Source Han Sans CN;
                          font-weight: 500;
                          color: #ffffff;
                          line-height: 30px;

                          .greening-num {
                            font-size: 20px;
                          }

                          .greening-unit {
                            margin-left: 6px;
                            font-size: 12px;
                          }
                        }
                      }
                    }

                    .greening-item:nth-child(1) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/fe855aa8aed2cebe1714781619c273a2.png") no-repeat;
                    }

                    .greening-item:nth-child(2) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/f67dccd3ce66ad38ce7029c1102bc4ca.png") no-repeat;
                    }

                    .greening-item:nth-child(3) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/36de911b6f7ba31b0207941647251fca.png") no-repeat;
                    }

                    .greening-item:nth-child(4) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/ccc3290de4f0716f1031b02a980edc40.png") no-repeat;
                    }

                    .greening-item:nth-child(5) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/236e2b7357052c6f50fb3507467a4d4b.png") no-repeat;
                    }

                    .greening-item:nth-child(6) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/41843079c6c14ab70d62e9b344e50125.png") no-repeat;
                    }

                    .greening-item:nth-child(7) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/765e4622895304e4c62b9d916ccfcc47.png") no-repeat;
                    }

                    .greening-item:nth-child(8) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/3d3a29c11663b29a3834fcd8676e7af8.png") no-repeat;
                    }
                  }
                }

                // 右边内容盒子
                .greening-right-varieties-box {
                  width: 370px;
                  height: 100%;

                  .greening-inner-top-box {
                    margin-top: 25px;
                    width: 366px;
                    height: 15px;

                    .ring {
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 2px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 9px;
                      font-size: 16px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 15px;
                    }
                  }

                  .greening-inner-center-box {
                    width: 366px;
                    height: 223px;
                  }

                  .greening-inner-bottom-box {
                    display: flex;
                    align-items: center;
                    width: 366px;
                    height: 41px;

                    .greening-num-icon {
                      width: 32px;
                      height: 32px;
                      background: url("../../../assets/screen_display_img/L_icon_lvzhi.png") no-repeat;
                      background-size: contain;
                    }

                    .greening-num-data {
                      margin: 0 11px;
                      width: 93px;
                      height: 36px;

                      .greening-num-title {
                        width: 100%;
                        height: 15px;
                        font-size: 12px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #B6B6BD;
                      }

                      .greening-num-percentage {
                        width: 100%;
                        height: 20px;

                        .greening-num {
                          margin-right: 6px;
                          font-size: 16px;
                          font-family: Source Han Sans CN;
                          font-weight: 500;
                          color: #FFFFFF;
                        }

                        .percentage {
                          margin-right: 6px;
                          font-size: 12px;
                          font-family: Source Han Sans CN;
                          font-weight: 400;
                          color: #B6B6BD;
                        }

                        .icon_up {
                          margin-bottom: 3px;
                          display: inline-block;
                          width: 10px;
                          height: 6px;
                          background: url("../../../assets/screen_display_img/icon_up.png") no-repeat;
                          background-size: contain;
                        }

                      }
                    }

                    .greening-num-chart {
                      width: 152px;
                      height: 32px;
                    }
                  }
                }
              }
            }

            // 市容情况容器
            .city-situation-container {
              width: 100%;
              height: 485px;

              .city-situation-box {
                width: 803px;
              }

              // 市容情况标题
              .city-situation-title {
                position: relative;
                width: 100%;
                height: 40px;
                font-size: 20px;
                font-family: Source Han Sans CN;
                font-weight: bold;
                color: #ffffff;
                line-height: 40px;
                background: linear-gradient(90deg,
                    #283498 0%,
                    rgba(40, 52, 152, 0.2) 100%);

                .city-title {
                  padding-left: 32px;
                }
              }

              // 市容情况数据
              .city-situation-content {
                width: 803px;
                height: 444px;

                .city-situation-inner {
                  display: flex;
                  margin: 25px 0 0 80px;
                  width: 109px;
                  height: 419px;

                  .city-left-classify-box {
                    margin-right: 60px;
                    width: 250px;
                    height: 419px;

                    // title - 垃圾分类排名情况
                    .city-left-classify-top-box {
                      margin-bottom: 21px;
                      width: 100%;
                      height: 15px;

                      .ring {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border: 2px solid #28DFAE;
                        border-radius: 50%;
                        margin: auto;
                      }

                      .chart-title {
                        margin-left: 9px;
                        font-size: 16px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #FFFFFF;
                        line-height: 15px;
                      }
                    }

                    .city-left-classify-center-box {
                      margin-bottom: 9px;
                      width: 100%;
                      height: 56px;
                      // background-color: #fff;
                    }

                    .city-left-classify-bottom-box {
                      width: 100%;
                      height: 316px;
                      font-size: 12px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 31px;

                      .classify-ranking-table-item {
                        width: 100%;
                        height: 31px;
                        border-bottom: 1px solid #656594;

                        .serial-number {
                          margin-left: 6px;
                          margin-right: 30px;
                          display: inline-block;
                          width: 10px;
                          height: 100%;
                        }

                        .community-name {
                          margin-right: 6px;
                          display: inline-block;
                          width: 116px;
                          height: 100%;
                        }

                        .percentage {
                          margin-right: 13px;
                          display: inline-block;
                          width: 36px;
                          height: 100%;
                        }

                        .rise-and-fall {
                          margin-bottom: 3px;
                          display: inline-block;
                          width: 10px;
                          height: 6px;
                          background-repeat: no-repeat;
                          background-size: contain;
                        }
                      }
                    }
                  }

                  // 垃圾分类事件处置
                  .city-right-event-box {
                    width: 468px;
                    height: 419px;

                    // title
                    .city-right-classify-top-box {
                      margin-bottom: 21px;
                      width: 100%;
                      height: 15px;

                      .ring {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border: 2px solid #28DFAE;
                        border-radius: 50%;
                        margin: auto;
                      }

                      .chart-title {
                        margin-left: 9px;
                        font-size: 16px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #FFFFFF;
                        line-height: 15px;
                      }
                    }
                  }

                  // data - table
                  .city-right-classify-bottom-box {
                    width: 468px;
                    height: 382px;
                    background: #181839;
                    border: 1px solid #656594;
                    overflow: hidden;

                    // table - title
                    // 垃圾分类排名情况 - table
                    .city-table-title {
                      width: 100%;
                      height: 32px;
                      border-bottom: 1px solid #656594;
                      background: #2D2D64;
                      font-size: 12px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 32px;

                      .housing-estate {
                        margin-left: 50px;
                      }

                      .garbage-room {
                        margin-left: 61px;
                      }

                      .event {
                        margin-left: 79px;
                      }

                      .happen-time {
                        margin-left: 81px;
                      }
                    }

                    // 垃圾分类事件处置 - table
                    .city-table-detail-box {
                      width: 100%;
                      height: 100%;
                      overflow: scroll;
                      font-size: 12px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 35px;

                      // table - item
                      .city-table-detail-item {
                        margin: 0 auto;
                        width: 433px;
                        height: 35px;
                        border-bottom: 1px solid #656594;

                        span {
                          height: 35px;
                          display: inline-block;
                          white-space: nowrap;
                          /*超出的空白区域不换行*/
                          overflow: hidden;
                          /*超出隐藏*/
                          text-overflow: ellipsis;
                          /*文本超出显示省略号*/
                        }

                        .housing-estate-item {
                          margin-left: 10px;
                          width: 96px;
                        }

                        .garbage-room-item {
                          margin-left: 23px;
                          width: 36px;
                        }

                        .event-item {
                          margin-left: 20px;
                          width: 121px;
                        }

                        .happen-time-item {
                          margin-left: 35px;
                          width: 86px;
                        }

                      }

                      // .city-table-detail-item:last-child {
                      //   margin-bottom: 100px;
                      //   border-bottom: none;
                      // }
                    }

                    // 隐藏滚动条
                    .city-table-detail-box::-webkit-scrollbar {
                      display: none;
                    }


                  }
                }
              }
            }

            // 街道总览-人口概括-环境概括
            .dataOverview {
              width: 100%;
              height: 100%;
            }

            // 城市运行库-安全生产-平安态势
            .citySafety {
              width: 100%;
              height: 100%;
              //background: url("../../../assets/screen_display_img/lzz_left.png") no-repeat;
              background-size: contain;
            }

            // 人房库
            .peopleRoom {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/623e00c8a341c1787803bf9ad2a72e5d.png") no-repeat;
              background-size: contain;
            }

            // 民生服务库
            .liveService {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/e7c7d9c87febab5dcbf027e1a24dedd8.png") no-repeat;
              background-size: contain;
            }

            // 党建库-党建情况-工青妇-社区党校活动
            .partyBuilding {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/a1b42ad559b60533a05beb3f033ba763.png") no-repeat;
              background-size: contain;
            }
          }
        }
      }
    }

    // 左边半环
    .ring-left-box {
      width: 202px;
      height: 825px;
      background: url("../../../assets/screen_display_img/home_map_l_img.png") no-repeat;
      background-size: contain;
      margin-top: 145px;
    }
  }

  // 右边背景
  .background-right {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    width: 1315px;
    height: 1080px;
    background: url("../../../assets/screen_display_img/R_bg_yinying.png") no-repeat;
    background-size: contain;

    .background-right-bg{
      width:1141px;
      height:1080px;
      background-image: url("../../../assets/screen_display_img/home_wg_r_bg.png");
      position: absolute;
      margin-left: 154px;
      z-index: 1;
    }

    // 右边半环
    .ring-right-box {
      position: relative;
      width: 202px;
      height: 825px;
      background: url("../../../assets/screen_display_img/home_map_r_img.png") no-repeat;
      background-size: contain;
      margin-top: 145px;
      margin-left: 154px;
    }

    // 右边内容容器
    .chart-right-container {
      width: 958px;
      height: 1080px;

      .chart-right-inner {
        display: flex;
        margin: 35px 18px 0 24px;
        width: 911px;
        height: 1016px;
        position: absolute;
        z-index:2;

        // 右边内容
        .chart-right-content {
          margin-right: 82px;
          width: 803px;
          height: 1016px;

          // 最新通知
          .latest-notice-box {
            display: flex;
            margin-bottom: 53px;
            width: 803px;
            height: 57px;

            span {
              display: inline-block;
            }

            // 图标
            .latest-notice-icon {
              margin: 13px 7px 0 0;
              width: 26px;
              height: 31px;
              background: url("../../../assets/screen_display_img/R_icon_tongzhi.png") no-repeat;
              background-size: contain;
            }

            // "最新通知"字样
            .latest-notice-word {
              font-size: 20px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #C0D1F7;
              line-height: 57px;
            }

            // 通告内容
            .latest-notice-title {
              display: inline-block;
              width: 500px;
              margin-right: 25px;
              font-size: 20px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #FFFFFF;
              line-height: 57px;
              overflow: hidden;

              .notice-content {
                white-space: nowrap;
                width: max-content;
                // 跑马灯-动画效果
                // @keyframes 匀速运动 运动周期 无限执行 延迟1s 运动方向
                animation: roll linear 15s infinite 1s normal;
              }

              .notice-content:hover {
                animation-play-state: paused;
              }


            }

            // 通告时间
            .latest-notice-time {
              margin: 26px 26px 0 0;
              font-size: 12px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #B6B6BD;
            }

            // 通告切换
            .latest-notice-change {
              padding: 16px 0;
              display: flex;
              flex-flow: row wrap;
              align-items: center;
              width: 5px;

              i {
                color: #fff;
                cursor: pointer;
                background-size: contain;
              }

              // change-up
              // .change-up {}

              // change-down
            }
          }

          // 网格化事件
          .gridding-events-box {
            width: 803px;
            height: 905px;

            // 网格化事件-标题
            .gridding-events-title-box {
              position: relative;
              width: 100%;
              height: 40px;
              background: linear-gradient(90deg, #283498 0%, rgba(40, 52, 152, 0.2000) 100%);
              font-size: 20px;
              font-family: Source Han Sans CN;
              font-weight: bold;
              color: #ffffff;
              line-height: 40px;

              .gridding-events-title {
                padding-left: 32px;
              }

            }

            // 网格化事件-内容
            .gridding-events-content-box {
              width: 803px;
              // height: 2716px;

              .event-label-title-line{
                display: flex;
                justify-content: space-between;
                text-align: center;
                line-height: 46px;
                color: #FFFFFF;
                margin-top: 20px;
                font-size: 13px;
              }

              .event-label-title{
                width: 150px;
                height: 46px;
                background-image: url("../../../assets/screen_display_img/home_r_top_img.png");
              }

              // 网格化事件- 上部分
              .gridding-events-top-box {
                display: flex;
                width: 803px;

                // 案件核心指示
                .case-core-box {
                  width: 404px;
                  height: 298px;

                  // 标题
                  .case-core-title {
                    width: 100%;
                    height: 65px;
                    line-height: 65px;

                    .ring {
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 2px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 9px;
                      font-size: 16px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                  }

                  // 图表
                  .case-core-chart {
                    width: 100%;
                    height: 233px;
                    background: url("https://pic.rmb.bdstatic.com/bjh/9e3afe196fe776501e7f792ca5a9e9d4.png") no-repeat;
                    background-size: contain;
                  }
                }

                // 案件类别
                .case-category-box {
                  width: 366px;
                  height: 298px;

                  // 标题
                  .case-category-title {
                    width: 100%;
                    height: 65px;
                    line-height: 65px;

                    .ring {
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 2px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 9px;
                      font-size: 16px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }
                  }

                  // 图表
                  .case-category-chart {
                    display: flex;
                    width: 100%;
                    height: 233px;
                    background: url("https://pic.rmb.bdstatic.com/bjh/95341319b23ba9c98073b6b746afd2a7.png") no-repeat;
                    background-size: contain;

                    .case-category-chart-left {
                      width: 166px;
                      height: 100%;
                    }

                    .case-category-chart-right {
                      width: 199px;
                      height: 233px;
                    }
                  }
                }



              }

              // 网格化事件- 下部分
              .gridding-events-bottom-box {
                display: flex;
                width: 770px;
                height: 557px;

                // 事件上报情况
                .incident-reporting-box {
                  width: 542px;
                  height: 556px;

                  // title
                  .incident-reporting-title {
                    position: relative;
                    width: 100%;
                    height: 65px;
                    line-height: 65px;

                    .ring {
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 2px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 9px;
                      font-size: 16px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                    // 查看更多
                    .seeMore {
                      position: absolute;
                      top: 26px;
                      right: 0;
                      display: inline-block;
                      width: 47px;
                      height: 15px;
                      text-align: center;
                      background: #1C1C36;
                      border: 2px solid #DFDFFF;
                      border-radius: 6px;
                      font-size: 6px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 14px;
                      cursor: pointer;
                    }
                  }

                  // 图表
                  .incident-reporting-table {
                    width: 100%;
                    height: 490px;
                    background: #181839;
                    border: 2px solid #656594;

                    // 表头
                    .incident-reporting-table-title {
                      width: 100%;
                      height: 32px;
                      border-bottom: 2px solid #656594;
                      background: #2D2D64;
                      font-size: 12px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 32px;

                      .event-number {
                        margin-left: 48px;
                      }

                      .reporting-time {
                        margin-left: 73px;
                      }

                      .event-description {
                        margin-left: 73px;
                      }

                      .case-progress {
                        margin-left: 92px;
                      }
                    }

                    // 表格
                    .incident-reporting-table-detail-box {
                      width: 100%;
                      height: 456px;
                      overflow: scroll;
                      font-size: 12px;
                      font-weight: 400;
                      color: #FFFFFF;
                      background-color: #181839;

                      .incident-reporting-table-detail-item {
                        display: flex;
                        margin: 0 auto;
                        width: 503px;
                        // height: 122px;
                        border-bottom: 1px solid #656594;
                        cursor: pointer;

                        span {
                          height: 83px;
                          display: inline-block;

                        }

                        .event-number-item {
                          margin-right: 16px;
                          width: 116px;
                          line-height: 83px;
                        }

                        .reporting-time-item {
                          width: 116px;
                          line-height: 83px;
                        }

                        .event-description-item {
                          display: block;
                          margin: 10px 0 10px 16px;
                          // padding: 30px 0;
                          width: 185px;
                          height: 63px;
                          overflow: hidden;
                          -webkit-line-clamp: 1;
                          text-overflow: ellipsis;
                          display: -webkit-box;
                          -webkit-box-orient: vertical;
                        }

                        .case-progress-item {
                          margin: 30px 0 0 20px;
                          width: 61px;
                          height: 21px;
                          line-height: 21px;
                          background: #1D736E;
                          border: 2px solid #00F9EC;
                          border-radius: 3px;
                          text-align: center;
                        }

                        // 已完成\未开始\进行中
                        .completed {
                          background: #379434;
                          border: 2px solid #6DFF68;
                        }

                        .not-started {
                          background: #A06F31;
                          border: 2px solid #FFAF48;
                        }

                        .processing {
                          background: #1D736E;
                          border: 2px solid #00F9EC;
                        }

                      }

                      .incident-reporting-table-detail-item:last-child {
                        margin-bottom: 3px;
                        // border-bottom: none;
                      }

                    }

                    // 隐藏滚动条
                    .incident-reporting-table-detail-box::-webkit-scrollbar {
                      display: none;
                    }
                  }
                }

                // 监控画面
                .monitoring-screen-box {
                  margin-left: 20px;
                  width: 208px;
                  height: 556px;

                  // title
                  .monitoring-screen-title {
                    position: relative;
                    width: 100%;
                    height: 65px;
                    line-height: 65px;

                    .ring {
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 2px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 9px;
                      font-size: 16px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                    // 查看更多
                    .seeMore {
                      position: absolute;
                      top: 26px;
                      right: 0;
                      display: inline-block;
                      width: 47px;
                      height: 15px;
                      text-align: center;
                      background: #1C1C36;
                      border: 1px solid #DFDFFF;
                      border-radius: 6px;
                      font-size: 6px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 14px;
                      cursor: pointer;
                    }
                  }

                  // 图表
                  .monitoring-screen-img {
                    width: 100%;
                    height: 490px;

                    div {
                      margin-bottom: 11px;
                      width: 208px;
                      height: 114px;
                      border: 2px solid #656594;
                      overflow: hidden;
                    }

                    div:last-child {
                      margin-bottom: 0;
                    }

                  }
                }
              }
            }
          }

          // 绿化林长制
          .gradedResponsible {
            width: 803px;
            height: 905px;
          }

          // 党建库-工青妇-社区党校活动-近期要闻
          .partyBuilding {
            width: 803px;
            height: 905px;
          }

          // 防汛防台 右侧 综合数据
          .fxft-events-box{
            width: 803px;
            height: 905px;
            //background-image: url("~@/assets/screen_display_img/fxft_right_1.png");
            //background-size: contain;
            //background-repeat: no-repeat;
            // 红色边框
            //border: 2px solid #656594;
          }

          // 环境整治 右侧 历史报警
          .hjzz-events-box{
            width: 803px;
            height: 905px;
            //background-image: url("~@/assets/screen_display_img/hjzz_right.png");
            //background-size: contain;
            //background-repeat: no-repeat;
            .alarm-list-bg {
              color: #FFFFFF;
              margin-top: 22px;
              font-size: 13px;
              border: 2px solid #656594;

              .alarm-list-item {
                height: 40px;
                line-height: 40px;
                background: #2D2D64;
                display: flex;
                justify-content: space-between;
                text-align: center;
                padding: 0 20px;
              }

              .alarm-list-scroll-content {
                height: 400px;
                overflow-y: auto;
                &::-webkit-scrollbar {
                  width:2px;
                  background-color:#F5F5F5;
                }
                /*定义滚动条轨道：内阴影+圆角*/
                &::-webkit-scrollbar-track {
                  background-color:#F5F5F5;
                }
                /*定义滑块：内阴影+圆角*/
                &::-webkit-scrollbar-thumb {
                  background-color:#555;
                }

                .handle-result {
                  width: 62px;
                  height: 22px;
                  line-height: 18px;
                  margin-left: 4px;
                  margin-top: 9px;
                  background: #155E32;
                  border: 2px solid #3BE677;
                  border-radius: 10px;

                  &.handling-result {
                    background: #5F4211;
                    border: 2px solid #E6C43B;
                  }
                }

                .handle-image-icon {
                  width: 32px;
                  height: 19px;
                  background-image: url('../../../assets/screen_display_img/hjzz_kkjk_img.png');
                  margin-left: 19px;
                  margin-top: 10px;
                  cursor: pointer;
                }

                .handle-result-icon {
                  width: 22px;
                  height: 20px;
                  background-image: url('../../../assets/screen_display_img/fxft_zczj_img_icon.png');
                  margin-left: 24px;
                  margin-top: 10px;
                  cursor: pointer;
                }
              }

              .sec-color{
                background: #181839;
              }
            }

            .case_statistic_list{
              width: 400px;
              height: 235px;
              border: 2px solid #656594;
              border-top:none;
              background: #181839;
              margin-top: 25px;
              padding: 25px 10px 2px 10px;
              overflow-y: scroll;
              scrollbar-color: transparent transparent; //兼容火狐
              &::-webkit-scrollbar {
                display: none; //隐藏滚动条
              }
            }
          }
        }

        // 右边背景边框
        .chart-right-border-bg {
          width: 26px !important;
          height: 1016px;
          background: url("../../../assets/screen_display_img/R_img_biankuang.png") no-repeat;
          background-size: contain;
        }

      }
    }
  }
}
//左右侧模块的标题栏
.module_title{
  height: 40px;
  line-height: 40px;
  background: linear-gradient(90deg, #283498 0%, rgba(40,52,152,0.2) 100%);
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  padding-left: 33px;
}

.rxgd {
  margin-top: 20px;
  width: 764px;
  height: 210px;

  background-size: contain;
}

// 标题右侧-三方框样式
.small-square-box {
  display: flex;
  flex-flow: row wrap;
  // justify-content: space-around;
  justify-content: flex-end;
  // align-items: stretch;
  position: relative;
  top: 8px;
  right: 3px;
  width: 18px;
  height: 18px;

  span {
    display: block;
    margin: 4px 4px 0 0;
    width: 5px;
    height: 5px;
  }

  .square01 {
    background-color: #283498;
  }

  .square03 {
    background-color: #283498;
  }

  .square02 {
    background-color: #28dfae;
  }
}

.small-map{
  width: 301px;
  height:215px;
  margin-left: 51px;
  background-image: url("../../../assets/screen_display_img/home_l_top_img.png");
  padding-top: 12px;
  .people-num-title{
    font-size: 13px;
    font-weight: 400;
    color: #C0D1F7;
    position: relative;
    left: -4px;
  }
  .people-num{
    font-size: 16px;
    line-height: 16px;
    font-weight: bold;
    color: #FFFFFF;
    margin-top: 10px;
    position: relative;
    left: -4px;
    span{
      font-size: 12px;
      font-weight: normal;
      margin-left: 4px;
    }
  }
}

// 小地图右边四小块
.four-data-top-right{
  display: flex;
  display: -webkit-flex;
  flex-wrap: wrap;
  width: 410px;
  height: 192px;
  margin-left: 40px;
  .title{
    font-size: 13px;
    color: #B6B6BD;
    margin-top: 5px;
    margin-left: 10px;
  }
  .data{
    margin-top: 12px;
    margin-left: 24px;
    font-size: 20px;
    font-weight: bold;
    color: #4DEEFA;
    display: flex;
    align-items: baseline;
    span{
      font-size: 14px;
      margin-left: 4px;
      font-weight: normal;
    }
  }
}

.data-every-bg{
  width: 180px;
  height:86px;
  background-image: url("../../../assets/screen_display_img/home_l_top_button_normal.png");
  cursor: pointer;
  margin-left: 25px;
}

.data-every-bg-press{
  width: 180px;
  height:86px;
  background-image: url("../../../assets/screen_display_img/home_l_top_button_press.png");
  background-size: cover;
  cursor: pointer;
  margin-left: 25px;
}

.label_bg{
  display: flex;
  display: -webkit-flex;
  width: 720px;
  height: 40px;
  line-height:40px;
  background: #2F2F41;
  border: 1px solid #677BB5;
  opacity: 0.8;
  border-radius: 4px;
  position: absolute;
}

.label_item_bg{
  width: 180px;
  height:40px;
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  cursor: pointer;
}

.item_item_bg_press1{
  width: 199px;
  height:59px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  position: absolute;
  z-index: -1;
  top: -6px;
  left: -10px;
}

.item_item_bg_press2{
  width: 199px;
  height:59px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  position: absolute;
  z-index: -1;
  top: -6px;
  left: 170px;
}

.item_item_bg_press3{
  width: 199px;
  height:59px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  position: absolute;
  z-index: -1;
  top: -6px;
  left: 350px;
}

.item_item_bg_press4{
  width: 199px;
  height:59px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  position: absolute;
  z-index: -1;
  top: -6px;
  left: 530px;
}

.label_icon_sbzxqk{
  width: 26px;
  height: 22px;
  margin-top: 8px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_title_sbzxqk_cion.png");
}

.label_icon_lhsj{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_title_lvsj_cion.png");
}

.label_icon_hdsj{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_title_hdsj_cion.png");
}

.label_icon_xzsyztqk{
  width: 20px;
  height: 20px;
  margin-top: 10px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_title_xzsyztqk_cion.png");
}

.label_title{
  margin-left: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}

.content-label{
  width:803px;
  margin-top: 72px;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  position: absolute;
}

.content-label-river{
  height:264px;
  background-image: url("../../../assets/screen_display_img/home_l_bottom_hdsj_bg.png");
  background-size: cover;
  margin-top: 56px;
  opacity: 0.9;
}

.content-label-river-title{
  position: absolute;
  font-size: 13px;
  color: #FFFFFF;
  cursor: pointer;
  height: 105px;
  text-align: center;
}

.content-label-river-center-title{
  width: 100px;
  height: 26px;
  line-height: 26px;
  background: #0C0F35;
  opacity: 0.8;
  border-radius: 13px;
  position: absolute;
  left: 350px;
  top: 95px;
  color: #FFFFFF;
  font-size: 13px;
  text-align: center;
}

.content-label-river-center-data{
  position: absolute;
  left: 360px;
  top: 133px;
  color: #FFFFFF;
  font-size: 30px;
}

.content-label-river-center-dw{
  position: absolute;
  left: 388px;
  top: 172px;
  color: #FFFFFF;
  font-size: 13px;
}

.content-label-xzsyztqk-item{
  height: 270px;
  border: 1px solid #677BB5;
  opacity: 0.9;
  border-radius: 4px;
  display: flex;
  display: -webkit-box;
  flex-direction: column;
  align-items: center;
  font-size: 13px;
  color: #FFFFFF;
  text-align: center;

  .qy-icon{
    width: 72px;
    height:80px;
    margin-top: 16px;
    background-image: url("../../../assets/screen_display_img/home_l_bottom_xzsyztqk_scxqy_icon.png");
  }

  .qy-content{
    height:150px;
    line-height: 30px;
    margin-top:14px;
  }

  .qy-content-item{
    height: 30px;
    background: #092440;
    border-radius: 4px;
    cursor: pointer;
  }

  .school-icon{
    width: 72px;
    height:80px;
    margin-top: 16px;
    background-image: url("../../../assets/screen_display_img/home_l_bottom_xzsyztqk_xx_icon.png");
  }

  .school-content{
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    width: 330px;
    line-height: 30px;
    margin-top:14px;
  }

  .school-cotent-item{
    width: 160px;
    height: 30px;
    background: #114134;
    border-radius: 4px;
    cursor: pointer;
  }

  .zc-icon{
    width: 72px;
    height:80px;
    margin-top: 16px;
    background-image: url("../../../assets/screen_display_img/home_l_bottom_xzsyztqk_gyzc_icon.png");
  }

  .zc-content{
    height:150px;
    margin-top:14px;
  }

  .zc-content-item{
    height: 70px;
    background: #144D50;
    border-radius: 4px;
    cursor: pointer;
    color: #FFFFFF;
    font-size: 13px;
    padding-top: 8px;
  }
}

.content-label-sbzxqk-bg{
  width: 600px;
  display: flex;
}

.content-label-fxft-bg {
  position: relative;
  width: 240px;
  height: 230px;
  background-image: url("../../../assets/screen_display_img/home_img_fxft.png");
  background-size: cover;
  cursor: pointer;
}

.content-label-fxft-bg-1 {
  position: absolute;
  left: 25%;
  top: 60%;
  color: #FFFFFF;
}

.content-label-fxft-bg-2 {
  position: absolute;
  left: 35%;
  top: 60%;
  color: #FFFFFF;
}

.content-label-hjzz-bg {
  position: relative;
  width: 240px;
  height: 230px;
  margin-left: 20px;
  background-image: url("../../../assets/screen_display_img/home_img_hjzz.png");
  background-size: cover;
}

.content-label-item-bg{
  width: 152px;
  height: 252px;
  display: flex;
  display: -webkit-box;
  justify-content: center;
  align-items: center;
  .content-label-item{
    width:140px;
    height:240px;
    background-image: url("../../../assets/screen_display_img/home_l_bottom_lv_ibg.png");
    background-size: cover;
    display: flex;
    display: -webkit-box;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    .content-lebel-item-icon-lhmj{
      width: 60px;
      height:61px;
      background-image: url("../../../assets/screen_display_img/home_l_bottom_lvmj_icon.png");
      margin-top: 20px;
    }

    .content-lebel-item-icon-lhfgl{
      width: 60px;
      height:61px;
      background-image: url("../../../assets/screen_display_img/home_l_bottom_lvfgl_icon.png");
      margin-top: 20px;
    }

    .content-lebel-item-icon-lhfgl{
      width: 60px;
      height:61px;
      background-image: url("../../../assets/screen_display_img/home_l_bottom_ljzlvmj_icon.png");
      margin-top: 20px;
    }

    .content-lebel-item-icon-jzlhmj{
      width: 60px;
      height:61px;
      background-image: url("../../../assets/screen_display_img/home_l_bottom_dwlvmj_icon.png");
      margin-top: 20px;
    }

    .content-lebel-item-icon-dwlhmj{
      width: 60px;
      height:61px;
      background-image: url("../../../assets/screen_display_img/home_l_bottom_gglvmj_icon.png");
      margin-top: 20px;
    }

    .label-item-title{
      font-size: 16px;
      font-weight: bold;
      color: #FFFFFF;
      margin-top: 2px;
    }

    .label-item-data{
      font-size: 36px;
      font-weight: bold;
      color: #FFFFFF;
      text-shadow: 0px 10px 10px #021710;
      margin-top: 30px;
    }

    .label-item-data-dw{
      font-size: 13px;
      font-weight: 500;
      color: #FFFFFF;
      margin-top:8px;
      margin-bottom: 8px;
    }
  }
}

.other-link{
  width: 262px;
  height:52px;
  background-image: url("../../../assets/screen_display_img/home_r_bottom_lj_bg.png");
  font-size: 13px;
  color: #FFFFFF;
  line-height: 52px;
  padding-left: 34px;
  cursor:pointer;
}

.content-label-item-bg-border{
  border: 6px solid #1FFF64;
  border-radius: 10px;
}

// 街长制左半侧
.shop-overview{
  height:56px;
  display: flex;
  display: -webkit-flex;
  align-items: center;

  .category-circle-icon{
    width: 11px;
    height: 11px;
    border: 2px solid #28DFAE;
    border-radius: 50%
  }

  .category{
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 8px;
    margin-left: 10px;
  }
}

.overview-area {
  display: flex;
  display: -webkit-flex;

  .overview-area-left {
    width: 400px;
    height: 288px;
    display: flex;
    display: -webkit-flex;
    flex-direction: column;
    justify-content: space-between;

    .overview-item {
      height: 60px;
      background: #112A44;
      line-height: 60px;
      display: flex;
      display: -webkit-flex;
      justify-content: space-between;
    }

    .item-left {
      display: flex;
      display: -webkit-flex;
    }

    .item-left-icon {
      width: 10px;
      height: 100%;
      background: #28DFAE;
    }

    .item-left-title {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      margin-left: 12px;
    }

    .item-right-data {
      font-size: 30px;
      font-weight: bold;
      color: #4DEEFA;
      margin-right: 32px;
    }

    .item-right-dw {
      font-size: 13px;
      margin-left: 10px;
      font-weight: 400;
    }
  }
}

.patrol-today-data{
  font-size: 30px;
  font-weight: bold;
  color: #4DEEFA;
  text-align: right;
  line-height: 28px;

  .patrol-num-dw {
    font-size: 13px;
    margin-left: 10px;
    font-weight: 400;
  }
}

.patrol-data-area{
  margin-top: 80px;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;

  .patrol-data-item{
    width:160px;
    height:180px;
    display: flex;
    display: -webkit-box;
    flex-direction: column;
    align-items: center;

    .title{
      font-size: 13px;
      font-weight: 500;
      color: #FFFFFF;
      margin-top: 12px;
    }

    .line{
      width: 120px;
      height: 1px;
      background: #FFFFFF;
      margin-top: 13px;
    }

    .data{
      font-size: 40px;
      font-weight: bold;
      color: #FFFFFF;
      text-shadow: 0px 4px 4px rgba(56,56,56,0.4);
      margin-top: 65px;
      position: relative;
      left: 9px;
    }

    .dw{
      font-size: 18px;
      font-weight: 400;
      color: #FFFFFF;
      margin-left: 4px;
    }
  }

  .patrol-shop-today{
    background-image: url('../../../assets/screen_display_img/home_lzz_l_bottom_yxc_bg.png');
    background-size: cover;
  }

  .patrol-shop-location-warning{
    background-image: url('../../../assets/screen_display_img/home_lzz_l_bottom_wxc_bg.png');
    background-size: cover;
  }

  .patrol-shop-check-pass{
    background-image: url('../../../assets/screen_display_img/home_lzz_l_bottom_pass_bg.png');
    background-size: cover;
  }

  .patrol-shop-check-nopass{
    background-image: url('../../../assets/screen_display_img/home_lzz_l_bottom_xcwtg_bg.png');
    background-size: cover;
  }
}

.arraw-line{
  margin-top: 10px;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;

  .arraw-bg{
    width:160px;
    height:9px;
    display:flex;
    display: -webkit-flex;
    justify-content: center;
  }
}

.auto-scroll-title-line{
  background: #2D2D64;
  border: 2px solid #656594;
  height:40px;
  line-height: 40px;
  font-size: 13px;
  color: #FFFFFF;
  text-align:center;
  display: flex;
  display: -webkit-flex;
  box-sizing: content-box;
  margin-top: 20px;

  div{
    width:163px;
  }
}

.auto-scroll{
  height:282px;
  overflow: hidden;

  .item{
    height:40px;
    line-height: 40px;
    font-size: 13px;
    color: #FFFFFF;
    text-align:center;
    display: flex;
    display: -webkit-flex;

    div{
      width: 163px;
      display: flex;
      display: -webkit-flex;
      justify-content: center;
      align-items: center;
    }
  }
  .item1{
    height:40px;
    line-height: 40px;
    font-size: 13px;
    color: #FFFFFF;

  }
}

.scroll-list-item-result{
  padding:0 5px;
  height: 22px;
  line-height:18px;
  border-radius: 10px;
}

.correct{
  background: #379434;
  border: 2px solid #6DFF68;
}

.warning{
  background: #EEA21F;
  border: 2px solid #F8CB48;
}

.pass{
  background: #1E57D8;
  border: 2px solid #2B8EE8;
}

.nopass{
  background: #F3333C;
  border: 2px solid #FC5574;
}

.neighborhood-select{
  width: 400px;

  .el-select.el-select--medium {
    width: 100%;

    ::v-deep .el-input__inner{
      background: #08182F;
      border: 1px solid #515F6F;
      color: #FFFFFF;
      height:41px;
      font-size: 13px;
      padding-left: 26px;
    }
  }
}


.fxft-left-bg {
  margin-top: 12px;
  width: 800px;
  height: 794px;
  //background: url("https://pic.rmb.bdstatic.com/bjh/user/ca699ffa659ed87924514618b35966d7.png") no-repeat;
  //background-size: contain;
  .event-type-bg{
    display: flex;
    justify-content: space-between;
    .label{
      width: 160px;
      font-size: 13px;
      .label-icon{
        height: 36px;
        background: linear-gradient(180deg, #030315 0%, #1C8262 100%);
        border: 1px solid rgba(17,159,101,0.63);
        border-radius: 18px;
        color: #FFFFFF;
        line-height: 36px;
        text-align:center;
      }
      .label-num-bg{
        display: flex;
        justify-content: center;
        align-items: baseline;
        margin-top: 10px;
        .label-num{
          font-weight: bold;
          color: #FFFFFF;
          font-size: 24px;
        }
        .label-num-dw{
          color: #B6B6BD;
          margin-left: 6px;
        }
      }
    }
  }
  .alarm-list-bg{
    color: #FFFFFF;
    margin-top: 12px;
    font-size: 13px;
    border: 2px solid #656594;
    .alarm-list-item{
      height: 40px;
      line-height: 40px;
      background: #2D2D64;
      display: flex;
      justify-content: space-between;
      text-align:center;
      padding: 0 20px;
    }
    .alarm-list-scroll-content{
      height:360px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width:2px;
        background-color:#F5F5F5;
      }
      /*定义滚动条轨道：内阴影+圆角*/
      &::-webkit-scrollbar-track {
        background-color:#F5F5F5;
      }
      /*定义滑块：内阴影+圆角*/
      &::-webkit-scrollbar-thumb {
        background-color:#555;
      }
      .operation-bg{
        width: 62px;
        height: 22px;
        background: #175343;
        border: 2px solid #22f8bf;
        border-radius: 2px;
        margin-left: 4px;
        display: flex;
        justify-content: center;
        margin-top: 9px;
        cursor: pointer;
        .text{
          line-height: 18px;
          margin-left: 4px;
        }
        .arrow-icon-right{
          width: 0;
          height: 0;
          border-top: 5px solid transparent;
          border-right: 5px solid transparent;
          border-left: 5px solid #FFFFFF;
          border-bottom: 5px solid transparent;
          margin-left: 8px;
          margin-top: 5px;
        }
        .arrow-icon-bottom{
          width: 0;
          height: 0;
          border-top: 5px solid #FFFFFF;
          border-right: 5px solid transparent;
          border-left: 5px solid transparent;
          border-bottom: 5px solid transparent;
          margin-left: 6px;
          margin-top: 8px;
        }
      }
      .operation-confirm-btn{
        width: 62px;
        height: 22px;
        background: #173676;
        border: 2px solid #419FFF;
        border-radius: 2px;
        margin-left: 4px;
        line-height: 20px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        .arrow-icon-right{
          width: 0;
          height: 0;
          border-top: 5px solid transparent;
          border-right: 5px solid transparent;
          border-left: 5px solid #FFFFFF;
          border-bottom: 5px solid transparent;
          margin-left: 8px;
          margin-top: 5px;
        }
      }
      .handle-result{
        width: 62px;
        height: 22px;
        line-height: 18px;
        margin-left:4px;
        margin-top:9px;
        background: #155E32;
        border: 2px solid #3BE677;
        border-radius: 10px;
        &.handling-result{
          background: #5F4211;
          border: 2px solid #E6C43B;
        }
        &.unHandle-result{
          background: #183f90;
          border: 2px solid #419FFF;
        }
      }
      .handle-image-icon{
        width:22px;
        height:20px;
        background-image: url('../../../assets/screen_display_img/fxft_zczj_img_icon.png');
        margin-left: 24px;
        margin-top: 10px;
        cursor: pointer;
      }
    }
    .sec-color{
      background: #181839;
    }
  }
}

.hjzz-left-bg {
  margin-top: 20px;
  width: 800px;
  height: 794px;
  .tdljtj {
    margin-top: 15px;
    width: 800px;
    height: 254px;
  }
  .tdljtj_1 {
    width: 380px;
    background: #01152A;
    border: 1px solid rgba(81, 245, 255, 0.63);
    height: 232px;
    float:left;
    .tdljtj_1_1 {
      float: left;
      width: 168px;
      height: 232px
    }

    .tdljtj_1_2 {
      font-family: Source Han Sans CN;
      float: left;
      margin-left: 10px;
      margin-top: 20px;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 24px;
    }

    .tdljtj_1_3 {
      margin-left: 20px;
      margin-top: 20px;
      float: left;
    }
    .tdljtj_1_4{
      width:80%;
      float:left;
      font-family: Source Han Sans CN;
      margin-left:20px;
      line-height:55px;
      font-weight: 500;
      font-size: 13px;
      color: #B6B6BD;
    }
    .tdljtj_1_5{
      width:80%;
      float:left;
      font-family: Source Han Sans CN;
      margin-left:20px;
      font-weight: bold;
      font-size: 20px;
      color: #4DEEFA;
      line-height: 24px;
    }
  }
  .wgscrqtj {
    margin-top: 15px;
    width: 800px;
  }
  .alarm-list-bg {
    color: #FFFFFF;
    margin-top: 22px;
    font-size: 13px;
    border: 2px solid #656594;

    .alarm-list-item {
      height: 40px;
      line-height: 40px;
      background: #2D2D64;
      display: flex;
      justify-content: space-between;
      text-align: center;
      padding: 0 20px;
    }

    .alarm-list-scroll-content {
      height: 360px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width:2px;
        background-color:#F5F5F5;
      }
      /*定义滚动条轨道：内阴影+圆角*/
      &::-webkit-scrollbar-track {
        background-color:#F5F5F5;
      }
      /*定义滑块：内阴影+圆角*/
      &::-webkit-scrollbar-thumb {
        background-color:#555;
      }

      .operation-bg {
        width: 62px;
        height: 22px;
        background: #175343;
        border: 2px solid #22f8bf;
        border-radius: 2px;
        margin-left: 4px;
        display: flex;
        justify-content: center;
        margin-top: 9px;
        cursor: pointer;

        .text {
          line-height: 18px;
          margin-left: 4px;
        }

        .arrow-icon-right {
          width: 0;
          height: 0;
          border-top: 5px solid transparent;
          border-right: 5px solid transparent;
          border-left: 5px solid #FFFFFF;
          border-bottom: 5px solid transparent;
          margin-left: 8px;
          margin-top: 5px;
        }

        .arrow-icon-bottom {
          width: 0;
          height: 0;
          border-top: 5px solid #FFFFFF;
          border-right: 5px solid transparent;
          border-left: 5px solid transparent;
          border-bottom: 5px solid transparent;
          margin-left: 6px;
          margin-top: 8px;
        }
      }

      .operation-confirm-btn {
        width: 62px;
        height: 22px;
        background: #173676;
        border: 2px solid #419FFF;
        border-radius: 2px;
        margin-left: 4px;
        line-height: 20px;
        cursor: pointer;
        display: flex;
        justify-content: center;

        .arrow-icon-right {
          width: 0;
          height: 0;
          border-top: 5px solid transparent;
          border-right: 5px solid transparent;
          border-left: 5px solid #FFFFFF;
          border-bottom: 5px solid transparent;
          margin-left: 8px;
          margin-top: 5px;
        }
      }

      .handle-result {
        width: 62px;
        height: 22px;
        line-height: 18px;
        margin-left: 4px;
        margin-top: 9px;
        background: #155E32;
        border: 2px solid #3BE677;
        border-radius: 10px;

        &.handling-result {
          background: #5F4211;
          border: 2px solid #E6C43B;
        }
      }

      .handle-image-icon {
        width: 32px;
        height: 19px;
        background-image: url('../../../assets/screen_display_img/hjzz_kkjk_img.png');
        margin-left: 19px;
        margin-top: 10px;
        cursor: pointer;
      }
    }

    .sec-color {
      background: #181839;
    }

    //监控播放列表
    .monitor-video-bg{
      height:316px;
      display:flex;
      padding: 8px 12px;
      .monitor-item-list{
        margin-left: 15px;
        width:240px;
        height:300px;
        border: 2px solid #656594;
        text-align: center;
        overflow-y: scroll;
        line-height: 40px;
        scrollbar-color: transparent transparent; //兼容火狐
        &::-webkit-scrollbar {
          display: none; //隐藏滚动条
        }
      }
    }
  }

  .hjzz-monitor-switch-btn{
    height:34px;
    line-height: 30px;
    cursor:pointer;
    background: #13779370;
    border: 2px solid #17A7C5;
    border-radius: 4px;
  }
  .hjzz-monitor-switch-btn:hover{
    background: #137793;
  }
}

// 垃圾分类排名三种变化情况： 上升 下降 不变
.rise {
  background-image: url('../../../assets/screen_display_img/icon_up.png');
}

.fall {
  background-image: url('../../../assets/screen_display_img/icon_down.png');
}

// .noChange {
//   background-image: url();
// }


#gather-num {
  font-size: 11px;
  font-weight: bold;
  color: #fff;
  // border-color: ;
}

// 跑马灯-关键帧
@keyframes roll {
  0% {
    transform: translateX(500px);
  }

  100% {
    transform: translateX(-100%);
  }
}

.label_item_bg_press {
  background-image: url("../../../assets/screen_display_img/home_l_bottom_tabs_bg_press.png");
  background-size: cover;
  margin-top: -3px;
}
