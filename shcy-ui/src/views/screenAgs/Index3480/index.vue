<template>
  <div class="screen-container">
    <streetChiefMixin
      :checkLogThreeData="shopcheckLogList">
    </streetChiefMixin>
    <!-- 地图容器 -->
    <div class="map-container">
      <!-- 地图组件 -->
      <ScreenAgsMapShow
        ref="child"
        @getDefaultVal="getDefaultVal"
        @getAllBtnVal="getAllBtnVal"
        @resetJbgkPosition="resetJbgkPosition"
        v-show="!showSvgMap"
      />
      <ScreenSvgMapShow ref="svgMap" v-show="showSvgMap" @buttonClick="handleButtonClick" @nameClick="handleNameClick" />
      <!-- 图层切换按钮组 -->
      <div class="layer-buttons" v-show="layerChangeShow">
        <button v-for="(layer, index) in layers" :key="index" :class="{ 'active': layer.active }" @click="handleLayerChange(layer)">
          {{ layer.text }}
        </button>
      </div>
    </div>
    <!-- 上背景 -->
    <div class="background-top">
      <div class="title-container">
        <div class="title-box">
          <div class="title-chinese" >石化街道城市运行管理平台</div>
          <div class="title-pinyin">
            shihua neighborhood City operation management platform
          </div>
        </div>
        <div class="btn-container" >
          <div
            v-for="(item, index) in topBtn"
            :key="item.number"
            :class="
              selTopIndex === index
                ? ('btn-top-item btn-top-active btn-top-active'+(index+1))
                : 'btn-top-item'
            "
            @click="btnTopClick(index)"
            :ref="'topBtn0' + index"
          >
            <span class="btn-icon"></span>
            <span class="btn-title" :ref="'topBtnTitle0' + index">{{
              item.title
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 下背景 -->
    <div class="background-bottom">
    </div>
    <!-- 左背景 -->
    <div class="background-left">
      <div class="chart-left-container">
        <div class="background-left-bg"></div>
        <div class="chart-left-box">
          <!-- 边框背景 -->
          <div class="border-left-style"></div>
          <!-- 内容盒子 -->
          <div class="content-data-box">
            <!-- 日期时间容器 -->
            <div class="date-time-container">
              <span class="date">{{ dateYear }}</span>
              <span class="week">{{ dateWeek }}</span>
              <span class="time">{{ dateDay }}</span>
            </div>
            <!-- 今日值班人员 -->
            <div class="beOnDuty"></div>

            <div class="data-change-box">
              <!-- 城市运行库 -->
              <div class="dataOverview" v-show="dataOverviewChange">
                <BasicOverview
                  :jbgkPosition="jbgkPosition"
                  @show-in-map="showInMap"
                />

                <DeviceStatusTabs
                  :labelPosition="labelPosition"
                  :deviceStatusNum="deviceStatusNum"
                  @set-label-position="setLabelPosition"
                  @show-greening-in-map="showGreeningInMap"
                  @show-in-map-data="showInMapData"
                  @open-liquid-level-device-page="openLiquidLevelDevicePage"
                />
              </div>
              <!-- 街长制 -->
              <div class="citySafety" v-if="citySafetyChange">
                <div class="module_title">
                  <span>街长制概况</span>
                  <div class="small-square-box">
                    <span class="square01"></span>
                    <span class="square02"></span>
                    <span class="square03"></span>
                  </div>
                </div>
                <div class="label_bg" style="width: 360px;margin-top: 16px;">
                  <div class="label_item_bg" @click="setStreetRoadShopLabel(1)">
                    <div class="label_title">街长路段情况</div>
                  </div>
                  <div class="label_item_bg" @click="setStreetRoadShopLabel(0)">
                    <div class="label_title">商铺总体情况</div>
                  </div>
                  <div class="item_item_bg_press1" v-show="streetRoadShopLabel === 1"></div>
                  <div class="item_item_bg_press2" v-show="streetRoadShopLabel === 0"></div>
                </div>
                <div class="overview-area" style="margin-top: 70px;">
                  <div class="overview-area-left">
                    <div class="overview-item">
                      <div class="item-left">
                        <div class="item-left-icon"></div>
                        <div class="item-left-title">{{streetRoadShopLabel === 0 ? "商铺总数" : "街长路段总数"}}</div>
                      </div>
                      <div class="item-right-data">{{ streetRoadShopLabel === 0 ?shopInfo.allNum : shopInfo.roadNum}}<span class="item-right-dw">{{streetRoadShopLabel === 0 ? "户" : "条"}}</span></div>
                    </div>
                    <div class="overview-item">
                      <div class="item-left">
                        <div class="item-left-icon" style="background: #0086FF;"></div>
                        <div class="item-left-title">{{streetRoadShopLabel === 0 ? "正常商铺数量" : "各级街长人数"}}</div>
                      </div>
                      <div class="item-right-data">{{ streetRoadShopLabel === 0 ?shopInfo.normalNum : shopInfo.streetChiefNum}}<span class="item-right-dw">{{streetRoadShopLabel === 0 ? "户" : "名"}}</span></div>
                    </div>
                    <div class="overview-item">
                      <div class="item-left">
                        <div class="item-left-icon" style="background: #FFD300;"></div>
                        <div class="item-left-title">{{streetRoadShopLabel === 0 ? "歇业商铺数量" : "在线巡检人数"}}</div>
                      </div>
                      <div class="item-right-data">{{ streetRoadShopLabel === 0 ?shopInfo.stopNum : shopInfo.patrolNum}}<span class="item-right-dw">{{streetRoadShopLabel === 0 ? "户" : "名"}}</span></div>
                    </div>
                    <div class="overview-item">
                      <div class="item-left">
                        <div class="item-left-icon" style="background: #FB5252;"></div>
                        <div class="item-left-title">{{streetRoadShopLabel === 0 ? "关停商铺数量" : "重点路段总数"}}</div>
                      </div>
                      <div class="item-right-data">{{ streetRoadShopLabel === 0 ?shopInfo.closeNum : shopInfo.importantRoadNum}}<span class="item-right-dw">{{streetRoadShopLabel === 0 ? "户" : "条"}}</span></div>
                    </div>
                  </div>
                  <div style="width: 400px;height:250px;">
                    <TrafficChart height="288px" width="400px" :data="streetRoadShopLabel === 0 ? trafficData : trafficStreetChiefPatrolData"></TrafficChart>
                  </div>
                </div>
                <div class="shop-overview" style="height:42px;margin-top: 10px;">
                  <div class="category-circle-icon"></div>
                  <div class="category">巡查情况</div>
                </div>
                <div style="display: flex;display: -webkit-flex;">
                  <div class="label_bg" style="width: 540px">
                    <div class="label_item_bg" @click="setPatrolLabel(2)">
                      <div class="label_title">街长制商铺情况</div>
                    </div>
                    <div class="label_item_bg" @click="setPatrolLabel(0)">
                      <div class="label_title">今日商铺巡查情况</div>
                    </div>
                    <div class="label_item_bg" @click="setPatrolLabel(1)">
                      <div class="label_title">本月商铺巡查情况</div>
                    </div>
                    <div class="item_item_bg_press1" v-show="patrolPosition === 2"></div>
                    <div class="item_item_bg_press2" v-show="patrolPosition === 0"></div>
                    <div class="item_item_bg_press3" v-show="patrolPosition === 1"></div>
                  </div>
                  <div style="position: absolute;right:0;">
                    <div style="font-size: 13px;color: #FFFFFF">{{ patrolPosition === 0 ? '本日' : patrolPosition === 1 ? '本月' :'本日' }}巡查商铺总数</div>
                    <div class="patrol-today-data">{{ patrolBaoBaoShopData[patrolPosition][0].num }}<span class="patrol-num-dw">户</span></div>
                  </div>
                </div>
                <div class="patrol-data-area" style="margin-top: 56px;">
                  <div class="patrol-data-item patrol-shop-today">
                    <div class="title">{{ patrolBaoBaoShopData[patrolPosition][0].title }}</div>
                    <div class="line"></div>
                    <div class="data">{{ patrolBaoBaoShopData[patrolPosition][0].num }}<span class="dw">户</span></div>
                  </div>
                  <div class="patrol-data-item patrol-shop-location-warning">
                    <div class="title">{{ patrolBaoBaoShopData[patrolPosition][1].title }}</div>
                    <div class="line"></div>
                    <div class="data">{{ patrolBaoBaoShopData[patrolPosition][1].num }}<span class="dw">户</span></div>
                  </div>
                  <div class="patrol-data-item patrol-shop-check-pass">
                    <div class="title">{{ patrolBaoBaoShopData[patrolPosition][2].title }}</div>
                    <div class="line"></div>
                    <div class="data">{{ patrolBaoBaoShopData[patrolPosition][2].num }}<span class="dw">户</span></div>
                  </div>
                  <div class="patrol-data-item patrol-shop-check-nopass">
                    <div class="title">{{ patrolBaoBaoShopData[patrolPosition][3].title }}</div>
                    <div class="line"></div>
                    <div class="data">{{ patrolBaoBaoShopData[patrolPosition][3].num }}<span class="dw">户</span></div>
                  </div>
                </div>
                <div class="arraw-line">
                  <div class="arraw-bg">
                    <div style="border: 8px solid transparent;border-top-color:  #18B25D;"></div>
                  </div>
                  <div class="arraw-bg">
                    <div style="border: 8px solid transparent;border-top-color:  #FFB432;"></div>
                  </div>
                  <div class="arraw-bg">
                    <div style="border: 8px solid transparent;border-top-color:  #1E57D8;"></div>
                  </div>
                  <div class="arraw-bg">
                    <div style="border: 8px solid transparent;border-top-color:  #FB5252;"></div>
                  </div>
                </div>
                <div style="margin-top: 10px;width:800px;height:160px;" ref="patrol-shop-circle"></div>
              </div>
              <!-- 防汛防台 -->
              <div class="dataOverview" v-show="fxftChange">
                <div class="fxft-left-bg">
                  <!--事件处置-->
                  <div class="module_title" style="margin-top: 12px;">
                    <span>事件处置</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>

                  <div class="label_bg" style="position: relative;margin-top: 12px;width: 540px">
                    <div class="label_item_bg" :class="isActive == 'fxft' ? 'label_item_bg_press' : '' " @click="changeFxftAlarm('fxft')">
                      <div class="label_title" :style="isActive == 'fxft' ? {'margin-top': '3px'} : ''">液位超限</div>
                    </div>
                    <div class="label_item_bg" :class="isActive == 'tfsj' ? 'label_item_bg_press' : '' " @click="changeFxftAlarm('tfsj')">
                      <div class="label_title" :style="isActive == 'tfsj' ? {'margin-top': '3px'} : ''">突发事件</div>
                    </div>
                    <div class="label_item_bg" :class="isActive == 'jjrw' ? 'label_item_bg_press' : '' " @click="changeFxftAlarm('jjrw')">
                      <div class="label_title" :style="isActive == 'jjrw' ? {'margin-top': '3px'} : ''">紧急任务</div>
                    </div>
                  </div>

                  <!-- 液位超限 -->
                  <div class="alarm-list-bg" v-if="isFxftShow">
                    <!-- 报警列表表头 -->
                    <div class="alarm-list-item" style="padding-left: 10px; border-bottom: 2px solid #656594;">
                      <div style="width:200px;height:26px;">设备点位</div>
                      <div style="width:150px;">管网类型</div>
                      <div style="width:150px;">监测水体</div>
                      <div style="width:150px;">当日报警数</div>
                    </div>
                    <!-- 滚动列表 -->
                    <div class="alarm-list-scroll-content">
                      <div ref="main">
                        <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(nbAlarmRecordVO, index) in todayAlarmRecordList">
                          <div style="width:200px;">{{ nbAlarmRecordVO.deviceName }}</div>
                          <div style="width:150px;">{{ nbAlarmRecordVO.pipelineType }}</div>
                          <div style="width:150px;" >{{ nbAlarmRecordVO.monitoredWaterBody}}</div>
                          <div style="width:150px;" >{{ nbAlarmRecordVO.todayAlarmNum}}</div>
                        </div>
                      </div>
                      <div class="scroll-bottom" ref="scroll-bottom"></div>
                    </div>
                  </div>

                  <!-- 突发事件 -->
                  <div class="alarm-list-bg" v-if="isTfsjShow">
                    <!-- 报警列表表头 -->
                    <div class="alarm-list-item" style="padding-left: 10px; border-bottom: 2px solid #656594;">
                      <div style="width:86px;height:26px;">
                        <el-select v-model="selectEmergencyStatus" placeholder="请选择" @change="refreshFxftEmergencyList">
                          <el-option key="10" label="全部" value=""></el-option>
                          <el-option key="1" label="待处理" value="1"></el-option>
                          <el-option key="0" label="已处理" value="0"></el-option>
                        </el-select>
                      </div>
                      <div style="width:115px;">派遣时间</div>
                      <div style="width:154px;">事件类型</div>
                      <div style="width:135px;height:26px;">
                        <el-select v-model="selectEmergencyAddress" placeholder="事件地点" @change="refreshFxftEmergencyList" clearable>
                          <el-option v-for="(deviceAddress, index) in fxftAlarmDeviceAddressList" :key="index" :label="deviceAddress" :value="deviceAddress"></el-option>
                        </el-select>
                      </div>
                      <div style="width:115px;">处理人</div>
                      <div style="width:70px;margin-left: 20px;">处置情况</div>
                      <div style="width:140px;">处置状态<i class="el-icon-refresh" style="cursor: pointer" @click="refreshFxftEmergencyList" title="刷新"></i></div>
                    </div>
                    <!-- 滚动列表 -->
                    <div class="alarm-list-scroll-content">
                      <div ref="main">
                        <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(emergencyInfo, index) in fxftEmergencyList">
                          <el-checkbox :value="selectedEmergencyIds.indexOf(emergencyInfo.id) !== -1" @change=""></el-checkbox>
                          <div style="width:160px;margin-left: 15px;">{{ emergencyInfo.createTime }}</div>
                          <div style="width:154px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">突发事件</div>
                          <div style="width:135px;">{{ emergencyInfo.alarmLocation}}</div>
                          <div style="width:115px;">{{ emergencyInfo.disposalPerson }}</div>
                          <div style="width:70px;position:relative;margin-left: 20px;">
                            <div class="handle-image-icon" v-if="emergencyInfo.circulationState === '0'" @click="openFxftEmergencyWindow(index)"></div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="emergencyInfo.circulationState == 1">
                            <div class="handle-result handling-result" style="margin-left: 0;margin-top: 0">待处理</div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="emergencyInfo.circulationState == 0" >
                            <div class="handle-result" style="margin-left: 0;margin-top: 0">已处理</div>
                          </div>
                        </div>
                      </div>
                      <div class="scroll-bottom" ref="scroll-bottom"></div>
                    </div>
                  </div>

                  <!-- 紧急任务 -->
                  <div class="alarm-list-bg" v-if="isJjrwShow">
                    <!-- 报警列表表头 -->
                    <div class="alarm-list-item" style="padding-left: 10px; border-bottom: 2px solid #656594;">
                      <div style="width:86px;height:26px;">
                        <el-select v-model="selectUrgentTaskStatus" placeholder="请选择" @change="refreshFxftUrgentTaskList">
                          <el-option key="10" label="全部" value=""></el-option>
                          <el-option key="1" label="待处理" value="1"></el-option>
                          <el-option key="0" label="已处理" value="0"></el-option>
                        </el-select>
                      </div>
                      <div style="width:115px;">派遣时间</div>
                      <div style="width:154px;">事件类型</div>
                      <div style="width:135px;height:26px;">
                        <el-select v-model="selectUrgentTaskAddress" placeholder="事件地点" @change="refreshFxftUrgentTaskList" clearable>
                          <el-option v-for="(deviceAddress, index) in fxftAlarmDeviceAddressList" :key="index" :label="deviceAddress" :value="deviceAddress"></el-option>
                        </el-select>
                      </div>
                      <div style="width:115px;">处理人</div>
                      <div style="width:70px;margin-left: 20px;">处置情况</div>
                      <div style="width:140px;">处置状态<i class="el-icon-refresh" style="cursor: pointer" @click="refreshFxftUrgentTaskList" title="刷新"></i></div>
                    </div>
                    <!-- 滚动列表 -->
                    <div class="alarm-list-scroll-content">
                      <div ref="main">
                        <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(urgentTask, index) in fxftUrgentTaskList">
                          <el-checkbox :value="selectedUrgentTaskIds.indexOf(urgentTask.id) !== -1" @change=""></el-checkbox>
                          <div style="width:160px;margin-left: 15px;">{{ urgentTask.createTime }}</div>
                          <div style="width:154px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">紧急任务</div>
                          <div style="width:135px;">{{ urgentTask.alarmLocation}}</div>
                          <div style="width:115px;">{{ urgentTask.caseDealBy }}</div>
                          <div style="width:70px;position:relative;margin-left: 20px;">
                            <div class="handle-image-icon" v-if="urgentTask.circulationState === '0'" @click="openFxftUrgentTaskWindow(index)"></div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="urgentTask.circulationState == 1">
                            <div class="handle-result handling-result" style="margin-left: 0;margin-top: 0">待处理</div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="urgentTask.circulationState == 0">
                            <div class="handle-result" style="margin-left: 0;margin-top: 0">已处理</div>
                          </div>
                        </div>
                      </div>
                      <div class="scroll-bottom" ref="scroll-bottom"></div>
                    </div>
                  </div>

                  <!-- 设备报警事件统计 -->
                  <div class="module_title" style="margin-top: 12px;">
                    <span>物联感知事件统计</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>
                  <div style="width: 800px;height: 300px;margin-top:15px;">
                    <AlertDashboard></AlertDashboard>
                  </div>
                </div>
              </div>
              <!-- 环境整治 -->
              <div class="dataOverview" v-show="hjzzChange">
                <div class="module_title">
                  <span>违规行为事件列表</span>
                  <div class="small-square-box">
                    <span class="square01"></span>
                    <span class="square02"></span>
                    <span class="square03"></span>
                  </div>
                </div>

                <div class="label_bg" style="position: relative;margin-top: 12px;width: 360px">
                  <div class="label_item_bg" :class="isHjzzActive == 'tdlj' ? 'label_item_bg_press' : '' " @click="changeWgxwsj('tdlj')">
                    <div class="label_title" :style="isHjzzActive == 'tdlj' ? {'margin-top': '3px'} : ''">偷倒垃圾</div>
                  </div>
                  <div class="label_item_bg" :class="isHjzzActive == 'wgscrq' ? 'label_item_bg_press' : '' " @click="changeWgxwsj('wgscrq')">
                    <div class="label_title" :style="isHjzzActive == 'wgscrq' ? {'margin-top': '3px'} : ''">违规生产入侵</div>
                  </div>
                </div>

                <div class="hjzz-left-bg">
                  <div class="alarm-list-bg" style="margin-top: 12px" v-if="isTdljShow">
                    <!-- 报警列表表头 -->
                    <div class="alarm-list-item" style="padding-left: 10px; border-bottom: 2px solid #656594;">
                      <div style="width:80px;height:26px;">
                        <el-select v-model="selectMonitorEventStatus" placeholder="请选择" @change="refreshHjzzMonitorEventList">
                          <el-option key="01" label="全部" value="01"></el-option>
                          <el-option key="0" label="待处理" value="0"></el-option>
                          <el-option key="1" label="处理中" value="1"></el-option>
                        </el-select>
                      </div>
                      <div style="width:110px;height:26px;margin-left:5px;">
                        <el-select v-model="selectedMonitorEventAddress" placeholder="事件地点" @change="refreshHjzzMonitorEventList" clearable>
                          <el-option v-for="address in hjzzMonitorEventAddressList" :key="address" :label="address" :value="address"></el-option>
                        </el-select>
                      </div>
                      <div style="width:110px;">
                        事件类型
                      </div>
                      <div style="width:90px;text-align: center">车牌</div>
                      <div style="width:90px;text-align: center">车型</div>
                      <div style="width:150px;text-align: center">发现时间</div>
                      <div style="width:70px;text-align: center">抓拍视频</div>
                      <div style="width:140px;text-align: center">处置情况<i class="el-icon-refresh" style="cursor: pointer" @click="refreshHjzzMonitorEventList" title="刷新"></i></div>
                    </div>
                    <!-- 滚动列表 -->
                    <div class="alarm-list-scroll-content">
                      <div ref="main1">
                        <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(alarmInfo, index) in hjzzMonitorEventList">
                          <el-checkbox :value="selectedMonitorEventIds.indexOf(alarmInfo.id) != -1" @change="checked=>handleSelectHjzzEvent(checked, index)" style="width: 80px;"></el-checkbox>
                          <div style="width:110px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;text-align: left">{{ alarmInfo.alarmPosition}}</div>
                          <div style="width:110px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;margin-left:0px;text-align: center">{{ alarmInfo.alarmTypeName }}</div>
                          <div style="width:90px;cursor:pointer;text-decoration: underline;text-align: center" @click="openHjzzCaseInfoWinForCapture(alarmInfo.alarmPicture)">{{ alarmInfo.licensePlate }}</div>
                          <div style="width:90px;text-align: center">{{ alarmInfo.vehicleType == null ? '/' : alarmInfo.vehicleType }}</div>
                          <div style="width:150px;text-align: center">{{ alarmInfo.alarmDate }}</div>
                          <div style="width:70px;text-align: center">
                            <div class="handle-image-icon" @click="openHjzzMonitorVideoWin(index)"></div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="alarmInfo.status == null || alarmInfo.status === ''">
                            <div class="operation-confirm-btn" @click="debounceClick(index, '1')" style="margin-left: -4px;align-items: center">
                              流转
                            </div>
                            <div class="operation-confirm-btn" style="color: #fff;background-color: #611112;border-color: #f56c6c;align-items: center" @click="debounceClick(index, '2')">作废</div>
                          </div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center" v-if="alarmInfo.status === '1'">
                            <div class="handle-result handling-result" style="margin-left: 0;margin-top: 0">处理中</div>
                          </div>
                        </div>
                      </div>
                      <div class="scroll-bottom" ref="scroll-bottom1"></div>
                    </div>
                  </div>

                  <div class="alarm-list-bg" style="margin-top: 12px" v-if="isWgscrqShow">
                    <!-- 报警列表表头 -->
                    <div class="alarm-list-item" style="padding-left: 10px; border-bottom: 2px solid #656594;">
                      <div style="width:80px;height:26px;">
                        <el-select v-model="selectMonitorEventStatus" placeholder="请选择" @change="refreshHjzzMonitorEventList">
                        </el-select>
                      </div>
                      <div style="width:110px;height:26px;margin-left:5px;">
                        <el-select v-model="selectedMonitorEventAddress" placeholder="事件地点" @change="refreshHjzzMonitorEventList" clearable>
                        </el-select>
                      </div>
                      <div style="width:110px;">
                        事件类型
                      </div>
                      <div style="width:90px;text-align: center">车牌</div>
                      <div style="width:90px;text-align: center">车型</div>
                      <div style="width:150px;text-align: center">告警时间</div>
                      <div style="width:140px;text-align: center">处置情况<i class="el-icon-refresh" style="cursor: pointer" @click="refreshHjzzMonitorWgEventList" title="刷新"></i></div>
                    </div>
                    <!-- 滚动列表 -->
                    <div class="alarm-list-scroll-content">
                      <div ref="main1">
                        <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(alarmInfo, index) in hjzzMonitorWgEventList">
                          <el-checkbox :value="selectedMonitorEventIds.indexOf(alarmInfo.id) != -1" @change="checked=>handleSelectHjzzEvent(checked, index)" style="width: 80px;"></el-checkbox>
                          <div style="width:110px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;text-align: left">{{ alarmInfo.address }}</div>
                          <div style="width:110px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;margin-left:0px;text-align: center">{{ alarmInfo.caseName }}</div>
                          <div style="width:90px;cursor:pointer;text-decoration: underline;text-align: center" @click="openHjzzCaseInfoWinForCaptureWg(alarmInfo.alarmRecordId)">{{ alarmInfo.licensePlate }}</div>
                          <div style="width:90px;text-align: center">{{ alarmInfo.vehicleType == null ? '/' : alarmInfo.vehicleType }}</div>
                          <div style="width:150px;text-align: center">{{ alarmInfo.alarmDate }}</div>
                          <div style="width:140px;display: flex;justify-content: center;align-items: center">
                            <div class="handle-result handling-result" style="margin-left: 0;margin-top: 0">已派遣</div>
                          </div>
                        </div>
                      </div>
                      <div class="scroll-bottom" ref="scroll-bottom1"></div>
                    </div>
                  </div>

                  <div class="module_title" style="margin-top: 20px">
                    <span>案件数据统计</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>

                  <div class="label_bg" style="position: relative;margin-top: 12px;width: 360px">
                    <div class="label_item_bg" :class="isHjzzTjActive == 'wgscrqtj' ? 'label_item_bg_press' : '' " @click="changeAjsjtj('wgscrqtj')">
                      <div class="label_title" :style="isHjzzTjActive == 'wgscrqtj' ? {'margin-top': '3px'} : ''">违规入侵案件</div>
                    </div>
                    <div class="label_item_bg" :class="isHjzzTjActive == 'tdljtj' ? 'label_item_bg_press' : '' " @click="changeAjsjtj('tdljtj')">
                      <div class="label_title" :style="isHjzzTjActive == 'tdljtj' ? {'margin-top': '3px'} : ''">偷倒垃圾案件</div>
                    </div>
                  </div>
                  <div class="tdljtj" v-show="isTdljTjShow">
                    <div class="tdljtj_1"  >
                      <div class="tdljtj_1_1" >
                        <img src="@/assets/screen_display_img/img_dw.png" class="tdljtj_1_3" />
                        <div class="tdljtj_1_2" >{{tdljInfo.address}}</div>
                        <div class="tdljtj_1_4" >今日案件量</div>

                        <div class="tdljtj_1_5" >{{tdljInfo.dayCount}} 起</div>
                        <div class="tdljtj_1_4" >本月案件量</div>
                        <div class="tdljtj_1_5">{{tdljInfo.monthTotal}} 起</div>
                      </div>
                      <div style="float:left;width:208px;height:232px">
                        <TdljtjChart :width="'208px'" :height="'232px'" :address="'环江路垃圾房'"></TdljtjChart>
                      </div>
                    </div>
                    <div class="tdljtj_1" style="margin-left:30px;" >
                      <div class="tdljtj_1_1" >
                        <img src="@/assets/screen_display_img/img_dw.png" class="tdljtj_1_3" />
                        <div class="tdljtj_1_2" >{{tdljInfo1.address}}</div>
                        <div class="tdljtj_1_4" >今日案件量</div>
                        <div class="tdljtj_1_5" >{{tdljInfo1.dayCount}} 起</div>
                        <div class="tdljtj_1_4" >本月案件量</div>
                        <div class="tdljtj_1_5">{{tdljInfo1.monthTotal}}  起</div>
                      </div>
                      <div style="float:left;width:208px;height:232px">
                        <TdljtjChart :width="'208px'" :height="'232px'" :address="'龙胜路垃圾房'"></TdljtjChart>
                      </div>
                    </div>
                  </div>
                  <div class="wgscrqtj" v-show="isWgscrqTjShow">
                    <div class="tdljtj_1"  >
                      <div class="tdljtj_1_1" >
                        <img src="@/assets/screen_display_img/img_dw.png" class="tdljtj_1_3" />
                        <div class="tdljtj_1_2" >{{wgrqInfo.address}}</div>
                        <div class="tdljtj_1_4" style="margin-top:30px;" >今日案件量</div>
                        <div class="tdljtj_1_5" >{{wgrqInfo.dayCount}} 起</div>
                      </div>
                      <div style="float:left;width:208px;height:232px">
                        <TdljtjChart2 :width="'208px'" :height="'232px'" :address="'卫九路11号'"></TdljtjChart2>
                      </div>
                    </div>
                    <div class="tdljtj_1" style="margin-left:30px;" >
                      <div class="tdljtj_1_1" >
                        <img src="@/assets/screen_display_img/img_dw.png" class="tdljtj_1_3" />
                        <div class="tdljtj_1_2" >{{wgrqInfo1.address}}</div>
                        <div class="tdljtj_1_4" style="margin-top:30px;">今日案件量</div>
                        <div class="tdljtj_1_5" >{{wgrqInfo1.dayCount}} 起</div>
                      </div>
                      <div style="float:left;width:208px;height:232px">
                        <TdljtjChart2 :width="'208px'" :height="'232px'" :address="'卫九路大提路'"></TdljtjChart2>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 案事件管理 -->
              <div class="dataOverview" v-show="asjglChange">
                <div class="fxft-left-bg">
                  <!--事件处置-->
                  <div class="module_title" style="margin-top: 12px;">
                    <span>工单数量</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>
                  <div class="alarm-list-bg" style="border:0px;padding:0px;margin-top:20px" >
                    <GdslChart style="width:800px;height:250px"></GdslChart>
                  </div>
                  <div style="font-family: Source Han Sans CN;font-weight: bold;font-size: 20px;color: #28EDFF;line-height: 30px;width:100%;float:left;margin-top:10px">
                    分布数据
                  </div>
                  <div style="float:left;width:50%">
                    <fbsj-chart style="width:400px;height:220px;"></fbsj-chart>
                  </div>
                  <div style="float:left;width:50%">
                    <fbsj-chart1 style="width:400px;height:220px;"></fbsj-chart1>
                  </div>
                  <div class="module_title" style="margin-top: 12px;float:left;width:100%;">
                    <span>诉求情况</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>
                  <div style="float:left;width:50%">
                    <div style="font-family: Source Han Sans CN;font-weight: bold;font-size: 20px;color: #28EDFF;line-height: 30px;width:100%;float:left;margin-top:10px">
                      诉求类别
                    </div>
                    <SqlbChart style="height:250px;width:200px;float:left" :chartData="chartData" @chart-value-selected="handleChartValueSelected"></SqlbChart>
                    <SqlbChart1 style="height:250px;width:200px;float:left"  :chartData="chartData"  @chart-value-selected1="handleChartValueSelected1"></SqlbChart1>
                  </div>
                  <div style="float:left;width:50%">
                    <div style="font-family: Source Han Sans CN;font-weight: bold;font-size: 20px;color: #28EDFF;line-height: 30px;width:100%;float:left;margin-top:10px">
                      工单类别
                    </div>
                    <GdlbChart style="height:300px;width:400px;" :leibieIndex="leibieIndex"></GdlbChart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ring-left-box"></div>
    </div>
    <!-- 右背景 -->
    <div class="background-right">
      <!-- 右侧光环 -->
      <div class="ring-right-box"></div>
      <div class="background-right-bg"></div>
      <!-- 右侧内容区 -->
      <div class="chart-right-container">
        <div class="chart-right-inner">
          <!-- 右边内容 -->
          <div class="chart-right-content">
            <!-- 最新通知 -->
            <div class="latest-notice-box">
            </div>
            <!-- 城市运行库右侧 || 街长制右侧 -->
            <div class="gridding-events-box" v-if="dataOverviewChange || citySafetyChange">
              <!-- 数字城运派单系统-标题 -->
              <div class="module_title">
                <span>{{dataOverviewChange ? '数字城运派单系统' : (citySafetyChange ? '街长制巡检记录' : '')}}</span>
                <span v-if="false" style="cursor: pointer; display: flex; align-items: center;margin-left: 450px;" @click="openYzl" title="翼治理">
                  <img src="@/assets/screen_display_img/yzl.png" alt="翼治理" />
                </span>
                <div class="small-square-box">
                  <span class="square01"></span>
                  <span class="square02"></span>
                  <span class="square03"></span>
                </div>
              </div>
              <!-- 城市运行库和街长制-内容 -->
              <div class="gridding-events-content-box">
                <!-- 上部分 -->
                <div class="event-label-title-line" v-show="dataOverviewChange">
                  <div class="event-label-title">本月标准案件总量</div>
                  <div class="event-label-title">当日标准案件量</div>
                  <div class="event-label-title">本月突发事件总量</div>
                  <div class="event-label-title">本月突发事件结案率</div>
                </div>
                <div class="event-label-title-line" style="margin-top: 5px;" v-show="dataOverviewChange">
                  <div style="width: 150px;"><span style="font-size: 24px;font-weight: bold;margin-right:8px;">{{ caseNumberVO.monthCaseCount }}</span>件</div>
                  <div style="width: 150px;"><span style="font-size: 24px;font-weight: bold;margin-right:8px;">{{ caseNumberVO.todayCaseCount }}</span>件</div>
                  <div style="width: 150px;"><span style="font-size: 24px;font-weight: bold;margin-right:8px;">{{ caseNumberVO.monthEmergentCount }}</span>件</div>
                  <div style="width: 150px;"><span style="font-size: 24px;font-weight: bold;margin-right:8px;">{{ caseNumberVO.monthEmergentCloseRate }}</span>%</div>
                </div>
                <!-- 事件上报情况列表 -->
                <div style="display: flex;display: -webkit-flex;background: #2D2D64;border: 2px solid #656594;height:40px;line-height: 40px;font-size: 13px;color: #FFFFFF;text-align:center;box-sizing: content-box;" v-show="dataOverviewChange">
                  <div style="width: 100px;margin: 0;">案件状态</div>
                  <div style="width: 100px;margin: 0;">任务号</div>
                  <div style="width: 150px;margin: 0;">发现时间</div>
                  <div style="width: 250px;margin: 0;">发生地址</div>
                  <div style="width: 150px;margin: 0;">案件小类</div>
                  <div style="width: 100px;margin: 0;">核查照片<i class="el-icon-map-location" style="cursor: pointer" @click="changeShowEmergent" title="显示/关闭"></i></div>
                </div>
                <div class="auto-scroll" style="height:242px;" v-if="dataOverviewChange">
                  <vue-seamless-scroll
                    :data="eventReportInfo"
                    :class-option="defaultOption">
                    <div :style="index % 2 == 1 ? 'background-color:#181839;height:40px;line-height: 40px;font-size: 13px;color: #FFFFFF;text-align:center;display: flex;display: -webkit-flex;' : 'height:40px;line-height: 40px;font-size: 13px;color: #FFFFFF;text-align:center;display: flex;display: -webkit-flex;'"
                         v-for="(item, index) in eventReportInfo"
                         :key="index"
                         :data-obj="JSON.stringify(item)"
                         :id="index + 1">
                      <div style="width: 100px;">{{ item.statusname }}</div>
                      <div style="width: 100px;cursor: pointer;text-decoration: underline" @click="openEmergentDetail(item.taskid)">{{ item.taskid }}</div>
                      <div style="width: 150px;">{{ parseTime(item.discovertime) }}</div>
                      <div style="width: 250px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;text-align: left" :title="item.address">{{ item.address }}</div>
                      <div style="width: 150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="item.infoscname">
                        {{ item.infoscname }}
                      </div>
                      <div style="width: 100px;">
                        <el-image style="height: 23px; margin-bottom: -7px" v-if="item.checkimages" :src="item.checkimages[0]" :preview-src-list="item.checkimages"></el-image>
                      </div>
                    </div>
                  </vue-seamless-scroll>
                </div>

                <div v-show="citySafetyChange" class="neighborhood-select" style="margin-top:21px;">
                  <el-select v-model="chosenNeighborhood" filterable placeholder="请选择居委会" @change="getPatrolRecordByNeighborhood">
                    <el-option
                      v-for="item in neighborhoodCommittees"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <!-- 巡查记录列表-->
                <div class="auto-scroll-title-line" v-show="citySafetyChange">
                  <div>巡查时间</div>
                  <div>店铺名称</div>
                  <div>所属居委</div>
                  <div>社区街长</div>
                  <div>巡检结果</div>
                </div>
                <div class="auto-scroll"  v-if="citySafetyChange">
                  <vue-seamless-scroll
                    :data="patrolRecord"
                    :class-option="defaultOption"
                    :key="timeKeyOfPatrol">
                    <div class="item" :style="index % 2 == 1 ? (index == patrolRecord.length -1 ? 'background-color:#181839;margin-top:20px;' : 'background-color:#181839;') : ''"
                         v-for="(item, index) in patrolRecord"
                         :key="index"
                         :id="index + 1">
                      <div>{{item.time}}</div>
                      <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="item.shop">{{item.shop}}</div>
                      <div>{{ item.unit }}</div>
                      <div>{{ item.person }}</div>
                      <div><div :class="item.patrolResult == 0 ? 'scroll-list-item-result pass' : 'scroll-list-item-result nopass'" style="width:80px;">{{ item.patrolResult == 0 ?  '检查通过' : '检查未通过'}}</div></div>
                    </div>
                  </vue-seamless-scroll>
                </div>
                <div class="module_title" style="margin-top: 30px;">
                  <span>12345热线工单数量统计</span>
                  <div class="small-square-box">
                    <span class="square01"></span>
                    <span class="square02"></span>
                    <span class="square03"></span>
                  </div>
                </div>
                <div style="display: flex;margin-top: 16px;align-items: center;">
                  <div style="width: 10px;height: 10px;background: linear-gradient(180deg, #2A6DC5 0%, #9CC7FF 100%);border-radius: 50%;"></div>
                  <div style="font-size: 13px;color: #FFFFFF;margin-left: 11px;">热线工单数</div>
                  <div style="width: 10px;height: 10px;background: linear-gradient(180deg, #76FAFC 0%, #47BDFE 100%);border-radius: 50%;margin-left: 120px;"></div>
                  <div style="font-size: 13px;color: #FFFFFF;margin-left: 11px;">不满意案件数</div>
                </div>
                <div class="rxgd">
                  <HotlineChart :width="'780px'" :height="'350px'"></HotlineChart>
                </div>
              </div>
            </div>
            <!-- 防汛防台 右侧 -->
            <div class="fxft-events-box" v-show="fxftChange">
              <FxftZhsj></FxftZhsj>
            </div>
            <!--环境整治 右侧 处置历史信息库 处置时间 去掉罚款 名字打星号-->
            <div class="hjzz-events-box" v-show="hjzzChange" style="position:relative;">
              <div class="module_title">
                <span>处置历史信息库</span>
                <div class="small-square-box">
                  <span class="square01"></span>
                  <span class="square02"></span>
                  <span class="square03"></span>
                </div>
              </div>
              <div class="alarm-list-bg">
                <!-- 报警列表表头 -->
                <div class="alarm-list-item" style="border-bottom: 2px solid #656594;">
                  <div style="width:105px;">案件编号</div>
                  <div style="width:90px;">事件类型</div>
                  <div style="width:125px;">事件地点</div>
                  <div style="width:90px;">车牌</div>
                  <div style="width:70px;">抓拍视频</div>
                  <div style="width:70px;">车型</div>
                  <div style="width:90px;">涉事人</div>
                  <div style="width:135px;">处置时间</div>
                </div>
                <!-- 滚动列表 -->
                <div class="alarm-list-scroll-content">
                  <div ref="main1">
                    <div :class="index % 2 === 0 ? 'alarm-list-item sec-color' : 'alarm-list-item'" v-for="(alarmInfo, index) in hjzzEventResultList">
                      <div style="width:105px;cursor: pointer;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;text-decoration: underline;text-align: left" :title="alarmInfo.penaltyCaseNo" @click="openHjzzCaseInfoWin(index)">{{ alarmInfo.isFiling === '否' ? '已口头教育' : alarmInfo.penaltyCaseNo }}</div>
                      <div style="width:90px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">{{alarmInfo.caseName}}</div>
                      <div style="width:125px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">{{ alarmInfo.address}}</div>
                      <div style="width:90px;cursor:pointer;text-decoration: underline;text-align: center" @click="openHjzzCaseInfoWinForCaptureCase(index)">{{ alarmInfo.licensePlate }}</div>
                      <div style="width:70px;">
                        <div class="handle-image-icon" @click="openHjzzMonitorVideoWinCase(alarmInfo.caseNumber)"></div>
                      </div>
                      <div style="width:90px;">{{ alarmInfo.vehicleType == null ? '/' : alarmInfo.vehicleType }}</div>
                      <div style="width:90px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;" :title="alarmInfo.isPersonalBehavior == '否' ? alarmInfo.companyName : alarmInfo.personnelInfo">{{ alarmInfo.isPersonalBehavior == '否' ? alarmInfo.companyName : alarmInfo.personnelInfo }}</div>
                      <div style="width:135px;">{{ alarmInfo.createTime }}</div>
                    </div>
                  </div>
                  <div class="scroll-bottom" ref="scroll-bottom1"></div>
                </div>
              </div>
              <div style="margin-top: 40px;">
                <div style="display: flex;align-items: center;">
                  <div style="width: 11px;height: 11px;border: 2px solid #28DFAE;border-radius: 50%;"></div>
                  <div style="font-size: 16px;color: #FFFFFF;margin-left:13px;">处置统计分析</div>
                </div>
                <div style="display: flex;margin-top: 12px;">
                  <div style="width:290px;height:255px;display: flex;justify-content: center;align-items: center;">
                    <HjzzEventStatistic :width="'580px'" :height="'310px'"></HjzzEventStatistic>
                  </div>
                  <div style="width: 400px;margin-left:110px;font-size: 13px;color: #FFFFFF;position: relative;">
                    <div class="case_statistic_list">
                      <div style="border-bottom: 1px solid #D9E0E8;display: flex;height: 40px;line-height: 40px;text-align: center;" v-for="(item, index) in violationsList" :key="index">
                        <div style="width:240px;">{{ item.licensePlate }}</div>
                        <div style="width:140px;"><span style="color: #2CFCE5">{{ item.violationsCount }}</span>次</div>
                      </div>
                    </div>
                    <div style="width:100%;height: 50px;background: #112A44;border: 2px solid #32A1FF;border-radius: 24px;padding: 0 20px;display: flex;align-items: center;position: absolute;top: 0;">
                      <div style="width: 10px;height: 10px;background: #32A1FF;border-radius: 5px;"></div>
                      <div style="line-height: 24px;margin-left: 20px;">违规次数</div>
                      <div style="margin-left: 15px;">
                      <input type="date" v-model="startDate" /> - <input type="date" v-model="endDate" />
                      <i class="el-icon-search" style="cursor: pointer;margin-left: 5px;" @click="searchviolationsList" title="搜索"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 案事件管理 右侧 -->
            <div class="fxft-events-box" v-show="asjglChange">
              <div class="module_title">
                <span >{{ajTitle}}</span>
                <div class="small-square-box">
                  <span class="square01"></span>
                  <span class="square02"></span>
                  <span class="square03"></span>
                </div>
              </div>
              <div class="gridding-events-content-box">
                <!-- 上部分 -->
                <div style="margin-top:15px; color:#fff;height: 41px;" >
                  <el-form ref="queryForm" size="small" :inline="true"  label-width="auto">
                    <el-form-item label="">
                      <el-date-picker
                        style="width: 220px"
                        v-model="startMonth"
                        type="month"
                        placeholder="选择年月"
                        clearable
                      ></el-date-picker>
                      至
                      <el-date-picker
                        style="width: 220px"
                        v-model="endMonth"
                        type="month"
                        placeholder="选择年月"
                        clearable
                      ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini"  style="background: #2D2D64;border: 1px solid #fff;" @click="loadCaseData()">搜索</el-button>
                    </el-form-item>
                  </el-form>

                </div>
                <!-- 巡查记录列表-->
                <div class="auto-scroll-title-line" >
                  <div style="width:50px;margin: 0;float:left">序号</div>
                  <div style="width:120px;margin: 0;float:left">工单编号</div>
                  <div style="width:150px;margin: 0;float:left">发现时间</div>
                  <div style="width:120px;margin: 0;float:left">业务类型</div>
                  <div style="width:170px;margin-left:5px;margin-right:5px;float:left">发生地址</div>
                  <div style="width:180px;margin: 0;float:left">问题描述</div>
                </div>
                <div class="auto-scroll" style="height:150px"  >
                  <vue-seamless-scroll
                    :data="cList"
                    :class-option="defaultOption"
                    :key="timeKeyOfCase">
                    <div class="item1" :style="index % 2 == 1 ? (index == cList.length -1 ? 'background-color:#181839;' : 'background-color:#181839;') : ''"
                         v-for="(item, index) in cList"
                         :key="index"
                         :id="index + 1">
                      <div style="width:50px;float:left;text-align:center">{{index+1}}</div>
                      <div style="width:120px;float:left;text-align:center">{{item.hotlinesn}}</div>
                      <div style="width:150px;float:left;text-align:center">{{parseTime(item.discovertime)}}</div>
                      <div style="width:120px;float:left;text-align:center">{{ item.servicetypename }}</div>
                      <div style="width: 170px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;text-align: left;float:left;margin-left:5px;margin-right:5px;" :title="item.address">{{ item.address }}</div>
                      <div style="width:180px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;text-align: left;float:left" :title="item.description">{{ item.description }}</div>
                    </div>
                  </vue-seamless-scroll>
                </div>
                </div>

              <GdslChart1 style="width:800px;height:300px;" :resChartData="resChartData" ></GdslChart1>
              <SqChart style="width:300px;height:300px;float:left"  :schartData="schartData"></SqChart>
              <GdslChart1 style="width:500px;height:300px;float:left" :resChartData="resChartData1"></GdslChart1>
              </div>

          </div>
          <!-- 右边背景边框 -->
          <div class="chart-right-border-bg"></div>
        </div>
      </div>
    </div>

    <transition name="el-zoom-in-center">
      <FxftHandleWindow v-if="fxftAlarmHandleWindowShow" :alarm="fxftAlarmInfo" @closeWindow="closeAlarmInfo"></FxftHandleWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <FxftEmergencyWindow v-if="fxftEmergencyWindowShow" :emergency="fxftEmergency" @closeWindow="closeEmergencyWindow"></FxftEmergencyWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <FxftUrgentTaskWindow v-if="fxftUrgentTaskWindowShow" :urgentTask="fxftUrgentTask" @closeWindow="closeUrgentTaskWindow"></FxftUrgentTaskWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <HlsPreviewWindow v-if="hjzzMonitorVideoWindowShow" :previewUrl="hjzzMonitorVideoPreviewUrl" @closeWindow="closeHjzzMonitorVideoWin"></HlsPreviewWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <MP4PreviewWindow v-if="hjzzMonitorVideoHistoryWindowShow" :previewUrl="hjzzMonitorVideoHistoryPreviewUrl" @closeWindow="closeHjzzMonitorVideoHistoryWin"></MP4PreviewWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <DhPreviewWindow v-if="hjzzMonitorVideosWindowShow" :previewUrls="hjzzMonitorVideoPreviewUrls" @closeWindow="closeHjzzMonitorVideosWin"></DhPreviewWindow>
    </transition>
    <transition name="el-zoom-in-center">
      <HjzzCaseHandleDetail v-if="hjzzCaseHandleWindowShow" :title="hjzzCaseHandleWinTitle" :caseInfo="hjzzCaseInfo" @closeWindow="closeHjzzCaseInfoWin"></HjzzCaseHandleDetail>
    </transition>
    <transition name="el-zoom-in-center">
      <HjzzHandleDetail v-if="hjzzHandleShow" :hjzzCase="hjzzHandleInfo" :alarm-record="hjzzAlarmRecord" @closeWindow="closeHjzzHandleDetail"></HjzzHandleDetail>
    </transition>
  </div>
</template>

<script>

import vueSeamlessScroll from "vue-seamless-scroll";
import ScreenAgsMapShow from "../../../components/ScreenAgsMapShow/index.vue";
import ScreenSvgMapShow from "@/components/ScreenSvgMapShow/index.vue";
import countTo from "vue-count-to";
import TrafficChart from "@/views/screenAgs/Index3480/components/TrafficChart";
import streetChiefMixin from "@/components/ScreenAgsMapShow/mixin/streetChiefMixin";
import FxftZhsj from "@/views/screenAgs/Index3480/components/FxftZhsj.vue";

import * as echarts from "echarts";

import {
  listCheckRecordShopNumber,
  listScreenShopJuweihui,
  listScreenShopCheckStatusByDeptId,
  getTodayAlarmRecord,
  handleFxftAlarmRecord,
  handleHjzzAlarmRecord,
  getFxftAlarmInfo,
  getFxftEmergency,
  getFxftUrgentTask,
  getHjzzMonitorEventList,
  getHjzzMonitorWgEventList,
  getHjzzMonitorEventVideo,
  getHjzzMonitorVideo,
  getHjzzEventResultList,
  getFxftAlarmDeviceAddress,
  getHjzzMonitorEventTypeList,
  getHjzzMonitorEventAddressList,
  getAlarmRecord,
  getFxftEmergencyRecord,
  getFxftUrgentTaskRecord,
  getViolations,
  getDeviceStatusNum
} from "@/api/shcy/screen.js";

import { caseNumber, caseList } from "@/api/shcy/screenCsyxk";

import FxftHandleWindow from "@/views/screenAgs/Index3480/components/FxftHandleDetail";
import HjzzHandleDetail from "@/views/screenAgs/Index3480/components/HjzzHandleDetail.vue";
import FxftEmergencyWindow from "@/views/screenAgs/Index3480/components/FxftEmergencyDetail.vue";
import FxftUrgentTaskWindow from "@/views/screenAgs/Index3480/components/FxftUrgentTaskDetail.vue";
import MP4PreviewWindow from "@/components/ScreenAgsWindowInfo/MP4PreviewWindow";
import HikPreviewWindow from "@/components/ScreenAgsWindowInfo/HikPreviewWindow";
import HlsPreviewWindow from "@/components/ScreenAgsWindowInfo/HlsPreviewWindow.vue";
import DhPreviewWindow from "@/components/ScreenAgsWindowInfo/DhPreviewWindow.vue";
import FLVPlayer from "@/components/FLVPlayer";
import FxftAlarmHandleStatistic from "@/views/screenAgs/Index3480/components/FxftAlarmHandleStatistic";
import HjzzEventStatistic from "@/views/screenAgs/Index3480/components/HjzzEventStatistic";
import HjzzCaseHandleDetail from "@/views/screenAgs/Index3480/components/HjzzCaseHandleDetail";
import {getIccAccessToken} from "@/api/shcy/iccAlarmRecord";
import {formatDate, myDebounce} from '@/utils'
import urgentTask from "@/views/shcy/urgentTask/index.vue";
import HotlineChart from "@/views/screenAgs/Index3480/components/HotlineChart.vue";
import  AlertDashboard from "@/views/screenAgs/Index3480/AlertDashboard.vue";
import TdljtjChart from "@/views/screenAgs/Index3480/components/TdljtjChart.vue";
import TdljtjChart2 from "@/views/screenAgs/Index3480/components/TdljtjChart2.vue";
import {tdljCaseNumber, wgscrqCaseNumber} from "@/api/shcy/screenHjzz";
import GdslChart from "@/views/screenAgs/Index3480/components/GdslChart.vue";
import FbsjChart from "@/views/screenAgs/Index3480/components/FbsjChart.vue";
import SqlbChart from "@/views/screenAgs/Index3480/components/SqlbChart.vue";
import GdlbChart from "@/views/screenAgs/Index3480/components/GdlbChart.vue";
import SqChart from "@/views/screenAgs/Index3480/components/SqChart.vue";
import SqlbChart1 from "@/views/screenAgs/Index3480/components/SqlbChart1.vue";
import FbsjChart1 from "@/views/screenAgs/Index3480/components/FbsjChart1.vue";
import {
  getCList,
  getCaseNumberByDemandType,
  getMaxCaseNumberResidentialareaByType,
  getCountByTypeAndResidentialarea
} from "@/api/shcy/screenAsjgl";
import GdslChart1 from "@/views/screenAgs/Index3480/components/GdslChart1.vue";
import BasicOverview from "./BasicOverview.vue";
import DeviceStatusTabs from "./DeviceStatusTabs.vue";

export default {
  name: "index",
  components: {
    GdslChart1,
    FbsjChart1,
    SqlbChart1,
    SqChart,
    GdlbChart,
    SqlbChart,
    FbsjChart,
    GdslChart,
    TdljtjChart2,
    TdljtjChart,
    AlertDashboard,
    HotlineChart,
    FxftEmergencyWindow,
    FxftUrgentTaskWindow,
    vueSeamlessScroll,
    ScreenAgsMapShow,
    ScreenSvgMapShow,
    countTo,
    TrafficChart,
    streetChiefMixin,
    FxftHandleWindow,
    HjzzHandleDetail,
    MP4PreviewWindow,
    HikPreviewWindow,
    HlsPreviewWindow,
    DhPreviewWindow,
    FLVPlayer,
    FxftAlarmHandleStatistic,
    HjzzEventStatistic,
    HjzzCaseHandleDetail,
    FxftZhsj,
    BasicOverview,
    DeviceStatusTabs,
  },
  data() {
    return {
      showSvgMap:false,
      caseNumberVO: {
        monthCaseCount: 0,
        todayCaseCount: 0,
        monthEmergentCount: 0,
        monthEmergentCloseRate: 0
      },
      queryParams: {
        nianyue:null,
      },
      startMonth:null,
      endMonth:null,
      tdljInfo:{},
      tdljInfo1:{},
      wgrqInfo:{},
      wgrqInfo1:{},
      //查询所有检查列表
      shopcheckLogList:[],
      ajTitle:"案件总数",
      resName:null,
      // 定义的时间变量
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      weekday: [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ],
      timer: null,
      weather: null,
      // 上部按钮-数据
      topBtn: [
        {
          title: "城市运行库",
          number: 1001,
        },
        // {
        //   title: "街长制",
        //   number: 1002,
        // },
        // {
        //   title: "重点人员",
        //   number: 1003,
        // },
        // {
        //   title: "垃圾分类",
        //   number: 1004,
        // },
        {
          title: "防汛防台",
          number: 1005,
        },
        {
          title: "环境整治",
          number: 1006,
        },
        {
          title: "城运业务",
          number: 1007,
        },
      ],
      // 下部按钮-数据
      bottomBtn: [
        {
          id: "100001",
          title: "全部数据",
        },
        {
          id: "100002",
          title: "现状使用主体",
        },
        {
          id: "100003",
          title: "房地调查数据库",
        },
        {
          id: "100004",
          title: "绿化主体",
        },
        {
          id: "100005",
          title: "小区基础信息",
        },
        {
          id: "100006",
          title: "管网数据",
        },
      ],
      // 下部按钮-事件处理相关值
      selBottomIndex: -1,
      // 上部按钮-事件处理相关值
      selTopIndex: 0,
      // 输入框双向绑定数据
      inp_value: "请输入区域",
      // 事件上报情况
      eventReportInfo: [],
      // 街长制巡检记录
      patrolRecord: [],
      timeKeyOfPatrol:'1',
      timeKeyOfCase:'1',
      // 基础信息弹窗是否显示
      isBasicInfoShow: false,
      // 房地调查-查看更多
      // moreDisplay: null,
      // 左边切换为-城市运行库内容
      dataOverviewChange: true,
      // 左边是否切换为-城市安全库内容
      citySafetyChange: false,
      // 防汛防台切换
      fxftChange: false,
      // 环境整治切换
      hjzzChange: false,
      //案事件管理切换
      asjglChange:false,
      leibieIndex:0,
      chartData:[],
      schartData:[],
      resChartData:[],
      resChartData1:[],
      //绿化数据、河道数据、现状使用主体情况label
      labelPosition:0,
      //基本概况16个选项
      jbgkPosition: -1,
      //商铺信息
      shopInfo:{
        allNum:0,
        normalNum:0,
        stopNum:0,
        closeNum:0,
        roadNum: 112,
        streetChiefNum: 112,
        patrolNum: 24,
        importantRoadNum: 16
      },
      //街长制路段情况，商铺总体情况
      streetRoadShopLabel: 1,
      //街长制今日商铺巡查情况、本月商铺巡查情况label
      patrolPosition: 2,
      //巡查商铺数据及包保制情况
      patrolBaoBaoShopData: [
        [
          {'title':'今日已巡查商铺数','num': 0},
          {'title':'本期未巡查商铺数','num': 0},
          {'title':'今日巡查通过商铺数','num': 0},
          {'title':'今日巡查未通过商铺数','num': 0}
        ],
        [
          {'title':'本期已巡查商铺数','num': 0},
          {'title':'本期未巡查商铺数','num': 0},
          {'title':'本期巡查通过商铺数','num': 0},
          {'title':'本期巡查未通过商铺数','num': 0}
        ],
        [
          {'title':'今日已巡查商铺数','num': 0},
          {'title':'本期未巡查商铺数','num': 0},
          {'title':'今日巡查通过商铺数','num': 0},
          {'title':'今日巡查未通过商铺数','num': 0}
        ],
      ],
      //商铺巡查环形图*4
      patrolDataCircle: {
        typeLabel: ['累计已巡查比例', '未巡查比例', '巡查通过比例', '巡查未通过比例'],
        value: [
          [],
          [],
          []
        ]
      },
      //商铺情况-旋转饼图
      trafficData: [
        {
          name: '正常',
          value: 0
        },
        {
          name: '歇业',
          value: 0
        },
        {
          name: '关停',
          value: 0
        }
      ],
      trafficStreetChiefPatrolData: [
        {
          name: '离线',
          value: 108
        },
        {
          name: '在线',
          value: 24
        }
      ],
      //居委会数组，供巡检记录的筛检
      neighborhoodCommittees: [],
      //选择的居委会
      chosenNeighborhood: null,
      //街长制 商铺巡查记录（最近三次）
      checkLogThreeTimeList:[],
      //防汛防台 报警事件列表
      fxftAlarmList: [],
      todayAlarmRecordList: [],
      fxftEmergencyList: [],
      fxftUrgentTaskList: [],
      //警报信息列表下拉选项状态
      selectAlarmStatus: '',
      selectEmergencyStatus: '',
      selectUrgentTaskStatus: '',
      selectAlarmDeviceAddress: '',
      selectEmergencyAddress: '',
      selectUrgentTaskAddress: '',
      //选中的防汛防台警报id
      selectedAlarmIds: [],
      selectedEmergencyIds: [],
      selectedUrgentTaskIds: [],
      //显示处理的下拉位置
      selectedAlarmPos: -1,
      fxftAlarmHandleWindowShow: false,
      fxftEmergencyWindowShow: false,
      fxftUrgentTaskWindowShow: false,
      fxftAlarmInfo: {
        caseAddress: '',
        caseDealBy: '',
        caseEndTime: '',
        caseHandleContent:'',
        casePhotoUrls: []
      },
      fxftEmergency: {},
      fxftUrgentTask: {},
      fxftAlarmDeviceAddressList:[],
      //环境整治 报警监控列表
      hjzzMonitorEventList: [],
      hjzzMonitorWgEventList: [],
      //环境整治监控事件列表下拉选项状态
      selectMonitorEventStatus: '01',
      //选中的环境整治监控事件id
      selectedMonitorEventIds: [],
      //显示处理的下拉位置
      selectedMonitorEventPos: -1,
      selectedMonitorEventType: '',
      hjzzMonitorEventTypeList: [],
      selectedMonitorEventAddress: '',
      hjzzMonitorEventAddressList: [],
      hjzzMonitorVideoWindowShow: false,
      hjzzMonitorVideoHistoryWindowShow: false,
      hjzzMonitorVideosWindowShow: false,
      //环境整治卡口监控抓拍视频
      hjzzMonitorVideoPreviewUrl: '',
      hjzzMonitorVideoHistoryPreviewUrl: '',
      hjzzMonitorVideoPreviewUrls: [],
      //环境整治卡口监控视频流
      hjzzRealMonitorPreviewUrl: '',
      hjzzRealMonitorName: '',
      //环境整治右侧  处置历史信息库列表
      hjzzEventResultList: [],
      selectedHjzzCaseTypeStatistic:'',
      searchHjzzCaseCarCode: '',
      hjzzCaseHandleWindowShow: false,
      hjzzCaseInfo: {
        address: '',
        caseDealBy: '',
        caseEndTime: '',
        checkOthers:'',
        caseDealPhoto: []
      },
      hjzzCaseHandleWinTitle: '环境整治警报处置详情',
      isFxftShow: true,
      isTfsjShow: false,
      isAjzsShow:true,
      isZdajShow:false,
      isJjajShow:false,
      isCfajShow:false,
      isJjrwShow: false,
      isActive: 'fxft',
      isAjType:'1',
      isAjActive:'ajzs',
      isHjzzActive: 'tdlj',
      isHjzzTjActive: 'wgscrqtj',
      isTdljShow: true,
      isWgscrqShow: false,
      isTdljTjShow: false,
      isWgscrqTjShow: true,
      violationsList: [],
      cList:[],
      hjzzHandleShow: false,
      hjzzAlarmRecord: {},
      hjzzHandleInfo: {},
      // 日期范围
      startDate: null,
      endDate: null,
      deviceStatusNum: {
        fxftOnlineNum: 0,
        fxftTotalNum: 0,
        hjzzOnlineNum: 0,
        hjzzTotalNum: 0,
      },
      layers: [
        { text: '区域图层', type: 'area', active: true },
        { text: '地图图层', type: 'map', active: false }
      ],
      layerChangeShow: false,
    }
  },
  computed: {
    urgentTask() {
      return urgentTask
    },
    // 表格数据轮播的默认项
    defaultOption() {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  beforeRouteEnter(to, from, next) {
    if (from.name == "FloorInfo") {
      next((vm) => {
        setTimeout(() => {
          vm.btnTopClick(1);
        }, 0);
      });
    } else {
      next();
    }
  },
  mounted() {
    this.setDefaultDate();

    // 持续刷新当前时间
    this.timer = setInterval(() => {
      this.getDate();
    }, 1000);

    this.moreDisplay = false;
    // this.getShopInfoAndPatrolInfo()
    this.getNeiborhoodCommittees()
    this.getTodayAlarmRecords()
    this.getHjzzMonitorEvents()
    this.getHjzzMonitorWgEvents()
    this.getHjzzEventResults()
    this.getFxftAlarmDeviceAddressList()
    this.getHjzzMonitorEventTypes()
    this.getHjzzMonitorEventAddresses()
    this.getViolationsList()
    this.getCaseNumber()
    this.getCaseList()
    this.getTdlj()
    this.getWgrq()
    this.getDeviceStatusNum()
    this.getCaseNumberByDemandTypeList()
    this.getResName("1");
  },

  methods: {
    setDefaultDate() {
      this.startMonth=new Date(new Date().getFullYear()-1, 10, 1);
      this.endMonth=new Date(new Date().getFullYear(),9, 1);
    },
    getCaseNumberByDemandTypeList()
    {
     let form={};
     form.type="1";
      getCaseNumberByDemandType(form).then(res=>{
        if(res.data)
        {
          this.chartData = JSON.parse(JSON.stringify(res.data))
        }
      })
    },
    getWgrq(){
      wgscrqCaseNumber({address: '卫九路11号'}).then(res => {
        this.wgrqInfo=res.data;
      })
      wgscrqCaseNumber({address: '卫九路大提路'}).then(res => {
        this.wgrqInfo1=res.data;
      })
    },
    getTdlj(){
      tdljCaseNumber({address: '环江路垃圾房'}).then(res => {
        this.tdljInfo=res.data;
      })
      tdljCaseNumber({address: '龙胜路垃圾房'}).then(res => {
        this.tdljInfo1=res.data;
      })
    },
    // 获取城市运行库案件数量
    getCaseNumber() {
      caseNumber().then(res => {
        this.caseNumberVO = res.data
      })
    },
    // 获取近15天案事件列表
    getCaseList() {
      caseList().then(res => {
        this.eventReportInfo = res.data
      })
    },
    // 获取当前时间
    getDate() {
      const date = this.$dayjs(new Date());
      this.dateDay = date.format("HH:mm:ss");
      this.dateYear = date.format("YYYY年MM月DD日");
      this.dateWeek = date.format(this.weekday[date.day()]);
    },
    // 上部按钮的点击事件
    btnTopClick(index) {
      const lastIndex = this.selTopIndex
      this.selTopIndex = index;
      // 下方按钮恢复默认不选中状态
      this.getDefaultVal(-1);

      // 每个上部按钮的事件处理（---待封装---）
      if (index === 0) {
        // 点击-城市运行库
        this.dataOverviewChange = true;
        this.citySafetyChange = false;
        this.fxftChange = false;
        this.hjzzChange = false;
        this.asjglChange =false;
        this.$refs.child.winRestore();
        this.$refs.child.fxftSelBtnShow = false;
        this.showSvgMap = false;
        this.layerChangeShow = false;
      } else if (index === 999999) {
        // 点击-街长制
        this.dataOverviewChange = false;
        this.citySafetyChange = true;
        this.fxftChange = false;
        this.hjzzChange = false;
        this.asjglChange =false;
        this.$refs.child.winRestore();
        this.$refs.child.addCommitteeStreetChief()
        this.$refs.child.addRoadStreetChief()
        this.$refs.child.fxftSelBtnShow = false;
        this.showSvgMap = false;
        this.layerChangeShow = false;
      } else if (index === 9999) {
        if (this.$store.state.user.name != '司法所') {
          this.selTopIndex = lastIndex
          this.$modal.msgError("当前操作没有权限！")
          return
        }
        // 点击-重点人员
        this.dataOverviewChange = true;
        this.citySafetyChange = false;
        this.fxftChange = false;
        this.hjzzChange = false;
        this.asjglChange =false;
        this.$refs.child.winRestore();
        this.$refs.child.addPersonnelGridFeatureLayer()
        this.$refs.child.fxftSelBtnShow = false;
        this.showSvgMap = false;
        this.layerChangeShow = false;
      } else if (index === 1) {
        // 点击-防汛防台
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.hjzzChange = false;
        this.asjglChange =false;
        this.fxftChange = true;
        this.$refs.child.winRestore();
        this.$refs.child.fxftSelBtnShow = true;
        this.$refs.child.fxbwShow = true;
        this.$refs.child.$refs["fxft00"][0].style.backgroundColor = "#2bfbd9";
        this.$refs.child.ywcxShow = true;
        this.$refs.child.$refs["fxft01"][0].style.backgroundColor = "#2bfbd9";
        this.$refs.child.addFxftFeatureLayer();
        this.showSvgMap = false;
        this.layerChangeShow = false;
      } else if (index === 2) {
        // 点击-环境整治
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.fxftChange = false;
        this.hjzzChange = true;
        this.asjglChange =false;
        this.$refs.child.winRestore();
        this.$refs.child.addWlgzFeatureLayer()
        this.$refs.child.fxftSelBtnShow = false;
        this.showSvgMap = false;
        this.layerChangeShow = false;
      }else if (index === 3) {
        // 点击-案事件管理
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.fxftChange = false;
        this.hjzzChange = false;
        this.asjglChange = true;
        this.$refs.child.winRestore();
        this.$refs.child.addUrgentCaseFeatureLayer()
        this.$refs.child.fxftSelBtnShow = false;
        this.showSvgMap = true;
        this.layerChangeShow = true;
        this.layers[0].active = true;
        this.layers[1].active = false;
      }
      else if (index === 999) {
        // this.$refs.child.winRestore();
        window.open('https://inspector.zhimouyun.com', '_blank');
      }
    },
    // 获取默认值-1，是下方按钮不高亮
    getDefaultVal(val) {
      this.selBottomIndex = val;
    },
    // 点击临蒙居委后，默认选择"全部数据"按钮
    getAllBtnVal(val) {
      this.selBottomIndex = val;
    },
    resetJbgkPosition(val) {
      this.jbgkPosition = val;
    },
    setLabelPosition(pos){
      if(this.labelPosition !== pos){
        this.labelPosition = pos;
      }
    },
    setStreetRoadShopLabel(pos){
      if(this.streetRoadShopLabel !== pos) {
        this.streetRoadShopLabel = pos;
      }
    },
    setPatrolLabel(pos){
      if(this.patrolPosition !== pos) {
        this.patrolPosition = pos;
        if(pos !==2){
          this.drawDangerHandleStatus(0, 'patrol-shop-circle')
          if (pos === 0) {
            this.$refs.child.checkedShopMonth('remove')
            this.$refs.child.removeRoadStreetChief()
            // 今日商铺巡查
            this.$refs.child.checkedShop('pass')
            this.$refs.child.checkedShop('noPass')
          }else{
            this.$refs.child.checkedShop('remove')
            this.$refs.child.removeRoadStreetChief()
            // 本月商铺巡查
            this.$refs.child.checkedShopMonth('pass')
            this.$refs.child.checkedShopMonth('noPass')
            this.$refs.child.checkedShopMonth('noCheck')
          }
        } else {
          this.$refs.child.addRoadStreetChief()
          this.$refs.child.checkedShop('remove')
          this.$refs.child.checkedShopMonth('remove')
          // 今日商铺巡查
          // this.$refs.child.checkedShop('pass')
          // this.$refs.child.checkedShop('noPass')
        }
      }
    },
    showInMap(type, data, pos){
      this.jbgkPosition = pos;
      let value = [];
      value.push(data);
      this.$refs['child'].reset();
      this.$refs['child'].checkedTips({type, value});
    },
    showGreeningInMap(type, value){
      this.$refs['child'].reset();
      this.$refs['child'].checkedTips({type, value});
    },
    //动态读取商铺信息和巡查信息
    getShopInfoAndPatrolInfo(){
      listCheckRecordShopNumber().then(res=>{
        let data = res.data
        this.shopInfo.allNum = data.shopTotal;
        this.shopInfo.normalNum = data.shopNormalNum;
        this.shopInfo.stopNum = data.shopRestNum;
        this.shopInfo.closeNum = data.shopCloseNum;
        //今日商铺巡查情况
        this.patrolBaoBaoShopData[0][0].num = data.checkedShopNumDay
        this.patrolBaoBaoShopData[0][1].num = data.noCheckedShopDay
        this.patrolBaoBaoShopData[0][2].num = data.checkedAndPassShopNumDay
        this.patrolBaoBaoShopData[0][3].num = data.checkedAndNoPassShopNumDay
        //本月商铺巡查情况
        this.patrolBaoBaoShopData[1][0].num = data.checkedShopNumMonth
        this.patrolBaoBaoShopData[1][1].num = data.noCheckedShopMonth
        this.patrolBaoBaoShopData[1][2].num = data.checkedAndPassShopNumMonth
        this.patrolBaoBaoShopData[1][3].num = data.checkedAndNoPassShopNumMonth

        //今日街长巡查情况
        this.patrolBaoBaoShopData[2][0].num = data.checkedShopNumDay
        this.patrolBaoBaoShopData[2][1].num = data.noCheckedShopDay
        this.patrolBaoBaoShopData[2][2].num = data.checkedAndPassShopNumDay
        this.patrolBaoBaoShopData[2][3].num = data.checkedAndNoPassShopNumDay

        //饼状图数据
        this.trafficData[0].value = data.shopNormalNum
        this.trafficData[1].value = data.shopRestNum
        this.trafficData[2].value = data.shopCloseNum
        //今日商铺巡查情况饼图
        this.patrolDataCircle.value[0][0] = data.checkedShopNumDay
        this.patrolDataCircle.value[0][1] = data.noCheckedShopDay
        this.patrolDataCircle.value[0][2] = data.checkedAndPassShopNumDay
        this.patrolDataCircle.value[0][3] = data.checkedAndNoPassShopNumDay
        //本月商铺巡查情况饼图
        this.patrolDataCircle.value[1][0] = data.checkedShopNumMonth
        this.patrolDataCircle.value[1][1] = data.noCheckedShopMonth
        this.patrolDataCircle.value[1][2] = data.checkedAndPassShopNumMonth
        this.patrolDataCircle.value[1][3] = data.checkedAndNoPassShopNumMonth
        //今日街长巡查情况饼图
        this.patrolDataCircle.value[2][0] = data.checkedShopNumDay
        this.patrolDataCircle.value[2][1] = data.noCheckedShopDay
        this.patrolDataCircle.value[2][2] = data.checkedAndPassShopNumDay
        this.patrolDataCircle.value[2][3] = data.checkedAndNoPassShopNumDay

        this.drawDangerHandleStatus(0, 'patrol-shop-circle')
      })
    },
    //读取居委会列表
    getNeiborhoodCommittees(){
      listScreenShopJuweihui().then(res=>{
        let ncData = res.data
        ncData.depts.forEach((item)=>{
          this.neighborhoodCommittees.push({value:item.deptId, label:item.deptName})
        })
        let patrol
        if(ncData.checkRecords != null){
          ncData.checkRecords.forEach((item)=>{
            patrol = {}
            patrol.person = item.checkPerson
            patrol.unit = item.committee
            patrol.time = item.checkDate
            patrol.shop = item.shopName
            patrol.patrolResult = item.checkStatus == '检查通过' ? 0 : 1
            patrol.locationResult = item.distanceStatus == '巡逻点位正常' ? 0 : 1
            this.patrolRecord.push(patrol)
          })
        }
        this.timeKeyOfPatrol = new Date().getTime()
        this.chosenNeighborhood = ncData.deptId
      })
    },
    //通过选择的居委会读取巡查记录
    getPatrolRecordByNeighborhood(){
      this.patrolRecord = []
      listScreenShopCheckStatusByDeptId(this.chosenNeighborhood).then(res=>{
        let patrolData = res.data
        let patrol
        patrolData.forEach((item)=>{
          patrol = {}
          patrol.person = item.checkPerson
          patrol.unit = item.committee
          patrol.time = item.checkDate
          patrol.shop = item.shopName
          patrol.patrolResult = item.checkStatus == '检查通过' ? 0 : 1
          patrol.locationResult = item.distanceStatus == '巡逻点位正常' ? 0 : 1
          this.patrolRecord.push(patrol)
        })
        this.timeKeyOfPatrol = new Date().getTime()
      })
    },
    drawDangerHandleStatus (startIndex, refVal) {
      let contingencyPlanName = this.patrolDataCircle.typeLabel
      let valArr = this.patrolDataCircle.value
      let shopAllNum = this.shopInfo.allNum
      let labelPostion = this.patrolPosition
      let dataStyle = {
        normal: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          shadowBlur: 0,
          shadowColor: '#203665'
        }
      }
      let option = {
        backgroundColor: '',
        series: [
          {
            name: '第一个圆环',
            type: 'pie',
            clockWise: false,
            radius: [58, 72],
            itemStyle: dataStyle,
            hoverAnimation: false,
            center: ['10%', '50%'],
            data: [{
              value: this.patrolDataCircle.value[1][startIndex],//固定是本月累计，不管今日数据
              label: {
                normal: {
                  rich: {
                    a: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 30,
                      fontWeight: 'bold'
                    },
                    b: {
                      color: '#fff',
                      align: 'center',
                      fontSize: 12
                    },
                    c: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 16
                    },
                    d: {
                      fontSize: 8
                    }
                  },
                  formatter: function (params) {
                    return '{a|' +  Math.round(params.value / shopAllNum  * 100) + '}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n'  + '{b|' + contingencyPlanName[startIndex] + '}\n'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '14',
                    fontWeight: 'normal',
                    color: '#fff'
                  }
                }
              },
              itemStyle: {
                normal: {
                  color: '#18B25D',
                  shadowColor: '#18B25D',
                  shadowBlur: 0
                }
              }
            }, {
              value: this.shopInfo.allNum - this.patrolDataCircle.value[1][startIndex],
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#3C464F'
                },
                emphasis: {
                  color: '#3C464F'
                }
              }
            }]
          },
          {
            name: '第二个圆环',
            type: 'pie',
            clockWise: false,
            radius: [58, 72],
            itemStyle: dataStyle,
            hoverAnimation: false,
            center: ['36%', '50%'],
            data: [{
              value: this.patrolDataCircle.value[this.patrolPosition][startIndex + 1],
              label: {
                normal: {
                  rich: {
                    a: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 30,
                      fontWeight: 'bold'
                    },
                    b: {
                      width: 108,
                      color: '#fff',
                      align: 'center',
                      fontSize: 12
                    },
                    c: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 14
                    },
                    d: {
                      fontSize: 8
                    }
                  },
                  formatter: function (params) {
                    if(shopAllNum == 0){
                      return '{a|0}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 1] + '}\n'
                    }
                    return '{a|' + Math.round(params.value / shopAllNum * 100) + '}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 1] + '}\n'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '14',
                    fontWeight: 'normal',
                    color: '#fff'
                  }
                }
              },
              itemStyle: {
                normal: {
                  color: '#FFB432',
                  shadowColor: '#FFB432',
                  shadowBlur: 0
                }
              }
            }, {
              value: shopAllNum > 0 ? shopAllNum - this.patrolDataCircle.value[this.patrolPosition][startIndex + 1] : 1,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#3C464F'
                },
                emphasis: {
                  color: '#3C464F'
                }
              }
            }]
          },
          {
            name: '第三个圆环',
            type: 'pie',
            clockWise: false,
            radius: [58, 72],
            itemStyle: dataStyle,
            hoverAnimation: false,
            center: ['64%', '50%'],
            data: [{
              value: this.patrolDataCircle.value[this.patrolPosition][startIndex + 2],
              label: {
                normal: {
                  rich: {
                    a: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 30,
                      fontWeight: 'bold'
                    },
                    b: {
                      color: '#fff',
                      align: 'center',
                      fontSize: 12
                    },
                    c: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 16
                    },
                    d: {
                      fontSize: 8
                    }
                  },
                  formatter: function (params) {
                    if(valArr[labelPostion][startIndex] == 0){
                      return '{a|0}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 1] + '}\n'
                    }
                    return '{a|' + Math.round(params.value / valArr[labelPostion][startIndex] * 100) + '}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 2] + '}\n'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '14',
                    fontWeight: 'normal',
                    color: '#fff'
                  }
                }
              },
              itemStyle: {
                normal: {
                  color: '#1E57D8',
                  shadowColor: '#1E57D8',
                  shadowBlur: 0
                }
              }
            }, {
              value: this.patrolDataCircle.value[this.patrolPosition][0] > 0 ? this.patrolDataCircle.value[this.patrolPosition][0] - this.patrolDataCircle.value[this.patrolPosition][startIndex + 2] : 1,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#3C464F'
                },
                emphasis: {
                  color: '#3C464F'
                }
              }
            }]
          },
          {
            name: '第四个圆环',
            type: 'pie',
            clockWise: false,
            radius: [58, 72],
            itemStyle: dataStyle,
            hoverAnimation: false,
            center: ['90%', '50%'],
            data: [{
              value: this.patrolDataCircle.value[this.patrolPosition][startIndex + 3],
              label: {
                normal: {
                  rich: {
                    a: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 30,
                      fontWeight: 'bold'
                    },
                    b: {
                      color: '#fff',
                      align: 'center',
                      fontSize: 12
                    },
                    c: {
                      color: '#FFFFFF',
                      align: 'center',
                      fontSize: 16
                    },
                    d: {
                      fontSize: 8
                    }
                  },
                  formatter: function (params) {
                    if(valArr[labelPostion][startIndex] == 0){
                      return '{a|0}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 1] + '}\n'
                    }
                    return '{a|' + Math.round(params.value / valArr[labelPostion][startIndex] * 100) + '}' + '{c|' + ' %' + '}' + '{d| }\n' + '{d| }\n' + '{b|' + contingencyPlanName[startIndex + 3] + '}\n'
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '14',
                    fontWeight: 'normal',
                    color: '#fff'
                  }
                }
              },
              itemStyle: {
                normal: {
                  color: '#FB5252',
                  shadowColor: '#FB5252',
                  shadowBlur: 0
                }
              }
            }, {
              value: this.patrolDataCircle.value[this.patrolPosition][0] > 0 ? this.patrolDataCircle.value[this.patrolPosition][0] - this.patrolDataCircle.value[this.patrolPosition][startIndex + 3] : 1,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: '#3C464F'
                },
                emphasis: {
                  color: '#3C464F'
                }
              }
            }]
          }
        ]
      }
      // 基于准备好的dom，初始化echarts实例
      this.formTypeBarChart = echarts.init(this.$refs[refVal])
      // 绘制图表
      this.formTypeBarChart.setOption(option)
    },
    showInMapData(type) {
      this.$refs.child.winRestore()
      if (type === '生产型企业') {
        this.$refs.child.checkZdByUnitType('生产型企业')
      } else if (type === '生产型企业-二工区') {
        this.$refs.child.checkZdByUnitType('生产型企业-二工区')
      } else if (type === '幼儿园') {
        this.$refs.child.checkZdByUnitType('幼儿园')
      } else if (type === '小学') {
        this.$refs.child.checkZdByUnitType('小学')
      } else if (type === '初中') {
        this.$refs.child.checkZdByUnitType('初中')
      } else if (type === '中职') {
        this.$refs.child.checkZdByUnitType('中职')
      } else if (type === '高中') {
        this.$refs.child.checkZdByUnitType('高中')
      } else if (type === '功能型学校') {
        this.$refs.child.checkZdByUnitType('功能型学校')
      } else if (type === '辅读学校') {
        this.$refs.child.checkZdByUnitType('辅读学校')
      } else if (type === '国有资产') {
        this.$refs.child.checkZdByUnitType('国有资产')
      } else if (type === '河道总数') {
        this.$refs.child.checkHdByType('河道总数')
      } else if (type === '区管河道') {
        this.$refs.child.checkHdByType('区管河道')
      } else if (type === '镇管河道') {
        this.$refs.child.checkHdByType('镇管河道')
      } else if (type === '村管河道') {
        this.$refs.child.checkHdByType('村管河道')
      } else if (type === '其它河道') {
        this.$refs.child.checkHdByType('其它河道')
      } else if (type === '股份公司河道') {
        this.$refs.child.checkHdByType('股份公司河道')
      }
    },
    //左侧防汛防台处理操作状态触发
    clickHandleButton(index){
      if(this.selectedAlarmPos != index){
        this.selectedAlarmPos = index
      }else{
        this.selectedAlarmPos = -1
      }
    },
    refreshFxftEmergencyList(){
      this.getFxftEmergencyRecords()
    },
    refreshFxftUrgentTaskList(){
      this.getFxftUrgentTaskRecords()
    },
    changeAjAlarm(type) {
      switch (type) {
        case 'ajzs':
          this.isAjActive = 'ajzs'
          this.isAjType='1'
          this.loadCaseData("1")
          break
        case 'cfaj':
          this.isAjActive = 'cfaj'
          this.isAjType='2'
          this.loadCaseData("2")
          break
        case 'zdaj':
          this.isAjActive = 'zdaj'
          this.isAjType='3'
          this.loadCaseData("3")
          break
        case 'jjaj':
          this.isAjActive = 'jjaj'
          this.isAjType='4'
          this.loadCaseData("4")
          break
      }
    },
    changeFxftAlarm(type) {
      switch (type) {
        case 'fxft':
          this.isActive = 'fxft'
          this.isFxftShow = true
          this.isTfsjShow = false
          this.isJjrwShow = false
          this.getTodayAlarmRecords()
          break
        case 'tfsj':
          this.isActive = 'tfsj'
          this.isFxftShow = false
          this.isTfsjShow = true
          this.isJjrwShow = false
          this.refreshFxftEmergencyList()
          break
        case 'jjrw':
          this.isActive = 'jjrw'
          this.isFxftShow = false
          this.isTfsjShow = false
          this.isJjrwShow = true
          this.refreshFxftUrgentTaskList()
          break
      }
    },
    changeWgxwsj(type) {
      switch (type) {
        case 'tdlj':
          this.isHjzzActive = 'tdlj'
          this.isTdljShow = true
          this.isWgscrqShow = false
          break
        case 'wgscrq':
          this.isHjzzActive = 'wgscrq'
          this.isTdljShow = false
          this.isWgscrqShow = true
          break
      }
    },
    changeAjsjtj(type) {
      switch (type) {
        case 'tdljtj':
          this.isHjzzTjActive = 'tdljtj'
          this.isTdljTjShow = true
          this.isWgscrqTjShow = false
          break
        case 'wgscrqtj':
          this.isHjzzTjActive = 'wgscrqtj'
          this.isTdljTjShow = false
          this.isWgscrqTjShow = true
          break
      }
    },
    getTodayAlarmRecords(){
      getTodayAlarmRecord().then(res=>{
        this.todayAlarmRecordList = res.data
      })
    },
    loadCaseData(type){
      if(type == null)
      {
        type=this.isAjType;
      }
      const currentDate1 = new Date();
      var firstDay=null;
      var lastDay=null;
      if(this.startMonth == null)
      {
        const firstDay1 = new Date(currentDate1.getFullYear()-1, 10, 1);
        firstDay=formatDate(firstDay1,'yyyy-MM-dd');
      }
      else
      {
        firstDay=formatDate(this.startMonth,'yyyy-MM-dd');
      }

      if(this.endMonth == null)
      {
        const lastDay1 = new Date(currentDate1.getFullYear(), 9, 1);
        lastDay=formatDate(this.getLastDayOfMonth(lastDay1,'yyyy-MM-dd'));
      }
      else
      {
        lastDay=formatDate(this.getLastDayOfMonth(this.endMonth,'yyyy-MM-dd'));
      }
      this.timeKeyOfCase = new Date().getTime()
      this.getAjzsRecords(firstDay,lastDay,type);
      this.getCRecords(type,firstDay,lastDay);
      this.getResRecords(type);
      this.getResRecords1(type);
    },
    getResRecords(type){
      let form={};
      form.type=type;
      form.residentialarea=this.resName;
      getCountByTypeAndResidentialarea(form).then(res=>{
        if(res.data)
        {
          this.resChartData =Object.values(JSON.parse(JSON.stringify(res.data)))
        }
      })
    },
    getResRecords1(type){
      let form={};
      form.type=type;
      getCountByTypeAndResidentialarea(form).then(res=>{
        if(res.data)
        {
          this.resChartData1 =Object.values(JSON.parse(JSON.stringify(res.data)))
        }
      })
    },
    getResName(type){
      let form={};
      form.type=type;
      getMaxCaseNumberResidentialareaByType(form).then(res=>{
        this.resName=res.data
        this.loadCaseData(type);
      })
    },
    getCRecords(type,firstDay,lastDay){
      let form={};
      form.type=type;
      getCaseNumberByDemandType(form).then(res=>{
        if(res.data)
        {
          this.schartData = JSON.parse(JSON.stringify(res.data))
        }

      })
    },
    getLastDayOfMonth(time) {
      const year = time.getFullYear();
      const month = time.getMonth();
      const lastDay = new Date(year, month + 1, 0);
      lastDay.setHours(23, 59, 59, 999);
      return lastDay;
    },
    getAjzsRecords(firstDay,lastDay,type){
      let form ={};
      form.beginTime=firstDay;
      form.endTime=lastDay;
      form.type=type;
      form.residentialarea=this.resName
      getCList(form).then(res=>{
        this.cList=JSON.parse(JSON.stringify(res.data));
      })
    },
    // 防汛防台突发事件
    getFxftEmergencyRecords(){
      let query = {}
      query.circulationState = this.selectEmergencyStatus
      query.pageNum = 1
      query.pageSize = 99
      query.orderByColumn = 'create_time'
      query.isAsc = 'descending'
      query.alarmLocation = this.selectEmergencyAddress
      getFxftEmergencyRecord(query).then(res=>{
        this.fxftEmergencyList = res.rows
        this.selectedEmergencyIds = []
      })
    },
    // 防汛防台紧急任务
    getFxftUrgentTaskRecords(){
      let query = {}
      query.circulationState = this.selectUrgentTaskStatus
      query.pageNum = 1
      query.pageSize = 99
      query.orderByColumn = 'create_time'
      query.isAsc = 'descending'
      query.alarmLocation = this.selectUrgentTaskAddress
      getFxftUrgentTaskRecord(query).then(res=>{
        this.fxftUrgentTaskList = res.rows
        this.selectedUrgentTaskIds = []
      })
    },
    // 处理防汛防台报警信息
    handleFxftAlarmRecords(ids, type){
      let data = {}
      data.ids = ids
      data.type = type
      handleFxftAlarmRecord(data).then(res=>{
        //取消所有勾选
        this.selectedAlarmIds = []
        this.fxftAlarmList.forEach(alarm=>{
          if(ids.indexOf(alarm.id) != -1){
            alarm.status = type == '1' ? 1 : 2
          }
        })
        this.$message.success("操作成功, 共处理" + ids.length + "条报警信息")
      })
    },
    // 处理环境整治报警信息
    handleHjzzAlarmRecords(ids, type){
      let data = {}
      data.ids = ids
      data.type = type
      handleHjzzAlarmRecord(data).then(res=>{
        //取消所有勾选
        this.selectedMonitorEventIds = []
        this.hjzzMonitorEventList.forEach(alarm=>{
          if(ids.indexOf(alarm.id) != -1){
            alarm.status = type === '1' ? '1' : '2'
          }
        })
        this.$message.success("操作成功, 共处理" + ids.length + "条报警信息")
      })
    },
    // 打开突发事件处置情况窗口
    openFxftEmergencyWindow(index){
      getFxftEmergency(this.fxftEmergencyList[index].id).then(res=>{
        this.fxftEmergency = res.data
        if(res.data.photoUrls != null){
          this.fxftEmergency.photoUrls  = res.data.photoUrls.map(item=>{
            return process.env.VUE_APP_BASE_API + item
          })
        }
        this.fxftEmergencyWindowShow = true
      })
    },
    // 打开紧急任务处置情况窗口
    openFxftUrgentTaskWindow(index){
      getFxftUrgentTask(this.fxftUrgentTaskList[index].id).then(res=>{
        this.fxftUrgentTask = res.data
        if(res.data.sandbagPhotoUrls != null){
          this.fxftUrgentTask.photoUrls  = res.data.sandbagPhotoUrls.map(item=>{
            return process.env.VUE_APP_BASE_API + item
          })
        }
        this.fxftUrgentTaskWindowShow = true
      })
    },
    closeAlarmInfo(){
      this.fxftAlarmHandleWindowShow = false
    },
    closeEmergencyWindow(){
      this.fxftEmergencyWindowShow = false
    },
    closeUrgentTaskWindow(){
      this.fxftUrgentTaskWindowShow = false
    },
    //防汛防台警报设备地址读取
    getFxftAlarmDeviceAddressList(){
      getFxftAlarmDeviceAddress().then(res=>{
        this.fxftAlarmDeviceAddressList = res.data
      })
    },
    // 环境整治 读取数据列表
    getHjzzMonitorEvents(){
      let query = {}
      query.status = this.selectMonitorEventStatus
      query.pageNum = 1
      query.pageSize = 99
      query.orderByColumn = 'alarm_date'
      query.isAsc = 'descending'
      query.alarmTypeName = '偷倒垃圾'
      query.alarmPosition = this.selectedMonitorEventAddress
      getHjzzMonitorEventList(query).then(res=>{
        this.hjzzMonitorEventList = res.rows
        this.selectedMonitorEventIds = []
      })
    },
    getHjzzMonitorWgEvents(){
      let query = {}
      query.pageNum = 1
      query.pageSize = 99
      query.orderByColumn = 'alarm_date'
      query.isAsc = 'descending'
      query.caseType = '2'
      getHjzzMonitorWgEventList(query).then(res=>{
        this.hjzzMonitorWgEventList = res.rows
      })
    },
    //左侧环境整治警报勾选操作处理
    handleSelectHjzzEvent(checked, index){
      let posInSelected = this.selectedMonitorEventIds.indexOf(this.hjzzMonitorEventList[index].id)
      if(checked && this.hjzzMonitorEventList[index].status == null && posInSelected == -1){
        this.selectedMonitorEventIds.push(this.hjzzMonitorEventList[index].id)
      }else if(!checked && posInSelected != -1){
        this.selectedMonitorEventIds.splice(posInSelected, 1)
      }
    },
    //左侧环境整治处理操作状态触发
    clickHjzzHandleButton(index){
      if(this.selectedMonitorEventPos != index){
        this.selectedMonitorEventPos = index
      }else{
        this.selectedMonitorEventPos = -1
      }
    },
    debounceClick: myDebounce(function(index, handleType){
      this.handleSelectedMonitorEvents(index, handleType)
    },1000),
    //左侧环境整治处理流转操作
    handleSelectedMonitorEvents(index, handleType){
      let selectedIds = this.selectedMonitorEventIds
      if(selectedIds.indexOf(this.hjzzMonitorEventList[index].id) == -1){
        //点击处理按钮的那条报警不存在勾选列表中，先勾上
        selectedIds.push(this.hjzzMonitorEventList[index].id)
      }
      //判断是否有第二个设备的报警信息
      let selectedDeviceImei = []
      let hasTwoDevice = false
      selectedIds.forEach(id => {
        if(selectedDeviceImei.length == 0){
          selectedDeviceImei.push(this.hjzzMonitorEventList.find(e => e.id == id).deviceImei)
        }else{
          if(selectedDeviceImei.indexOf(this.hjzzMonitorEventList.find(e => e.id == id).deviceImei) == -1){
            hasTwoDevice = true
          }
        }
      })
      if(hasTwoDevice){
        this.$message.warning('批量操作不能含有两种设备')
        return
      }
      if(selectedIds.length > 0){
        //隐藏打开下拉框
        this.selectedMonitorEventPos = -1
        //调用接口流转报警信息
        this.handleHjzzAlarmRecords(selectedIds, handleType)
      }
    },
    //下拉环境整治处理状态
    refreshHjzzMonitorEventList(){
      this.getHjzzMonitorEvents()
    },
    refreshHjzzMonitorWgEventList(){
      this.getHjzzMonitorWgEvents()
    },
    //打开环境整治卡口监控列表的视频弹窗
    openHjzzMonitorVideoWin(index){
      this.hjzzMonitorVideoWindowShow = false
      let query = {}
      query.alarmCode = this.hjzzMonitorEventList[index].alarmCode
      getHjzzMonitorEventVideo(query).then(res=>{
        this.hjzzMonitorVideoPreviewUrl = res.data
        this.hjzzMonitorVideoWindowShow = true
      })
    },
    openHjzzMonitorVideoWinCase(caseNumber) {
      this.hjzzMonitorVideoHistoryWindowShow = false;
      this.hjzzMonitorVideoHistoryPreviewUrl = `${process.env.VUE_APP_BASE_API}/profile/hjzz/${caseNumber}.mp4`;
      this.hjzzMonitorVideoHistoryWindowShow = true;
    },
    //打开环境整治卡口监控视频流
    openHjzzMonitorRealVideoWin(channelName, channelCode){
      let query = {}
      query.channelCode = channelCode
      getHjzzMonitorVideo(query).then(res=>{
        this.hjzzRealMonitorPreviewUrl = res.data
        this.$refs.myFlvPlayer.playHjzzVideo(this.hjzzRealMonitorPreviewUrl)
        this.hjzzRealMonitorName = channelName
      })
    },
    //关闭环境整治卡口监控列表的视频弹窗
    closeHjzzMonitorVideoWin(){
      this.hjzzMonitorVideoWindowShow = false
    },
    closeHjzzMonitorVideoHistoryWin(){
      this.hjzzMonitorVideoHistoryWindowShow = false
    },
    closeHjzzMonitorVideosWin(){
      this.hjzzMonitorVideosWindowShow = false
    },
    //读取环境整治警报类型、事件地址
    getHjzzMonitorEventTypes(){
      getHjzzMonitorEventTypeList().then(res=>{
        this.hjzzMonitorEventTypeList = res.data
      })
    },
    getViolationsList() {
      getViolations().then(res => {
        this.violationsList = res.data
      })
    },
    searchviolationsList() {
      let dateRange = [];
      dateRange.push(this.startDate);
      dateRange.push(this.endDate);
      let queryParams = {}
      getViolations(this.addDateRange(queryParams, dateRange)).then(res => {
        this.violationsList = res.data
      })
    },
    getHjzzMonitorEventAddresses(){
      getHjzzMonitorEventAddressList().then(res=>{
        this.hjzzMonitorEventAddressList = res.data
      })
    },
    //读取环境整治处置历史信息列表
    getHjzzEventResults(){
      let query = {}
      query.pageNum = 1
      query.pageSize = 999
      query.orderByColumn = 'create_time'
      query.isAsc = 'descending'
      query.circulationState = '0'
      query.caseType = '1'
      getHjzzEventResultList(query).then(res=>{
        this.hjzzEventResultList = res.rows
        this.hjzzEventResultList.forEach(item=>{
          if (item.photoUrls != null && item.photoUrls.length > 0) {
            item.photoUrls = item.photoUrls.map(photo => {
              return process.env.VUE_APP_BASE_API + photo
            })
          }
          // item.personnelInfo数据脱敏
          if (item.personnelInfo != null && item.personnelInfo.length > 0) {
            item.personnelInfo = item.personnelInfo.substr(0, 1) + '*'.repeat(item.personnelInfo.length - 1);
          }
          // item.companyName数据脱敏
          if (item.companyName != null && item.companyName.length > 0) {
            let firstPart = item.companyName.slice(0, 2);
            let lastPart = item.companyName.slice(-4);
            let middlePart = '*'.repeat(item.companyName.length - 6);
            let result = firstPart + middlePart + lastPart;
            item.companyName = result;
          }
        })
      })
    },
    openHjzzCaseInfoWin(index){
      this.hjzzCaseInfo = this.hjzzEventResultList[index]
      this.hjzzCaseInfo.photoUrls = []
      getAlarmRecord(this.hjzzCaseInfo.alarmRecordId).then(res=>{
        if (res.data) {
          const alarmPicture = res.data.alarmPicture
          getIccAccessToken().then(response => {
            const accessToken = response.data
            if (accessToken) {
              let url = 'https://172.16.33.100/evo-pic/' + alarmPicture + '?token=' + accessToken + '&oss_addr=172.16.33.100:8925'
              this.hjzzCaseInfo.photoUrls = [url]
            }
          })
        }
        this.hjzzAlarmRecord = res.data
        this.hjzzHandleInfo = this.hjzzCaseInfo
        this.hjzzHandleShow = true
      })
    },
    openHjzzCaseInfoWinForCapture(alarmPicture, title='环境整治违规行为详情'){
      if(this.hjzzCaseHandleWindowShow) {
        this.hjzzCaseHandleWindowShow = false
      }
      getIccAccessToken().then(response => {
        const accessToken = response.data
        if (accessToken) {
          let url = 'https://172.16.33.100/evo-pic/' + alarmPicture + '?token=' + accessToken + '&oss_addr=172.16.33.100:8925'
          this.hjzzCaseInfo.photoUrls = [url]
          this.hjzzCaseHandleWinTitle = title
          this.hjzzCaseHandleWindowShow = true
        }
      })
    },
    openHjzzCaseInfoWinForCaptureWg(alarmRecordId, title='违规生产入侵'){
      if(this.hjzzCaseHandleWindowShow) {
        this.hjzzCaseHandleWindowShow = false
      }
      getAlarmRecord(alarmRecordId).then(response => {
        let url = 'https://172.16.33.100/evo-apigw/evo-oss/' + response.data.alarmPicture
        this.hjzzCaseInfo.photoUrls = [url]
        this.hjzzCaseHandleWinTitle = title
        this.hjzzCaseHandleWindowShow = true
      })
    },
    openHjzzCaseInfoWinForCaptureCase(index) {
      this.hjzzCaseInfo = this.hjzzEventResultList[index]
      this.hjzzCaseInfo.photoUrls = []
      // 判断是否有附件
      if (this.hjzzCaseInfo.attachment != null && this.hjzzCaseInfo.attachment.length > 0) {
        const photoUrl = process.env.VUE_APP_BASE_API + this.hjzzCaseInfo.attachment
        this.hjzzCaseInfo.photoUrls = [photoUrl]
        this.hjzzCaseHandleWinTitle = '环境整治警报处置详情'
        this.hjzzCaseHandleWindowShow = true
      } else {
        getAlarmRecord(this.hjzzCaseInfo.alarmRecordId).then(res=>{
          const alarmPicture = res.data.alarmPicture
          this.openHjzzCaseInfoWinForCapture(alarmPicture, '环境整治警报处置详情')
        })
      }
    },
    closeHjzzCaseInfoWin() {
      this.hjzzCaseHandleWindowShow = false
    },
    closeHjzzHandleDetail() {
      this.hjzzHandleShow = false
    },
    handleResponsiblePersonName(alarm) {
      let name = alarm.personnelInfo
      if (alarm.isPersonalBehavior === '否') {
        name = alarm.companyName
      }
      let nameLength = name != null ? name.length : 0
      if(nameLength > 1){
        name = name.substr(0, 1)
        for(let i = nameLength-1; i >= 1; i--){
          name = name + '*'
        }
      }
      return name
    },
    // 关闭/显示突发事件图层
    changeShowEmergent() {
      this.$refs.child.showEmergentFeatureLayer()
    },
    openEmergentDetail(taskid) {
      this.$refs.child.openEmergentDetail(taskid)
    },
    getDeviceStatusNum() {
      getDeviceStatusNum().then(res => {
        this.deviceStatusNum = res.data
      })
    },
    handleChartValueSelected(value) {
      this.leibieIndex=value;
    },
    handleChartValueSelected1(value) {
      this.leibieIndex=value;
    },
    handleButtonClick(type) {
      // 使用对象映射标题,避免多个if-else
      const titleMap = {
        '1': '案件总数',
        '2': '重复案件',
        '3': '重点案件',
        '4': '紧急案件'
      }

      this.isAjType = type
      this.ajTitle = titleMap[type] || '案件总数' // 添加默认值
      this.getResName(type);

    },
    handleNameClick(name) {
      this.resName=name
      this.loadCaseData(this.isAjType);
    },
    handleLayerChange(layer) {
      // 切换按钮背景图
      this.layers.forEach(l => {
        l.active = l.type === layer.type;
      });
      // 处理图层切换逻辑 layerType: area 区域图层，map 地图图层
      if (layer.type === 'area') {
        this.showSvgMap = true;
      } else {
        this.showSvgMap = false;
      }
    },
    openYzl() {
      window.open('https://icity.shdict.com:8880/dict/icity/#/yzlscreen', '_blank')
    },
    // 打开液位设备页面
    openLiquidLevelDevicePage() {
      const routeUrl = this.$router.resolve({
        path: '/flood/liquidLevelDevice',
        query: {
          deviceState: '离线'
        }
      })
      window.open(routeUrl.href, '_blank')
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },

};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
.el-select-dropdown{
  background-color: #08182F;
  border: 1px solid #515F6F;
}
/*修改单个的选项的样式*/
.el-select-dropdown__item {
  color: #ffffff;
  font-size: 13px;
  background-color: #08182F;
}
/*item选项的hover样式*/
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background-color: #122856;
  color: #ffffff;
}

.el-popper[x-placement^=bottom] .popper__arrow:after {
  top: 1px;
  margin-left: -6px;
  border-top-width: 0;
  border-bottom-color: #08182F;
}
.el-input--medium .el-input__inner{
  height: 26px;
  line-height: 26px;
  margin-bottom: 4px;
}
.el-input--medium .el-input__icon{
  line-height: 30px;
}
.el-input__inner{
  padding: 0 10px;
  background-color: #333F6A;
  color: #FFFFFF;
}
.monitor-video-bg #dhFlvVideoWrapper{
  width: 520px !important;
  height: 300px !important;
}
</style>
