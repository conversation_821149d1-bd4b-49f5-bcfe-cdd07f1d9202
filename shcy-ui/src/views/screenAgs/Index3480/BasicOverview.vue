<template>
  <div>
    <!-- 基本概况开始 -->
    <div class="module_title">
      <span>基本概况</span>
      <div class="small-square-box">
        <span class="square01"></span>
        <span class="square02"></span>
        <span class="square03"></span>
      </div>
    </div>
    <div style="display: flex;display: -webkit-flex;margin-top: 24px;">
      <div class="small-map">
        <div class="people-num-title">人口数量</div>
        <div class="people-num">89600<span>人</span></div>
      </div>
      <div class="four-data-top-right">
        <div :class="jbgkPosition === 0 ? 'data-every-bg-press' : 'data-every-bg'" title="行政辖区面积" @click="showInMap('现状使用主体', '行政辖区面积', 0)">
          <div class="title">行政辖区面积</div>
          <div class="data">19.12<span>平方公里</span></div>
        </div>
        <div :class="jbgkPosition === 1 ? 'data-every-bg-press' : 'data-every-bg'" title="非住宅物业" @click="showInMap('现状使用主体', '非住宅物业', 1)">
          <div class="title">非住宅物业</div>
          <div class="data">21<span>家</span></div>
        </div>
        <div :class="jbgkPosition === 2 ? 'data-every-bg-press' : 'data-every-bg'" title="小区垃圾房" @click="showInMap('小区基础信息', '垃圾房', 2)">
          <div class="title">小区垃圾房</div>
          <div class="data">168<span>个</span></div>
        </div>
        <div :class="jbgkPosition === 3 ? 'data-every-bg-press' : 'data-every-bg'" title="建筑(大件)垃圾堆放点" @click="showInMap('小区基础信息', '建筑垃圾', 3)">
          <div class="title">建筑(大件)垃圾堆放点</div>
          <div class="data">57<span>个</span></div>
        </div>
      </div>
    </div>
    <div class="four-data-top-right" style="width:803px;height:288px;margin-left: 0px;position:relative;top:-22px;padding-left: 6px;">
      <div :class="jbgkPosition === 4 ? 'data-every-bg-press' : 'data-every-bg'" title="公寓租赁房" style="margin-left: 0px;" @click="showInMap('现状使用主体', '公寓租赁房', 4)">
        <div class="title">公寓租赁房</div>
        <div class="data">31<span>个</span></div>
      </div>
      <div :class="jbgkPosition === 5 ? 'data-every-bg-press' : 'data-every-bg'" title="文化娱乐场所" @click="showInMap('现状使用主体', '文化娱乐场所', 5)">
        <div class="title">文化娱乐场所</div>
        <div class="data">21<span>个</span></div>
      </div>
      <div :class="jbgkPosition === 6 ? 'data-every-bg-press' : 'data-every-bg'" title="排污泵站" @click="showInMap('排水信息', '排污泵站', 6)">
        <div class="title">排污泵站</div>
        <div class="data">5<span>座</span></div>
      </div>
      <div :class="jbgkPosition === 7 ? 'data-every-bg-press' : 'data-every-bg'" title="排涝泵站" @click="showInMap('排水信息', '排涝泵站', 7)">
        <div class="title">排涝泵站</div>
        <div class="data">2<span>座</span></div>
      </div>
      <div class="data-every-bg" title="市政污水管道" style="margin-left: 0px;">
        <div class="title">市政污水管道</div>
        <div class="data">28<span>条</span></div>
      </div>
      <div class="data-every-bg" title="市政污水井盖">
        <div class="title">市政污水井盖</div>
        <div class="data">964<span>座</span></div>
      </div>
      <div class="data-every-bg" title="市政雨水管道">
        <div class="title">市政雨水管道</div>
        <div class="data">28<span>条</span></div>
      </div>
      <div class="data-every-bg" title="市政雨水井盖">
        <div class="title">市政雨水井盖</div>
        <div class="data">3379<span>座</span></div>
      </div>
      <div class="data-every-bg" title="城市沙滩停车场" style="margin-left: 0px;">
        <div class="title">城市沙滩停车场</div>
        <div class="data">3<span>个</span></div>
      </div>
      <div class="data-every-bg" title="交通场站">
        <div class="title">交通场站</div>
        <div class="data">4<span>个</span></div>
      </div>
      <div class="data-every-bg" title="养老院">
        <div class="title">养老院</div>
        <div class="data">4<span>个</span></div>
      </div>
      <div class="data-every-bg" title="综合为老服务">
        <div class="title">综合为老服务</div>
        <div class="data">2<span>个</span></div>
      </div>
    </div>
    <!-- 基本概况结束 -->
  </div>
</template>

<script>
export default {
  name: "BasicOverview",
  props: {
    jbgkPosition: {
      type: Number,
      default: -1
    }
  },
  methods: {
    showInMap(type, data, pos) {
      this.$emit('show-in-map', type, data, pos);
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style> 