.screen-container {
  position: relative;
  width: 11520px;
  height: 3240px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  // 地图容器
  .map-container {
    margin: 0 auto;
    width: 6270px;
    height: 3240px;
  }

  // 上背景样式
  .background-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 11520px;
    height: 448px;
    background: url("../../../assets/screen_display_img/top_bg_yinying.png") no-repeat;
    background-size: contain;

    // 标题盒子
    .title-container {
      position: relative;
      margin: 0 auto;
      margin-top: 6px;
      width: 6012px;
      height: 429px;
      background: url("../../../assets/screen_display_img/top_bg_title.png") no-repeat;
      background-size: contain;


      .title-box {
        -moz-user-select: none;
        -khtml-user-select: none;
        user-select: none;
        text-align: center;


        .title-chinese {
          font-size: 140px;
          font-family: Hiragino Sans GB;
          font-weight: 900;
          letter-spacing: 42px;
          color: #FEFEFE;
          background: linear-gradient(0deg, #A5C8FD 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .title-pinyin {
          font-size: 31px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #AFC5FD;
          text-transform: uppercase;
          letter-spacing: 20px;
        }

      }

      // 顶部按钮
      .btn-container {
        display: flex;
        position: absolute;
        top: 440px;
        left: 885px;
        width: 4242px;
        height: 143px;
        text-align: center;
        z-index: 999;

        .btn-top-item {
          position: relative;
          margin: 0 49px;
          width: 609px;
          height: 143px;
          background: url("https://pic.rmb.bdstatic.com/bjh/618794ab53e6235d892beff133bd0921.png") no-repeat;
          background-size: contain;
          cursor: pointer;

          .btn-icon {
            position: absolute;
            top: 29px;
            left: 68px;
            display: inline-block;
            width: 93px;
            height: 90px;
            background-size: contain;
          }

          .btn-title {
            display: inline-block;
            margin-left: 180px;
            width: 330px;
            height: 143px;
            font-size: 46px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            letter-spacing: 8px;
            text-align: center;
            color: #FFFFFF;
            line-height: 143px;
            -moz-user-select: none;
            -khtml-user-select: none;
            user-select: none;
          }
        }

        .btn-top-active {
          background: url("https://pic.rmb.bdstatic.com/bjh/623c1d959b20eb4709d6059e12c3f5a7.png") no-repeat;
        }

        .party-logo {
          background: url('https://bj.bcebos.com/baidu-rmb-video-cover-1/14d11e09d4f7acb5dbb261feccb0ae87.png') no-repeat !important;
        }

        .btn-top-item:hover::after {
          position: absolute;
          content: '';
          display: block;
          // top: 50px;
          left: 130px;
          width: 362px;
          height: 44px;
          background: url("https://pic.rmb.bdstatic.com/bjh/9ea4b78cda6236d3d914d00ebd315237.png") no-repeat;
          background-size: contain;
        }

        .btn-top-item:hover {
          background: url("https://pic.rmb.bdstatic.com/bjh/623c1d959b20eb4709d6059e12c3f5a7.png") no-repeat;
        }

        .btn-top-item:nth-child(5):hover {
          background: url('https://bj.bcebos.com/baidu-rmb-video-cover-1/14d11e09d4f7acb5dbb261feccb0ae87.png') no-repeat;
        }

        .btn-top-item:nth-child(5) {
          background: url("https://bj.bcebos.com/baidu-rmb-video-cover-1/62de358e145551aada9e2687119c95a2.png") no-repeat;
        }

        .btn-top-item:nth-child(1) {
          .btn-icon {
            background: url("https://pic.rmb.bdstatic.com/bjh/67d97dc1785470d203adacb32ba95f86.png") no-repeat;
          }
        }

        .btn-top-item:nth-child(2) {
          .btn-icon {
            background: url("https://bj.bcebos.com/baidu-rmb-video-cover-1/691da180adfb286f23386cadcc7a78c3.png") no-repeat;
          }
        }

        .btn-top-item:nth-child(3) {
          .btn-icon {
            background: url("https://pic.rmb.bdstatic.com/bjh/983833ade9f13703f6159cc0330742a4.png") no-repeat;
          }
        }

        .btn-top-item:nth-child(4) {
          .btn-icon {
            background: url("https://bj.bcebos.com/baidu-rmb-video-cover-1/4ef711b69e0242fd52fe9dc00801791e.png") no-repeat;
          }
        }

        .btn-top-item:nth-child(6) {
          .btn-icon {
            background: url("https://pic.rmb.bdstatic.com/bjh/c43ed98a1299fd8f1aca2cc0d94fed78.png") no-repeat;
          }
        }
      }
    }
  }

  // 下方背景样式
  .background-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 11520px;
    height: 448px;
    background: url("../../../assets/screen_display_img/bottom_bg_yinying.png") no-repeat;
    background-size: contain;

    // 下方按钮容器
    .btn-bottom-container {
      position: relative;
      margin: 189px auto 0;
      width: 5257px;
      height: 226px;
      background: url("../../../assets/screen_display_img/bottom_bg_tab.png") no-repeat;
      background-size: contain;
      z-index: 999;

      // 按钮
      .btn-bottom-box {
        position: relative;
        display: flex;
        margin: 0 auto;
        width: 3312px;
        height: 183px;

        // 小区基础信息弹窗
        .basicInfoWindow {
          position: absolute;
          bottom: 1500px;
          right: -500px;
          width: 717px;
          height: 738px;
          background: url("../../../assets/screen_display_img/basicInfoWindow.png") no-repeat;
          background-size: contain;
          z-index: 20;
        }

        .btn-bottom-item {
          margin: 40px 36px 0;
          width: 480px;
          height: 183px;
          background: url("../../../assets/screen_display_img/bottom_img_tab_press.png") no-repeat;
          background-size: contain;
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 183px;
          text-align: center;
          letter-spacing: 6px;
          cursor: pointer;
          opacity: 0.6;
          -moz-user-select: none;
          -khtml-user-select: none;
          user-select: none;
        }

        // 按钮透明度
        .active {
          opacity: 1;
        }
      }

      // 搜索框、下拉列表
      .search-option-container {
        display: flex;
        position: absolute;
        top: -300px;
        left: 1753px;
        margin: 0 auto;
        width: 1750px;
        height: 124px;

        // 搜索框
        .search-box {
          display: flex;
          width: 764px;
          height: 124px;

          .input-search {
            padding: 0 0 0 50px;
            width: 640px;
            height: 124px;
            outline: none;
            border: none;
            background-color: #36365E;
            font-size: 36px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #8F8FA7;
            line-height: 124px;
          }

          .input-search-icon {
            position: relative;
            display: inline-block;
            width: 124px;
            height: 124px;
            background-color: #474768;
            cursor: pointer;

            .search-icon {
              position: absolute;
              top: 41px;
              left: 41px;
              display: block;
              width: 38px;
              height: 44px;
              background: url("../../../assets/screen_display_img/bottom_icon_search.png") no-repeat;
              background-size: contain;
            }
          }
        }

        // 下拉框
        .sel-option-box {
          margin-left: 222px;
          // width: 764px;
          // height: 124px;
        }
      }
    }

    // 下方按钮容器
    .btn-bottom-party-container {
      position: relative;
      margin: 189px auto 0;
      width: 5257px;
      height: 226px;
      background: url("../../../assets/screen_display_img/bottom_bg_tab.png") no-repeat;
      background-size: contain;
      z-index: 999;

      // 按钮
      .btn-bottom-box {
        position: relative;
        display: flex;
        margin: 0 auto;
        width: 3312px;
        height: 183px;

        // 小区基础信息弹窗
        .basicInfoWindow {
          position: absolute;
          bottom: 1500px;
          right: -500px;
          width: 717px;
          height: 738px;
          background: url("../../../assets/screen_display_img/basicInfoWindow.png") no-repeat;
          background-size: contain;
          z-index: 20;
        }

        .btn-bottom-item {
          margin: 40px 36px 0;
          width: 480px;
          height: 183px;
          background: url("../../../assets/screen_display_img/bottom_img_tab_press.png") no-repeat;
          background-size: contain;
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 183px;
          text-align: center;
          letter-spacing: 6px;
          cursor: pointer;
          opacity: 0.6;
          -moz-user-select: none;
          -khtml-user-select: none;
          user-select: none;
        }

        // 按钮透明度
        .active {
          opacity: 1;
        }
      }
    }

  }

  // 左边背景
  .background-left {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    width: 3945px;
    height: 3240px;
    background: url("../../../assets/screen_display_img/L_bg_yinying.png") no-repeat;
    background-size: contain;

    // 左边内容容器
    .chart-left-container {
      width: 2875px;
      height: 3240px;

      // 内容盒子
      .chart-left-box {
        display: flex;
        width: 2744px;
        height: 3049px;
        margin: 105px 0 86px 56px;

        // 边框样式
        .border-left-style {
          width: 78px;
          height: 3049px;
          background: url("../../../assets/screen_display_img/L_img_biankuuang.png") no-repeat;
          background-size: contain;
        }

        // 内容展示区
        .content-data-box {
          margin-left: 254px;
          width: 2410px;
          height: 100%;

          // 日期-时间容器
          .date-time-container {
            position: relative;
            margin: 55px 0 55px 0;
            width: 100%;
            height: 75px;

            span {
              font-size: 60px;
              // line-height: 75px;
              font-weight: 500;
              font-family: Source Han Sans CN;
              color: #c0d1f7;
            }

            .date {
              margin-right: 120px;
            }

            .week {
              margin-right: 140px;
            }

            .time {
              margin-right: 160px;
            }

            .today-title {
              font-size: 36px;
              font-weight: 400;
              color: #b6b6bd;
              vertical-align: super;
            }
          }

          // 今日执勤
          .beOnDuty {
            height: 100px;
            margin: 20px 0 25px 0;
            font-size: 46px;
            font-family: Source Han Sans CN;
            // letter-spacing: 8px;

            .title {
              color: #c0d1f7;
            }

            .people {
              color: #fff;
            }

          }

          .data-change-box {
            width: 2410px;
            height: 2685px;

            // 绿化情况容器
            .greening-situation-container {
              margin-bottom: 200px;
              width: 2410px;
              height: 1035px;

              .greening-situation-box {
                width: 2410px;
              }

              // 绿化情况 标题
              .greening-situation-title {
                position: relative;
                width: 2410px;
                height: 120px;
                font-size: 60px;
                font-family: Source Han Sans CN;
                font-weight: bold;
                color: #ffffff;
                line-height: 120px;
                background: linear-gradient(90deg,
                    #283498 0%,
                    rgba(40, 52, 152, 0.2) 100%);

                .greening-title {
                  padding-left: 98px;
                }
              }

              // 绿化情况 内容
              .greening-situation-content {
                display: flex;
                width: 2410px;
                height: 915px;

                // 左边内容盒子
                .greening-left-data-box {
                  width: 1310px;
                  height: 100%;

                  .greening-inner-box {
                    display: flex;
                    // flex-flow: row wrap;
                    flex-wrap: wrap;
                    margin: 96px 0 0 75px;
                    width: 1235px;
                    height: 819px;

                    .greening-item {
                      display: flex;
                      margin-right: 10px;
                      margin-bottom: 72px;
                      width: 575px;
                      height: 151px;

                      .greening-icon {
                        width: 151px;
                        height: 151px !important;
                        background-size: contain;
                      }

                      .greening-detail-data {
                        margin-left: 61px;
                        width: 363px;
                        height: 151px;

                        .greening-detail-title {
                          font-size: 36px;
                          font-family: Source Han Sans CN;
                          font-weight: 400;
                          color: #b6b6bd;
                          line-height: 61px;
                        }

                        .greening-detail-number {
                          font-family: Source Han Sans CN;
                          font-weight: 500;
                          color: #ffffff;
                          line-height: 90px;

                          .greening-num {
                            font-size: 60px;
                          }

                          .greening-unit {
                            margin-left: 20px;
                            font-size: 36px;
                          }
                        }
                      }
                    }

                    .greening-item:nth-child(1) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/fe855aa8aed2cebe1714781619c273a2.png") no-repeat;
                    }

                    .greening-item:nth-child(2) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/f67dccd3ce66ad38ce7029c1102bc4ca.png") no-repeat;
                    }

                    .greening-item:nth-child(3) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/36de911b6f7ba31b0207941647251fca.png") no-repeat;
                    }

                    .greening-item:nth-child(4) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/ccc3290de4f0716f1031b02a980edc40.png") no-repeat;
                    }

                    .greening-item:nth-child(5) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/236e2b7357052c6f50fb3507467a4d4b.png") no-repeat;
                    }

                    .greening-item:nth-child(6) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/41843079c6c14ab70d62e9b344e50125.png") no-repeat;
                    }

                    .greening-item:nth-child(7) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/765e4622895304e4c62b9d916ccfcc47.png") no-repeat;
                    }

                    .greening-item:nth-child(8) .greening-icon {
                      background: url("https://pic.rmb.bdstatic.com/bjh/3d3a29c11663b29a3834fcd8676e7af8.png") no-repeat;
                    }
                  }
                }

                // 右边内容盒子
                .greening-right-varieties-box {
                  width: 1110px;
                  height: 100%;

                  .greening-inner-top-box {
                    margin-top: 75px;
                    width: 1100px;
                    height: 45px;

                    .ring {
                      display: inline-block;
                      width: 36px;
                      height: 36px;
                      border: 8px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 27px;
                      font-size: 48px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 45px;
                    }
                  }

                  .greening-inner-center-box {
                    width: 1100px;
                    height: 670px;
                  }

                  .greening-inner-bottom-box {
                    display: flex;
                    align-items: center;
                    width: 1100px;
                    height: 125px;

                    .greening-num-icon {
                      width: 96px;
                      height: 96px;
                      background: url("../../../assets/screen_display_img/L_icon_lvzhi.png") no-repeat;
                      background-size: contain;
                    }

                    .greening-num-data {
                      margin: 0 33px;
                      width: 280px;
                      height: 110px;

                      .greening-num-title {
                        width: 100%;
                        height: 45px;
                        font-size: 36px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #B6B6BD;
                      }

                      .greening-num-percentage {
                        width: 100%;
                        height: 60px;

                        .greening-num {
                          margin-right: 20px;
                          font-size: 48px;
                          font-family: Source Han Sans CN;
                          font-weight: 500;
                          color: #FFFFFF;
                        }

                        .percentage {
                          margin-right: 20px;
                          font-size: 36px;
                          font-family: Source Han Sans CN;
                          font-weight: 400;
                          color: #B6B6BD;
                        }

                        .icon_up {
                          margin-bottom: 10px;
                          display: inline-block;
                          width: 31px;
                          height: 18px;
                          background: url("../../../assets/screen_display_img/icon_up.png") no-repeat;
                          background-size: contain;
                        }

                      }
                    }

                    .greening-num-chart {
                      width: 456px;
                      height: 96px;
                    }
                  }
                }
              }
            }

            // 市容情况容器
            .city-situation-container {
              width: 100%;
              height: 1455px;

              .city-situation-box {
                width: 2410px;
              }

              // 市容情况标题
              .city-situation-title {
                position: relative;
                width: 100%;
                height: 120px;
                font-size: 60px;
                font-family: Source Han Sans CN;
                font-weight: bold;
                color: #ffffff;
                line-height: 120px;
                background: linear-gradient(90deg,
                    #283498 0%,
                    rgba(40, 52, 152, 0.2) 100%);

                .city-title {
                  padding-left: 98px;
                }
              }

              // 市容情况数据
              .city-situation-content {
                width: 2410px;
                height: 1334px;

                .city-situation-inner {
                  display: flex;
                  margin: 76px 0 0 80px;
                  width: 2330px;
                  height: 1258px;

                  .city-left-classify-box {
                    margin-right: 180px;
                    width: 750px;
                    height: 1258px;

                    // title - 垃圾分类排名情况
                    .city-left-classify-top-box {
                      margin-bottom: 64px;
                      width: 100%;
                      height: 45px;

                      .ring {
                        display: inline-block;
                        width: 36px;
                        height: 36px;
                        border: 8px solid #28DFAE;
                        border-radius: 50%;
                        margin: auto;
                      }

                      .chart-title {
                        margin-left: 27px;
                        font-size: 48px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #FFFFFF;
                        line-height: 45px;
                      }
                    }

                    .city-left-classify-center-box {
                      margin-bottom: 29px;
                      width: 100%;
                      height: 170px;
                      // background-color: #fff;
                    }

                    .city-left-classify-bottom-box {
                      width: 100%;
                      height: 950px;
                      font-size: 36px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 94px;

                      .classify-ranking-table-item {
                        width: 100%;
                        height: 94px;
                        border-bottom: 1px solid #656594;

                        .serial-number {
                          margin-left: 20px;
                          margin-right: 90px;
                          display: inline-block;
                          width: 30px;
                          height: 100%;
                        }

                        .community-name {
                          margin-right: 20px;
                          display: inline-block;
                          width: 350px;
                          height: 100%;
                        }

                        .percentage {
                          margin-right: 40px;
                          display: inline-block;
                          width: 140px;
                          height: 100%;
                        }

                        .rise-and-fall {
                          margin-bottom: 10px;
                          display: inline-block;
                          width: 31px;
                          height: 18px;
                          background-repeat: no-repeat;
                          background-size: contain;
                        }
                      }
                    }
                  }

                  // 垃圾分类事件处置
                  .city-right-event-box {
                    width: 1404px;
                    height: 1258px;

                    // title
                    .city-right-classify-top-box {
                      margin-bottom: 64px;
                      width: 100%;
                      height: 45px;

                      .ring {
                        display: inline-block;
                        width: 36px;
                        height: 36px;
                        border: 8px solid #28DFAE;
                        border-radius: 50%;
                        margin: auto;
                      }

                      .chart-title {
                        margin-left: 27px;
                        font-size: 48px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #FFFFFF;
                        line-height: 45px;
                      }
                    }
                  }

                  // data - table
                  .city-right-classify-bottom-box {
                    width: 1404px;
                    height: 1146px;
                    background: #181839;
                    border: 4px solid #656594;
                    overflow: hidden;

                    // table - title
                    // 垃圾分类排名情况 - table
                    .city-table-title {
                      width: 100%;
                      height: 96px;
                      border-bottom: 4px solid #656594;
                      background: #2D2D64;
                      font-size: 36px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 96px;

                      .housing-estate {
                        margin-left: 152px;
                      }

                      .garbage-room {
                        margin-left: 183px;
                      }

                      .event {
                        margin-left: 238px;
                      }

                      .happen-time {
                        margin-left: 244px;
                      }
                    }

                    // 垃圾分类事件处置 - table
                    .city-table-detail-box {
                      width: 100%;
                      height: 100%;
                      overflow: scroll;
                      font-size: 36px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 105px;

                      // table - item
                      .city-table-detail-item {
                        margin: 0 auto;
                        width: 1299px;
                        height: 105px;
                        border-bottom: 1px solid #656594;

                        span {
                          height: 105px;
                          display: inline-block;
                          white-space: nowrap;
                          /*超出的空白区域不换行*/
                          overflow: hidden;
                          /*超出隐藏*/
                          text-overflow: ellipsis;
                          /*文本超出显示省略号*/
                        }

                        .housing-estate-item {
                          margin-left: 30px;
                          width: 290px;
                        }

                        .garbage-room-item {
                          margin-left: 70px;
                          width: 110px;
                        }

                        .event-item {
                          margin-left: 60px;
                          width: 364px;
                        }

                        .happen-time-item {
                          margin-left: 105px;
                          width: 260px;
                        }

                      }

                      // .city-table-detail-item:last-child {
                      //   margin-bottom: 100px;
                      //   border-bottom: none;
                      // }
                    }

                    // 隐藏滚动条
                    .city-table-detail-box::-webkit-scrollbar {
                      display: none;
                    }


                  }
                }
              }
            }

            // 街道总览-人口概括-环境概括
            .dataOverview {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/54355a4d042f5c59f6f781d49dc51ab9.png") no-repeat;
              background-size: contain;
            }

            // 城市运行库-安全生产-平安态势
            .citySafety {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/d3500b5c9e3937219a778b82586777fb.png") no-repeat;
              background-size: contain;
            }

            // 人房库
            .peopleRoom {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/623e00c8a341c1787803bf9ad2a72e5d.png") no-repeat;
              background-size: contain;
            }

            // 民生服务库
            .liveService {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/e7c7d9c87febab5dcbf027e1a24dedd8.png") no-repeat;
              background-size: contain;
            }

            // 党建库-党建情况-工青妇-社区党校活动
            .partyBuilding {
              width: 100%;
              height: 100%;
              background: url("https://pic.rmb.bdstatic.com/bjh/a1b42ad559b60533a05beb3f033ba763.png") no-repeat;
              background-size: contain;
            }
          }
        }
      }
    }

    // 左边半环
    .ring-left-box {
      width: 1070px;
      height: 3240px;
      background: url("../../../assets/screen_display_img/middle_L_img_bg.png") no-repeat;
      background-size: contain;
    }
  }

  // 右边背景
  .background-right {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    width: 3945px;
    height: 3240px;
    background: url("../../../assets/screen_display_img/R_bg_yinying.png") no-repeat;
    background-size: contain;

    // 右边半环
    .ring-right-box {
      position: relative;
      width: 1070px;
      height: 3240px;
      background: url("../../../assets/screen_display_img/middle_R_img_bg.png") no-repeat;
      background-size: contain;
    }

    // 右边内容容器
    .chart-right-container {
      width: 2875px;
      height: 3240px;

      .chart-right-inner {
        display: flex;
        margin: 105px 56px 0 72px;
        width: 2734px;
        height: 3049px;

        // 右边内容
        .chart-right-content {
          margin-right: 246px;
          width: 2410px;
          height: 3049px;

          // 最新通知
          .latest-notice-box {
            display: flex;
            margin-bottom: 160px;
            width: 2410px;
            height: 173px;

            span {
              display: inline-block;
            }

            // 图标
            .latest-notice-icon {
              margin: 40px 22px 0 0;
              width: 80px;
              height: 93px;
              background: url("../../../assets/screen_display_img/R_icon_tongzhi.png") no-repeat;
              background-size: contain;
            }

            // "最新通知"字样
            .latest-notice-word {
              font-size: 60px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #C0D1F7;
              line-height: 173px;
            }

            // 通告内容
            .latest-notice-title {
              display: inline-block;
              width: 1500px;
              margin-right: 77px;
              font-size: 60px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #FFFFFF;
              line-height: 173px;
              overflow: hidden;

              .notice-content {
                white-space: nowrap;
                width: max-content;
                // 跑马灯-动画效果
                // @keyframes 匀速运动 运动周期 无限执行 延迟1s 运动方向
                animation: roll linear 15s infinite 1s normal;
              }

              .notice-content:hover {
                animation-play-state: paused;
              }


            }

            // 通告时间
            .latest-notice-time {
              margin: 78px 80px 0 0;
              font-size: 36px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #B6B6BD;
            }

            // 通告切换
            .latest-notice-change {
              padding: 50px 0;
              display: flex;
              flex-flow: row wrap;
              align-items: center;
              width: 16px;

              i {
                color: #fff;
                cursor: pointer;
                background-size: contain;
              }

              // change-up
              // .change-up {}

              // change-down
            }
          }

          // 网格化事件
          .gridding-events-box {
            width: 2410px;
            height: 2716px;

            // 网格化事件-标题
            .gridding-events-title-box {
              position: relative;
              width: 100%;
              height: 120px;
              background: linear-gradient(90deg, #283498 0%, rgba(40, 52, 152, 0.2000) 100%);
              font-size: 60px;
              font-family: Source Han Sans CN;
              font-weight: bold;
              color: #ffffff;
              line-height: 120px;

              .gridding-events-title {
                padding-left: 98px;
              }

            }

            // 网格化事件-内容
            .gridding-events-content-box {
              padding-left: 98px;
              width: 2410px;
              // height: 2716px;

              // 网格化事件- 上部分
              .gridding-events-top-box {
                display: flex;
                width: 2410px;

                // 案件核心指示
                .case-core-box {
                  width: 1213px;
                  height: 896px;

                  // 标题
                  .case-core-title {
                    width: 100%;
                    height: 197px;
                    line-height: 197px;

                    .ring {
                      display: inline-block;
                      width: 36px;
                      height: 36px;
                      border: 8px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 27px;
                      font-size: 48px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                  }

                  // 图表
                  .case-core-chart {
                    width: 100%;
                    height: 699px;
                    background: url("https://pic.rmb.bdstatic.com/bjh/9e3afe196fe776501e7f792ca5a9e9d4.png") no-repeat;
                    background-size: contain;
                  }
                }

                // 案件类别
                .case-category-box {
                  width: 1099px;
                  height: 896px;

                  // 标题
                  .case-category-title {
                    width: 100%;
                    height: 197px;
                    line-height: 197px;

                    .ring {
                      display: inline-block;
                      width: 36px;
                      height: 36px;
                      border: 8px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 27px;
                      font-size: 48px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }
                  }

                  // 图表
                  .case-category-chart {
                    display: flex;
                    width: 100%;
                    height: 699px;
                    background: url("https://pic.rmb.bdstatic.com/bjh/95341319b23ba9c98073b6b746afd2a7.png") no-repeat;
                    background-size: contain;

                    .case-category-chart-left {
                      width: 500px;
                      height: 100%;
                    }

                    .case-category-chart-right {
                      width: 599px;
                      height: 699px;
                    }
                  }
                }



              }

              // 网格化事件- 下部分
              .gridding-events-bottom-box {
                display: flex;
                width: 2312px;
                height: 1672px;

                // 事件上报情况
                .incident-reporting-box {
                  width: 1628px;
                  height: 1669px;

                  // title
                  .incident-reporting-title {
                    position: relative;
                    width: 100%;
                    height: 197px;
                    line-height: 197px;

                    .ring {
                      display: inline-block;
                      width: 36px;
                      height: 36px;
                      border: 8px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 27px;
                      font-size: 48px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                    // 查看更多
                    .seeMore {
                      position: absolute;
                      top: 78px;
                      right: 0;
                      display: inline-block;
                      width: 142px;
                      height: 46px;
                      text-align: center;
                      background: #1C1C36;
                      border: 2px solid #DFDFFF;
                      border-radius: 20px;
                      font-size: 18px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 42px;
                      cursor: pointer;
                    }
                  }

                  // 图表
                  .incident-reporting-table {
                    width: 100%;
                    height: 1472px;
                    background: #181839;
                    border: 4px solid #656594;

                    // 表头
                    .incident-reporting-table-title {
                      width: 100%;
                      height: 96px;
                      border-bottom: 4px solid #656594;
                      background: #2D2D64;
                      font-size: 36px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 96px;

                      .event-number {
                        margin-left: 145px;
                      }

                      .reporting-time {
                        margin-left: 220px;
                      }

                      .event-description {
                        margin-left: 330px;
                      }

                      .case-progress {
                        margin-left: 278px;
                      }
                    }

                    // 表格
                    .incident-reporting-table-detail-box {
                      width: 100%;
                      height: 1368px;
                      overflow: scroll;
                      font-size: 36px;
                      font-weight: 400;
                      color: #FFFFFF;
                      background-color: #181839;

                      .incident-reporting-table-detail-item {
                        display: flex;
                        margin: 0 auto;
                        width: 1510px;
                        // height: 122px;
                        border-bottom: 1px solid #656594;
                        cursor: pointer;

                        span {
                          height: 250px;
                          display: inline-block;

                        }

                        .event-number-item {
                          margin-right: 50px;
                          width: 350px;
                          line-height: 250px;
                        }

                        .reporting-time-item {
                          width: 350px;
                          line-height: 250px;
                        }

                        .event-description-item {
                          display: block;
                          margin: 30px 0 30px 50px;
                          // padding: 30px 0;
                          width: 555px;
                          height: 190px;
                          overflow: hidden;
                          -webkit-line-clamp: 4;
                          text-overflow: ellipsis;
                          display: -webkit-box;
                          -webkit-box-orient: vertical;
                        }

                        .case-progress-item {
                          margin: 90px 0 0 60px;
                          width: 184px;
                          height: 64px;
                          line-height: 64px;
                          background: #1D736E;
                          border: 4px solid #00F9EC;
                          border-radius: 10px;
                          text-align: center;
                        }

                        // 已完成\未开始\进行中
                        .completed {
                          background: #379434;
                          border: 4px solid #6DFF68;
                        }

                        .not-started {
                          background: #A06F31;
                          border: 4px solid #FFAF48;
                        }

                        .processing {
                          background: #1D736E;
                          border: 4px solid #00F9EC;
                        }

                      }

                      .incident-reporting-table-detail-item:last-child {
                        margin-bottom: 10px;
                        // border-bottom: none;
                      }

                    }

                    // 隐藏滚动条
                    .incident-reporting-table-detail-box::-webkit-scrollbar {
                      display: none;
                    }
                  }
                }

                // 监控画面
                .monitoring-screen-box {
                  margin-left: 60px;
                  width: 624px;
                  height: 1669px;

                  // title
                  .monitoring-screen-title {
                    position: relative;
                    width: 100%;
                    height: 197px;
                    line-height: 197px;

                    .ring {
                      display: inline-block;
                      width: 36px;
                      height: 36px;
                      border: 8px solid #28DFAE;
                      border-radius: 50%;
                      margin: auto;
                    }

                    .chart-title {
                      margin-left: 27px;
                      font-size: 48px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                    }

                    // 查看更多
                    .seeMore {
                      position: absolute;
                      top: 78px;
                      right: 0;
                      display: inline-block;
                      width: 142px;
                      height: 46px;
                      text-align: center;
                      background: #1C1C36;
                      border: 2px solid #DFDFFF;
                      border-radius: 20px;
                      font-size: 18px;
                      font-family: Source Han Sans CN;
                      font-weight: 400;
                      color: #FFFFFF;
                      line-height: 42px;
                      cursor: pointer;
                    }
                  }

                  // 图表
                  .monitoring-screen-img {
                    width: 100%;
                    height: 1472px;

                    div {
                      margin-bottom: 35px;
                      width: 624px;
                      height: 342px;
                      border: 4px solid #656594;
                      overflow: hidden;
                    }

                    div:last-child {
                      margin-bottom: 0;
                    }

                  }
                }
              }
            }
          }

          // 绿化林长制
          .gradedResponsible {
            width: 2410px;
            height: 2716px;
          }

          // 党建库-工青妇-社区党校活动-近期要闻
          .partyBuilding {
            width: 2410px;
            height: 2716px;
          }
        }

        // 右边背景边框
        .chart-right-border-bg {
          width: 78px !important;
          height: 3049px;
          background: url("../../../assets/screen_display_img/R_img_biankuang.png") no-repeat;
          background-size: contain;
        }

      }
    }
  }
}

// 标题右侧-三方框样式
.small-square-box {
  display: flex;
  flex-flow: row wrap;
  // justify-content: space-around;
  justify-content: flex-end;
  // align-items: stretch;
  position: absolute;
  top: 26px;
  right: 26px;
  width: 62px;
  height: 62px;

  span {
    display: block;
    margin: 10px 10px 0 0;
    width: 16px;
    height: 16px;
  }

  .square01 {
    background-color: #283498;
  }

  .square03 {
    margin-top: 0;
    background-color: #283498;
  }

  .square02 {
    background-color: #28dfae;
  }
}

// 垃圾分类排名三种变化情况： 上升 下降 不变
.rise {
  background-image: url('../../../assets/screen_display_img/icon_up.png');
}

.fall {
  background-image: url('../../../assets/screen_display_img/icon_down.png');
}

// .noChange {
//   background-image: url();
// }


#gather-num {
  font-size: 35px;
  font-weight: bold;
  color: #fff;
  // border-color: ;
}

// 跑马灯-关键帧
@keyframes roll {
  0% {
    transform: translateX(1500px);
  }

  100% {
    transform: translateX(-100%);
  }
}
