<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">石化街道城市运行管理平台</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="账号"
        >
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaOnOff">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <!--<el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>-->
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <p>网络行为规范</p>
      <p>1.严禁使用本系统传递涉密信息、内部文件。</p>
      <p>2.严禁在非涉密计算机上处理、存储涉密信息。</p>
      <p>3.严禁拍照涉密文件，严禁使用图文识别小程序扫描涉密文件。</p>
      <p>4.不得在网络上发布涉密信息或敏感信息。</p>
    </div>

    <el-dialog title="您的密码已长时间未修改，请修改密码" :visible.sync="loginInfo.constraint">
      <el-form ref="updatePwdForm" :model="updatePwdForm" :rules="updatePwdRules">
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="updatePwdForm.newPassword" autocomplete="off" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updatePwd">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getCodeImg, getInfo } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { updateUserPwd } from "@/api/system/user";


export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaOnOff: false,
      // 注册开关
      register: false,
      redirect: undefined,
      loginInfo:{
        constraint: false
      },
      updatePwdForm: {
        newPassword: '',
      },
      updatePwdRules: {
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          {
            pattern: /^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[_!@#$%^&*()-+]).{8,}$/,
            message: "密码中必须包含大小字母、数字、特殊字符，至少8个字符",
            trigger: "blur"
          }
        ],
      }
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    // this.getCode();
    this.getCookie();
  },
  methods: {

    getCode() {
      getCodeImg().then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    // handleLogin() {
    //   this.$refs.loginForm.validate(valid => {
    //     if (valid) {
    //       this.loading = true;
    //       if (this.loginForm.rememberMe) {
    //         Cookies.set("username", this.loginForm.username, { expires: 30 });
    //         Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
    //         Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
    //       } else {
    //         Cookies.remove("username");
    //         Cookies.remove("password");
    //         Cookies.remove('rememberMe');
    //       }
    //       this.$store.dispatch("Login", this.loginForm).then(() => {
    //         this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
    //       }).catch(() => {
    //         this.loading = false;
    //         if (this.captchaOnOff) {
    //           this.getCode();
    //         }
    //       });
    //     }
    //   });
    // }
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set("rememberMe", this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store.dispatch("Login", this.loginForm).then((res) => {
            getInfo().then(loginInfo => {
              this.loginInfo = loginInfo.user
              // 如果返回的constraint为false则正常跳转到主页面
              if(!this.loginInfo.constraint) {
                this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
              } else {
                // 否则设置constraint值为true，使页面不能跳转到主页面
                localStorage.setItem('constraint', true)
              }
            }).catch(() => {})
          })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    updatePwd(){
      this.$refs["updatePwdForm"].validate(valid => {
        if (valid) {
          updateUserPwd("", this.updatePwdForm.newPassword, true).then(response => {
            // 修改完成将constraint改为false
            localStorage.setItem('constraint', false)
            this.$modal.msgSuccess("修改成功");
            this.$router.push({ path: this.redirect || "/" }).catch(() => {});
          })
            .catch((e) => {
              console.info(e)
            });
        }
      });
    },
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  position: fixed;
  bottom: 20px;
  width: 100%;
  display: flex;              /* 让内部内容可以水平居中 */
  flex-direction: column;     /* 垂直堆叠每个 p */
  align-items: center;        /* 将每个 p 作为整体水平居中 */
  color: orange;
  font-family: Arial;
  font-weight: bold;
  font-size: 30px;
  letter-spacing: 1px;
}

.el-login-footer p {
  text-align: left;           /* p 内文字左对齐 */
  width: 100%;
  max-width: 1000px;           /* 可按需调整内容行宽 */
  margin: 0;
  padding: 0;            /* 两侧留白，避免贴边 */
  margin-left: 600px;
}
.login-code-img {
  height: 38px;
}
</style>
