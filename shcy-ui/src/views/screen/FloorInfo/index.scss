.screen-container {
  position: relative;
  width: 11520px;
  height: 3240px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  // 地图容器
  .map-container {
    margin: 0 auto;
    width: 6270px;
    height: 3240px;
    background-color: #030315;
  }

  // 上背景样式
  .background-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 11520px;
    height: 448px;
    background: url("../../../assets/screen_display_img/top_bg_yinying.png") no-repeat;
    background-size: contain;

    // 标题盒子
    .title-container {
      position: relative;
      margin: 0 auto;
      margin-top: 6px;
      width: 6012px;
      height: 429px;
      background: url("../../../assets/screen_display_img/top_bg_title.png") no-repeat;
      background-size: contain;


      .title-box {
        -moz-user-select: none;
        -khtml-user-select: none;
        user-select: none;
        text-align: center;


        .title-chinese {
          font-size: 140px;
          font-family: <PERSON><PERSON><PERSON> Sans GB;
          font-weight: 900;
          letter-spacing: 42px;
          color: #FEFEFE;
          background: linear-gradient(0deg, #A5C8FD 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .title-pinyin {
          font-size: 31px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #AFC5FD;
          text-transform: uppercase;
          letter-spacing: 20px;
        }

      }

    }
  }

  // 左边背景
  .background-left {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    width: 3945px;
    height: 3240px;
    background: url("../../../assets/screen_display_img/L_bg_yinying.png") no-repeat;
    background-size: contain;

    // 左边内容容器
    .chart-left-container {
      width: 2875px;
      height: 3240px;

      // 内容盒子
      .chart-left-box {
        display: flex;
        width: 2744px;
        height: 3049px;
        margin: 105px 0 86px 56px;

        // 边框样式
        .border-left-style {
          width: 78px;
          height: 3049px;
          background: url("../../../assets/screen_display_img/L_img_biankuuang.png") no-repeat;
          background-size: contain;
        }

        // 内容展示区
        .content-data-box {
          margin-left: 254px;
          width: 2410px;
          height: 100%;

          // 日期-时间容器
          .date-time-container {
            margin: 55px 0;
            width: 100%;
            height: 75px;

            span {
              font-size: 60px;
              font-weight: 500;
              font-family: Source Han Sans CN;
              color: #c0d1f7;
            }

            .date {
              margin-right: 120px;
            }

            .week {
              margin-right: 140px;
            }

            .time {
              margin-right: 160px;
            }

            .today-title {
              font-size: 36px;
              font-weight: 400;
              color: #b6b6bd;
              vertical-align: super;
            }
          }

          .data-change-box {
            // width: 2413px;
            // height: 2673px;
          }
        }
      }
    }
  }

  // 右边背景
  .background-right {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    width: 3945px;
    height: 3240px;
    background: url("../../../assets/screen_display_img/R_bg_yinying.png") no-repeat;
    background-size: contain;

    // 右边半环
    .ring-right-box {
      position: relative;
      width: 1070px;
      height: 3240px;
    }

    // 右边内容容器
    .chart-right-container {
      width: 2875px;
      height: 3240px;

      .chart-right-inner {
        display: flex;
        margin: 105px 56px 0 72px;
        width: 2734px;
        height: 3049px;

        // 右边内容
        .chart-right-content {
          margin-right: 246px;
          width: 2410px;
          height: 3049px;

          // 最新通知
          .latest-notice-box {
            display: flex;
            margin-bottom: 160px;
            width: 2410px;
            height: 173px;

            span {
              display: inline-block;
            }

            // 图标
            .latest-notice-icon {
              margin: 40px 22px 0 0;
              width: 80px;
              height: 93px;
              background: url("../../../assets/screen_display_img/R_icon_tongzhi.png") no-repeat;
              background-size: contain;
            }

            // "最新通知"字样
            .latest-notice-word {
              font-size: 60px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #C0D1F7;
              line-height: 173px;
            }

            // 通告内容
            .latest-notice-title {
              margin-right: 77px;
              font-size: 60px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #FFFFFF;
              line-height: 173px;
            }

            // 通告时间
            .latest-notice-time {
              margin: 78px 80px 0 0;
              font-size: 36px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #B6B6BD;
            }

            // 通告切换
            .latest-notice-change {
              padding: 50px 0;
              display: flex;
              flex-flow: row wrap;
              align-items: center;
              width: 16px;

              i {
                color: #fff;
                cursor: pointer;
                background-size: contain;
              }

              // change-up
              // .change-up {}

              // change-down
            }
          }
        }

        // 右边背景边框
        .chart-right-border-bg {
          width: 78px;
          height: 3049px;
          background: url("../../../assets/screen_display_img/R_img_biankuang.png") no-repeat;
          background-size: contain;
        }

      }


    }
  }

  // 房地二级
  .floor-content {
    position: absolute;
    top: 300px;
    left: 390px;
    // background-color: #fff;
    .back-box {
      margin-bottom: 60px;

      .backImg {
        cursor: pointer;
      }
    }

    .content-box {
      display: flex;

      .content-left {
        margin-right: 430px;
      }
    }
  }
}