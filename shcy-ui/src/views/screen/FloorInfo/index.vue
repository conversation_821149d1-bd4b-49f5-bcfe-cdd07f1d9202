<template>
  <div class="screen-container">
    <!-- 地图容器 -->
    <div class="map-container"></div>
    <!-- 上背景 -->
    <div class="background-top">
      <div class="title-container">
        <div class="title-box">
          <div class="title-chinese">石化街道城市运行管理平台</div>
          <div class="title-pinyin">
            shihua neighborhood City operation management platform
          </div>
        </div>
      </div>
    </div>
    <!-- 下背景 -->
    <div class="background-bottom"></div>
    <!-- 左背景 -->
    <div class="background-left">
      <div class="chart-left-container">
        <div class="chart-left-box">
          <!-- 边框背景 -->
          <div class="border-left-style"></div>
          <!-- 内容盒子 -->
          <div class="content-data-box">
            <!-- 日期时间容器 -->
            <div class="date-time-container">
              <span class="date">{{ dateYear }}</span>
              <span class="week">{{ dateWeek }}</span>
              <span class="time">{{ dateDay }}</span>
              <span class="weather">
                <span class="today-title">今日天气</span>
                {{ temperature }}°
                <span class="weather-icon"
                  ><i class="el-icon-cloudy-and-sunny"></i
                ></span>
                {{ weather }}
              </span>
            </div>
            <div class="data-change-box"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右背景 -->
    <div class="background-right">
      <!-- 右侧光环 -->
      <div class="ring-right-box"></div>
      <!-- 右侧内容区 -->
      <div class="chart-right-container">
        <div class="chart-right-inner">
          <!-- 右边内容 -->
          <div class="chart-right-content">
            <!-- 最新通知 -->
            <div class="latest-notice-box">
              <span class="latest-notice-icon"></span>
              <span class="latest-notice-word">最新通知：</span>
              <span class="latest-notice-title"
                >高温黄色预警！请外勤人员做好防暑降温工作。</span
              >
              <span class="latest-notice-time">2022/08/1 8:04:49</span>
              <span class="latest-notice-change">
                <i @click="changeUp()" class="change-up el-icon-caret-top"></i>
                <i
                  @click="changeDown()"
                  class="change-down el-icon-caret-bottom"
                ></i>
              </span>
            </div>
          </div>
          <!-- 右边背景边框 -->
          <div class="chart-right-border-bg"></div>
        </div>
      </div>
    </div>
    <!-- 房地二级 -->
    <div class="floor-content">
      <div class="back-box">
        <img
          @click="back()"
          class="backImg"
          width="310px"
          height="110px"
          src="https://pic.rmb.bdstatic.com/bjh/3b45c20b83dfa639ce76c987732f2fc4.png"
          alt=""
        />
      </div>
      <div class="content-box">
        <div class="content-left">
          <img
            width="3290px"
            height="2526px"
            src="https://pic.rmb.bdstatic.com/bjh/b1057ec8bd95b079bcf32be891cd1e71.png"
            alt=""
          />
        </div>
        <div class="content-right">
          <img
            width="7025px"
            height="2688px"
            src="https://pic.rmb.bdstatic.com/bjh/ee1a11e0dff26bda561eeb6d81513508.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
export default {
  name: "index",
  components: {},
  data() {
    return {
      // 定义的时间变量
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      weekday: [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ],
      timer: null,
      countToTimer: null,
      weather: null,
      temperature: null,
    };
  },
  computed: {
    // 表格数据轮播的默认项
    defaultOption() {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },

  mounted() {
    // 持续刷新当前时间
    this.timer = setInterval(() => {
      this.getDate();
    }, 1000);
    // 触发获取天气方法
    this.getWeather();

    this.moreDisplay = false;
  },
  methods: {
    // 获取当前时间
    getDate() {
      const date = this.$dayjs(new Date());
      this.dateDay = date.format("HH:mm:ss");
      this.dateYear = date.format("YYYY年MM月DD日");
      this.dateWeek = date.format(this.weekday[date.day()]);
      if (this.dateDay == "00:00:01") {
        this.getWeather();
      }
    },
    // 获取天气
    getWeather() {
      axios({
        method: "get",
        url: "https://devapi.qweather.com/v7/weather/now?key=d5038bc80c154f18b935e728e8fb1cbf&location=121.299950,30.732100",
      }).then(async (res) => {
        this.weather = res.data.now.text;
        this.temperature = res.data.now.temp;
      });
    },
    // 返回
    back() {
      this.$router.go(-1);
    },
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.countToTimer) {
      clearInterval(this.countToTimer);
    }
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
