<template>
  <div class="screen-container">
    <!-- 地图容器 -->
    <div class="map-container">
      <!-- 地图组件 -->
      <ScreenMapShow
        ref="child"
        @getDefaultVal="getDefaultVal"
        @getAllBtnVal="getAllBtnVal"
      />
    </div>
    <!-- 上背景 -->
    <div class="background-top">
      <div class="title-container">
        <div class="title-box">
          <div class="title-chinese">石化街道城市运行管理平台</div>
          <div class="title-pinyin">
            shihua neighborhood City operation management platform
          </div>
        </div>
        <div class="btn-container">
          <div
            v-for="(item, index) in topBtn"
            :key="item.number"
            :class="
              selTopIndex === index
                ? 'btn-top-item btn-top-active'
                : 'btn-top-item'
            "
            @click="btnTopClick(index)"
            :ref="'topBtn0' + index"
          >
            <span class="btn-icon"></span>
            <span class="btn-title" :ref="'topBtnTitle0' + index">{{
              item.title
            }}</span>
          </div>
        </div>
        <!-- 房地调查数据库的区域详情数据弹窗 -->
        <LandSurveyWindow />
        <!-- 事件上报情况弹窗 -->
        <transition name="el-zoom-in-center">
          <EventReportWindowVue
            v-show="isEventReportShow"
            @closeWindow="closeEventReportWindow"
            :eventReportData="eventReportData"
          />
        </transition>
      </div>
    </div>
    <!-- 下背景 -->
    <div class="background-bottom">
      <!-- 下方按钮-城市运行库 -->
      <div class="btn-bottom-container" v-show="btnBottomShow">
        <!-- 下方按钮盒子 -->
        <div class="btn-bottom-box">
          <!-- 按钮选项 -->
          <div
            v-for="(item, index) in bottomBtn"
            :key="item.id"
            :class="
              selBottomIndex === index
                ? 'btn-bottom-item active'
                : 'btn-bottom-item'
            "
            @click="btnBottomClick(index)"
          >
            {{ item.title }}
          </div>
        </div>

        <!-- 搜索框、下拉列表 -->
        <div class="search-option-container" v-if="false">
          <!-- 搜索框 -->
          <div class="search-box">
            <input
              class="input-search"
              type="search"
              placeholder="请输入区域"
            />
            <span class="input-search-icon" @click="isSearch()">
              <i class="search-icon"></i>
            </span>
          </div>
          <!-- 下拉框组件 -->
          <ScreenDisplaySelect
            class="sel-option-box"
            :selStyle="selStyle"
            :seldata="seldata"
            :selDefault="selDefault"
          />
        </div>
      </div>
      <!-- 下方按钮-党建库 -->
      <div class="btn-bottom-party-container" v-show="btnBottomPartyShow">
        <!-- 下方按钮盒子 -->
        <div class="btn-bottom-box">
          <!-- 按钮选项 -->
          <div
            v-for="item in bottomBtnParty"
            :key="item.id"
            class="btn-bottom-item"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <!-- 左背景 -->
    <div class="background-left">
      <div class="chart-left-container">
        <div class="chart-left-box">
          <!-- 边框背景 -->
          <div class="border-left-style"></div>
          <!-- 内容盒子 -->
          <div class="content-data-box">
            <!-- 日期时间容器 -->
            <div class="date-time-container">
              <span class="date">{{ dateYear }}</span>
              <span class="week">{{ dateWeek }}</span>
              <span class="time">{{ dateDay }}</span>
              <span class="weather">
                <span class="today-title">今日天气</span>
                {{ temperature }}°
                <span class="weather-icon"
                  ><i class="el-icon-cloudy-and-sunny"></i
                ></span>
                {{ weather }}
              </span>
            </div>
            <!-- 今日值班人员 -->
            <div class="beOnDuty">
              <span class="title"> 今日值班人员：</span>
              <span class="people">禹麦冬 沙俊郎 荀曼 雁雀雅</span>
            </div>

            <div class="data-change-box">
              <!-- 绿化情况容器 -->
              <div class="greening-situation-container" v-if="greenDataChange">
                <div class="greening-situation-box">
                  <!-- 绿化情况标题 -->
                  <div class="greening-situation-title">
                    <span class="greening-title">绿化情况</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>
                  <div class="greening-situation-content">
                    <!-- 绿化情况 -->
                    <div class="greening-left-data-box">
                      <div class="greening-inner-box">
                        <!-- 绿地总面积 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">绿地总面积</div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.greeningArea"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">km²</span>
                            </div>
                          </div>
                        </div>
                        <!-- 绿化覆盖率 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">绿化覆盖率</div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.coverage"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">%</span>
                            </div>
                          </div>
                        </div>
                        <!-- 每人公共绿地占有量 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">
                              每人公共绿地占有量
                            </div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.personOccupancy"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">m²/人</span>
                            </div>
                          </div>
                        </div>
                        <!-- 公共绿地面积率 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">
                              公共绿地面积率
                            </div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.publicGreenSpace"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">%</span>
                            </div>
                          </div>
                        </div>
                        <!-- 苗圃拥有量 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">苗圃拥有量</div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.nurseryOwnership"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">a/km²</span>
                            </div>
                          </div>
                        </div>
                        <!-- 每人树木占有量 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">
                              每人树木占有量
                            </div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.treeOccupancy"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">株/人</span>
                            </div>
                          </div>
                        </div>
                        <!-- 居住区绿地覆盖 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">
                              居住区绿地覆盖
                            </div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.liveCoverage"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">%</span>
                            </div>
                          </div>
                        </div>
                        <!-- 道路交通绿化覆盖 -->
                        <div class="greening-item">
                          <div class="greening-icon"></div>
                          <div class="greening-detail-data">
                            <div class="greening-detail-title">
                              道路交通绿化覆盖
                            </div>
                            <div class="greening-detail-number">
                              <span class="greening-num">
                                <count-to
                                  :start-val="0"
                                  :end-val="greeningInfo.greeningArea"
                                  :duration="3000"
                                  :decimals="2"
                                ></count-to>
                              </span>
                              <span class="greening-unit">%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 绿化图表 -->
                    <div class="greening-right-varieties-box">
                      <div class="greening-inner-top-box">
                        <span class="ring"></span>
                        <span class="chart-title">绿植品种</span>
                      </div>
                      <div class="greening-inner-center-box">
                        <BubbleChart :width="width" :height="height" />
                      </div>
                      <div class="greening-inner-bottom-box">
                        <div class="greening-num-icon"></div>
                        <div class="greening-num-data">
                          <div class="greening-num-title">2022年绿植数量</div>
                          <div class="greening-num-percentage">
                            <span class="greening-num">5.84</span>
                            <span class="percentage">%</span>
                            <span class="icon_up"></span>
                          </div>
                        </div>
                        <div class="greening-num-chart">
                          <ScreenLineChart
                            :width="lineWidth"
                            :height="lineHeight"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 市容情况容器 -->
              <div class="city-situation-container" v-if="greenDataChange">
                <div class="city-situation-box">
                  <!-- 市容情况标题 -->
                  <div class="city-situation-title">
                    <span class="city-title">市容情况</span>
                    <div class="small-square-box">
                      <span class="square01"></span>
                      <span class="square02"></span>
                      <span class="square03"></span>
                    </div>
                  </div>
                  <!-- 市容情况数据 -->
                  <div class="city-situation-content">
                    <div class="city-situation-inner">
                      <!-- 垃圾分类排名情况 -->
                      <div class="city-left-classify-box">
                        <!-- title -->
                        <div class="city-left-classify-top-box">
                          <span class="ring"></span>
                          <span class="chart-title">垃圾分类排名情况</span>
                        </div>
                        <!-- chart -->
                        <div class="city-left-classify-center-box">
                          <SeparationChart
                            :width="separationWidth"
                            :height="separationHeight"
                          />
                        </div>

                        <!-- table -->
                        <div class="city-left-classify-bottom-box">
                          <div
                            class="classify-ranking-table-item"
                            v-for="item in classifyRanking"
                            :key="item.name"
                          >
                            <span class="serial-number">{{ item.index }}.</span>
                            <span class="community-name">{{ item.name }}</span>
                            <span class="percentage">{{
                              item.percentage
                            }}</span>
                            <span
                              class="rise-and-fall"
                              :class="item.change"
                            ></span>
                          </div>
                        </div>
                      </div>
                      <!-- 垃圾分类事件处置 -->
                      <div class="city-right-event-box">
                        <!-- title -->
                        <div class="city-right-classify-top-box">
                          <span class="ring"></span>
                          <span class="chart-title">垃圾分类事件处置</span>
                        </div>
                        <!-- data-table -->
                        <div class="city-right-classify-bottom-box">
                          <div class="city-table-title">
                            <span class="housing-estate">小区</span>
                            <span class="garbage-room">垃圾房</span>
                            <span class="event">事件</span>
                            <span class="happen-time">最近发生时间</span>
                          </div>
                          <div class="city-table-detail-box">
                            <vue-seamless-scroll
                              :data="classifyEvent"
                              :class-option="defaultOption"
                            >
                              <div
                                class="city-table-detail-item"
                                v-for="(item, index) in classifyEvent"
                                :key="index"
                              >
                                <span class="housing-estate-item">{{
                                  item.housingEstate
                                }}</span>
                                <span class="garbage-room-item">{{
                                  item.garbageRoom
                                }}</span>
                                <span class="event-item">{{ item.event }}</span>
                                <span class="happen-time-item">{{
                                  item.happenTime
                                }}</span>
                              </div>
                            </vue-seamless-scroll>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 城市运行库---街道总览-人口概括-环境概括 -->
              <div class="dataOverview" v-show="dataOverviewChange"></div>
              <!-- 城市安全库---安全生产-平安态势 -->
              <div class="citySafety" v-show="citySafetyChange"></div>
              <!-- 人房库 -->
              <div class="peopleRoom" v-show="peopleRoomChange"></div>
              <!-- 民生服务库 -->
              <div class="liveService" v-show="liveServiceChange"></div>
              <!-- 党建库---党建情况-工青妇-社区党校活动 -->
              <div class="partyBuilding" v-show="partyBuildingChange"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="ring-left-box"></div>
    </div>
    <!-- 右背景 -->
    <div class="background-right">
      <!-- 右侧光环 -->
      <div class="ring-right-box"></div>
      <!-- 右侧内容区 -->
      <div class="chart-right-container">
        <div class="chart-right-inner">
          <!-- 右边内容 -->
          <div class="chart-right-content">
            <!-- 最新通知 -->
            <div class="latest-notice-box">
              <span class="latest-notice-icon"></span>
              <span class="latest-notice-word">最新通知：</span>
              <div class="latest-notice-title">
                <span class="notice-content">
                  林长制巡查：临蒙居委 - 临潮一村 - 街心花园公共绿地001
                  上报一条新事件。
                </span>
              </div>
              <span class="latest-notice-time">2022/08/5 22:04:49</span>
              <span class="latest-notice-change">
                <i @click="changeUp()" class="change-up el-icon-caret-top"></i>
                <i
                  @click="changeDown()"
                  class="change-down el-icon-caret-bottom"
                ></i>
              </span>
            </div>
            <!-- 网格化事件 -->
            <div class="gridding-events-box" v-show="griddingChange">
              <!-- 网格化事件-标题 -->
              <div class="gridding-events-title-box">
                <span class="gridding-events-title">网格化事件</span>
                <div class="small-square-box">
                  <span class="square01"></span>
                  <span class="square02"></span>
                  <span class="square03"></span>
                </div>
              </div>
              <!-- 网格化事件-内容 -->
              <div class="gridding-events-content-box">
                <!-- 上部分 -->
                <div class="gridding-events-top-box">
                  <!-- 案件核心指示 -->
                  <div class="case-core-box">
                    <!-- 标题-"案件核心指示" -->
                    <div class="case-core-title">
                      <span class="ring"></span>
                      <span class="chart-title">近一年网格案件趋势</span>
                    </div>
                    <!-- 图表 -->
                    <div class="case-core-chart">
                      <VerticalChart
                        :width="verticalWidth"
                        :height="verticalHeight"
                        v-if="false"
                      />
                    </div>
                  </div>
                  <!-- 案件类别 -->
                  <div class="case-category-box">
                    <!-- 标题-"案件类别" -->
                    <div class="case-category-title">
                      <span class="ring"></span>
                      <span class="chart-title">案件类别</span>
                    </div>
                    <!-- 图表 -->
                    <div class="case-category-chart">
                      <div class="case-category-chart-left" v-if="false">
                        <HistogramChart
                          :width="cateWidth"
                          :height="cateHeight"
                        />
                      </div>
                      <div class="case-category-chart-right" v-if="false">
                        <RoundedChart
                          :width="roundedWidth"
                          :height="roundedHeight"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 下部分 -->
                <div class="gridding-events-bottom-box">
                  <!-- 事件上报情况 -->
                  <div class="incident-reporting-box">
                    <!-- 标题-"事件上报情况" -->
                    <div class="incident-reporting-title">
                      <span class="ring"></span>
                      <span class="chart-title">事件上报情况</span>
                      <span class="seeMore">查看更多</span>
                    </div>
                    <!-- 图表 -->
                    <div class="incident-reporting-table">
                      <!-- 表头 -->
                      <div class="incident-reporting-table-title">
                        <span class="event-number">案件编号</span>
                        <span class="reporting-time">发现时间</span>
                        <span class="event-description">案件描述</span>
                        <span class="case-progress">案件状态</span>
                      </div>
                      <!-- 表格 -->
                      <div
                        class="incident-reporting-table-detail-box"
                        @click="eventReport($event)"
                      >
                        <vue-seamless-scroll
                          :data="eventReportInfo"
                          :class-option="defaultOption"
                        >
                          <div
                            class="incident-reporting-table-detail-item item"
                            v-for="(item, index) in eventReportInfo"
                            :key="index"
                            :data-obj="JSON.stringify(item)"
                            :id="index + 1"
                          >
                            <span class="event-number-item" :title="item.num">{{
                              item.num
                            }}</span>
                            <span class="reporting-time-item">{{
                              item.time
                            }}</span>
                            <span
                              class="event-description-item"
                              :title="item.description"
                              >{{ item.description }}</span
                            >
                            <span
                              class="case-progress-item"
                              :class="item.progress"
                              >{{ item.progress_title }}</span
                            >
                          </div>
                        </vue-seamless-scroll>
                      </div>
                    </div>
                  </div>
                  <!-- 监控画面 -->
                  <div class="monitoring-screen-box">
                    <!-- 标题-"监控画面" -->
                    <div class="monitoring-screen-title">
                      <span class="ring"></span>
                      <span class="chart-title">监控画面</span>
                      <span class="seeMore">查看更多</span>
                    </div>
                    <!-- 图 -->
                    <div class="monitoring-screen-img">
                      <div class="monitoring-screen-img-item01">
                        <img
                          src="https://pic.rmb.bdstatic.com/bjh/ba1068568081bb5981dd8b2453e97a9b.png"
                          alt=""
                        />
                      </div>
                      <div class="monitoring-screen-img-item02">
                        <img
                          src="https://pic.rmb.bdstatic.com/bjh/6a659194b048450798f9af0a9f4092d4.png"
                          alt=""
                        />
                      </div>
                      <div class="monitoring-screen-img-item03">
                        <img
                          src="https://pic.rmb.bdstatic.com/bjh/dad4a919f9c80f4c710a84a9f91a81ba.png"
                          alt=""
                        />
                      </div>
                      <div class="monitoring-screen-img-item04">
                        <img
                          src="https://pic.rmb.bdstatic.com/bjh/9b16651ae926e2c15fa648faa4a79b57.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 绿化林长制 -->
            <div class="gradedResponsible" v-show="gradedResponsibleChange">
              <img
                src="https://pic.rmb.bdstatic.com/bjh/4a9141240c0e0e2b303a7d73302260f3.png"
                width="2410px"
              />
            </div>
            <!-- 党建库 -->
            <div class="partyBuilding" v-show="partyBuildingChange">
              <img
                src="https://pic.rmb.bdstatic.com/bjh/2381de51cfeeadfbe8a1a33debe947cc.png"
                width="2410px"
              />
            </div>
          </div>
          <!-- 右边背景边框 -->
          <div class="chart-right-border-bg"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
// 绿植品种（气泡）chart
import BubbleChart from "../../dashboard/BubbleChart.vue";
// 绿植品种-2022年绿植数量-lineChart
import ScreenLineChart from "../../dashboard/ScreenLineChart.vue";
// 垃圾分类排名情况 chart
import SeparationChart from "../../dashboard/SeparationChart.vue";
// 案件核心指示 chart
import VerticalChart from "../../dashboard/VerticalChart.vue";
// 案件类别 （左）chart
import HistogramChart from "../../dashboard/HistogramChart.vue";
// 案件类别 （右）chart
import RoundedChart from "../../dashboard/RoundedChart.vue";
// 表格数据轮播
import vueSeamlessScroll from "vue-seamless-scroll";
// 下拉框组件
import ScreenDisplaySelect from "../../../components/ScreenDisplaySelect/index.vue";
// 地图组件
import ScreenMapShow from "../../../components/ScreenMapShow/index.vue";
// 数字翻牌器
import countTo from "vue-count-to";
// 房地调查数据库的区域详情数据弹窗
import LandSurveyWindow from "../../../components/ScreenWindowInfo/LandSurveyWindow.vue";
// 事件上报情况弹窗
import EventReportWindowVue from "../../../components/ScreenWindowInfo/EventReportWindow.vue";

export default {
  name: "index",
  components: {
    BubbleChart,
    ScreenLineChart,
    VerticalChart,
    HistogramChart,
    RoundedChart,
    SeparationChart,
    vueSeamlessScroll,
    ScreenDisplaySelect,
    ScreenMapShow,
    countTo,
    LandSurveyWindow,
    EventReportWindowVue,
  },
  data() {
    return {
      // 绿化情况-数据
      greeningData: [
        {
          // 绿地总面积
          greeningArea: 4.08,
          // 绿化覆盖率
          coverage: 37.69,
          // 每人公共绿地占有量
          personOccupancy: 0.27,
          // 公共绿地面积率
          publicGreenSpace: 21.51,
          // 苗圃拥有量
          nurseryOwnership: 4.08,
          // 每人树木占有量
          treeOccupancy: 2.52,
          // 居住区绿地覆盖
          liveCoverage: 28.88,
          // 道路交通绿化覆盖
          roadGreening: 12.08,
        },
        {
          greeningArea: 6.11,
          coverage: 22.53,
          personOccupancy: 2.37,
          publicGreenSpace: 11.91,
          nurseryOwnership: 6.78,
          treeOccupancy: 4.02,
          liveCoverage: 19.88,
          roadGreening: 22.28,
        },
        {
          greeningArea: 8.09,
          coverage: 32.77,
          personOccupancy: 2.97,
          publicGreenSpace: 19.91,
          nurseryOwnership: 3.04,
          treeOccupancy: 3.61,
          liveCoverage: 31.12,
          roadGreening: 10.14,
        },
      ],
      greeningInfo: {},

      // 定义的时间变量
      dateDay: null,
      dateYear: null,
      dateWeek: null,
      weekday: [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ],
      timer: null,
      countToTimer: null,
      weather: null,
      temperature: null,
      // 绿植品种chart的宽高
      width: "1000px",
      height: "670px",
      // 绿植品种line-chart的宽高
      lineWidth: "456px",
      lineHeight: "96px",
      // 案件核心指示vertical-chart的宽高
      verticalWidth: "100%",
      verticalHeight: "699px",
      // 案件类别histogram-chart的宽高
      cateWidth: "485px",
      cateHeight: "699px",
      // 案例类别 Rounded-chart的宽高
      roundedWidth: "599px",
      roundedHeight: "699px",
      // 垃圾分类排名chart宽高
      separationWidth: "750px",
      separationHeight: "170px",
      // 垃圾分类排名情况-数据
      classifyRanking: [
        {
          index: 1,
          name: "东礁新村第二社区",
          percentage: "89.19%",
          change: "rise",
        },
        {
          index: 2,
          name: "七村社区",
          percentage: "87.62%",
          change: "fall",
        },
        {
          index: 3,
          name: "临潮三村社区",
          percentage: "87.48%",
          change: "noChange",
        },
        {
          index: 4,
          name: "合生社区",
          percentage: "85.27%",
          change: "rise",
        },
        {
          index: 5,
          name: "滨海二村社区",
          percentage: "82.83%",
          change: "fall",
        },
        {
          index: 6,
          name: "合浦社区",
          percentage: "80.97%",
          change: "noChange",
        },
        {
          index: 7,
          name: "海棠社区",
          percentage: "79.92%",
          change: "noChange",
        },
        {
          index: 8,
          name: "山龙新村社区",
          percentage: "79.08%",
          change: "rise",
        },
        {
          index: 9,
          name: "山鑫阳光城社区",
          percentage: "78.13%",
          change: "rise",
        },
        {
          index: 10,
          name: "东礁新村第一社区",
          percentage: "77.33%",
          change: "fall",
        },
      ],
      // 垃圾分类事件处置-数据

      // 随意堆放在垃圾房门口
      // 干湿分类不完善
      // 堆放有害垃圾
      // 建筑垃圾随意堆放
      // 大件垃圾随意堆放

      classifyEvent: [
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "2号",
          event: "随意堆放在垃圾房门口",
          happenTime: "07-31 10:27",
        },
        {
          housingEstate: "滨海二村社区",
          garbageRoom: "2号",
          event: "干湿分类不完善",
          happenTime: "07-31 11:15",
        },
        {
          housingEstate: "临蒙社区",
          garbageRoom: "3号",
          event: "堆放有害垃圾",
          happenTime: "07-31 11:23",
        },
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "1号",
          event: "随意堆放在垃圾房门口",
          happenTime: "07-31 17:38",
        },
        {
          housingEstate: "九村社区",
          garbageRoom: "2号",
          event: "大件垃圾随意堆放",
          happenTime: "07-31 19:51",
        },
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "2号",
          event: "建筑垃圾随意堆放",
          happenTime: "07-31 21:27",
        },
        {
          housingEstate: "辰凯社区",
          garbageRoom: "1号",
          event: "随意堆放在垃圾房门口",
          happenTime: "08-01 8:09",
        },
        {
          housingEstate: "紫卫社区",
          garbageRoom: "4号",
          event: "干湿分类不完善",
          happenTime: "08-01 10:27",
        },
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "2号",
          event: "大件垃圾随意堆放",
          happenTime: "08-01 11:27",
        },
        {
          housingEstate: "合生社区",
          garbageRoom: "3号",
          event: "干湿分类不完善",
          happenTime: "08-01 11:42",
        },
        {
          housingEstate: "临潮三村社区",
          garbageRoom: "2号",
          event: "堆放有害垃圾",
          happenTime: "08-01 12:07",
        },
        {
          housingEstate: "山龙新村社区",
          garbageRoom: "1号",
          event: "大件垃圾随意堆放",
          happenTime: "08-01 12:12",
        },
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "2号",
          event: "随意堆放在垃圾房门口",
          happenTime: "08-01 12:23",
        },
        {
          housingEstate: "辰凯社区",
          garbageRoom: "1号",
          event: "大件垃圾随意堆放",
          happenTime: "08-01 14:09",
        },
        {
          housingEstate: "紫卫社区",
          garbageRoom: "4号",
          event: "干湿分类不完善",
          happenTime: "08-01 16:27",
        },
        {
          housingEstate: "辰凯社区",
          garbageRoom: "1号",
          event: "随意堆放在垃圾房门口",
          happenTime: "08-02 9:09",
        },
        {
          housingEstate: "紫卫社区",
          garbageRoom: "4号",
          event: "干湿分类不完善",
          happenTime: "08-02 10:27",
        },
        {
          housingEstate: "梅州新村社区",
          garbageRoom: "2号",
          event: "建筑垃圾随意堆放",
          happenTime: "08-02 11:27",
        },
        {
          housingEstate: "合生社区",
          garbageRoom: "3号",
          event: "堆放有害垃圾",
          happenTime: "08-02 11:42",
        },
        {
          housingEstate: "临潮三村社区",
          garbageRoom: "2号",
          event: "建筑垃圾随意堆放",
          happenTime: "08-02 14:27",
        },
      ],
      // 上部按钮-数据
      topBtn: [
        {
          title: "城市运行库",
          number: 1001,
        },
        {
          title: "城市安全库",
          number: 1002,
        },
        {
          title: "人房库",
          number: 1003,
        },
        {
          title: "民生服务库",
          number: 1004,
        },
        {
          title: "党建库",
          number: 1005,
        },
        {
          title: "基础库",
          number: 1006,
        },
      ],
      // 下部按钮-数据
      bottomBtn: [
        {
          id: "100001",
          title: "全部数据",
        },
        {
          id: "100002",
          title: "现状使用主体",
        },
        {
          id: "100003",
          title: "房地调查数据库",
        },
        {
          id: "100004",
          title: "绿化主体",
        },
        {
          id: "100005",
          title: "小区基础信息",
        },
        {
          id: "100006",
          title: "管网数据",
        },
      ],
      // 下部党建库按钮
      bottomBtnParty: [
        {
          title: "学习强国",
        },
        {
          title: "大事记",
        },
        {
          title: "特色品牌",
        },
        {
          title: "培训管理",
        },
        {
          title: "组织架构",
        },
        {
          title: "党费管理",
        },
      ],
      // 下部按钮-事件处理相关值
      selBottomIndex: -1,
      // 上部按钮-事件处理相关值
      selTopIndex: 0,
      // 输入框双向绑定数据
      inp_value: "请输入区域",
      // 下拉框-position样式
      selStyle: {
        left: "-4px",
        bottom: "116px",
      },
      // 下拉框数据
      seldata: [
        "全部",
        "临潮一村",
        "临潮二村（功能待开放）",
        "临潮三村（功能待开放）",
      ],
      selDefault: "请选择区域",
      // 事件上报情况
      eventReportInfo: [
        {
          // 案件阶段
          stage: "结案",
          // 案件状态
          state: "已结案",
          // 案件编号
          num: "2207151601000428",
          // 案件大类
          majorCategory: "联勤联动",
          // 案件小类
          minorCategory: "联勤联动事项",
          // 案件子类
          subCategory: "“五违”整治",
          // 所属网格
          belongGrid: "16170",
          // 发生地址
          address: "石化二村257号(石化二村257号)",
          // 案件描述
          description:
            "城运联勤工作站开展综合检查，对新开业三产服务业提前指导和服务，告知环境保护、市容环境、街面秩序等要求，并结合疫情防控要求，开展检查指导。",
          // 上报人
          reportedBy: "盛海春",
          // 发现事件
          time: "2022/7/15 14:38:37",
          // 处置街道
          disposalStreet: "石化街道",
          // 处置部门
          disposalDepartment: "石化街道社区管理办",
          // 处置人
          disposer: "盛海春",
          // 处置时间
          disposalTime: "2022/7/21 9:23:45",
          // 结案结果
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
        {
          stage: "结案",
          state: "已结案",
          num: "2207111601000452",
          majorCategory: "联勤联动",
          minorCategory: "联勤联动事项",
          subCategory: "环境保护",
          belongGrid: "16180",
          address: "隆安路 234号 果木烤鸭",
          description:
            "接到海棠居委会反馈，隆安路234号烧烤店油烟扰民、跨门经营。工作站组织管理办、城管于夜间19点开展联合检查，责令商铺整改烟道，进一步优化排烟净化措施，并加强市容环境门前三包管理。",
          reportedBy: "盛海春",
          time: "2022/7/11 19:10:05",
          disposalStreet: "石化街道",
          disposalDepartment: "石化街道社区管理办",
          disposer: "盛海春",
          disposalTime: "2022/7/20 19:09:52",
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
        {
          stage: "结案",
          state: "已结案",
          num: "2207041601000341",
          majorCategory: "联勤联动",
          minorCategory: "联勤联动事项",
          subCategory: "环境保护",
          belongGrid: "16172",
          address: "板桥西路 482号",
          description:
            "城运联勤工作站开展综合检查，对新开业三产服务业提前指导和服务，告知环境保护、市容环境、街面秩序等要求，并结合疫情防控要求，开展检查指导。",
          reportedBy: "盛海春",
          time: "2022/7/4 14:36:58",
          disposalStreet: "石化街道",
          disposalDepartment: "石化街道社区管理办",
          disposer: "盛海春",
          disposalTime: "2022/7/4 16:34:49",
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
        {
          stage: "结案",
          state: "已结案",
          num: "2207151601000428",
          majorCategory: "联勤联动",
          minorCategory: "联勤联动事项",
          subCategory: "环境保护",
          belongGrid: "16180",
          address: "982号(石化二村258号)",
          description:
            "城运联勤工作站开展综合检查，对新开业三产服务业提前指导和服务，告知环境保护、市容环境、街面秩序等要求，并结合疫情防控要求，开展检查指导。",
          reportedBy: "盛海春",
          time: "2022/6/28 13:53:38",
          disposalStreet: "石化街道",
          disposalDepartment: "石化街道社区管理办",
          disposer: "盛海春",
          disposalTime: "2022/6/28 14:01:46",
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
        {
          stage: "结案",
          state: "已结案",
          num: "2206141601000203",
          majorCategory: "联勤联动",
          minorCategory: "联勤联动事项",
          subCategory: "环境保护",
          belongGrid: "16172",
          address: "卫零路 386-1",
          description:
            "与城管、管理办开展联勤联动检查，对多家新开业餐饮提前指导和服务，告知环境保护、市容环境、街面秩序等要求",
          reportedBy: "丁立伟",
          time: "2022/6/14 15:10:41",
          disposalStreet: "石化街道",
          disposalDepartment: "石化街道城运中心（网格化）",
          disposer: "丁立伟",
          disposalTime: "2022/6/14 15:22:07",
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
        {
          stage: "结案",
          state: "已结案",
          num: "2202231601000251",
          majorCategory: "联勤联动",
          minorCategory: "联勤联动事项",
          subCategory: "市容环卫",
          belongGrid: "16180",
          address: "石化街道柳城路138号(柳城路138号)",
          description:
            "柳城路沿线餐饮专项整治，召开现场会要求经营户落实垃圾分类主体责任，签订餐厨垃圾清运协议，设置垃圾收集容器。同时撤除原安放在公共区域的垃圾桶，清理长期受油腻污染的公共设施",
          reportedBy: "赵冬辉",
          time: "2022/2/23 13:02:55",
          disposalStreet: "石化街道",
          disposalDepartment: "城运联勤工作站（象州）",
          disposer: "赵冬辉",
          disposalTime: "2022/2/23 15:15:33",
          caseClosingResult: "解决",
          progress: "completed",
          progress_title: "已结案",
        },
      ],
      eventReportData: {
        stage: "结案",
        state: "已结案",
        num: "2207111601000452",
        majorCategory: "联勤联动",
        minorCategory: "联勤联动事项",
        subCategory: "环境保护",
        belongGrid: "16180",
        address: "隆安路 234号 果木烤鸭",
        description:
          "接到海棠居委会反馈，隆安路234号烧烤店油烟扰民、跨门经营。工作站组织管理办、城管于夜间19点开展联合检查，责令商铺整改烟道，进一步优化排烟净化措施，并加强市容环境门前三包管理。",
        reportedBy: "盛海春",
        time: "2022/7/11 19:10:05",
        disposalStreet: "石化街道",
        disposalDepartment: "石化街道社区管理办",
        disposer: "盛海春",
        disposalTime: "2022/7/20 19:09:52",
        caseClosingResult: "解决",
        progress: "completed",
        progress_title: "已结案",
      },

      // 基础信息弹窗是否显示
      isBasicInfoShow: false,
      // 房地调查-查看更多
      // moreDisplay: null,

      // 是否关闭事件上报窗口
      isEventReportShow: false,

      // 左边切换为绿化页面
      greenDataChange: false,
      // 左边切换为-城市运行库内容
      dataOverviewChange: true,
      // 左边是否切换为-城市安全库内容
      citySafetyChange: false,
      // 左边是否切换为-人房库内容
      peopleRoomChange: false,
      // 左边是否切换为-民生服务库
      liveServiceChange: false,
      // 左边是否切换为-党建库内容
      partyBuildingChange: false,

      // 右侧是否切换为-绿化林长制
      gradedResponsibleChange: false,
      // 右侧是否切换为-网格化事件
      griddingChange: true,

      // 下方按钮是否显示
      btnBottomShow: true,
      // 下方“党建库”按钮显示
      btnBottomPartyShow: false,
    };
  },
  computed: {
    // 表格数据轮播的默认项
    defaultOption() {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 3, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  beforeRouteEnter(to, from, next) {
    if (from.name == "FloorInfo") {
      next((vm) => {
        setTimeout(() => {
          vm.btnTopClick(1);
          vm.btnBottomClick(2);
        }, 0);
      });
    } else {
      next();
    }
  },
  mounted() {
    // console.log(this.moreDisplay);
    // 触发获取天气方法
    this.getWeather();

    this.greeningInfo = this.greeningData[0];
    // 每6s触发一次 数字翻牌器
    let i = 0;
    this.countToTimer = setInterval(() => {
      if (i >= this.greeningData.length) {
        i = 0;
      }
      this.greeningInfo = this.greeningData[i];
      i++;
    }, 6000);

    // 持续刷新当前时间
    this.timer = setInterval(() => {
      this.getDate();
    }, 1000);

    this.moreDisplay = false;
    // “党建库”默认选中状态
    // this.$nextTick(() => {
    //   this.$refs.topBtn0[4].classList = "btn-top-item party-logo";
    // });
    // setTimeout(() => {
    //   this.btnTopClick(0);
    // }, 0)
    // this.btnTopClick(0);
  },
  methods: {
    // 获取当前时间
    getDate() {
      const date = this.$dayjs(new Date());
      this.dateDay = date.format("HH:mm:ss");
      this.dateYear = date.format("YYYY年MM月DD日");
      this.dateWeek = date.format(this.weekday[date.day()]);
      if (this.dateDay == "00:00:01") {
        this.getWeather();
      }
    },
    // 获取天气
    getWeather() {
      axios({
        method: "get",
        url: "https://devapi.qweather.com/v7/weather/now?key=d5038bc80c154f18b935e728e8fb1cbf&location=121.299950,30.732100",
      }).then(async (res) => {
        this.weather = res.data.now.text;
        this.temperature = res.data.now.temp;
      });
    },
    // 下方按钮 事件处理
    btnBottomClick(index) {
      this.selBottomIndex = index;
      // 小区基础信息弹窗
      if (index == 4) {
        this.isBasicInfoShow = true;
      } else {
        this.isBasicInfoShow = false;
      }
      // 绿化主体更改左边内容
      if (index == 3) {
        // 左侧-绿化内容
        this.greenDataChange = true;
        // 左侧-城市运行库内容
        this.dataOverviewChange = false;
        // 左侧-城市安全库内容
        this.citySafetyChange = false;
        // 右侧-网格事件
        this.griddingChange = false;
        // 右侧-林长制
        this.gradedResponsibleChange = true;
      } else {
        this.greenDataChange = false;
        this.dataOverviewChange = true;
        this.citySafetyChange = false;
        this.griddingChange = true;
        this.gradedResponsibleChange = false;
      }

      // 地图组件中的-获取按钮数据的函数
      this.$refs.child.getParentBtnTitle(this.bottomBtn[index].title);
    },
    // 点击搜索按钮
    isSearch() {
      console.log("点击了搜索");
    },
    // 切换到上一个通告
    changeUp() {
      console.log("切换到上一个通告");
    },
    // 切换到下一个通告
    changeDown() {
      console.log("切换到下一个通告");
    },
    // 事件上报情况
    eventReport(e) {
      // e.path[1]
      let target;
      for (let i = 0; i < e.path.length; i++) {
        if (
          e.path[i].className === "incident-reporting-table-detail-item item"
        ) {
          target = e.path[i];
          break;
        }
      }
      // let index = target.id;
      this.eventReportData = JSON.parse(target.dataset.obj);
      this.isEventReportShow = true;
    },
    // 关闭事件上报窗口
    closeEventReportWindow(value) {
      this.isEventReportShow = value;
    },
    // 上部按钮的点击事件
    btnTopClick(index) {
      this.selTopIndex = index;
      // 下方按钮恢复默认不选中状态
      this.getDefaultVal(-1);

      // 右侧-网格事件
      // this.griddingChange = true;
      if (index == 4) {
        this.griddingChange = false;
      } else {
        this.griddingChange = true;
      }
      // 右侧-林长制
      this.gradedResponsibleChange = false;

      // 每个上部按钮的事件处理（---待封装---）
      if (index == 0) {
        // 点击-城市运行库
        // 绿化
        this.greenDataChange = false;
        // 城市运行库
        this.dataOverviewChange = true;
        // 城市安全裤
        this.citySafetyChange = false;
        // 人房库
        this.peopleRoomChange = false;
        // 民生服务库
        this.liveServiceChange = false;
        // 党建库
        this.partyBuildingChange = false;
        // 下方“城市运行库”按钮显示
        this.btnBottomShow = true;
        // 下方“党建库”按钮显示
        this.btnBottomPartyShow = false;
        // 触发还原按钮
        this.$refs.child.winRestore();
      } else if (index == 1) {
        // 点击-城市安全库
        this.greenDataChange = false;
        this.dataOverviewChange = false;
        this.citySafetyChange = true;
        this.peopleRoomChange = false;
        this.liveServiceChange = false;
        this.partyBuildingChange = false;
        this.btnBottomShow = false;
        this.btnBottomPartyShow = false;
        this.$refs.child.winRestore();
      } else if (index == 2) {
        // 点击-人房库
        this.greenDataChange = false;
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.peopleRoomChange = true;
        this.liveServiceChange = false;
        this.partyBuildingChange = false;
        this.btnBottomShow = false;
        this.btnBottomPartyShow = false;
        this.$refs.child.winRestore();
      } else if (index == 3) {
        // 点击-民生服务库
        this.greenDataChange = false;
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.peopleRoomChange = false;
        this.liveServiceChange = true;
        this.partyBuildingChange = false;
        this.btnBottomShow = false;
        this.btnBottomPartyShow = false;
        this.$refs.child.winRestore();
      } else if (index == 4) {
        // 点击-党建库
        // 改变"党建库"的按钮选中样式
        this.$nextTick(() => {
          this.$refs.topBtn04[0].classList = "btn-top-item party-logo";
          // console.log(this.$refs.topBtn04);
        });
        this.greenDataChange = false;
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.peopleRoomChange = false;
        this.liveServiceChange = false;
        this.partyBuildingChange = true;
        this.btnBottomShow = false;
        this.btnBottomPartyShow = true;
        this.griddingChange = false;
        // 点击“党建库”后的地图事件
        // setTimeout(() => {
        this.$refs.child.partyBuildingClick();
        // }, 500);
      } else if(index == 5){
        this.greenDataChange = false;
        this.dataOverviewChange = false;
        this.citySafetyChange = false;
        this.peopleRoomChange = false;
        this.liveServiceChange = false;
        this.partyBuildingChange = false;
        this.btnBottomShow = false;
        this.btnBottomPartyShow = false;
        this.griddingChange = false;
      }
    },
    // 获取默认值-1，是下方按钮不高亮
    getDefaultVal(val) {
      this.selBottomIndex = val;
    },
    // 点击临蒙居委后，默认选择“全部数据”按钮
    getAllBtnVal(val) {
      this.selBottomIndex = val;
    },
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.countToTimer) {
      clearInterval(this.countToTimer);
    }
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
