<template>
  <div id="viewDiv"></div>
</template>

<script>
import {loadModules} from 'esri-loader'
import {parseCoordinate, formatCoordinate} from "@/utils/coordinate"
import axios from "axios"

export default {
  name: "MapDrawEdit",
  props: {
    type: {
      type: String,
      default: ""
    },
    coordinate: {
      type: String,
      default: ""
    },
    mapName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      form: {},
      centerX: -12954,
      centerY: -56261,
      map: null,
    }
  },
  methods: {
    _initMap() {
      axios.get("http://**************/arcgis/api/auth/get_token?user_key=PacaHWGx6awBNifTckMxrwXkd44nMF66").then(response => {
        const jwt = response.data.data
        let options = {
          url: 'http://**************/arcgis9080/arcgis_js_api/javascript/4.21/init.js',
          css: 'http://**************/arcgis9080/arcgis_js_api/javascript/4.21/esri/themes/light/main.css'
        }
        loadModules([
            "esri/Map",
            "esri/views/MapView",
            "esri/layers/TileLayer",
            "esri/layers/FeatureLayer",
            "esri/widgets/Sketch",
            "esri/Graphic",
            "esri/layers/GraphicsLayer",
            "esri/geometry/Point",
            "esri/geometry/Polyline",
            "esri/geometry/Polygon",
            "esri/geometry/SpatialReference",
          ],
          options).then(([
                           Map,
                           MapView,
                           TileLayer,
                           FeatureLayer,
                           Sketch,
                           Graphic,
                           GraphicsLayer,
                           Point,
                           Polyline,
                           Polygon,
                           SpatialReference,
                         ]) => {
          const layer = new TileLayer({
            url: "http://**************/arcgis9080/ags_tile_svc/basemap_zw/MapServer",
            customParameters: {
              jwt: jwt,
            }
          });
          this.map = new Map();
          this.map.add(layer)

          if (this.type === 'point' && this.coordinate) {
            const coordinate = this.coordinate.split(",")
            this.centerX = coordinate[0]
            this.centerY = coordinate[1]
          }

          const view = new MapView({
            map: this.map,
            zoom: 7,
            center: new Point({
              x: this.centerX,
              y: this.centerY,
              spatialReference: 3857,
            }),
            container: "viewDiv"
          });

          // 石化街道轮廓图
          const statesRenderer = {
            type: "simple",
            symbol: {
              type: "simple-fill",
              color: [0, 0, 0, 0],
              outline: {
                color: [255, 181, 39, 0.7],
                width: 2
              }
            }
          };
          const shjdFeatureLayer = new FeatureLayer({
            url:
              "http://**************/server/geoscene/rest/services/bianjie/FeatureServer/0",
            renderer: statesRenderer,
          });
          this.map.add(shjdFeatureLayer);

          // 门牌号图层
          const doorplateFeatureLayer = new FeatureLayer({
            url:
              "http://**************/server/geoscene/rest/services/cad/FeatureServer/2",
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "simple-marker",
                style: "circle",
                color: [255, 0, 0, 0],
                size: "0px",
                outline: {
                  color: [255, 0, 0, 0],
                  width: 0
                }
              }
            },
            popupTemplate: {
              title: "text",
              content: [{
                type: "fields",
                fieldInfos: [
                  {
                    fieldName: "text",
                    label: "text"
                  },
                  {
                    fieldName: "layer",
                    label: "layer"
                  },
                ]
              }]
            },
            labelingInfo: [{
              symbol: {
                type: "text",
                horizontalAlignment: "left",
                color: "#363636",
                font: {
                  size: 8,
                }
              },
              labelPlacement: "center-center",
              labelExpressionInfo: {
                expression: "$feature.text"
              }
            }]
          });
          doorplateFeatureLayer.definitionExpression = "layer = 'Doorplate'";
          if (this.mapName === 'cameras') {
            this.map.add(doorplateFeatureLayer)
          }
          // 门牌号图层 end

          const graphicsLayer = new GraphicsLayer();
          this.map.add(graphicsLayer)

          const sketch = new Sketch({
            layer: graphicsLayer,
            view: view,
            creationMode: "update"
          });
          let _this = this;
          sketch.on("create", function (event) {
            if (event.state === "complete") {
              if (event.graphic.geometry.type === "point") {
                _this.form.type = event.graphic.geometry.type
                _this.form.coordinate = event.graphic.geometry.x + "," + event.graphic.geometry.y
                _this.$emit('submitMapForm', _this.form)
              } else if (event.graphic.geometry.type === "polyline") {
                _this.form.type = event.graphic.geometry.type
                _this.form.coordinate = formatCoordinate(event.graphic.geometry.paths[0])
                _this.$emit('submitMapForm', _this.form)
              } else if (event.graphic.geometry.type === "polygon") {
                _this.form.type = event.graphic.geometry.type
                _this.form.coordinate = formatCoordinate(event.graphic.geometry.rings[0])
                _this.$emit('submitMapForm', _this.form)
              }
            }
          });
          view.ui.add(sketch, "top-right");

          sketch.on("update", function (event) {
            if (event.state === "complete") {
              if (event.graphics[0].geometry.type === "point") {
                _this.form.type = event.graphics[0].geometry.type
                _this.form.coordinate = event.graphics[0].geometry.x + "," + event.graphics[0].geometry.y
                _this.$emit('submitMapForm', _this.form)
              } else if (event.graphics[0].geometry.type === "polyline") {
                _this.form.type = event.graphics[0].geometry.type
                _this.form.coordinate = formatCoordinate(event.graphics[0].geometry.paths[0])
                _this.$emit('submitMapForm', _this.form)
              } else if (event.graphics[0].geometry.type === "polygon") {
                _this.form.type = event.graphics[0].geometry.type
                _this.form.coordinate = formatCoordinate(event.graphics[0].geometry.rings[0])
                _this.$emit('submitMapForm', _this.form)
              }
            }
          })

          // draw
          let coordinate = this.coordinate;
          if (this.type === "point") {
            if (coordinate) {
              let pointSymbol = {
                type: "picture-marker",
                url: "http://**************/images/icon/icon_mark.png",
                width: "21px",
                height: "33px"
              };

              let arr = coordinate.split(",")
              const point = new Point({
                x: arr[0],
                y: arr[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const pointGraphic = new Graphic({
                geometry: point,
                symbol: pointSymbol
              });
              graphicsLayer.add(pointGraphic);
            }
          } else if (this.type === "polyline") {
            const polylineSymbol = {
              type: "simple-line",
              color: [226, 119, 40],
              width: 2
            }
            const p = parseCoordinate(coordinate)
            const polyline = new Polyline({
              paths: p,
              spatialReference: SpatialReference.WebMercator,
            });
            const polylineGraphics = new Graphic({
              geometry: polyline,
              symbol: polylineSymbol
            })
            graphicsLayer.add(polylineGraphics)
          } else if (this.type === "polygon") {
            const polygonSimple = {
              type: "simple-fill",
              color: [227, 139, 79, 0.8],
              outline: {
                color: [255, 255, 255],
                width: 1
              }
            }
            const p = parseCoordinate(coordinate)
            const polygon = new Polygon({
              rings: p,
              spatialReference: SpatialReference.WebMercator,
            });
            const polygonGraphics = new Graphic({
              geometry: polygon,
              symbol: polygonSimple
            })
            graphicsLayer.add(polygonGraphics)
          }
        })
      })
    },
  },
  beforeDestroy() {
    this.map.removeAll()
  },
}
</script>

<style lang="scss" scoped>
#viewDiv {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 80vh;
}
</style>
