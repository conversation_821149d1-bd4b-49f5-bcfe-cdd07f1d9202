<template>
  <div class="hls-player">
    <video ref="hlsVideo" width="100%" controls preload="auto" autoplay="autoplay" muted></video>
  </div>
</template>
<script>
export default {
  name: "HLSPlayer",
  props: {
    videoUrl: {
      type: String,
      required: true
    },
    timeout: {
      type: Number,
      default: 2000
    }
  },
  data() {
    return {
      video: null,
      hlsPlayer: null
    };
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.playVideo();
      }, this.timeout);
    })
  },
  watch: {
    videoUrl() {
      this.playVideo();
    }
  },
  methods: {
    playVideo() {
      this.video =  this.$refs.hlsVideo;
      this.hlsPlayer = new Hls();
      this.hlsPlayer.loadSource(decodeURIComponent(this.videoUrl));
      this.hlsPlayer.attachMedia(this.video);
      this.hlsPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
        this.video.play();
      });
    }
  },
  beforeDestroy() {
    this.video.pause()
    this.hlsPlayer.destroy()
  }
};
</script>
<style lang="scss" scoped>
.hls-player {
  width: 100%;
  //position: relative;
}
::v-deep #dhHlsVideoWrapper {
  height: auto !important;
}

::v-deep #dhHlsVdieoCloseBtn {
  display: none !important;
}
</style>
