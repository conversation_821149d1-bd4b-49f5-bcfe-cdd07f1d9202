<template>
  <div :id="id" :style="{ width: width, height: height }"></div>
</template>

<script>
import {getHikPreviewUrlWs} from "@/api/shcy/screen";

const IS_MOVE_DEVICE = document.body.clientWidth < 992 // 是否移动设备
const MSE_IS_SUPPORT = !!window.MediaSource // 是否支持mse

export default {
  name: "H5Player",
  props: {
    videoUrl: {
      type: String,
      required: true
    },
    cameraIndexCode: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    id: {
      type: String,
      default: "player",
    },
  },
  data() {
    return {
      isMoveDevice: IS_MOVE_DEVICE,
      player: null,
      splitNum: IS_MOVE_DEVICE ? 1 : 2,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? 'mse' : 'decoder',
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      urls: {
        realplay: this.videoUrl,
      },
      muted: true,
    }
  },
  mounted() {
    this.$el.style.setProperty('display', 'block')
    this.init()
    this.createPlayer()
    this.realplay()
  },
  computed: {
    mode: function() {
      return this.tabActive === 'mse' ? 0 : 1
    }
  },
  methods: {

    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener('resize', () => {
        this.player.JS_Resize()
      })
    },
    createPlayer() {
      this.player = new window.JSPlugin({
        szId: this.id,
        szBasePath: "./",
        iMaxSplit: 1,
        iCurrentSplit: IS_MOVE_DEVICE ? 1 : 2,
        openDebug: true,
        oStyle: {
          // borderSelect: IS_MOVE_DEVICE ? '#000' : '#FFCC00',
          borderSelect: '',
        }
      })
      let that = this
      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
          console.log('windowSelect callback: ', iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
          console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
          getHikPreviewUrlWs(that.cameraIndexCode).then(res => {
            that.urls = {
              realplay: res.data,
            }
            that.realplay()
          })
        },
        windowEventOver: function (iWndIndex) {  //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {  //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {  //全屏切换回调
          console.log('fullScreen callback: ', bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
          console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {  //性能不足回调
          console.log('performanceLack callback: ');
        }
      });
    },
    /* 预览&对讲 */
    realplay() {
      let { player, mode, urls } = this,
        index = player.currentWindowIndex,
        playURL = urls.realplay

      player.JS_Play(playURL, { playURL, mode }, index).then(
        () => { console.log('realplay success') },
        e => { console.error(e) }
      )
    },
    stopAllPlay() {
      this.player.JS_StopRealPlayAll().then(
          () => { console.log('stopAllPlay success') },
          e => { console.error(e) }
      )
    },

  },
  beforeDestroy() {
    this.stopAllPlay()
  }

}
</script>

<style scoped lang="scss">

</style>
