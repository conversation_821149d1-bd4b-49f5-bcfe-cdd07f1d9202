#container {
  position: relative;
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  border-radius: 433px;

  .amap-layer {
    position: fixed;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  // 小区基础信息弹窗
  .basicInfoWindow {
    width: 717px;
    height: 738px;
    background: url("https://pic.rmb.bdstatic.com/bjh/175a6805a353c9e1cb813fe307de9e83.png") no-repeat;
    background-size: contain;
    z-index: 9999;
  }

  // 临蒙居委会弹窗-截图
  .buildingNumWindow {
    width: 717px;
    height: 738px;
    background: url("https://pic.rmb.bdstatic.com/bjh/175a6805a353c9e1cb813fe307de9e83.png") no-repeat;
    background-size: contain;
    z-index: 9999;
  }

  // 地图操作浮窗
  .floatingWin {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
    font-size: 13px;
    line-height: 100px;
  }

  // 还原
  .floatingWin-restore {
    right: 250px;
    bottom: 745px;
    background: url("../../assets/screen_display_img/home_map_button_sx_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  .floatingWin-restore:active{
    background: url("../../assets/screen_display_img/home_map_button_sx_press.png") no-repeat;
    background-size: contain;
  }

  // 网络
  .floatingWin-patternSpot {
    right: 185px;
    bottom: 535px;
    background: url("../../assets/screen_display_img/home_map_button_jkgc_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  .floatingWin-patternSpot-press {
    right: 185px;
    bottom: 535px;
    background: url("../../assets/screen_display_img/home_map_button_jkgc_press.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  // 搜索按钮
  .floatingWin-search {
    right: 205px;
    bottom: 645px;
    border-radius: 50%;
    background: url("../../assets/screen_display_img/home_map_button_cz_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  .floatingWin-search-press {
    right: 205px;
    bottom: 645px;
    border-radius: 50%;
    background: url("../../assets/screen_display_img/home_map_button_cz_press.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  // 搜索框
  .search-container {
    // display: none;
    position: absolute;
    top: 336px;
    left: 600px;
    width: 900px;
    background: linear-gradient(209deg, #2D2D64, #030315);
    border: 2px solid #FFFFFF;
    border-radius: 3px;
    z-index: 9999;

    // 搜索框
    .search-input {
      padding: 23px 33px;
      width: 100%;
      outline: none;
      border: none;
      border-bottom: 2px solid #FFFFFF;
      background-color: transparent;
      font-size: 15px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #acacac;
    }

    // 搜索历史
    .search-history {
      padding: 0 33px 23px 33px;
      width: 100%;
      height: 363px;

      .search-title {
        display: flex;
        justify-content: space-between;
        font-size: 15px;
        color: #acacac;

        .clear {
          cursor: pointer;
        }
      }

      .search-keyword {
        display: inline-block;
        margin-right: 23px;
        margin-bottom: 23px;
        font-size: 15px;
        padding: 10px 16px;
        background-color: #343475;
        border-radius: 33px;
        color: #acacac;
        cursor: pointer;
      }

    }
  }



  // 图层切换
  .floatingWin-switch {
    right: 185px;
    bottom: 425px;
    background: url("../../assets/screen_display_img/home_button_shitu_stqh_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }
  .floatingWin-switch-press {
    right: 185px;
    bottom: 425px;
    background: url("../../assets/screen_display_img/home_button_shitu_stqh_press.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  // 疫情防控
  .floatingWin-illnessControl {
    right: 284px;
    bottom: 213px;
    background: url("https://s1.ax1x.com/2022/10/18/xrAIrn.png") no-repeat;
    background-size: contain;
  }

  // 标注示例
  .floatingWin-tips {
    right: 205px;
    bottom: 315px;
    background: url("../../assets/screen_display_img/home_map_button_bszs_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }
  .floatingWin-tips-press {
    right: 205px;
    bottom: 315px;
    background: url("../../assets/screen_display_img/home_map_button_bszs_press.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  /* 监控 */
  .floatingWin-surveillance {
    right: 250px;
    bottom: 215px;
    background: url("http://**************/images/icon/home_map_button_jk_normal.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }
  .floatingWin-surveillance-press {
    right: 250px;
    bottom: 215px;
    background: url("http://**************/images/icon/home_map_button_jk_press.png") no-repeat;
    background-size: contain;
    font-size: 10px;
    color:#ffffff;
    padding-top: 17px;
    text-shadow: 0px 2px 2px #0A1545;
  }

  // 小区基础信息中的icon按钮选项
  .iconSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 1730px;
    bottom: 360px;
    width: 2800px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .icon-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .icon {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #2BFBD9;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

  }

  // 绿化主题按钮选项
  .greeningSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 2630px;
    bottom: 360px;
    width: 1500px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .greening-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .greening {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #2BFBD9;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

    // .pipe-item:hover .pipe{
    //   background: #c7c7c6;
    // }
  }

  // 管网数据中的管网按钮选项
  .pipeSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 2630px;
    bottom: 360px;
    width: 1000px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .pipe-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .pipe {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #2BFBD9;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

    // .pipe-item:hover .pipe{
    //   background: #c7c7c6;
    // }
  }

  // 防汛防台按钮选项
  .fxftSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    bottom: 120px;
    width: 650px;
    height: 56px;
    background: #186FE8;
    border: 3px solid #2DC6ED;
    border-radius: 26px;
    z-index: 999;

    .fxft-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .fxft-check {
        margin-right: 6px;
        width: 17px;
        height: 17px;
        line-height: 56px;
        border: 3px solid #FFFFFF;
        background: #c7c7c6;
        border-radius: 50%;
        vertical-align: -3px;
      }

      .fxft-title {
        font-size: 16px;
        line-height: 56px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }
  }

  // 现状使用主体数据中的按钮选项
  .currentUserSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 1630px;
    bottom: 360px;
    width: 3000px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .currentUser-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .currentUser {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #c7c7c6;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

    // .pipe-item:hover .pipe{
    //   background: #c7c7c6;
    // }
  }



  ::v-deep .amap-marker-label {
    padding-bottom: 8px;
    font-size: 24px;
    color: #fff;
    border: 0;
    background-color: transparent;
  }

  /* 可以设置不同的进入和离开动画 */
  /* 设置持续时间和动画函数 */
  .slide-fade-enter-active {
    transition: all 0.5s ease;
  }

  .slide-fade-leave-active {
    transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter,
  .slide-fade-leave-to {
    // transform: translateX(260px);
    width: 0;
    opacity: 0;
  }
}
