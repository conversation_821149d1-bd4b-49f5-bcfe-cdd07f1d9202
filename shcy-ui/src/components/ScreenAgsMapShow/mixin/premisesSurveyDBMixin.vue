<template>

</template>

<script>
import {
  listScreenParcelInformation,
  listScreenFactoryWarehouse,
  listComprehensiveGrid
} from "@/api/shcy/screen";
import {loadModules} from "esri-loader";
import {parseCoordinate} from "@/utils/coordinate";

// template
const premisesSurveyTemplate = {
  title: "房地信息",
  content: [{
    type: "fields",
    fieldInfos: [
      // {
      //   fieldName: "FID",
      //   label: "FID"
      // },
      // {
      //   fieldName: "SHAPE_Area",
      //   label: "SHAPE_Area"
      // },
      // {
      //   fieldName: "LOT_NUMBER",
      //   label: "LOT_NUMBER"
      // },
      {
        fieldName: "权属单",
        label: "权属单位"
      },
      // {
      //   fieldName: "单位类",
      //   label: "单位类"
      // },
      // {
      //   fieldName: "单位_1",
      //   label: "单位_1"
      // },
      // {
      //   fieldName: "单位_12",
      //   label: "单位_12"
      // },
    ]
  }],
  outFields: ["*"],
  actions: [
    {
      title: "详细",
      id: "popup-detail",
    }
  ]
};
const premisesSurveyTemplate1 = {
  title: "房地信息",
  content: [{
    type: "fields",
    fieldInfos: [
      {
        fieldName: "现状使",
        label: "现状使用主体"
      },
    ]
  }],
  outFields: ["*"],
  actions: [
    {
      title: "详细",
      id: "popup-detail",
    }
  ]
};

// labelClass
const labelClass = {
  symbol: {
    type: "text",
    color: "white",
    font: {
      size: 8,
      weight: "bold"
    }
  },
  labelPlacement: "always-horizontal",
  labelExpressionInfo: {
    expression: "$feature.权属单"
  }
};
const labelClass1 = {
  symbol: {
    type: "text",
    color: "white",
    font: {
      size: 8,
      weight: "bold"
    }
  },
  labelPlacement: "always-horizontal",
  labelExpressionInfo: {
    expression: "$feature.现状使"
  }
};

// renderer
const renderer = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: "red"
  }
};
const renderer0 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [99, 231, 236, 0.1],
    outline: {
      color: [99, 231, 236, 0.3],
      width: 1
    }
  }
};
const renderer1 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [52, 125, 244, 0.4],
    outline: {
      color: [52, 125, 244, 0.8],
      width: 1
    }
  }
};
const renderer2 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [92, 221, 147, 0.4],
    outline: {
      color: [92, 221, 147, 0.8],
      width: 1
    }
  }
};
const renderer3 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [255, 163, 36, 0.4],
    outline: {
      color: [230, 166, 78, 0.8],
      width: 1
    }
  }
};
const renderer4 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [187, 178, 215, 0.4],
    outline: {
      color: [187, 178, 215, 0.8],
      width: 1
    }
  }
};
const renderer5 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [213, 216, 111, 0.4],
    outline: {
      color: [207, 210, 109, 0.8],
      width: 1
    }
  }
};
const renderer6 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [139, 224, 252, 0.4],
    outline: {
      color: [78, 192, 230, 0.8],
      width: 1
    }
  }
};
const renderer7 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [212, 101, 164, 0.4],
    outline: {
      color: [212, 101, 164, 0.8],
      width: 1
    }
  }
};

export default {
  name: 'premisesSurveyDBMixin',
  data() {
    return {
      // 房地信息
      parcelInformationList: [],
      parcelInformationData: {},
      isPremisesSurveyWindowShow: false,
      premisesSurveyFeatureLayer: null,
      // 26个宗地
      premisesSurvey26FeatureLayer: null,
      // 中石化上海工程公司（1.“一环一河”项目 2.再生资源整治 3.其他）
      premisesSurveyZsh1FeatureLayer: null,
      premisesSurveyZsh2FeatureLayer: null,
      premisesSurveyZsh3FeatureLayer: null,
      // 联欧
      premisesSurveyLo1FeatureLayer: null,
      premisesSurveyLo2FeatureLayer: null,
      premisesSurveyLo3FeatureLayer: null,
      // 锦宏
      premisesSurveyJh1FeatureLayer: null,
      // 生产型企业
      premisesSurveyProdFeatureLayer: null,
      // 生产型企业-二工区
      premisesSurveyProdEGQFeatureLayer: null,
      // 幼儿园
      kindergartenFeatureLayer: null,
      // 小学
      primarySchoolFeatureLayer: null,
      // 初中
      juniorMiddleSchoolFeatureLayer: null,
      // 中职
      vocationalSchoolFeatureLayer: null,
      // 高中
      highSchoolFeatureLayer: null,
      // 功能型学校
      functionalSchoolFeatureLayer: null,
      // 辅读学校
      supplementarySchoolFeatureLayer: null,
      // 国产资产
      domesticAssetsFeatureLayer: null,
      // 排涝泵站
      pumpStationFeatureLayer: null,
      // 排污泵站
      pumpStationFeatureLayer1: null,
      // 厂房仓库
      factoryWarehouseList: [],
      isFactoryWarehouseWindowShow: false,
      factoryWarehouseFeatureLayer: null,
      // 综合网格
      comprehensiveGridList: [],
      comprehensiveGridGraphicArr: [],
      comprehensiveGridFeatureLayer: null,
      gridId: null,
      isComprehensiveGridWindowShow: false,
    }
  },
  mounted() {
    listScreenParcelInformation(this.queryParams).then(response => {
      this.parcelInformationList = response.data
    })

    // 厂房仓库
    listScreenFactoryWarehouse(this.queryParams).then(response => {
      this.factoryWarehouseList = response.data
    })

    // 房地信息 宗地
    loadModules([
        "esri/layers/FeatureLayer",
      ],
      this.options).then(([
                            FeatureLayer,
                          ]) => {
      this.premisesSurveyFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer0,
        popupTemplate: premisesSurveyTemplate1,
        definitionExpression: "现状使 NOT IN ('上海臻友设备工程技术有限公司', '上海精热机械热镀有限公司', '上海君跃化工机械有限公司', '上海恒雄电气有限公司-二车间', '上海石化金亮机电设备制造有限公司', '上海金阳起重设备安装修理有限公司')"
      });
      this.premisesSurveyFeatureLayer.labelingInfo = [labelClass1];

      // 26个宗地
      this.premisesSurvey26FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer,
        popupTemplate:premisesSurveyTemplate,

      });
      this.premisesSurvey26FeatureLayer.definitionExpression = "lot_number in ( '1840509140003001'," +
        "'1840509140003002'," +
        "'1840509100028000'," +
        "'1840509090019000'," +
        "'1840509090042003'," +
        "'1840509100020000'," +
        "'1840509100025000'," +
        "'1840509100025007'," +
        "'1840509100025003'," +
        "'1840509100021001'," +
        "'1840509030035000'," +
        "'1840509100005002'," +
        "'1840509100005001'," +
        "'1840509090044000'," +
        "'1840509090023002'," +
        "'1840509090027000'," +
        "'1840509100018001'," +
        "'1840509110008002'," +
        "'1840509090018000'," +
        "'1840509090020000'," +
        "'1840509100026000'," +
        "'1840509100016006' ) or 权属单 = '上海联欧工程建设服务有限公司'";
      this.premisesSurvey26FeatureLayer.labelingInfo = [labelClass];

      // 中石化上海工程公司（1.“一环一河”项目）
      this.premisesSurveyZsh1FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate:premisesSurveyTemplate,
      });
      this.premisesSurveyZsh1FeatureLayer.definitionExpression = "lot_number in ('1840509100005002'," +
        "'1840509090027000'," +
        "'1840509090020000')";
      this.premisesSurveyZsh1FeatureLayer.labelingInfo = [labelClass];
      // 中石化上海工程公司（2.再生资源）
      this.premisesSurveyZsh2FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate:premisesSurveyTemplate,
      });
      this.premisesSurveyZsh2FeatureLayer.definitionExpression = "lot_number = '1840509100021001'";
      this.premisesSurveyZsh2FeatureLayer.labelingInfo = [labelClass];
      // 中石化上海工程公司（3.其他）
      this.premisesSurveyZsh3FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate:premisesSurveyTemplate,
      });
      this.premisesSurveyZsh3FeatureLayer.definitionExpression = "lot_number in ( '1840509140003001'," +
        "'1840509140003002'," +
        "'1840509100028000'," +
        "'1840509090019000'," +
        "'1840509100020000'," +
        "'1840509100025000'," +
        "'1840509100025007'," +
        "'1840509100025003'," +
        "'1840509030035000'," +
        "'1840509100018001'," +
        "'1840509110008002'," +
        "'1840509100026000' ) ";
      this.premisesSurveyZsh3FeatureLayer.labelingInfo = [labelClass];
      // 联欧 （1.“一环一河”项目）
      this.premisesSurveyLo1FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer4,
        popupTemplate:premisesSurveyTemplate,

      });
      this.premisesSurveyLo1FeatureLayer.definitionExpression = "lot_number = '1840509100016006'";
      this.premisesSurveyLo1FeatureLayer.labelingInfo = [labelClass];
      // 联欧 （2.再生资源）
      this.premisesSurveyLo2FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer5,
        popupTemplate:premisesSurveyTemplate,

      });
      this.premisesSurveyLo2FeatureLayer.definitionExpression = "lot_number in ( '1840509090018000', '1840509100016006' ) " +
        " or 现状使 = '上海俊洋废旧物资回收有限公司' or 现状使 = '上海交盾物资调剂有限公司'";
      this.premisesSurveyLo2FeatureLayer.labelingInfo = [labelClass];
      // 中石化上海工程公司（3.其他）
      this.premisesSurveyLo3FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer6,
        popupTemplate:premisesSurveyTemplate,

      });
      this.premisesSurveyLo3FeatureLayer.definitionExpression = "lot_number in ( '1840509090042003'," +
        "'1840509090044000'," +
        "'1840509090023002' ) " +
        " or 现状使 = '鹤康建设发展（上海）有限公司' or 现状使 = '上海翔诚货物储运有限公司'";
      this.premisesSurveyLo3FeatureLayer.labelingInfo = [labelClass];

      // 锦宏（1.“一环一河”项目）
      this.premisesSurveyJh1FeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer7,
        popupTemplate:premisesSurveyTemplate,
      });
      this.premisesSurveyJh1FeatureLayer.definitionExpression = "lot_number = '1840509100005001'";
      this.premisesSurveyJh1FeatureLayer.labelingInfo = [labelClass];

      // 生产型企业
      this.premisesSurveyProdFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate:premisesSurveyTemplate1,
      });
      this.premisesSurveyProdFeatureLayer.definitionExpression = "单位类 = '生产型企业' or 单位_1 = '生产型企业' or 单位_12 = '生产型企业'";
      this.premisesSurveyProdFeatureLayer.labelingInfo = [labelClass1];

      // 生产型企业-二工区
      this.premisesSurveyProdEGQFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate:premisesSurveyTemplate1,
      });
      this.premisesSurveyProdEGQFeatureLayer.definitionExpression = "单位类 like '%生产型企业-二工区%' or 单位_1 like '%生产型企业-二工区%' or 单位_12 like '%生产型企业-二工区%'";
      this.premisesSurveyProdEGQFeatureLayer.labelingInfo = [labelClass1];

      // 幼儿园
      this.kindergartenFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.kindergartenFeatureLayer.definitionExpression = "单位类 like '%幼儿园%'";
      this.kindergartenFeatureLayer.labelingInfo = [labelClass1];

      // 小学
      this.primarySchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.primarySchoolFeatureLayer.definitionExpression = "单位类 like '%小学%'";
      this.primarySchoolFeatureLayer.labelingInfo = [labelClass1];

      // 初中
      this.juniorMiddleSchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.juniorMiddleSchoolFeatureLayer.definitionExpression = "单位类 like '%初中%'";
      this.juniorMiddleSchoolFeatureLayer.labelingInfo = [labelClass1];

      // 中职
      this.vocationalSchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.vocationalSchoolFeatureLayer.definitionExpression = "单位类 like '%中职%'";
      this.vocationalSchoolFeatureLayer.labelingInfo = [labelClass1];

      // 高中
      this.highSchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.highSchoolFeatureLayer.definitionExpression = "单位类 like '%高中%'";
      this.highSchoolFeatureLayer.labelingInfo = [labelClass1];

      // 功能型学校
      this.functionalSchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.functionalSchoolFeatureLayer.definitionExpression = "单位类 like '%功能型学校%'";
      this.functionalSchoolFeatureLayer.labelingInfo = [labelClass1];

      // 辅读学校
      this.supplementarySchoolFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate1,
      });
      this.supplementarySchoolFeatureLayer.definitionExpression = "单位类 like '%辅读学校%'";
      this.supplementarySchoolFeatureLayer.labelingInfo = [labelClass1];

      // 国有资产
      this.domesticAssetsFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: premisesSurveyTemplate,
      });
      this.domesticAssetsFeatureLayer.definitionExpression = "是否国 like '%是%'";
      this.domesticAssetsFeatureLayer.labelingInfo = [labelClass];

      // 排涝泵站
      this.pumpStationFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate:premisesSurveyTemplate1,
      });
      this.pumpStationFeatureLayer.definitionExpression = "单位类 = '排涝泵站'";
      this.pumpStationFeatureLayer.labelingInfo = [labelClass1];

      // 排污泵站
      this.pumpStationFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate:premisesSurveyTemplate1,
      });
      this.pumpStationFeatureLayer1.definitionExpression = "单位类 = '排污泵站'";
      this.pumpStationFeatureLayer1.labelingInfo = [labelClass1];

      // 厂房仓库
      this.factoryWarehouseFeatureLayer = new FeatureLayer({
                url: 'http://12.112.123.249/server/geoscene/rest/services/jingyi/changfangcangku2/MapServer/0',
                outFields: ['*'],
                renderer: {
                  type: 'simple',
                  symbol: {
                    type: 'simple-fill',
                    color: [52, 125, 244, 0.4],
                    outline: {
                      color: [52, 125, 244, 0.8],
                      width: 1,
                    },
                  },
                },
                // 过滤掉单位名为空的要素
                definitionExpression: "单位名 <> ''",
                popupTemplate: {
                  title: '厂房仓库信息',
                  content: [
                    {
                      type: 'fields',
                      fieldInfos: [
                        {
                          fieldName: '单位名',
                          label: '单位名称',
                        },
                        {
                          fieldName: '建筑类',
                          label: '建筑类型',
                        }
                      ],
                    },
                  ],
                  outFields: ['*'],
                  actions: [
                    {
                      title: "详细",
                      id: "popup-detail",
                    }
                  ]
                },
                labelingInfo: [{
                  symbol: {
                  type: "text",
                  color: "white",
                  font: {
                    size: 6,
                    weight: "bold"
                  }
                },
                labelPlacement: "always-horizontal",
                labelExpressionInfo: {
                  expression: "$feature.单位名"
                }
                }],
              })

      // 综合网格
      listComprehensiveGrid(this.queryParams).then(response => {
        this.comprehensiveGridList = response.data
        loadModules([
          "esri/Graphic",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let comprehensiveGridRenderer = {
          type: "unique-value",
          field: "gridName",
          defaultSymbol: {
            type: "simple-fill",
            color: [37, 127, 255, 0.2],
            outline: {
              color: [37, 127, 255],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        this.comprehensiveGridList.forEach(comprehensiveGrid => {
          const fillColor = comprehensiveGrid.fillColor ? comprehensiveGrid.fillColor.split(",") : [37, 127, 255, 0.2]
          const outlineColor = comprehensiveGrid.outlineColor ? comprehensiveGrid.outlineColor.split(",") : [37, 127, 255]

          let uniqueValueInfo = {
            value: comprehensiveGrid.gridName,
            symbol: {
              type: "simple-fill",
              color: fillColor,
              outline: {
                color: outlineColor,
                width: 2
              }
            }
          }
          comprehensiveGridRenderer.uniqueValueInfos.push(uniqueValueInfo)
          comprehensiveGrid.graphicType = 'comprehensiveGrid'
          const attributes = JSON.parse(JSON.stringify(comprehensiveGrid))
          if (comprehensiveGrid.coordinate) {
            if (comprehensiveGrid.type === "polygon") {
              const r = parseCoordinate(comprehensiveGrid.coordinate);
              const polygon = new Polygon({
                rings: r,
                spatialReference: SpatialReference.WebMercator,
              });
              const comprehensiveGridGraphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              this.comprehensiveGridGraphicArr.push(comprehensiveGridGraphic)
            }
          }
        })
        const fields = [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "graphicType",
            alias: "graphicType",
            type: "string"
          },
          {
            name: "gridName",
            alias: "gridName",
            type: "string"
          },
          {
            name: "gridNumber",
            alias: "gridNumber",
            type: "string"
          },
          {
            name: "id",
            alias: "id",
            type: "long"
          }
        ]
        this.comprehensiveGridFeatureLayer = new FeatureLayer({
          source: this.comprehensiveGridGraphicArr,
          fields: fields,
          renderer: comprehensiveGridRenderer,
          outFields: ["*"],
        })
        const labelClass = {
          symbol: {
            type: "text",
            color: "white",
            font: {
              size: 18,
              weight: "bold"
            }
          },
          labelPlacement: "always-horizontal",
          labelExpressionInfo: {
            expression: "$feature.gridName"
          }
        };
        this.comprehensiveGridFeatureLayer.labelingInfo = [labelClass];

      })

      })
    })
  },
  methods: {
    premisesSurveySelBtn() {
      this.map.add(this.greenSpaceFeatureLayer)
    },
    searchPremisesSurvey(keyword) {
      loadModules([
          "esri/Graphic",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              FeatureLayer,
                            ]) => {
        const premisesSurveyFeatureLayer = new FeatureLayer({
          url:
            "http://12.112.123.249/server/geoscene/rest/services/zd/FeatureServer/0",
          outFields: ["*"],
        });

        let query = premisesSurveyFeatureLayer.createQuery();
        query.where = "权属单 like '%" + keyword + "%'";
        query.outFields = ["*"];
        query.returnGeometry = true;
        const _this = this
        premisesSurveyFeatureLayer.queryFeatures(query)
          .then(function (result) {
            if (result.features.length > 0) {
              result.features.forEach(function (feature) {
                const g = new Graphic({
                  geometry: feature.geometry,
                  attributes: feature.attributes,
                  symbol: {
                    type: "simple-fill",
                    color: [77, 238, 250, 0.3],
                    outline: {
                      color: [77, 238, 250],
                      width: 1
                    }
                  },
                });
                _this.graphicsLayer.add(g)
              });
            }
          });
      })
    },
    addPremisesSurvey26FeatureLayer() {
      this.map.add(this.premisesSurvey26FeatureLayer)
    },
    checkZd(value) {
      if (value === true) {
        this.map.add(this.premisesSurveyFeatureLayer)
        // this.map.add(this.premisesSurveyZsh1FeatureLayer)
        // this.map.add(this.premisesSurveyZsh2FeatureLayer)
        // this.map.add(this.premisesSurveyZsh3FeatureLayer)
        // this.map.add(this.premisesSurveyLo1FeatureLayer)
        // this.map.add(this.premisesSurveyLo2FeatureLayer)
        // this.map.add(this.premisesSurveyLo3FeatureLayer)
        // this.map.add(this.premisesSurveyJh1FeatureLayer)
        this.map.add(this.premisesSurveyProdFeatureLayer)
        this.map.add(this.premisesSurveyProdEGQFeatureLayer)
        this.map.add(this.committeeGridFeatureLayer)
        this.map.add(this.comprehensiveGridFeatureLayer)
      } else if (value === false) {
        this.map.remove(this.premisesSurveyFeatureLayer)
        // this.map.remove(this.premisesSurveyZsh1FeatureLayer)
        // this.map.remove(this.premisesSurveyZsh2FeatureLayer)
        // this.map.remove(this.premisesSurveyZsh3FeatureLayer)
        // this.map.remove(this.premisesSurveyLo1FeatureLayer)
        // this.map.remove(this.premisesSurveyLo2FeatureLayer)
        // this.map.remove(this.premisesSurveyLo3FeatureLayer)
        // this.map.remove(this.premisesSurveyJh1FeatureLayer)
        this.map.remove(this.premisesSurveyProdFeatureLayer)
        this.map.remove(this.premisesSurveyProdEGQFeatureLayer)
        this.map.remove(this.committeeGridFeatureLayer)
        this.map.remove(this.comprehensiveGridFeatureLayer)
      } else {
        const zdOptions = ['房地信息', '综合网格', '厂房仓库', '生产型企业', '生产型企业-二工区'];
        let a = new Set(value);
        let b = new Set(zdOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '房地信息') {
            this.map.remove(this.premisesSurveyFeatureLayer)
          }
          // else if (item === '工程公司-“一环一河”项目') {
          //   this.map.remove(this.premisesSurveyZsh1FeatureLayer)
          // } else if (item === '工程公司-再生资源整治') {
          //   this.map.remove(this.premisesSurveyZsh2FeatureLayer)
          // } else if (item === '工程公司-其他') {
          //   this.map.remove(this.premisesSurveyZsh3FeatureLayer)
          // } else if (item === '联欧-“一环一河”项目') {
          //   this.map.remove(this.premisesSurveyLo1FeatureLayer)
          // } else if (item === '联欧-再生资源整治') {
          //   this.map.remove(this.premisesSurveyLo2FeatureLayer)
          // } else if (item === '联欧-其他') {
          //   this.map.remove(this.premisesSurveyLo3FeatureLayer)
          // } else if (item === '锦宏-“一环一河”项目') {
          //   this.map.remove(this.premisesSurveyJh1FeatureLayer)
          // }
          else if (item === '生产型企业') {
            this.map.remove(this.premisesSurveyProdFeatureLayer)
          } else if (item === '生产型企业-二工区') {
            this.map.remove(this.premisesSurveyProdEGQFeatureLayer)
          } else if (item === '厂房仓库') {
            this.map.remove(this.factoryWarehouseFeatureLayer)
          } else if (item === '综合网格') {
            this.map.remove(this.committeeGridFeatureLayer)
            this.map.remove(this.comprehensiveGridFeatureLayer)
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '房地信息') {
              this.map.add(this.premisesSurveyFeatureLayer)
            }
            // else if (item === '工程公司-“一环一河”项目') {
            //   this.map.add(this.premisesSurveyZsh1FeatureLayer)
            // } else if (item === '工程公司-再生资源整治') {
            //   this.map.add(this.premisesSurveyZsh2FeatureLayer)
            // } else if (item === '工程公司-其他') {
            //   this.map.add(this.premisesSurveyZsh3FeatureLayer)
            // } else if (item === '联欧-“一环一河”项目') {
            //   this.map.add(this.premisesSurveyLo1FeatureLayer)
            // } else if (item === '联欧-再生资源整治') {
            //   this.map.add(this.premisesSurveyLo2FeatureLayer)
            // } else if (item === '联欧-其他') {
            //   this.map.add(this.premisesSurveyLo3FeatureLayer)
            // } else if (item === '锦宏-“一环一河”项目') {
            //   this.map.add(this.premisesSurveyJh1FeatureLayer)
            // }
            else if (item === '生产型企业') {
              this.map.add(this.premisesSurveyProdFeatureLayer)
            } else if (item === '生产型企业-二工区') {
              this.map.add(this.premisesSurveyProdEGQFeatureLayer)
            } else if (item === '厂房仓库') {
              this.map.add(this.factoryWarehouseFeatureLayer)
            } else if (item === '综合网格') {
              this.map.add(this.committeeGridFeatureLayer)
              this.map.add(this.comprehensiveGridFeatureLayer)
            }
          }
        }
      }
    },
    checkZdByUnitType(unitType) {
      if (unitType === '生产型企业') {
        this.map.add(this.premisesSurveyProdFeatureLayer)
      } else if (unitType === '生产型企业-二工区') {
        this.map.add(this.premisesSurveyProdEGQFeatureLayer)
      } else if (unitType === '幼儿园') {
        this.map.add(this.kindergartenFeatureLayer)
      } else if (unitType === '小学') {
        this.map.add(this.primarySchoolFeatureLayer)
      } else if (unitType === '初中') {
        this.map.add(this.juniorMiddleSchoolFeatureLayer)
      } else if (unitType === '中职') {
        this.map.add(this.vocationalSchoolFeatureLayer)
      } else if (unitType === '高中') {
        this.map.add(this.highSchoolFeatureLayer)
      } else if (unitType === '功能型学校') {
        this.map.add(this.functionalSchoolFeatureLayer)
      } else if (unitType === '辅读学校') {
        this.map.add(this.supplementarySchoolFeatureLayer)
      } else if (unitType === '国有资产') {
        this.map.add(this.domesticAssetsFeatureLayer)
      } else if (unitType === '厂房仓库') {
        this.map.add(this.factoryWarehouseFeatureLayer)
      }
    },
    drawAllZds() {
      this.map.add(this.premisesSurveyFeatureLayer)
      this.$refs.tipsWindow.checkedZds = ['房地信息']
      this.$refs.tipsWindow.isIndeterminateZd = true
    },
  }
}
</script>
