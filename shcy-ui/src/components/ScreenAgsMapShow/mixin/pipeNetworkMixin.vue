<template>

</template>

<script>

import {
  listScreenCover,
  listPumpStation,
  listPumpStationArea,
  listScreenLiquidLevelDevice,
  listScreenLiquidLevelDeviceAlarm,
  listScreenLiquidLevelDeviceArea,
  listIccDevice,
  listCameras,
  listFloodReserveInfo,
  listFloodShelterInfo,
  listScreenSensorDevice,
} from "@/api/shcy/screen";
import {loadModules} from "esri-loader";
import {parseCoordinate} from "@/utils/coordinate";

const hedaoPopupTemplate = {
  title: "河道",
  content: [{
    type: "fields",
    fieldInfos: [
      {
        fieldName: "水体名",
        label: "水体名"
      },
      {
        fieldName: "分类",
        label: "分类"
      },
      {
        fieldName: "等级",
        label: "等级"
      },
      {
        fieldName: "是否跨",
        label: "是否跨"
      },
      {
        fieldName: "流经街",
        label: "流经街"
      },
      {
        fieldName: "行政区",
        label: "行政区"
      },
      {
        fieldName: "原分类",
        label: "原分类"
      },
      {
        fieldName: "区县街",
        label: "区县街"
      },
    ]
  }]
}
const hedaoLabelClass = {
  symbol: {
    type: "text",
    color: "white",
    font: {
      size: 12,
      weight: "bold"
    }
  },
  labelPlacement: "always-horizontal",
  labelExpressionInfo: {
    expression: "$feature.水体名"
  }
}


export default {
  name: 'pipeNetWorkMixin',
  data() {
    return {
      // 市政管网
      coverList: [],
      coverGraphicArr: [],
      coverFeatureLayer: null,
      coverData:{},
      isCoverWindowShow: false,
      // 河道
      hedaoFeatureLayer0: null,
      hedaoFeatureLayer1: null,
      // 区管河道
      hedaoFeatureLayer2: null,
      // 镇管河道
      hedaoFeatureLayer3: null,
      // 村管河道
      hedaoFeatureLayer4: null,
      // 其它河道
      hedaoFeatureLayer5: null,
      // 施工中河道 => 股份公司河道
      hedaoFeatureLayer6: null,

      // 市政管网 cad
      cadFeatureLayer0: null,
      cadFeatureLayer1: null,
      cadFeatureLayer2: null,
      cadFeatureLayer3: null,
      cadFeatureLayer4: null,
      // 雨水管线
      ysFeatureLayer: null,
      wsFeatureLayer: null,

      // 排污泵站点、线、面
      pumpStationPointFeatureLayer: null,
      pumpStationPolylineFeatureLayer: null,
      pumpStationPolygonFeatureLayer: null,

      // 液位超限感知在线设备
      liquidLevelDeviceList: [],
      liquidLevelDeviceGraphicArr: [],
      liquidLevelDeviceFeatureLayer: null,

      // 液位超限感知离线设备
      liquidLevelDeviceOfflineGraphicArr: [],
      liquidLevelDeviceOfflineFeatureLayer: null,

      // 液位装置报警
      liquidLevelDeviceAlarmList: [],
      liquidLevelDeviceAlarmGraphicArr: [],
      liquidLevelDeviceAlarmFeatureLayer: null,

      // 防汛防台重点区域图层
      liquidLevelDeviceAreaFeatureLayer: null,
      floodControlKeyAreaData: [],
      isFloodControlKeyAreaWindowShow: false,
      liquidLevelDeviceAreaPointFeatureLayer: null,

      isFloodControlKeyAreaCameraWindowShow: false,
      isLiquidLevelDeviceCameraWindowShow: false,

      alarmRecordData: [],
      isAlarmRecordWindowShow: false,

      // icc监控设备
      iccDeviceFeatureLayer: null,
      iccDeviceData: [],
      isIccDeviceWindowShow: false,

      iccDeviceName: '',
      iccDevicePreviewUrl: '',
      isIccDevicePreviewWindowShow: false,

      // 防汛重点部位监控
      fxzdbwCameraFeatureLayer: null,

      // 海康视频监控
      currentCameraIndexCode: null,
      previewUrl: '',
      isHikPreviewWindowShow: false,

      // 监控分组
      isCameraGroupWindowShow: false,

      isCameraFindWindowShow: false,

      // 防汛物资储备信息
      floodReserveInfoFeatureLayer: null,

      // 防汛安置点信息
      floodShelterInfoFeatureLayer: null,

      // 泵站区域图层
      pumpStationAreaPointFeatureLayer: null,
      pumpStationAreaPolylineFeatureLayer: null,
      pumpStationAreaPolygonFeatureLayer: null,

      // 航天水位传感器设备
      sensorDeviceList: [],
      sensorDeviceGraphicArr: [],
      sensorDeviceFeatureLayer: null,

    }
  },
  mounted() {

    // 井盖
    listScreenCover(this.queryParams).then(response => {
      this.coverList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.coverList.forEach(cover => {
          cover.graphicType = 'cover'
          const attributes = JSON.parse(JSON.stringify(cover))
          if (cover.type === "point" && cover.coordinate) {
            const p = cover.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const coverGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.coverGraphicArr.push(coverGraphic)
          }
        })
        this.coverFeatureLayer = new FeatureLayer({
          source: this.coverGraphicArr,
          fields: [
            {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "fieldStationNo",
              alias: "fieldStationNo",
              type: "string"
            },
            {
              name: "wsgc",
              alias: "wsgc",
              type: "string"
            },
            {
              name: "pipelineProperty",
              alias: "pipelineProperty",
              type: "string"
            },
            {
              name: "burialDepth",
              alias: "burialDepth",
              type: "string"
            },
            {
              name: "pipelineMaterial",
              alias: "pipelineMaterial",
              type: "string"
            },
            {
              name: "road",
              alias: "road",
              type: "string"
            },
            {
              name: "wellDepth",
              alias: "wellDepth",
              type: "string"
            },
            {
              name: "coverMaterial",
              alias: "coverMaterial",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "simple-marker",
              style: "circle",
              color: "red",
              size: "2px",
              outline: {
                color: [255, 0, 0],
                width: 3
              }
            }
          },
          popupTemplate: {
            title: "井盖信息",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "fieldStationNo",
                  label: "外业点号"
                },
                {
                  fieldName: "wsgc",
                  label: "吴淞高程"
                },
                {
                  fieldName: "pipelineProperty",
                  label: "管线性质"
                },
                {
                  fieldName: "burialDepth",
                  label: "埋深"
                },
                {
                  fieldName: "pipelineMaterial",
                  label: "管线材质"
                },
                {
                  fieldName: "road",
                  label: "所在道路"
                },
                {
                  fieldName: "wellDepth",
                  label: "井深"
                },
                {
                  fieldName: "coverMaterial",
                  label: "井盖材质"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          },
          labelingInfo: [{
            symbol: {
              type: "text",
              horizontalAlignment: "left",
              color: "white",
              font: {
                size: 8,
                // weight: "bold"
              }
            },
            labelPlacement: "center-right",
            labelExpressionInfo: {
              expression: "$feature.fieldStationNo"
            }
          }]

        })
      })
    })

    // 河道
    loadModules([
        "esri/layers/FeatureLayer",
      ],
      this.options).then(([
                            FeatureLayer,
                          ]) => {

      // hedao_jianguan 水体中心线 (0)
      this.hedaoFeatureLayer0 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/0",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate
      });

      // hedao_jianguan 水体边界
      this.hedaoFeatureLayer1 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      this.hedaoFeatureLayer1.labelingInfo = [hedaoLabelClass];

      // 区管河道
      this.hedaoFeatureLayer2 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      this.hedaoFeatureLayer2.definitionExpression = "等级 like '%区%' or 变化类 like '%新增新开%'";
      this.hedaoFeatureLayer2.labelingInfo = [hedaoLabelClass];

      // 镇管河道
      this.hedaoFeatureLayer3 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      this.hedaoFeatureLayer3.definitionExpression = "等级 like '%镇%'";
      this.hedaoFeatureLayer3.labelingInfo = [hedaoLabelClass];

      // 村管河道
      this.hedaoFeatureLayer4 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      this.hedaoFeatureLayer4.definitionExpression = "等级 like '%村%'";
      this.hedaoFeatureLayer4.labelingInfo = [hedaoLabelClass];

      // 其它河道
      this.hedaoFeatureLayer5 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_jianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      this.hedaoFeatureLayer5.definitionExpression = "等级 like '%其它%' or 分类 like '%其它%'";
      this.hedaoFeatureLayer5.labelingInfo = [hedaoLabelClass];

      // 施工中河道 => 股份公司河道
      this.hedaoFeatureLayer6 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/hedao_feijianguan/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: hedaoPopupTemplate,
      });
      // this.hedaoFeatureLayer6.definitionExpression = "变化类 like '%新增新开%'";
      this.hedaoFeatureLayer6.labelingInfo = [hedaoLabelClass];

      // cad 底图
      this.cadFeatureLayer0 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad_szgwbc/FeatureServer/0",
        outFields: ["*"]
      });
      this.cadFeatureLayer1 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad/FeatureServer/2",
        outFields: ["*"],
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-marker",
            style: "circle",
            color: [255, 0, 0, 0],
            size: "0px",
            outline: {
              color: [255, 0, 0, 0],
              width: 0
            }
          }
        },
        popupTemplate: {
          title: "text",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "text",
                label: "text"
              },
              {
                fieldName: "layer",
                label: "layer"
              },
            ]
          }]
        },
        labelingInfo: [{
          symbol: {
            type: "text",
            horizontalAlignment: "left",
            color: "white",
            font: {
              size: 8,
            }
          },
          labelPlacement: "center-center",
          labelExpressionInfo: {
            expression: "$feature.text"
          }
        }]
      });
      this.cadFeatureLayer1.definitionExpression = "layer = 'Doorplate'";


      this.cadFeatureLayer2 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad_szgw/FeatureServer/2",
        outFields: ["*"],
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-line",
            color: "red",
            width: 2
          }
        },
        popupTemplate: {
          title: "市政管网",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "layer",
                label: "layer"
              },
            ]
          }]
        },
      });

      // 雨水管线
      this.ysFeatureLayer = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad_szgw/FeatureServer/2",
        outFields: ["*"],
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-line",
            color: [64, 164, 248],
            width: 2
          }
        },
      });
      this.ysFeatureLayer.definitionExpression = "layer like 'YS%'";

      // 污水管线
      this.wsFeatureLayer = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad_szgw/FeatureServer/2",
        outFields: ["*"],
        renderer: {
          type: "simple",
          symbol: {
            type: "simple-line",
            color: [207, 40, 40],
            width: 2
          }
        },
      });
      this.wsFeatureLayer.definitionExpression = "layer like 'WS%'";

      this.cadFeatureLayer3 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad230227/FeatureServer/3",
        outFields: ["*"]
      });
      this.cadFeatureLayer4 = new FeatureLayer({
        url:
          "http://**************/server/geoscene/rest/services/cad230227/FeatureServer/4",
        outFields: ["*"]
      });

    })

    listPumpStation(this.queryParams).then(response => {
      const pumpStations = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/Polyline",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              Polyline,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        let pumpStationPointGraphicArr = []
        let pumpStationPolylineGraphicArr = []
        let pumpStationPolygonGraphicArr = []

        let pumpStationRenderer = {
          type: "unique-value",
          field: "pumpStationName",
          defaultSymbol: {
            type: "simple-fill",
            color: [112, 178, 226, 0.2],
            outline: {
              color: [112, 178, 226],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        let pumpStationPolylineRenderer = {
          type: "unique-value",
          field: "pumpStationType",
          defaultSymbol: {
            type: "simple-line",
            color: [226, 119, 40],
            width: 4,
            style: "short-dash",
            marker: {
              color: [226, 119, 40],
              placement: "end",
              style: "arrow"
            }
          },
          uniqueValueInfos: []
        }

        pumpStations.forEach(pumpStation => {
          const attributes = JSON.parse(JSON.stringify(pumpStation))
          if (pumpStation.coordinate) {
            if (pumpStation.type === "polygon") {
              const fillColor = pumpStation.fillColor ? pumpStation.fillColor.split(",") : [112, 178, 226, 0.2]
              const outlineColor = pumpStation.outlineColor ? pumpStation.outlineColor.split(",") : [112, 178, 226]
              let uniqueValueInfo = {
                value: pumpStation.pumpStationName,
                symbol: {
                  type: "simple-fill",
                  color: fillColor,
                  outline: {
                    color: outlineColor,
                    width: 2
                  }
                }
              }
              pumpStationRenderer.uniqueValueInfos.push(uniqueValueInfo)
              const rings = parseCoordinate(pumpStation.coordinate);
              const polygon = new Polygon({
                rings: rings,
                spatialReference: SpatialReference.WebMercator,
              });
              const graphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              pumpStationPolygonGraphicArr.push(graphic)
            } else if (pumpStation.type === "point") {
              const p = pumpStation.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const graphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              pumpStationPointGraphicArr.push(graphic)
            } else if (pumpStation.type === "polyline") {
              if (pumpStation.pumpStationType === "入口") {
                let polylineUniqueValueInfo = {
                  value: pumpStation.pumpStationType,
                  symbol: {
                    type: "simple-line",
                    color: [52, 125, 244],
                    width: 4,
                    style: "short-dash",
                    marker: {
                      color: [52, 125, 244],
                      placement: "end",
                      style: "arrow"
                    }
                  }
                }
                pumpStationPolylineRenderer.uniqueValueInfos.push(polylineUniqueValueInfo)
              }

              const paths = parseCoordinate(pumpStation.coordinate)
              const polyline = new Polyline({
                paths: paths,
                spatialReference: SpatialReference.WebMercator,
              });
              const graphic = new Graphic({
                geometry: polyline,
                attributes: attributes,
              });
              pumpStationPolylineGraphicArr.push(graphic)
            }
          }
        })
        const fields = [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "pumpStationName",
            alias: "pumpStationName",
            type: "string"
          },
          {
            name: "pumpStationType",
            alias: "pumpStationType",
            type: "string"
          }
        ]

        this.pumpStationPointFeatureLayer = new FeatureLayer({
          source: pumpStationPointGraphicArr,
          fields: fields,
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/guanwang_icon_pwbz.png",
              width: "29px",
              height: "37px"
            }
          },
          outFields: ["*"],
          labelingInfo: [{
            symbol: {
              type: "text",
              horizontalAlignment: "left",
              color: "white",
              font: {
                size: 20,
                weight: "bold"
              }
            },
            labelPlacement: "center-right",
            labelExpressionInfo: {
              expression: "$feature.pumpStationName"
            }
          }]
        })

        this.pumpStationPolylineFeatureLayer = new FeatureLayer({
          source: pumpStationPolylineGraphicArr,
          fields: fields,
          renderer: pumpStationPolylineRenderer,
          outFields: ["*"],
        })

        this.pumpStationPolygonFeatureLayer = new FeatureLayer({
          source: pumpStationPolygonGraphicArr,
          fields: fields,
          renderer: pumpStationRenderer,
          outFields: ["*"],
          labelingInfo: [{
            symbol: {
              type: "text",
              color: "white",
              font: {
                size: 8,
                weight: "bold"
              }
            },
            labelPlacement: "always-horizontal",
            labelExpressionInfo: {
              expression: "$feature.pumpStationName"
            }
          }]
        })
      })

    });

    // 液位超限感知设备
    listScreenLiquidLevelDevice(this.queryParams).then(response => {
      this.liquidLevelDeviceList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.liquidLevelDeviceList.forEach(liquidLevelDevice => {
          liquidLevelDevice.graphicType = 'liquidLevelDevice'
          const attributes = JSON.parse(JSON.stringify(liquidLevelDevice))
          if (liquidLevelDevice.type === "point" && liquidLevelDevice.coordinate) {
            const p = liquidLevelDevice.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const liquidLevelDeviceGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });

            // 判断设备状态
            if (liquidLevelDevice.deviceState === '在线') {
              this.liquidLevelDeviceGraphicArr.push(liquidLevelDeviceGraphic)
            } else if (liquidLevelDevice.deviceState === '离线'){
              this.liquidLevelDeviceOfflineGraphicArr.push(liquidLevelDeviceGraphic)
            }
          }
        })
        this.liquidLevelDeviceFeatureLayer = new FeatureLayer({
          source: this.liquidLevelDeviceGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "deviceName",
              alias: "deviceName",
              type: "string"
            },
            {
              name: "deviceState",
              alias: "deviceState",
              type: "string"
            },
            {
              name: "address",
              alias: "address",
              type: "string"
            },
            {
              name: "monitoredWaterBody",
              alias: "monitoredWaterBody",
              type: "string"
            },
            {
              name: "pipelineType",
              alias: "pipelineType",
              type: "string"
            },
            {
              name: "drainageDirection",
              alias: "drainageDirection",
              type: "string"
            },
            {
              name: "cameras",
              alias: "cameras",
              type: "string"
            },
            {
              name: "pumpStationArea",
              alias: "pumpStationArea",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "unique-value",
            field: "monitoredWaterBody",
            defaultSymbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/guanwang_icon_ywcxgzsb.png",
              width: "43px",
              height: "56px"
            },
            uniqueValueInfos: [{
              value: "雨水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/guanwang_icon_ysgj.png",
                width: "43px",
                height: "56px"
              }
            },{
              value: "污水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/guanwang_icon_wsgj.png",
                width: "43px",
                height: "56px"
              }
            }]
          },
          popupTemplate: {
            title: "液位告警",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "deviceName",
                  label: "设备名称"
                },
                {
                  fieldName: "deviceState",
                  label: "设备状态"
                },
                {
                  fieldName: "address",
                  label: "地址"
                },
                {
                  fieldName: "monitoredWaterBody",
                  label: "监测水体"
                },
                {
                  fieldName: "pipelineType",
                  label: "管网类型"
                },
                {
                  fieldName: "drainageDirection",
                  label: "排水去向"
                },
              ]
            }],
            actions: [
              {
                title: "关联视频",
                id: "popup-camera",
              },
              {
                title: "泵站区域",
                id: "popup-pump-station-area",
              }
            ]
          }
        })

        this.liquidLevelDeviceOfflineFeatureLayer = new FeatureLayer({
          source: this.liquidLevelDeviceOfflineGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "deviceName",
              alias: "deviceName",
              type: "string"
            },
            {
              name: "deviceState",
              alias: "deviceState",
              type: "string"
            },
            {
              name: "address",
              alias: "address",
              type: "string"
            },
            {
              name: "monitoredWaterBody",
              alias: "monitoredWaterBody",
              type: "string"
            },
            {
              name: "pipelineType",
              alias: "pipelineType",
              type: "string"
            },
            {
              name: "drainageDirection",
              alias: "drainageDirection",
              type: "string"
            },
            {
              name: "cameras",
              alias: "cameras",
              type: "string"
            },
            {
              name: "pumpStationArea",
              alias: "pumpStationArea",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/guanwang_icon_lxsb.png",
              width: "43px",
              height: "56px"
            }
          },
          popupTemplate: {
            title: "液位告警",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "deviceName",
                  label: "设备名称"
                },
                {
                  fieldName: "deviceState",
                  label: "设备状态"
                },
                {
                  fieldName: "address",
                  label: "地址"
                },
                {
                  fieldName: "monitoredWaterBody",
                  label: "监测水体"
                },
                {
                  fieldName: "pipelineType",
                  label: "管网类型"
                },
                {
                  fieldName: "drainageDirection",
                  label: "排水去向"
                },
              ]
            }],
            actions: [
              {
                title: "关联视频",
                id: "popup-camera",
              },
              {
                title: "泵站区域",
                id: "popup-pump-station-area",
              }
            ]
          }
        })
      })
    })

    // 液位超限报警感知设备
    listScreenLiquidLevelDeviceAlarm(this.queryParams).then(response => {
      this.liquidLevelDeviceAlarmList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.liquidLevelDeviceAlarmList.forEach(liquidLevelDeviceAlarm => {
          liquidLevelDeviceAlarm.graphicType = 'liquidLevelDeviceAlarm'
          const attributes = JSON.parse(JSON.stringify(liquidLevelDeviceAlarm))
          if (liquidLevelDeviceAlarm.type === "point" && liquidLevelDeviceAlarm.coordinate) {
            const p = liquidLevelDeviceAlarm.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const liquidLevelDeviceAlarmGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.liquidLevelDeviceAlarmGraphicArr.push(liquidLevelDeviceAlarmGraphic)
          }
        })
        this.liquidLevelDeviceAlarmFeatureLayer = new FeatureLayer({
          source: this.liquidLevelDeviceAlarmGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "deviceName",
              alias: "deviceName",
              type: "string"
            },
            {
              name: "deviceState",
              alias: "deviceState",
              type: "string"
            },
            {
              name: "address",
              alias: "address",
              type: "string"
            },
            {
              name: "monitoredWaterBody",
              alias: "monitoredWaterBody",
              type: "string"
            },
            {
              name: "pipelineType",
              alias: "pipelineType",
              type: "string"
            },
            {
              name: "drainageDirection",
              alias: "drainageDirection",
              type: "string"
            },
            {
              name: "cameras",
              alias: "cameras",
              type: "string"
            },
            {
              name: "pumpStationArea",
              alias: "pumpStationArea",
              type: "string"
            }],
          outFields: ["*"],
          // renderer: {
          //   type: "simple",
          //   symbol: {
          //     type: "picture-marker",
          //     url: "http://**************/images/icon/guanwang_icon_ysgj_4.png",
          //     width: "120px",
          //     height: "120px"
          //   },
          // },
          renderer: {
            type: "unique-value",
            field: "monitoredWaterBody",
            defaultSymbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/guanwang_icon_ysgj_4.png",
              width: "120px",
              height: "120px"
            },
            uniqueValueInfos: [{
              value: "雨水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/guanwang_icon_ysgj_4.png",
                width: "120px",
                height: "120px"
              }
            },{
              value: "污水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/guanwang_icon_ysgj_3.png",
                width: "120px",
                height: "120px"
              }
            }]
          },
          popupTemplate: {
            title: "液位告警",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "deviceName",
                  label: "设备名称"
                },
                {
                  fieldName: "deviceState",
                  label: "设备状态"
                },
                {
                  fieldName: "address",
                  label: "地址"
                },
                {
                  fieldName: "monitoredWaterBody",
                  label: "监测水体"
                },
                {
                  fieldName: "pipelineType",
                  label: "管网类型"
                },
                {
                  fieldName: "drainageDirection",
                  label: "排水去向"
                }
              ]
            }],
            actions: [
              {
                title: "报警记录",
                id: "popup-detail",
              },
              {
                title: "关联视频",
                id: "popup-camera",
              },
              {
                title: "泵站区域",
                id: "popup-pump-station-area",
              }
            ]
          }
        })
      })
    })

    // 液位装置范围
    listScreenLiquidLevelDeviceArea(this.queryParams).then(response => {
      const liquidLevelDeviceAreaList = response.data
      const liquidLevelDeviceAreaGraphicArr = []
      const liquidLevelDeviceAreaPointGraphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let liquidLevelDeviceAreaRenderer = {
          type: "unique-value",
          field: "riskSite",
          defaultSymbol: {
            type: "simple-fill",
            color: [255, 200, 32, 0.2],
            outline: {
              color: [255, 160, 84],
              width: 1
            }
          },
          uniqueValueInfos: []
        }

        liquidLevelDeviceAreaList.forEach(liquidLevelDeviceArea => {

          const fillColor = liquidLevelDeviceArea.fillColor ? liquidLevelDeviceArea.fillColor.split(",") : [255, 200, 32, 0.2]
          const outlineColor = liquidLevelDeviceArea.outlineColor ? liquidLevelDeviceArea.outlineColor.split(",") : [255, 160, 84]

          let uniqueValueInfo = {
            value: liquidLevelDeviceArea.riskSite,
            symbol: {
              type: "simple-fill",
              color: fillColor,
              outline: {
                color: outlineColor,
                width: 1
              }
            }
          }
          liquidLevelDeviceAreaRenderer.uniqueValueInfos.push(uniqueValueInfo)

          liquidLevelDeviceArea.graphicType = 'liquidLevelDeviceArea'
          const attributes = JSON.parse(JSON.stringify(liquidLevelDeviceArea))
          if (liquidLevelDeviceArea.coordinate) {
            if (liquidLevelDeviceArea.type === "polygon") {
              const r = parseCoordinate(liquidLevelDeviceArea.coordinate);
              const polygon = new Polygon({
                rings: r,
                spatialReference: SpatialReference.WebMercator,
              });
              const liquidLevelDeviceAreaGraphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              liquidLevelDeviceAreaGraphicArr.push(liquidLevelDeviceAreaGraphic)
            } else if (liquidLevelDeviceArea.type === "point") {
              // 防汛防台重点区域点位
              const p = liquidLevelDeviceArea.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const liquidLevelDeviceAreaPointGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              liquidLevelDeviceAreaPointGraphicArr.push(liquidLevelDeviceAreaPointGraphic)
            }
          }
        })
        this.liquidLevelDeviceAreaFeatureLayer = new FeatureLayer({
          source: liquidLevelDeviceAreaGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "riskSite",
              alias: "riskSite",
              type: "string"
            },
            {
              name: "riskLevel",
              alias: "riskLevel",
              type: "string"
            },
            {
              name: "riskType",
              alias: "riskType",
              type: "string"
            },
            {
              name: "vulnerableArea",
              alias: "vulnerableArea",
              type: "string"
            },
            {
              name: "areaSize",
              alias: "areaSize",
              type: "string"
            },
            {
              name: "riskOverview",
              alias: "riskOverview",
              type: "string"
            },
            {
              name: "affectedRange",
              alias: "affectedRange",
              type: "string"
            },
            {
              name: "controlPeriod",
              alias: "controlPeriod",
              type: "string"
            },
            {
              name: "riskRating",
              alias: "riskRating",
              type: "string"
            },
            {
              name: "countermeasure1",
              alias: "countermeasure1",
              type: "string"
            },
            {
              name: "startCondition1",
              alias: "startCondition1",
              type: "string"
            },
            {
              name: "responsibleDepartment1",
              alias: "responsibleDepartment1",
              type: "string"
            },
            {
              name: "contactPerson1",
              alias: "contactPerson1",
              type: "string"
            },
            {
              name: "contactPhone1",
              alias: "contactPhone1",
              type: "string"
            },
            {
              name: "countermeasure2",
              alias: "countermeasure2",
              type: "string"
            },{
              name: "startCondition2",
              alias: "startCondition2",
              type: "string"
            },{
              name: "responsibleDepartment2",
              alias: "responsibleDepartment2",
              type: "string"
            },{
              name: "contactPerson2",
              alias: "contactPerson2",
              type: "string"
            },{
              name: "contactPhone2",
              alias: "contactPhone2",
              type: "string"
            },{
              name: "cameras",
              alias: "cameras",
              type: "string"
            }],
          outFields: ["*"],
          renderer: liquidLevelDeviceAreaRenderer,
          popupTemplate: {
            title: "灾害风险部位",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "riskSite",
                  label: "风险部位"
                },
                // {
                //   fieldName: "riskLevel",
                //   label: "风险等级"
                // },
                {
                  fieldName: "riskType",
                  label: "风险类型"
                },
                {
                  fieldName: "vulnerableArea",
                  label: "脆弱性区域"
                },
                // {
                //   fieldName: "areaSize",
                //   label: "区域面积"
                // },
                // {
                //   fieldName: "riskOverview",
                //   label: "风险概况"
                // },
                // {
                //   fieldName: "affectedRange",
                //   label: "受影响范围"
                // },
                // {
                //   fieldName: "controlPeriod",
                //   label: "防控期"
                // },
                // {
                //   fieldName: "riskRating",
                //   label: "风险评级"
                // }
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详情",
                id: "popup-detail",
              },
              {
                title: "关联视频",
                id: "popup-video",
                image: "http://**************/images/icon/play-button.png",
              }
            ]
          }
        })

        this.liquidLevelDeviceAreaPointFeatureLayer = new FeatureLayer({
          source: liquidLevelDeviceAreaPointGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          }, {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
          },{
            name: "riskSite",
            alias: "riskSite",
            type: "string"
          },
            {
              name: "riskLevel",
              alias: "riskLevel",
              type: "string"
            },
            {
              name: "riskType",
              alias: "riskType",
              type: "string"
            },
            {
              name: "vulnerableArea",
              alias: "vulnerableArea",
              type: "string"
            },
            {
              name: "areaSize",
              alias: "areaSize",
              type: "string"
            },
            {
              name: "riskOverview",
              alias: "riskOverview",
              type: "string"
            },
            {
              name: "affectedRange",
              alias: "affectedRange",
              type: "string"
            },
            {
              name: "controlPeriod",
              alias: "controlPeriod",
              type: "string"
            },
            {
              name: "riskRating",
              alias: "riskRating",
              type: "string"
            },
            {
              name: "countermeasure1",
              alias: "countermeasure1",
              type: "string"
            },
            {
              name: "startCondition1",
              alias: "startCondition1",
              type: "string"
            },
            {
              name: "responsibleDepartment1",
              alias: "responsibleDepartment1",
              type: "string"
            },
            {
              name: "contactPerson1",
              alias: "contactPerson1",
              type: "string"
            },
            {
              name: "contactPhone1",
              alias: "contactPhone1",
              type: "string"
            },
            {
              name: "countermeasure2",
              alias: "countermeasure2",
              type: "string"
            },{
              name: "startCondition2",
              alias: "startCondition2",
              type: "string"
            },{
              name: "responsibleDepartment2",
              alias: "responsibleDepartment2",
              type: "string"
            },{
              name: "contactPerson2",
              alias: "contactPerson2",
              type: "string"
            },{
              name: "contactPhone2",
              alias: "contactPhone2",
              type: "string"
            },{
              name: "cameras",
              alias: "cameras",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "unique-value",
            field: "vulnerableArea",
            defaultSymbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/map_icon_fxjg_4.png",
              width: "61px",
              height: "55px"
            },
            uniqueValueInfos: [{
              value: "危棚简屋",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/map_icon_wpjw.png",
                width: "43px",
                height: "56px"
              }
            }, {
              value: "下立交",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/map_icon_xlj.png",
                width: "43px",
                height: "56px"
              }
            }, {
              value: "低洼积水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/map_icon_dwd.png",
                width: "43px",
                height: "56px"
              }
            }, {
              value: "雨水倒灌",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/map_icon_ysdg.png",
                width: "43px",
                height: "56px"
              }
            }]
          },
          popupTemplate: {
            title: "灾害风险部位",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "riskSite",
                  label: "风险部位"
                },
                {
                  fieldName: "riskType",
                  label: "风险类型"
                },
                {
                  fieldName: "vulnerableArea",
                  label: "脆弱性区域"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详情",
                id: "popup-detail",
              },
              {
                title: "关联视频",
                id: "popup-video",
                image: "http://**************/images/icon/play-button.png",
              }
            ]
          }
        })


      })
    })

    // icc监控设备图层
    listIccDevice(this.queryParams).then(response => {
      const iccDeviceList = response.data
      const iccDeviceGraphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        iccDeviceList.forEach(iccDevice => {
          iccDevice.graphicType = 'iccDevice'
          const attributes = JSON.parse(JSON.stringify(iccDevice))
          if (iccDevice.type === "point" && iccDevice.coordinate) {
            const p = iccDevice.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const iccDeviceGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            iccDeviceGraphicArr.push(iccDeviceGraphic)
          }
        })
        this.iccDeviceFeatureLayer = new FeatureLayer({
          source: iccDeviceGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "deviceName",
              alias: "deviceName",
              type: "string"
            },
            {
              name: "deviceStatus",
              alias: "deviceStatus",
              type: "string"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/hjzz_icon_jiankong_8.gif",
              width: "100px",
              height: "100px"
            }
          },
          // popupTemplate: {
          //   title: "ICC监控设备",
          //   content: [{
          //     type: "fields",
          //     fieldInfos: [
          //       {
          //         fieldName: "deviceName",
          //         label: "设备名称"
          //       },
          //       {
          //         fieldName: "deviceStatus",
          //         label: "设备状态"
          //       }
          //     ]
          //   }],
          //   outFields: ["*"],
          //   actions: [
          //     // {
          //     //   title: "报警记录",
          //     //   id: "popup-detail",
          //     // }
          //   ]
          // }
        })
      })
    })

    // 防汛重点部位监控图层
    listCameras({cameraTypeName: "防汛重点部位"}).then(response => {
      const cameraList = response.data
      const cameraGraphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        cameraList.forEach(camera => {
          const attributes = JSON.parse(JSON.stringify(camera))
          if (camera.type === "point" && camera.coordinate) {
            const p = camera.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const cameraGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            cameraGraphicArr.push(cameraGraphic)
          }
        })
        this.fxzdbwCameraFeatureLayer = new FeatureLayer({
          source: cameraGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "cameraIndexCode",
              alias: "cameraIndexCode",
              type: "string"
            },
            {
              name: "name",
              alias: "name",
              type: "string"
            },
            {
              name: "cameraTypeName",
              alias: "cameraTypeName",
              type: "string"
            },
            {
              name: "positionName",
              alias: "positionName",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_jiankong.png",
              width: "29px",
              height: "37px"
            }
          },
          popupTemplate: {
            title: "监控资源",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "cameraIndexCode",
                  label: "监控点编号"
                },
                {
                  fieldName: "name",
                  label: "监控点名称"
                },
                {
                  fieldName: "cameraTypeName",
                  label: "监控点类型"
                },
                {
                  fieldName: "positionName",
                  label: "监控点位置"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "播放",
                id: "popup-play",
                image: "http://**************/images/icon/play-button.png",
              }
            ]
          }
        })
      })
    })

    // 防汛物资储备信息图层
    listFloodReserveInfo(this.queryParams).then(response => {
      const floodReserveInfoList = response.data
      const graphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        floodReserveInfoList.forEach(floodReserveInfo => {
          floodReserveInfo.graphicType = 'floodReserveInfo'
          const attributes = JSON.parse(JSON.stringify(floodReserveInfo))
          if (floodReserveInfo.type === "point" && floodReserveInfo.coordinate) {
            const p = floodReserveInfo.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const floodReserveInfoGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            graphicArr.push(floodReserveInfoGraphic)
          }
        })
        this.floodReserveInfoFeatureLayer = new FeatureLayer({
          source: graphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "propertyName",
              alias: "propertyName",
              type: "string"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/map_icon_fxwz.png",
              width: "43px",
              height: "55px"
            }
          },
          popupTemplate: {
            title: "防汛物资储备信息",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "propertyName",
                  label: "物业名称"
                }
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }
        })
      })
    })

    // 防汛安置点信息图层
    listFloodShelterInfo(this.queryParams).then(response => {
      const floodShelterInfoList = response.data
      const graphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        floodShelterInfoList.forEach(floodShelterInfo => {
          floodShelterInfo.graphicType = 'floodShelterInfo'
          const attributes = JSON.parse(JSON.stringify(floodShelterInfo))
          if (floodShelterInfo.type === "point" && floodShelterInfo.coordinate) {
            const p = floodShelterInfo.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const floodShelterInfoGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            graphicArr.push(floodShelterInfoGraphic)
          }
        })
        this.floodShelterInfoFeatureLayer = new FeatureLayer({
          source: graphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "shelterName",
              alias: "shelterName",
              type: "string"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/map_icon_fxazd.png",
              width: "43px",
              height: "55px"
            }
          },
          popupTemplate: {
            title: "防汛安置点信息",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "shelterName",
                  label: "安置点名称"
                }
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }
        })
      })
    })

    // 航天水位传感器
    listScreenSensorDevice(this.queryParams).then(response => {
      this.sensorDeviceList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.sensorDeviceList.forEach(sensorDevice => {
          sensorDevice.graphicType = 'sensorDevice'
          const attributes = JSON.parse(JSON.stringify(sensorDevice))
          if (sensorDevice.type === "point" && sensorDevice.coordinate) {
            const p = sensorDevice.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const sensorDeviceGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.sensorDeviceGraphicArr.push(sensorDeviceGraphic)
          }
        })
        this.sensorDeviceFeatureLayer = new FeatureLayer({
          source: this.sensorDeviceGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "deviceName",
              alias: "deviceName",
              type: "string"
            },
            {
              name: "sensorimeiTypeName",
              alias: "sensorimeiTypeName",
              type: "string"
            },
            {
              name: "status",
              alias: "status",
              type: "string"
            },
            {
              name: "imei",
              alias: "imei",
              type: "string"
            },
            {
              name: "value",
              alias: "value",
              type: "string"
            },
            {
              name: "unit",
              alias: "unit",
              type: "string"
            },
            {
              name: "waterLevelType",
              alias: "waterLevelType",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "unique-value",
            field: "waterLevelType",
            defaultSymbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/fxft_map_icon_sxygc_hdsw.png",
              width: "54px",
              height: "66px"
            },
            uniqueValueInfos: [{
              value: "河道",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/fxft_map_icon_sxygc_hdsw.png",
                width: "54px",
                height: "66px"
              }
            },{
              value: "污水",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/fxft_map_icon_sxygc_wssw.png",
                width: "54px",
                height: "66px"
              }
            }]
          },
          popupTemplate: {
            title: "水位",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "deviceName",
                  label: "设备名称"
                },
                {
                  fieldName: "sensorimeiTypeName",
                  label: "传感器类型"
                },
                {
                  fieldName: "status",
                  label: "状态"
                },
                {
                  fieldName: "imei",
                  label: "IMEI"
                },
                {
                  fieldName: "value",
                  label: "值"
                },
                {
                  fieldName: "unit",
                  label: "单位"
                }
              ]
            }]
          },
          labelingInfo: [{
            symbol: {
              type: "text",
              color: "white",
              font: {
                size: 12,
                weight: "bold"
              }
            },
            labelPlacement: "above-center",
            labelExpressionInfo: {
              expression: "$feature.value"
            }
          }]
        })
      })
    })

  },
  methods: {
    // 管网数据的icon显示
    pipeSelBtn(index) {
      if (index === 0) {
        // 雨水管线
        if (this.rainPipeShow) {
          // this.map.remove(this.rainPipeArr);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#c7c7c6";
          this.rainPipeShow = false;
        } else {
          // this.map.add(this.rainPipeArr);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#2bfbd9";
          this.rainPipeShow = true;
        }
      } else if (index === 1) {
        // 污水管线
        if (this.sewagePipeShow) {
          // this.map.remove(this.sewagePipe);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#c7c7c6";
          this.sewagePipeShow = false;
        } else {
          // this.map.add(this.sewagePipe);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#2bfbd9";
          this.sewagePipeShow = true;
        }
      } else if (index === 2) {
        // 市政管网
        if (this.coverIconShow) {
          this.coverGraphicArr.forEach(item => {
            this.graphicsLayer.remove(item);
          });
          this.$refs["pipe0" + index][0].style.backgroundColor = "#c7c7c6";
          this.coverIconShow = false;
        } else {
          this.coverGraphicArr.forEach(item => {
            this.graphicsLayer.add(item);
          });
          this.$refs["pipe0" + index][0].style.backgroundColor = "#2bfbd9";
          this.coverIconShow = true;
        }
      }
    },
    // 防汛防台的icon显示
    fxftSelBtn(index) {
      if (index === 0) {
        // 风险部位
        if (this.fxbwShow) {
          this.map.remove(this.liquidLevelDeviceAreaFeatureLayer)
          this.map.remove(this.liquidLevelDeviceAreaPointFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fxbwShow = false;
        } else {
          this.map.add(this.liquidLevelDeviceAreaFeatureLayer)
          this.map.add(this.liquidLevelDeviceAreaPointFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fxbwShow = true;
        }
      } else if (index === 1) {
        // 液位超限
        if (this.ywcxShow) {
          this.map.remove(this.liquidLevelDeviceFeatureLayer)
          this.map.remove(this.liquidLevelDeviceOfflineFeatureLayer)
          this.map.remove(this.liquidLevelDeviceAlarmFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.ywcxShow = false;
        } else {
          this.map.add(this.liquidLevelDeviceFeatureLayer)
          this.map.add(this.liquidLevelDeviceOfflineFeatureLayer)
          this.map.add(this.liquidLevelDeviceAlarmFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.ywcxShow = true;
        }
      } else if (index === 2) {
        // 防汛重点部位监控
        if (this.fxzdbwjkShow) {
          this.map.remove(this.fxzdbwCameraFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fxzdbwjkShow = false;
        } else {
          this.map.add(this.fxzdbwCameraFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fxzdbwjkShow = true;
        }
      } else if (index === 3) {
        // 管网走向图
        if (this.gwzxtShow) {
          this.map.remove(this.pumpStationPolygonFeatureLayer)
          this.map.remove(this.pumpStationFeatureLayer1)
          this.map.remove(this.pumpStationPolylineFeatureLayer)
          this.map.remove(this.pumpStationPointFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.gwzxtShow = false;
        } else {
          this.map.add(this.pumpStationPolygonFeatureLayer)
          this.map.add(this.pumpStationFeatureLayer1)
          this.map.add(this.pumpStationPolylineFeatureLayer)
          this.map.add(this.pumpStationPointFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.gwzxtShow = true;
        }
      } else if (index === 4) {
        // 市政管网
        if (this.szgwShow) {
          this.map.remove(this.coverFeatureLayer)
          this.map.remove(this.ysFeatureLayer);
          this.map.remove(this.wsFeatureLayer);
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.szgwShow = false;
        } else {
          this.map.add(this.coverFeatureLayer)
          this.map.add(this.ysFeatureLayer);
          this.map.add(this.wsFeatureLayer);
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.szgwShow = true;
        }
      } else if (index === 5) {
        // 防汛物资储备信息
        if (this.fxwzcbxxShow) {
          this.map.remove(this.floodReserveInfoFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fxwzcbxxShow = false;
        } else {
          this.map.add(this.floodReserveInfoFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fxwzcbxxShow = true;
        }
      } else if (index === 6) {
        // 防汛安置点信息
        if (this.fxazdxxShow) {
          this.map.remove(this.floodShelterInfoFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fxazdxxShow = false;
        } else {
          this.map.add(this.floodShelterInfoFeatureLayer)
          this.$refs["fxft0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fxazdxxShow = true;
        }
      }
    },
    checkPipeNetwork(value) {
      if (value === true) {
        this.map.add(this.coverFeatureLayer)
        this.map.add(this.pumpStationFeatureLayer)
        this.map.add(this.pumpStationPolygonFeatureLayer)
        this.map.add(this.pumpStationFeatureLayer1)
        this.map.add(this.pumpStationPolylineFeatureLayer)
        this.map.add(this.pumpStationPointFeatureLayer)
        // this.map.add(this.liquidLevelDeviceAreaFeatureLayer)
        // this.map.add(this.liquidLevelDeviceAreaPointFeatureLayer)
        this.map.add(this.liquidLevelDeviceFeatureLayer)
        this.map.add(this.liquidLevelDeviceOfflineFeatureLayer)
        this.map.add(this.liquidLevelDeviceAlarmFeatureLayer)
        // this.map.add(this.fxzdbwCameraFeatureLayer)
      } else if (value === false) {
        this.map.remove(this.coverFeatureLayer)
        this.map.remove(this.pumpStationFeatureLayer)
        this.map.remove(this.pumpStationPolygonFeatureLayer)
        this.map.remove(this.pumpStationFeatureLayer1)
        this.map.remove(this.pumpStationPolylineFeatureLayer)
        this.map.remove(this.pumpStationPointFeatureLayer)
        // this.map.remove(this.liquidLevelDeviceAreaFeatureLayer)
        // this.map.remove(this.liquidLevelDeviceAreaPointFeatureLayer)
        this.map.remove(this.liquidLevelDeviceFeatureLayer)
        this.map.remove(this.liquidLevelDeviceOfflineFeatureLayer)
        this.map.remove(this.liquidLevelDeviceAlarmFeatureLayer)
        // this.map.remove(this.fxzdbwCameraFeatureLayer)
      } else {
        const pipeNetworkOptions = ['市政管网', '排涝泵站', '排污泵站', '液位告警'];
        let a = new Set(value);
        let b = new Set(pipeNetworkOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '市政管网') {
            this.map.remove(this.coverFeatureLayer)
            this.map.remove(this.ysFeatureLayer);
            this.map.remove(this.wsFeatureLayer);
          } else if (item === '排涝泵站') {
            this.map.remove(this.pumpStationFeatureLayer)
          } else if (item === '排污泵站') {
            this.map.remove(this.pumpStationPolygonFeatureLayer)
            this.map.remove(this.pumpStationFeatureLayer1)
            this.map.remove(this.pumpStationPolylineFeatureLayer)
            this.map.remove(this.pumpStationPointFeatureLayer)
          } else if (item === '液位告警') {
            // this.map.remove(this.liquidLevelDeviceAreaFeatureLayer)
            // this.map.remove(this.liquidLevelDeviceAreaPointFeatureLayer)
            this.map.remove(this.liquidLevelDeviceFeatureLayer)
            this.map.remove(this.liquidLevelDeviceOfflineFeatureLayer)
            this.map.remove(this.liquidLevelDeviceAlarmFeatureLayer)
            // this.map.remove(this.fxzdbwCameraFeatureLayer)
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '市政管网') {
              this.map.add(this.coverFeatureLayer)
              this.map.add(this.ysFeatureLayer);
              this.map.add(this.wsFeatureLayer);
            } else if (item === '排涝泵站') {
              this.map.add(this.pumpStationFeatureLayer)
            } else if (item === '排污泵站') {
              this.map.add(this.pumpStationPolygonFeatureLayer)
              this.map.add(this.pumpStationFeatureLayer1)
              this.map.add(this.pumpStationPolylineFeatureLayer)
              this.map.add(this.pumpStationPointFeatureLayer)
            } else if (item === '液位告警') {
              // this.map.add(this.liquidLevelDeviceAreaFeatureLayer)
              // this.map.add(this.liquidLevelDeviceAreaPointFeatureLayer)
              this.map.add(this.liquidLevelDeviceFeatureLayer)
              this.map.add(this.liquidLevelDeviceOfflineFeatureLayer)
              this.map.add(this.liquidLevelDeviceAlarmFeatureLayer)
              // this.map.add(this.fxzdbwCameraFeatureLayer)
            }
          }
        }
      }
    },
    checkRiver(value) {
      if (value === true) {
        this.map.add(this.hedaoFeatureLayer0)
        this.map.add(this.hedaoFeatureLayer1)
        this.map.add(this.hedaoFeatureLayer6)
      } else if (value === false) {
        this.map.remove(this.hedaoFeatureLayer0)
        this.map.remove(this.hedaoFeatureLayer1)
        this.map.remove(this.hedaoFeatureLayer6)
      } else {
        const riverOptions = ['河道'];
        let a = new Set(value);
        let b = new Set(riverOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '河道') {
            this.map.remove(this.hedaoFeatureLayer0)
            this.map.remove(this.hedaoFeatureLayer1)
            this.map.remove(this.hedaoFeatureLayer6)
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '河道') {
              this.map.add(this.hedaoFeatureLayer0)
              this.map.add(this.hedaoFeatureLayer1)
              this.map.add(this.hedaoFeatureLayer6)
            }
          }
        }
      }
    },
    checkHdByType(type) {
      if (type === '河道总数') {
        this.map.add(this.hedaoFeatureLayer0)
        this.map.add(this.hedaoFeatureLayer1)
        this.map.add(this.hedaoFeatureLayer6)
      } else if (type === '区管河道') {
        this.map.add(this.hedaoFeatureLayer2)
      } else if (type === '镇管河道') {
        this.map.add(this.hedaoFeatureLayer3)
      } else if (type === '村管河道') {
        this.map.add(this.hedaoFeatureLayer4)
      } else if (type === '其它河道') {
        this.map.add(this.hedaoFeatureLayer5)
      } else if (type === '股份公司河道') {
        this.map.add(this.hedaoFeatureLayer6)
      }
    },
    addFxftFeatureLayer() {
      this.map.add(this.liquidLevelDeviceAreaFeatureLayer)
      this.map.add(this.liquidLevelDeviceAreaPointFeatureLayer)
      this.map.add(this.liquidLevelDeviceFeatureLayer)
      this.map.add(this.liquidLevelDeviceOfflineFeatureLayer)
      this.map.add(this.liquidLevelDeviceAlarmFeatureLayer)
      // this.map.add(this.fxzdbwCameraFeatureLayer)
      this.map.add(this.sensorDeviceFeatureLayer)
    },
    addWlgzFeatureLayer() {
      this.map.add(this.iccDeviceFeatureLayer)
    },
    async addPumpStationAreaLayer(ids) {
      this.map.remove(this.pumpStationAreaPointFeatureLayer)
      this.map.remove(this.pumpStationAreaPolylineFeatureLayer)
      this.map.remove(this.pumpStationAreaPolygonFeatureLayer)

      // 判断ids是否为空，为空则return
      if (!ids) {
        return
      }

      await listPumpStationArea(ids).then(response => {
          const pumpStations = response.data
          loadModules([
              "esri/Graphic",
              "esri/geometry/Point",
              "esri/geometry/Polyline",
              "esri/geometry/Polygon",
              "esri/geometry/SpatialReference",
              "esri/layers/FeatureLayer",
            ],
            this.options).then(([
                                  Graphic,
                                  Point,
                                  Polyline,
                                  Polygon,
                                  SpatialReference,
                                  FeatureLayer,
                                ]) => {
            let pumpStationPointGraphicArr = []
            let pumpStationPolylineGraphicArr = []
            let pumpStationPolygonGraphicArr = []

            let pumpStationRenderer = {
              type: "unique-value",
              field: "pumpStationName",
              defaultSymbol: {
                type: "simple-fill",
                color: [112, 178, 226, 0.2],
                outline: {
                  color: [112, 178, 226],
                  width: 2
                }
              },
              uniqueValueInfos: []
            }

            let pumpStationPolylineRenderer = {
              type: "unique-value",
              field: "pumpStationType",
              defaultSymbol: {
                type: "simple-line",
                color: [226, 119, 40],
                width: 4,
                style: "short-dash",
                marker: {
                  color: [226, 119, 40],
                  placement: "end",
                  style: "arrow"
                }
              },
              uniqueValueInfos: []
            }

            pumpStations.forEach(pumpStation => {
              const attributes = JSON.parse(JSON.stringify(pumpStation))
              if (pumpStation.coordinate) {
                if (pumpStation.type === "polygon") {
                  const fillColor = pumpStation.fillColor ? pumpStation.fillColor.split(",") : [112, 178, 226, 0.2]
                  const outlineColor = pumpStation.outlineColor ? pumpStation.outlineColor.split(",") : [112, 178, 226]
                  let uniqueValueInfo = {
                    value: pumpStation.pumpStationName,
                    symbol: {
                      type: "simple-fill",
                      color: fillColor,
                      outline: {
                        color: outlineColor,
                        width: 2
                      }
                    }
                  }
                  pumpStationRenderer.uniqueValueInfos.push(uniqueValueInfo)
                  const rings = parseCoordinate(pumpStation.coordinate);
                  const polygon = new Polygon({
                    rings: rings,
                    spatialReference: SpatialReference.WebMercator,
                  });
                  const graphic = new Graphic({
                    geometry: polygon,
                    attributes: attributes,
                  })
                  pumpStationPolygonGraphicArr.push(graphic)
                } else if (pumpStation.type === "point") {
                  const p = pumpStation.coordinate.split(",");
                  const point = new Point({
                    x: p[0],
                    y: p[1],
                    spatialReference: SpatialReference.WebMercator,
                  });
                  const graphic = new Graphic({
                    geometry: point,
                    attributes: attributes,
                  });
                  pumpStationPointGraphicArr.push(graphic)
                } else if (pumpStation.type === "polyline") {
                  if (pumpStation.pumpStationType === "入口") {
                    let polylineUniqueValueInfo = {
                      value: pumpStation.pumpStationType,
                      symbol: {
                        type: "simple-line",
                        color: [52, 125, 244],
                        width: 4,
                        style: "short-dash",
                        marker: {
                          color: [52, 125, 244],
                          placement: "end",
                          style: "arrow"
                        }
                      }
                    }
                    pumpStationPolylineRenderer.uniqueValueInfos.push(polylineUniqueValueInfo)
                  }

                  const paths = parseCoordinate(pumpStation.coordinate)
                  const polyline = new Polyline({
                    paths: paths,
                    spatialReference: SpatialReference.WebMercator,
                  });
                  const graphic = new Graphic({
                    geometry: polyline,
                    attributes: attributes,
                  });
                  pumpStationPolylineGraphicArr.push(graphic)
                }
              }
            })
            const fields = [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "pumpStationName",
                alias: "pumpStationName",
                type: "string"
              },
              {
                name: "pumpStationType",
                alias: "pumpStationType",
                type: "string"
              }
            ]

            this.pumpStationAreaPointFeatureLayer = new FeatureLayer({
              source: pumpStationPointGraphicArr,
              fields: fields,
              renderer: {
                type: "simple",
                symbol: {
                  type: "picture-marker",
                  url: "http://**************/images/icon/guanwang_icon_pwbz.png",
                  width: "29px",
                  height: "37px"
                }
              },
              outFields: ["*"],
              labelingInfo: [{
                symbol: {
                  type: "text",
                  horizontalAlignment: "left",
                  color: "white",
                  font: {
                    size: 20,
                    weight: "bold"
                  }
                },
                labelPlacement: "center-right",
                labelExpressionInfo: {
                  expression: "$feature.pumpStationName"
                }
              }]
            })

            this.pumpStationAreaPolylineFeatureLayer = new FeatureLayer({
              source: pumpStationPolylineGraphicArr,
              fields: fields,
              renderer: pumpStationPolylineRenderer,
              outFields: ["*"],
            })

            this.pumpStationAreaPolygonFeatureLayer = new FeatureLayer({
              source: pumpStationPolygonGraphicArr,
              fields: fields,
              renderer: pumpStationRenderer,
              outFields: ["*"],
              labelingInfo: [{
                symbol: {
                  type: "text",
                  color: "white",
                  font: {
                    size: 8,
                    weight: "bold"
                  }
                },
                labelPlacement: "always-horizontal",
                labelExpressionInfo: {
                  expression: "$feature.pumpStationName"
                }
              }]
            })
          })
        });
      this.map.add(this.pumpStationAreaPointFeatureLayer)
      this.map.add(this.pumpStationAreaPolylineFeatureLayer)
      this.map.add(this.pumpStationAreaPolygonFeatureLayer)
    }
  }
}

</script>
