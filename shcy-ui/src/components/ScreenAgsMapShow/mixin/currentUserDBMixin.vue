<template>

</template>

<script>

import {
  listScreenShop,
  listScreenPropertyContract,
  listScreenEntertainment,
  listCameras,
  listScreenApartmentRental,
} from "@/api/shcy/screen";
import {loadModules} from "esri-loader";

const clusterConfig = {
  type: "cluster",
  clusterRadius: "160px",
  popupTemplate: {
    title: "聚合详情",
    content: "{cluster_count}个地图要素",
    fieldInfos: [
      {
        fieldName: "cluster_count",
        format: {
          places: 0,
          digitSeparator: true
        }
      }
    ]
  },
  clusterMinSize: "37px",
  clusterMaxSize: "74px",
  labelingInfo: [
    {
      deconflictionStrategy: "none",
      labelExpressionInfo: {
        expression: "Text($feature.cluster_count, '#,###')"
      },
      symbol: {
        type: "text",
        color: "white",
        font: {
          weight: "bold",
          family: "Noto Sans",
          size: "28px"
        }
      },
      labelPlacement: "above-center"
    }
  ]
};

export default {
  name: 'currentUserDBMixin',
  data() {
    return {
      // 沿街商铺
      shopList: [],
      shopGraphicArr: [],
      shopFeatureLayer: null,
      shopData: {},
      checkLogData:[],
      isShopWindowShow: false,

      // 非住宅物业
      propertyContractList: [],
      propertyContractGraphicArr: [],
      propertyContractFeatureLayer: null,
      propertyContractData:{},
      isPropertyContractWindowShow:false,

      // 文化娱乐场所
      entertainmentList: [],
      entertainmentGraphicArr: [],
      entertainmentFeatureLayer: null,
      entertainmentData:{},
      isEntertainmentWindowShow:false,

      // 监控资源
      cameraList: [],
      cameraGraphicArr: [],
      cameraFeatureLayer: null,
      cameraIndexCode: '',
      isCameraWindowShow: false,

      // 公寓租赁房
      apartmentRentalList: [],
      apartmentRentalGraphicArr: [],
      apartmentRentalFeatureLayer: null,
      apartmentRentalData:{},
      isApartmentRentalWindowShow:false,

    }
  },
  mounted() {

    // 沿街商铺
    listScreenShop(this.queryParams).then(response => {
      this.shopList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.shopList.forEach(shop => {
          shop.graphicType = 'shop'
          const attributes = JSON.parse(JSON.stringify(shop))
          if (shop.type === "point" && shop.coordinate) {
            const p = shop.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const shopGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.shopGraphicArr.push(shopGraphic)
          }
        })
        this.shopFeatureLayer = new FeatureLayer({
          source: this.shopGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "shopName",
              alias: "shopName",
              type: "string"
            },
            {
              name: "shopAddress",
              alias: "shopAddress",
              type: "string"
            },
            {
              name: "shopLicense",
              alias: "shopLicense",
              type: "string"
            },
            {
              name: "shopCreditCode",
              alias: "shopCreditCode",
              type: "string"
            },
            {
              name: "shopRegisterAddress",
              alias: "shopRegisterAddress",
              type: "string"
            },
            {
              name: "shopOperatingAddress",
              alias: "shopOperatingAddress",
              type: "string"
            },
            {
              name: "shopCommittee",
              alias: "shopCommittee",
              type: "string"
            },
            {
              name: "shopCategory",
              alias: "shopCategory",
              type: "string"
            },
            {
              name: "shopSubcategory",
              alias: "shopSubcategory",
              type: "string"
            },
            {
              name: "shopLittlecategory",
              alias: "shopLittlecategory",
              type: "string"
            },
            {
              name: "shopContract",
              alias: "shopContract",
              type: "string"
            },
            {
              name: "shopContactPhone",
              alias: "shopContactPhone",
              type: "string"
            },
            {
              name: "shopHouseOwnership",
              alias: "shopHouseOwnership",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_yanjieshangpu.png",
              width: "29px",
              height: "37px"
            }
          },
          featureReduction: clusterConfig,
          popupTemplate: {
            title: "{shopName}",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "shopName",
                  label: "商铺名称"
                },
                {
                  fieldName: "shopLicense",
                  label: "营业执照"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }
        })
      })
    })

    // 非住宅物业
    listScreenPropertyContract(this.queryParams).then(response => {
      this.propertyContractList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.propertyContractList.forEach(propertyContract => {
          propertyContract.graphicType = 'propertyContract'
          const attributes = JSON.parse(JSON.stringify(propertyContract))
          if (propertyContract.type === "point" && propertyContract.coordinate) {
            const p = propertyContract.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const propertyContractGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.propertyContractGraphicArr.push(propertyContractGraphic)
          }
        })
        this.propertyContractFeatureLayer = new FeatureLayer({
          source: this.propertyContractGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "propertyServicePosition",
              alias: "propertyServicePosition",
              type: "string"
            },
            {
              name: "propertyCompany",
              alias: "propertyCompany",
              type: "string"
            },
            {
              name: "address",
              alias: "address",
              type: "string"
            },
            {
              name: "contact",
              alias: "contact",
              type: "string"
            },
            {
              name: "contactPhone",
              alias: "contactPhone",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_feizhuzhaiwuye.png",
              width: "28px",
              height: "37px"
            }
          },
          popupTemplate: {
            title: "非住宅物业",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "propertyServicePosition",
                  label: "物业服务点"
                },
                {
                  fieldName: "propertyCompany",
                  label: "物业服务公司"
                },
                // {
                //   fieldName: "address",
                //   label: "地址"
                // },
                // {
                //   fieldName: "contact",
                //   label: "联系人"
                // },
                // {
                //   fieldName: "contactPhone",
                //   label: "联系电话"
                // },
              ]
            }],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }

        })
      })
    })

    // 文化娱乐场所
    listScreenEntertainment(this.queryParams).then(response => {
      this.entertainmentList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.entertainmentList.forEach(entertainment => {
          entertainment.graphicType = 'entertainment'
          const attributes = JSON.parse(JSON.stringify(entertainment))
          if (entertainment.type === "point" && entertainment.coordinate) {
            const p = entertainment.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const entertainmentGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.entertainmentGraphicArr.push(entertainmentGraphic)
          }
        })
        this.entertainmentFeatureLayer = new FeatureLayer({
          source: this.entertainmentGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "licenseName",
              alias: "licenseName",
              type: "string"
            },
            {
              name: "operateName",
              alias: "operateName",
              type: "string"
            },
            {
              name: "legalPerson",
              alias: "legalPerson",
              type: "string"
            },
            {
              name: "operateAddress",
              alias: "operateAddress",
              type: "string"
            },
            {
              name: "contactPhone",
              alias: "contactPhone",
              type: "string"
            },
            {
              name: "remark",
              alias: "remark",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_wenhuayule.png",
              width: "29px",
              height: "37px"
            }
          },

          popupTemplate: {
            title: "文化娱乐场所",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "licenseName",
                  label: "营业执照名称"
                },
                {
                  fieldName: "operateName",
                  label: "对外经营名称"
                },
                // {
                //   fieldName: "legalPerson",
                //   label: "法人"
                // },
                // {
                //   fieldName: "operateAddress",
                //   label: "经营地址"
                // },
                // {
                //   fieldName: "contactPhone",
                //   label: "联系方式"
                // },
              ]
            }],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }

        })
      })
    })

    // 监控资源
    listCameras(this.queryParams).then(response => {
      this.cameraList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.cameraList.forEach(camera => {
          const attributes = JSON.parse(JSON.stringify(camera))
          if (camera.type === "point" && camera.coordinate) {
            const p = camera.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const cameraGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.cameraGraphicArr.push(cameraGraphic)
          }
        })
        this.cameraFeatureLayer = new FeatureLayer({
          source: this.cameraGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "cameraIndexCode",
              alias: "cameraIndexCode",
              type: "string"
            },
            {
              name: "name",
              alias: "name",
              type: "string"
            },
            {
              name: "cameraTypeName",
              alias: "cameraTypeName",
              type: "string"
            },
            {
              name: "positionName",
              alias: "positionName",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_jiankong.png",
              width: "29px",
              height: "37px"
            }
          },
          featureReduction: clusterConfig,
          popupTemplate: {
            title: "{name}",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "cameraIndexCode",
                  label: "监控点编号"
                },
                {
                  fieldName: "name",
                  label: "监控点名称"
                },
                {
                  fieldName: "cameraTypeName",
                  label: "监控点类型"
                },
                {
                  fieldName: "positionName",
                  label: "监控点位置"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "播放",
                id: "popup-play",
                image: "http://**************/images/icon/play-button.png",
              }
            ]
          }
        })
      })
    })

    // 公寓租赁房
    listScreenApartmentRental(this.queryParams).then(response => {
      this.apartmentRentalList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.apartmentRentalList.forEach(apartmentRental => {
          apartmentRental.graphicType = 'apartmentRental'
          const attributes = JSON.parse(JSON.stringify(apartmentRental))
          if (apartmentRental.type === "point" && apartmentRental.coordinate) {
            const p = apartmentRental.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const apartmentRentalGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.apartmentRentalGraphicArr.push(apartmentRentalGraphic)
          }
        })
        this.apartmentRentalFeatureLayer = new FeatureLayer({
          source: this.apartmentRentalGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "graphicType",
            alias: "graphicType",
            type: "string"
          },
          {
            name: "name",
            alias: "name",
            type: "string"
          },
          {
            name: "licenseName",
            alias: "licenseName",
            type: "string"
          },
          {
            name: "contacts",
            alias: "contacts",
            type: "string"
          },
          {
            name: "contactNumber",
            alias: "contactNumber",
            type: "string"
          },
          {
            name: "detailAddress",
            alias: "detailAddress",
            type: "string"
          },
          {
            name: "roomNumber",
            alias: "roomNumber",
            type: "string"
          },
          {
            name: "employeeNumber",
            alias: "employeeNumber",
            type: "string"
          }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xianzhaung_icon_gongyuzulingfang.png",
              width: "29px",
              height: "37px"
            }
          },
          popupTemplate: {
            title: "公寓租赁房",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "name",
                  label: "租赁房名称"
                },
                {
                  fieldName: "licenseName",
                  label: "营业执照名称"
                },
              ]
            }],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }

        })
      })
    })


  },
  methods: {
    // 现状使用主体的icon显示（弃用）
    currentUserSelBtn(index) {
      if (index === 0) {
        // 监控
        if (this.monitorIconShow) {
          // 已显示-移除图标
          // this.map.remove(this.controlArr);
          // 已显示-修改为未选中样式
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          // 修改为未选中状态
          this.monitorIconShow = false;
        } else {
          // 未显示-添加图标
          // this.map.add(this.controlArr);
          // 未显示-修改为选中样式
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          // 修改为选中状态
          this.monitorIconShow = true;
        }
      } else if (index === 1) {
        // 沿街商铺
        if (this.streetShopIconShow) {
          // this.map.remove(this.streetShopArr);
          // this.map.remove(this.gather_num_arr);
          // this.map.remove(this.gether_big_num_arr);
          this.map.remove(this.shopFeatureLayer)
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.streetShopIconShow = false;
        } else {
          // if (this.map.getZoom() <= 18.5) {
          //   this.map.add(this.gether_big_num_arr);
          // } else {
          //   this.map.add(this.gather_num_arr);
          // }
          // this.map.add(this.streetShopArr);
          this.map.add(this.shopFeatureLayer)
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.streetShopIconShow = true;
        }
      } else if (index === 2) {
        // 宾旅馆
        if (this.hotelIconShow) {
          // this.map.remove(this.hotelArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.hotelIconShow = false;
        } else {
          // this.map.add(this.hotelArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.hotelIconShow = true;
        }
      } else if (index === 3) {
        // 商业网点
        if (this.networkIconShow) {
          // this.map.remove(this.networkArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.networkIconShow = false;
        } else {
          // this.map.add(this.networkArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.networkIconShow = true;
        }
      } else if (index === 4) {
        // 农贸市场
        if (this.farmMarketIconShow) {
          // this.map.remove(this.farmMarketArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.farmMarketIconShow = false;
        } else {
          // this.map.add(this.farmMarketArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.farmMarketIconShow = true;
        }
      } else if (index === 5) {
        // 学校
        if (this.schoolIconShow) {
          // this.map.remove(this.schoolArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.schoolIconShow = false;
        } else {
          // this.map.add(this.schoolArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.schoolIconShow = true;
        }
      } else if (index === 6) {
        // 养老机构
        if (this.nursingIconShow) {
          // this.map.remove(this.nursingArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.nursingIconShow = false;
        } else {
          // this.map.add(this.nursingArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.nursingIconShow = true;
        }
      } else if (index === 7) {
        // 宗教场所
        if (this.religionIconShow) {
          // this.map.remove(this.religionArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.religionIconShow = false;
        } else {
          // this.map.add(this.religionArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.religionIconShow = true;
        }
      } else if (index === 8) {
        // 隔离酒店
        if (this.isolateHoteIconShow) {
          // this.map.remove(this.isolateHoteArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.isolateHoteIconShow = false;
        } else {
          // this.map.add(this.isolateHoteArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.isolateHoteIconShow = true;
        }
      }
    },
    // 标识指示现状使用主体选择
    checkCurrentUser(value) {
      if (value === true) {
        this.map.add(this.shopFeatureLayer)
        this.map.add(this.propertyContractFeatureLayer)
        this.map.add(this.entertainmentFeatureLayer)
        this.map.add(this.apartmentRentalFeatureLayer)
      } else if (value === false) {
        this.map.remove(this.shopFeatureLayer)
        this.map.remove(this.propertyContractFeatureLayer)
        this.map.remove(this.entertainmentFeatureLayer)
        this.map.remove(this.apartmentRentalFeatureLayer)
      } else {
        // const currentUserOptions = ['沿街商铺', '非住宅物业', '文化娱乐场所', '商业网点', '农贸市场'];
        const currentUserOptions = ['沿街商铺', '非住宅物业', '文化娱乐场所', '商业网点', '公寓租赁房'];
        let a = new Set(value);
        let b = new Set(currentUserOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '沿街商铺') {
            this.map.remove(this.shopFeatureLayer)
          } else if (item === '非住宅物业') {
            this.map.remove(this.propertyContractFeatureLayer)
          } else if (item === '文化娱乐场所') {
            this.map.remove(this.entertainmentFeatureLayer)
          } else if (item === '公寓租赁房') {
            this.map.remove(this.apartmentRentalFeatureLayer)
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '沿街商铺') {
              this.map.add(this.shopFeatureLayer)
            } else if (item === '非住宅物业') {
              this.map.add(this.propertyContractFeatureLayer)
            } else if (item === '文化娱乐场所') {
              this.map.add(this.entertainmentFeatureLayer)
            } else if (item === '公寓租赁房') {
              this.map.add(this.apartmentRentalFeatureLayer)
            }
          }
        }
      }
    },
    checkApartmentRental() {
      this.map.add(this.apartmentRentalFeatureLayer)
    },
    // 查找沿街商铺
    searchShop(keyword) {
      loadModules([
          "esri/Graphic",
        ],
        this.options).then(([
                              Graphic,
                            ]) => {

        let query = this.shopFeatureLayer.createQuery();
        query.where = "shopName like '%" + keyword + "%'";
        query.outFields = ["*"];
        query.returnGeometry = true;
        const _this = this
        this.shopFeatureLayer.queryFeatures(query)
          .then(function(result){
            if ( result.features.length > 0) {
              result.features.forEach(function (feature) {
                const g = new Graphic({
                  geometry: feature.geometry,
                  attributes: feature.attributes,
                  symbol: {
                    type: "picture-marker",
                    url: "http://**************/images/icon/xianzhaung_icon_yanjieshangpu.png",
                    width: "29px",
                    height: "37px"
                  },
                });
                _this.graphicsLayer.add(g)
              });
            }
          });
      })

    },
    // 搜索视频监控图形
    searchCameraGraphic(searchType, keyword) {
      loadModules([
          "esri/Graphic",
        ],
        this.options).then(([
                              Graphic,
                            ]) => {

        let query = this.cameraFeatureLayer.createQuery();
        if (searchType === '全部') {
          if (keyword) {
            query.where = "positionName like '%" + keyword + "%'";
          }
        } else {
          if (keyword) {
            query.where = "cameraTypeName like '%" + searchType + "%' and positionName like '%" + keyword + "%'";
          } else {
            query.where = "cameraTypeName like '%" + searchType + "%'";
          }
        }
        query.outFields = ["*"];
        query.returnGeometry = true;
        const _this = this
        this.cameraFeatureLayer.queryFeatures(query)
          .then(function(result){
            if ( result.features.length > 0) {
              result.features.forEach(function (feature) {
                const g = new Graphic({
                  geometry: feature.geometry,
                  attributes: feature.attributes,
                  symbol: {
                    type: "picture-marker",
                    url: "http://**************/images/icon/xianzhaung_icon_jiankong.png",
                    width: "29px",
                    height: "37px"
                  },
                  popupTemplate: {
                    title: "{name}",
                    content: [{
                      type: "fields",
                      fieldInfos: [
                        {
                          fieldName: "cameraIndexCode",
                          label: "监控点编号"
                        },
                        {
                          fieldName: "name",
                          label: "监控点名称"
                        },
                        {
                          fieldName: "cameraTypeName",
                          label: "监控点类型"
                        },
                        {
                          fieldName: "positionName",
                          label: "监控点位置"
                        },
                      ]
                    }],
                    outFields: ["*"],
                    actions: [
                      {
                        title: "播放",
                        id: "popup-play",
                        image: "http://**************/images/icon/play-button.png",
                      }
                    ]
                  }
                });
                _this.graphicsLayer.add(g)
              });
            }
          });
      })

    },
  }
}

</script>
