<template>

</template>

<script>

import {
   listScreenGreenGrid,
} from "@/api/shcy/screen";

import {loadModules} from "esri-loader";
import {parseCoordinate} from "@/utils/coordinate";

const renderer1 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [88, 240, 68, 0.4],
    outline: {
      color: [88, 240, 68, 1],
      width: 1
    }
  }
};

const renderer2 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [42, 204, 95, 0.4],
    outline: {
      color: [42, 204, 95, 1],
      width: 1
    }
  }
};

const renderer3 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [0, 143, 60, 0.4],
    outline: {
      color: [0, 143, 60, 1],
      width: 1
    }
  }
};

const renderer4 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [31, 162, 49, 0.4],
    outline: {
      color: [31, 162, 49, 1],
      width: 1
    }
  }
};

const renderer5 = {
  type: "simple",
  symbol: {
    type: "simple-fill",
    color: [65, 199, 18, 0.4],
    outline: {
      color: [65, 199, 18, 1],
      width: 1
    }
  }
};

export default {
  name: 'greeningDBMixin',
  data() {
    return {
      // 绿地要素层 Deprecated
      greenSpaceFeatureLayer: null,
      // 郊区行道树要素层 Deprecated
      suburbanStreetTreeFeatureLayer: null,
      // 商品房绿化要素层 Deprecated
      commercialHousingGreeningFeatureLayer: null,
      // 售后公房绿化要素层 Deprecated
      afterSalePublicHousingGreeningFeatureLayer: null,

      // Deprecated
      greenFeatureLayer0: null,
      greenFeatureLayer1: null,
      greenFeatureLayer4: null,
      greenFeatureLayer5: null,
      greenFeatureLayer9: null,
      greenFeatureLayer6: null,
      greenFeatureLayer7: null,
      greenFeatureLayer8: null,

      // 单位绿地
      danweilvdiFeatureLayer0: null,
      danweilvdiFeatureLayer1: null,
      danweilvdiFeatureLayer2: null,
      danweilvdiFeatureLayer3: null,

      // 公共绿地
      gonggonglvdiFeatureLayer0: null,
      gonggonglvdiFeatureLayer1: null,
      gonggonglvdiFeatureLayer2: null,
      gonggonglvdiFeatureLayer3: null,

      // 行道树
      xingdaoshuFeatureLayer0: null,
      xingdaoshuFeatureLayer1: null,
      xingdaoshuFeatureLayer2: null,
      xingdaoshuFeatureLayer3: null,
      xingdaoshuFeatureLayer4: null,

      // 商品房绿化
      shangpinfanglvhuaFeatureLayer0: null,
      shangpinfanglvhuaFeatureLayer1: null,

      // 售后公房绿化
      shouhougongfanglvhuaFeatureLayer0: null,

      // 绿化网格
      greenGridList: [],
      greenGridGraphicArr: [],
      greenGridFeatureLayer: null,

    }
  },
  mounted() {

    // Deprecated
    loadModules([
        "esri/layers/FeatureLayer",
      ],
      this.options).then(([
                            FeatureLayer,
                          ]) => {

      const popTemplate = {
          title: "绿化信息",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "id",
                label: "id"
              },
              {
                fieldName: "绿化大",
                label: "绿化大类"
              },
              {
                fieldName: "绿化中",
                label: "绿化中类"
              },
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        }

        // 绿地 0 + 1 + 2 + 4 + 5 + 9
      this.greenFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/0",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenSpaceFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/2",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer4 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/4",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer5 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/5",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer9 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/9",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      // 行道树 3 + 6 + 7 + 8
      this.suburbanStreetTreeFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/3",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer6 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/6",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer7 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/7",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      this.greenFeatureLayer8 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js1/FeatureServer/8",
        outFields: ["*"],
        popupTemplate: popTemplate,
      });

      const popTemplate1 = {
        title: "绿化主体",
        content: [{
          type: "fields",
          fieldInfos: [
            {
              fieldName: "小区名",
              label: "小区名称"
            },
            {
              fieldName: "房屋类",
              label: "房屋类型"
            },
            {
              fieldName: "绿化大",
              label: "绿化大类"
            },
            {
              fieldName: "绿化中",
              label: "绿化中类"
            },
            {
              fieldName: "面积",
              label: "面积"
            },
          ]
        }]
      }
      this.commercialHousingGreeningFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js3/FeatureServer/0",
        outFields: ["*"],
        popupTemplate: popTemplate1,
      });

      this.afterSalePublicHousingGreeningFeatureLayer = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js3/FeatureServer/1",
        outFields: ["*"],
        popupTemplate: popTemplate1,
      });


    })

    loadModules([
        "esri/layers/FeatureLayer",
      ],
      this.options).then(([
                            FeatureLayer,
                          ]) => {

      // 单位绿地 屋顶绿化
      this.danweilvdiFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/danweilvdi/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: {
          title: "单位绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "shape_leng",
                label: "shape_leng"
              },
              {
                fieldName: "变化情",
                label: "变化情"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
              {
                fieldName: "绿地类",
                label: "绿地类"
              },
              {
                fieldName: "绿化类",
                label: "绿化类"
              },
              {
                fieldName: "绿化大",
                label: "绿化大"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });

      // 单位绿地 绿地
      this.danweilvdiFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/danweilvdi/FeatureServer/1",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: {
          title: "单位绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "shape_leng",
                label: "shape_leng"
              },
              {
                fieldName: "面积",
                label: "面积"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
              {
                fieldName: "绿化大",
                label: "绿化大"
              },
              {
                fieldName: "绿化中",
                label: "绿化中"
              },
              {
                fieldName: "区域划",
                label: "区域划"
              },
            ]
          }]
        },
      });

      // 单位绿地 建成区屋顶绿化
      this.danweilvdiFeatureLayer2 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/danweilvdi/FeatureServer/2",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: {
          title: "单位绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
            ]
          }]
        },
      });

      // 单位绿地 建成区绿地
      this.danweilvdiFeatureLayer3 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/danweilvdi/FeatureServer/3",
        outFields: ["*"],
        renderer: renderer1,
        popupTemplate: {
          title: "单位绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "所属小",
                label: "所属小"
              },
              {
                fieldName: "面积",
                label: "面积"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
              {
                fieldName: "绿化大",
                label: "绿化大"
              },
              {
                fieldName: "绿化中",
                label: "绿化中"
              },
            ]
          }]
        },
      });

      // 行道树 石化街道建成区行道树
      this.xingdaoshuFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/xingdaoshu/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate: {
          title: "行道树",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "原区名",
                label: "原区名"
              },
              {
                fieldName: "原镇名",
                label: "原镇名"
              },
              {
                fieldName: "原行政",
                label: "原行政"
              },
            ]
          }]
        },
      });

      // 行道树 郊区行道树
      this.xingdaoshuFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/xingdaoshu/FeatureServer/1",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate: {
          title: "行道树",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
            ]
          }]
        },
      });

      // 行道树 建成区行道树与绿地重叠
      this.xingdaoshuFeatureLayer2 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/xingdaoshu/FeatureServer/2",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate: {
          title: "行道树",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
            ]
          }]
        },
      });

      // 行道树 行道树与绿地重叠
      this.xingdaoshuFeatureLayer3 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/xingdaoshu/FeatureServer/3",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate: {
          title: "行道树",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
              {
                fieldName: "区属",
                label: "区属"
              },
              {
                fieldName: "街道镇",
                label: "街道镇"
              },
            ]
          }]
        },
      });

      // 行道树 八匹马行道树
      this.xingdaoshuFeatureLayer4 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/bpmxingdaoshu/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer2,
        popupTemplate: {
          title: "行道树",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });

      // 公共绿地 有屋顶绿化的建筑
      this.gonggonglvdiFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/gonggonglvdi/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate: {
          title: "公共绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "建筑物",
                label: "建筑物"
              },
            ]
          }]
        },
      });


      // 公共绿地 建成区有屋顶绿化的建筑
      this.gonggonglvdiFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/gonggonglvdi/FeatureServer/1",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate: {
          title: "公共绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "建筑物",
                label: "建筑物"
              },
            ]
          }]
        },
      });


      // 公共绿地 绿地
      this.gonggonglvdiFeatureLayer2 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/gonggonglvdi/FeatureServer/2",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate: {
          title: "公共绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });


      // 公共绿地 建成区绿地
      this.gonggonglvdiFeatureLayer3 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/gonggonglvdi/FeatureServer/3",
        outFields: ["*"],
        renderer: renderer3,
        popupTemplate: {
          title: "公共绿地",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });



      // 商品房绿化
      this.shangpinfanglvhuaFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js3/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer4,
        popupTemplate: {
          title: "商品房绿化",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });



      // 商品房绿化 八匹马绿地
      this.shangpinfanglvhuaFeatureLayer1 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/bpmlvdi/FeatureServer/0",
        outFields: ["*"],
        renderer: renderer4,
        popupTemplate: {
          title: "商品房绿化",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });



      // 售后公房绿化
      this.shouhougongfanglvhuaFeatureLayer0 = new FeatureLayer({
        url:
          "http://12.112.123.249/server/geoscene/rest/services/js3/FeatureServer/1",
        outFields: ["*"],
        renderer: renderer5,
        popupTemplate: {
          title: "售后公房绿化",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "面积",
                label: "面积"
              },
            ]
          }]
        },
      });


    })


    // 绿化网格
    listScreenGreenGrid(this.queryParams).then(response => {
      this.greenGridList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let greenGridRenderer = {
          type: "unique-value",
          field: "committeeName",
          defaultSymbol: {
            type: "simple-fill",
            color: [112, 178, 226, 0.2],
            outline: {
              color: [112, 178, 226],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        this.greenGridList.forEach(greenGrid => {
          const fillColor = greenGrid.fillColor ? greenGrid.fillColor.split(",") : [112, 178, 226, 0.2]
          const outlineColor = greenGrid.outlineColor ? greenGrid.outlineColor.split(",") : [112, 178, 226]

          let uniqueValueInfo = {
            value: greenGrid.committeeName,
            symbol: {
              type: "simple-fill",
              color: fillColor,
              outline: {
                color: outlineColor,
                width: 2
              }
            }
          }
          greenGridRenderer.uniqueValueInfos.push(uniqueValueInfo)

          const attributes = JSON.parse(JSON.stringify(greenGrid))
          if (greenGrid.coordinate) {
            if (greenGrid.type === "polygon") {
              const r = parseCoordinate(greenGrid.coordinate);
              const polygon = new Polygon({
                rings: r,
                spatialReference: SpatialReference.WebMercator,
              });
              this.greenGridGraphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              this.greenGridGraphicArr.push(this.greenGridGraphic)
            }
          }
        })
        this.greenGridFeatureLayer = new FeatureLayer({
          source: this.greenGridGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "committeeName",
              alias: "committeeName",
              type: "string"
            }
          ],
          renderer: greenGridRenderer,
          popupTemplate: {
            title: "绿化网格",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "committeeName",
                  label: "居委会名称"
                },
              ]
            }]
          },
        })
        const labelClass = {
          symbol: {
            type: "text",
            color: "white",
            font: {
              size: 20,
              weight: "bold"
            }
          },
          labelPlacement: "always-horizontal",
          labelExpressionInfo: {
            expression: "$feature.committeeName"
          }
        };
        this.greenGridFeatureLayer.labelingInfo = [labelClass];

      })
    })

  },
  methods: {
    // 绿化主体的icon显示 Deprecated
    greeningSelBtn(index) {
      if (index === 0) {
        // 绿地 greenSpace
        if (this.greenSpaceIconShow) {
          this.map.remove(this.greenSpaceFeatureLayer)
          this.map.remove(this.greenFeatureLayer0)
          this.map.remove(this.greenFeatureLayer1)
          this.map.remove(this.greenFeatureLayer4)
          this.map.remove(this.greenFeatureLayer5)
          this.map.remove(this.greenFeatureLayer9)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.greenSpaceIconShow = false;
        } else {
          this.map.add(this.greenSpaceFeatureLayer)
          this.map.add(this.greenFeatureLayer0)
          this.map.add(this.greenFeatureLayer1)
          this.map.add(this.greenFeatureLayer4)
          this.map.add(this.greenFeatureLayer5)
          this.map.add(this.greenFeatureLayer9)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.greenSpaceIconShow = true;
        }
      } else if (index === 1) {
        // 郊区行道树 suburbanStreetTree
        if (this.suburbanStreetTreeIconShow) {
          this.map.remove(this.suburbanStreetTreeFeatureLayer)
          this.map.remove(this.greenFeatureLayer6)
          this.map.remove(this.greenFeatureLayer7)
          this.map.remove(this.greenFeatureLayer8)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.suburbanStreetTreeIconShow = false;
        } else {
          this.map.add(this.suburbanStreetTreeFeatureLayer)
          this.map.add(this.greenFeatureLayer6)
          this.map.add(this.greenFeatureLayer7)
          this.map.add(this.greenFeatureLayer8)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.suburbanStreetTreeIconShow = true;
        }
      } else if (index === 2) {
        // 商品房绿化 commercialHousingGreening
        if (this.commercialHousingGreeningIconShow) {
          this.map.remove(this.commercialHousingGreeningFeatureLayer)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.commercialHousingGreeningIconShow = false;
        } else {
          this.map.add(this.commercialHousingGreeningFeatureLayer)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.commercialHousingGreeningIconShow = true;
        }
      } else if (index === 3) {
        // 售后公房绿化 afterSalePublicHousingGreening
        if (this.afterSalePublicHousingGreeningIconShow) {
          this.map.remove(this.afterSalePublicHousingGreeningFeatureLayer)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.afterSalePublicHousingGreeningIconShow = false;
        } else {
          this.map.add(this.afterSalePublicHousingGreeningFeatureLayer)
          this.$refs["greening0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.afterSalePublicHousingGreeningIconShow = true;
        }
      }
    },
    checkGreening(value) {
      if (value === true) {
        this.map.add(this.danweilvdiFeatureLayer0)
        this.map.add(this.danweilvdiFeatureLayer1)
        this.map.add(this.danweilvdiFeatureLayer2)
        this.map.add(this.danweilvdiFeatureLayer3)

        this.map.add(this.gonggonglvdiFeatureLayer0)
        this.map.add(this.gonggonglvdiFeatureLayer1)
        this.map.add(this.gonggonglvdiFeatureLayer2)
        this.map.add(this.gonggonglvdiFeatureLayer3)

        this.map.add(this.xingdaoshuFeatureLayer0)
        this.map.add(this.xingdaoshuFeatureLayer1)
        this.map.add(this.xingdaoshuFeatureLayer2)
        this.map.add(this.xingdaoshuFeatureLayer3)
        this.map.add(this.xingdaoshuFeatureLayer4)

        this.map.add(this.shangpinfanglvhuaFeatureLayer0)
        this.map.add(this.shangpinfanglvhuaFeatureLayer1)

        this.map.add(this.shouhougongfanglvhuaFeatureLayer0)

        this.map.add(this.greenGridFeatureLayer)


      } else if (value === false) {
        this.map.remove(this.danweilvdiFeatureLayer0)
        this.map.remove(this.danweilvdiFeatureLayer1)
        this.map.remove(this.danweilvdiFeatureLayer2)
        this.map.remove(this.danweilvdiFeatureLayer3)

        this.map.remove(this.gonggonglvdiFeatureLayer0)
        this.map.remove(this.gonggonglvdiFeatureLayer1)
        this.map.remove(this.gonggonglvdiFeatureLayer2)
        this.map.remove(this.gonggonglvdiFeatureLayer3)

        this.map.remove(this.xingdaoshuFeatureLayer0)
        this.map.remove(this.xingdaoshuFeatureLayer1)
        this.map.remove(this.xingdaoshuFeatureLayer2)
        this.map.remove(this.xingdaoshuFeatureLayer3)
        this.map.remove(this.xingdaoshuFeatureLayer4)

        this.map.remove(this.shangpinfanglvhuaFeatureLayer0)
        this.map.remove(this.shangpinfanglvhuaFeatureLayer1)

        this.map.remove(this.shouhougongfanglvhuaFeatureLayer0)

        this.map.remove(this.greenGridFeatureLayer)
      } else {
        const greeningOptions = ['单位绿地', '公共绿地', '行道树', '商品房绿化', '售后公房绿化', '绿化网格'];
        let a = new Set(value);
        let b = new Set(greeningOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '单位绿地') {
            this.map.remove(this.danweilvdiFeatureLayer0)
            this.map.remove(this.danweilvdiFeatureLayer1)
            this.map.remove(this.danweilvdiFeatureLayer2)
            this.map.remove(this.danweilvdiFeatureLayer3)
          } else if (item === '公共绿地') {
            this.map.remove(this.gonggonglvdiFeatureLayer0)
            this.map.remove(this.gonggonglvdiFeatureLayer1)
            this.map.remove(this.gonggonglvdiFeatureLayer2)
            this.map.remove(this.gonggonglvdiFeatureLayer3)
          } else if (item === '行道树') {
            this.map.remove(this.xingdaoshuFeatureLayer0)
            this.map.remove(this.xingdaoshuFeatureLayer1)
            this.map.remove(this.xingdaoshuFeatureLayer2)
            this.map.remove(this.xingdaoshuFeatureLayer3)
            this.map.remove(this.xingdaoshuFeatureLayer4)
          } else if (item === '商品房绿化') {
            this.map.remove(this.shangpinfanglvhuaFeatureLayer0)
            this.map.remove(this.shangpinfanglvhuaFeatureLayer1)
          } else if (item === '售后公房绿化') {
            this.map.remove(this.shouhougongfanglvhuaFeatureLayer0)
          } else if (item === '绿化网格') {
            this.map.remove(this.greenGridFeatureLayer)
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '单位绿地') {
              this.map.add(this.danweilvdiFeatureLayer0)
              this.map.add(this.danweilvdiFeatureLayer1)
              this.map.add(this.danweilvdiFeatureLayer2)
              this.map.add(this.danweilvdiFeatureLayer3)
            } else if (item === '公共绿地') {
              this.map.add(this.gonggonglvdiFeatureLayer0)
              this.map.add(this.gonggonglvdiFeatureLayer1)
              this.map.add(this.gonggonglvdiFeatureLayer2)
              this.map.add(this.gonggonglvdiFeatureLayer3)
            } else if (item === '行道树') {
              this.map.add(this.xingdaoshuFeatureLayer0)
              this.map.add(this.xingdaoshuFeatureLayer1)
              this.map.add(this.xingdaoshuFeatureLayer2)
              this.map.add(this.xingdaoshuFeatureLayer3)
              this.map.add(this.xingdaoshuFeatureLayer4)
            } else if (item === '商品房绿化') {
              this.map.add(this.shangpinfanglvhuaFeatureLayer0)
              this.map.add(this.shangpinfanglvhuaFeatureLayer1)
            } else if (item === '售后公房绿化') {
              this.map.add(this.shouhougongfanglvhuaFeatureLayer0)
            } else if (item === '绿化网格') {
              this.map.add(this.greenGridFeatureLayer)
            }
          }
        }
      }
    }
  }
}

</script>
