<template>

</template>

<script>

import {
  listScreenResidentialEntrance,
  listScreenGarbageHouse,
  listScreenCommittee,
  listScreenResidential,
  listScreenLargeGarbageSite,
  listScreenConstructionWasteSite,
  listScreenNonResidentialProperty,
  listScreenPersonnelGrid
} from "@/api/shcy/screen";
import {loadModules} from "esri-loader";
import {parseCoordinate} from "@/utils/coordinate";

export default {
  name: 'communityBasicInfoMixin',
  data() {
    return {
      // 居委会
      committeeList: [],
      committeeGraphicArr: [],
      committeeFeatureLayer: null,
      committeePointGraphicArr: [],
      committeePointFeatureLayer: null,
      committeeGridFeatureLayer: null,

      // 小区边界线
      residentialGraphicArr: [],
      residentialList: [],
      residentialFeatureLayer: null,

      // 小区出入口
      residentialEntranceList: [],
      residentialEntranceGraphicArr: [],
      residentialEntranceFeatureLayer: null,
      entranceData:{},
      isResidentialEntranceWindow:false,

      // 物业管理处
      nonResidentialPropertyList: [],
      nonResidentialPropertyGraphicArr: [],
      nonResidentialPropertyFeatureLayer: null,
      propertyManageData:{},
      isNonResidentialPropertyWindow:false,

      // 垃圾房
      garbageHouseList: [],
      garbageHouseGraphicArr: [],
      garbageHouseFeatureLayer: null,
      garbageHouseData:{},
      isGarbageHouseWindow:false,

      // 大件垃圾
      largeGarbageSiteList: [],
      largeGarbageSiteGraphicArr: [],
      largeGarbageSiteFeatureLayer: null,

      // 建筑垃圾
      constructionWasteSiteList: [],
      constructionWasteSiteGraphicArr: [],
      constructionWasteSiteFeatureLayer: null,

      // 两类人员网格化
      personnelGridList: [],
      personnelGridGraphicArr: [],
      personnelGridFeatureLayer: null,
      personnelGridData:{},
      isPersonnelGridWindow:false,

    }
  },
  mounted() {

    // 居委会
    listScreenCommittee(this.queryParams).then(response => {
      this.committeeList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let committeeRenderer = {
          type: "unique-value",
          field: "committeeName",
          defaultSymbol: {
            type: "simple-fill",
            color: [112, 178, 226, 0.2],
            outline: {
              color: [112, 178, 226],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        this.committeeList.forEach(committee => {
          const fillColor = committee.fillColor ? committee.fillColor.split(",") : [112, 178, 226, 0.2]
          const outlineColor = committee.outlineColor ? committee.outlineColor.split(",") : [112, 178, 226]

          let uniqueValueInfo = {
            value: committee.committeeName,
            symbol: {
              type: "simple-fill",
              color: fillColor,
              outline: {
                color: outlineColor,
                width: 2
              }
            }
          }
          committeeRenderer.uniqueValueInfos.push(uniqueValueInfo)

          // const attributes = {"ObjectID": committee.id, "name": committee.committeeName}
          const attributes = JSON.parse(JSON.stringify(committee))
          if (committee.coordinate) {
            if (committee.type === "polygon") {
              const r = parseCoordinate(committee.coordinate);
              const polygon = new Polygon({
                rings: r,
                spatialReference: SpatialReference.WebMercator,
              });
              this.committeeGraphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              this.committeeGraphicArr.push(this.committeeGraphic)
            } else if (committee.type === "point") {
              const p = committee.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const committeeGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.committeePointGraphicArr.push(committeeGraphic)
            }
          }
        })
        const fields = [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "committeeName",
            alias: "committeeName",
            type: "string"
          }
        ]
        const popupTemplate = {
          title: "居委会",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "committeeName",
                label: "居委会名称"
              },
            ]
          }]
        }
        this.committeeFeatureLayer = new FeatureLayer({
          source: this.committeeGraphicArr,
          fields: fields,
          renderer: committeeRenderer,
          popupTemplate: popupTemplate
        })
        const labelClass = {
          symbol: {
            type: "text",
            color: "white",
            font: {
              size: 10,
              // weight: "bold"
            }
          },
          labelPlacement: "always-horizontal",
          labelExpressionInfo: {
            expression: "$feature.committeeName"
          }
        };
        this.committeeFeatureLayer.labelingInfo = [labelClass];

        this.committeeGridFeatureLayer = new FeatureLayer({
          source: this.committeeGraphicArr,
          fields: fields,
          renderer: committeeRenderer,
          labelingInfo: [labelClass]
        })

        this.committeePointFeatureLayer = new FeatureLayer({
          source: this.committeePointGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "committeeName",
              alias: "committeeName",
              type: "string"
            },],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_juwei.png",
              width: "29px",
              height: "37px"
            }
          },
        })
      })
    })

    // 大件垃圾
    listScreenLargeGarbageSite(this.queryParams).then(response => {
      this.largeGarbageSiteList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.largeGarbageSiteList.forEach(largeGarbageSite => {
          const attributes = JSON.parse(JSON.stringify(largeGarbageSite))
          if (largeGarbageSite.type === "point" && largeGarbageSite.coordinate) {
            const p = largeGarbageSite.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const largeGarbageSiteGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.largeGarbageSiteGraphicArr.push(largeGarbageSiteGraphic)
          }
        })
        this.largeGarbageSiteFeatureLayer = new FeatureLayer({
          source: this.largeGarbageSiteGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "residential",
            alias: "residential",
            type: "string"
          },
          {
            name: "committee",
            alias: "committee",
            type: "string"
          }],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_dajianlaji.png",
              width: "28px",
              height: "38px"
            }
          },
          popupTemplate: {
            title: "大件垃圾",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "residential",
                  label: "所属小区"
                },
                {
                  fieldName: "committee",
                  label: "所属居委会"
                },
              ]
            }]
          }
        })
      })
    })

    // 建筑垃圾
    listScreenConstructionWasteSite(this.queryParams).then(response => {
      this.constructionWasteSiteList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.constructionWasteSiteList.forEach(constructionWasteSite => {
          const attributes = JSON.parse(JSON.stringify(constructionWasteSite))
          if (constructionWasteSite.type === "point" && constructionWasteSite.coordinate) {
            const p = constructionWasteSite.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const constructionWasteSiteGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.constructionWasteSiteGraphicArr.push(constructionWasteSiteGraphic)
          }
        })
        this.constructionWasteSiteFeatureLayer = new FeatureLayer({
          source: this.constructionWasteSiteGraphicArr,
          fields: [{
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "residential",
            alias: "residential",
            type: "string"
          },
          {
            name: "committee",
            alias: "committee",
            type: "string"
          }],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_jianzhulaji.png",
              width: "28px",
              height: "38px"
            }
          },
          popupTemplate: {
            title: "建筑垃圾",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "residential",
                  label: "所属小区"
                },
                {
                  fieldName: "committee",
                  label: "所属居委会"
                },
              ]
            }]
          }
        })
      })
    })

    // 物业管理处
    listScreenNonResidentialProperty(this.queryParams).then(response => {
      this.nonResidentialPropertyList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.nonResidentialPropertyList.forEach(nonResidentialProperty => {
          nonResidentialProperty.graphicType = 'nonResidentialProperty'
          const attributes = JSON.parse(JSON.stringify(nonResidentialProperty))
          if (nonResidentialProperty.type === "point" && nonResidentialProperty.coordinate) {
            const p = nonResidentialProperty.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const nonResidentialPropertyGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.nonResidentialPropertyGraphicArr.push(nonResidentialPropertyGraphic)
          }
        })
        this.nonResidentialPropertyFeatureLayer = new FeatureLayer({
          source: this.nonResidentialPropertyGraphicArr,
          fields: [
            {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "name",
              alias: "name",
              type: "string"
            },
            {
              name: "propertyServiceSite",
              alias: "propertyServiceSite",
              type: "string"
            },
            {
              name: "residential",
              alias: "residential",
              type: "string"
            },
            {
              name: "contacts",
              alias: "contacts",
              type: "string"
            },
            {
              name: "contactsPhone",
              alias: "contactsPhone",
              type: "string"
            }],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_wuye.png",
              width: "29px",
              height: "37px"
            }
          },
          popupTemplate: {
            title: "物业管理处",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "name",
                  label: "物业公司名称"
                },
                {
                  fieldName: "propertyServiceSite",
                  label: "物业服务地点"
                },
                {
                  fieldName: "residential",
                  label: "所属小区"
                },
                {
                  fieldName: "contacts",
                  label: "小区经理"
                },
                {
                  fieldName: "contactsPhone",
                  label: "办公电话"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          }
        })
      })
    })

    // 小区出入口
    listScreenResidentialEntrance(this.queryParams).then(response => {
      this.residentialEntranceList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.residentialEntranceList.forEach(residentialEntrance => {
          residentialEntrance.graphicType = 'residentialEntrance'
          const attributes = JSON.parse(JSON.stringify(residentialEntrance))
          if (residentialEntrance.type === "point" && residentialEntrance.coordinate) {
            const p = residentialEntrance.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const residentialEntranceGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.residentialEntranceGraphicArr.push(residentialEntranceGraphic)
          }
        })
        this.residentialEntranceFeatureLayer = new FeatureLayer({
          source: this.residentialEntranceGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "name",
              alias: "name",
              type: "string"
            },
            {
              name: "detailSite",
              alias: "detailSite",
              type: "string"
            },
            {
              name: "residential",
              alias: "residential",
              type: "string"
            },
            {
              name: "withGuard",
              alias: "withGuard",
              type: "string"
            },
            {
              name: "motorVehiclePassing",
              alias: "motorVehiclePassing",
              type: "string"
            },
            {
              name: "nonMotorVehicleTraffic",
              alias: "nonMotorVehicleTraffic",
              type: "string"
            },
            {
              name: "passenger",
              alias: "passenger",
              type: "string"
            },
            {
              name: "openTime",
              alias: "openTime",
              type: "string"
            },
            {
              name: "entranceProperty",
              alias: "entranceProperty",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "unique-value",
            field: "entranceProperty",
            defaultSymbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_churukou.png",
              width: "28px",
              height: "38px"
            },
            uniqueValueInfos: [
              {
                value: '大门',
                symbol: {
                  type: "picture-marker",
                  url: "http://**************/images/icon/xiaoqu_icon_churukou.png",
                  width: "28px",
                  height: "38px"
                }
              },
              {
                value: '转门',
                symbol: {
                  type: "picture-marker",
                  url: "http://**************/images/icon/xiaoqu_icon_zhuanmen.png",
                  width: "28px",
                  height: "38px"
                }
              },
            ]
          },
          popupTemplate: {
            title: "小区出入口",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "name",
                  label: "出入口名称"
                },
                {
                  fieldName: "detailSite",
                  label: "具体位置"
                },
                {
                  fieldName: "residential",
                  label: "小区"
                },
                {
                  fieldName: "withGuard",
                  label: "是否有门卫"
                },
                {
                  fieldName: "motorVehiclePassing",
                  label: "机动车通行"
                },
                {
                  fieldName: "nonMotorVehicleTraffic",
                  label: "非机动车通行"
                },
                {
                  fieldName: "passenger",
                  label: "行人"
                },
                {
                  fieldName: "openTime",
                  label: "开放时间"
                },
                {
                  fieldName: "entranceProperty",
                  label: "属性"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]

          }
        })
      })
    })

    // 垃圾房
    listScreenGarbageHouse(this.queryParams).then(response => {
      this.garbageHouseList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {
        this.garbageHouseList.forEach(garbageHouse => {
          garbageHouse.graphicType = 'garbageHouse'
          const attributes = JSON.parse(JSON.stringify(garbageHouse))
          if (garbageHouse.type === "point" && garbageHouse.coordinate) {
            const p = garbageHouse.coordinate.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const garbageHouseGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.garbageHouseGraphicArr.push(garbageHouseGraphic)
          }
        })
        this.garbageHouseFeatureLayer = new FeatureLayer({
          source: this.garbageHouseGraphicArr,
          fields: [
            {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "name",
              alias: "name",
              type: "string"
            },
            {
              name: "street",
              alias: "street",
              type: "string"
            },
            {
              name: "committee",
              alias: "committee",
              type: "string"
            },
            {
              name: "residential",
              alias: "residential",
              type: "string"
            },
            {
              name: "siteAddress",
              alias: "siteAddress",
              type: "string"
            },
            {
              name: "village",
              alias: "village",
              type: "string"
            },
            {
              name: "garbageType",
              alias: "garbageType",
              type: "string"
            },
            {
              name: "garbageOpenTime",
              alias: "garbageOpenTime",
              type: "string"
            }
          ],
          outFields: ["*"],
          renderer: {
            type: "simple",
            symbol: {
              type: "picture-marker",
              url: "http://**************/images/icon/xiaoqu_icon_lajifang.png",
              width: "29px",
              height: "38px"
            }
          },
          popupTemplate: {
            title: "垃圾房",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "residential",
                  label: "居住小区"
                },
                {
                  fieldName: "siteAddress",
                  label: "点位地址"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          },

        })
      })
    })

    // 小区边界线
    listScreenResidential(this.queryParams).then(response => {
      this.residentialList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let residentialRenderer = {
          type: "unique-value",
          field: "residential",
          defaultSymbol: {
            type: "simple-fill",
            color: [112, 178, 226, 0.2],
            outline: {
              color: [112, 178, 226],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        this.residentialList.forEach(residential => {
          const fillColor = [112, 178, 226, 0.2]
          const outlineColor = [112, 178, 226]

          let uniqueValueInfo = {
            value: residential.residential,
            symbol: {
              type: "simple-fill",
              color: fillColor,
              outline: {
                color: outlineColor,
                width: 2
              }
            }
          }
          residentialRenderer.uniqueValueInfos.push(uniqueValueInfo)

          // const attributes = {"ObjectID": residential.id, "name": residential.residential}
          const attributes = JSON.parse(JSON.stringify(residential))
          if (residential.type === "polygon") {
            const r = parseCoordinate(residential.coordinate);
            const polygon = new Polygon({
              rings: r,
              spatialReference: SpatialReference.WebMercator,
            });
            this.residentialGraphic = new Graphic({
              geometry: polygon,
              attributes: attributes,
            })
            this.residentialGraphicArr.push(this.residentialGraphic)
          }
        })

        const fields = [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "residential",
            alias: "residential",
            type: "string"
          },
          {
            name: "committee",
            alias: "committee",
            type: "string"
          }
        ]

        const popupTemplate = {
          title: "小区边界线",
          content: [{
            type: "fields",
            fieldInfos: [
              {
                fieldName: "residential",
                label: "小区名称"
              },
              {
                fieldName: "committee",
                label: "居委会"
              },
            ]
          }]
        }

        this.residentialFeatureLayer = new FeatureLayer({
          source: this.residentialGraphicArr,
          fields: fields,
          renderer: residentialRenderer,
          popupTemplate: popupTemplate
        })

        const labelClass = {
          symbol: {
            type: "text",
            color: "white",
            font: {
              size: 20,
              weight: "bold"
            }
          },
          labelPlacement: "always-horizontal",
          labelExpressionInfo: {
            expression: "$feature.residential"
          }
        };
        this.residentialFeatureLayer.labelingInfo = [labelClass];
      })
    })

    // 两类人员网格化
    // listScreenPersonnelGrid(this.queryParams).then(response => {
    //   this.personnelGridList = response.data
    //   loadModules([
    //       "esri/Graphic",
    //       "esri/geometry/Point",
    //       "esri/geometry/SpatialReference",
    //       "esri/layers/FeatureLayer",
    //     ],
    //     this.options).then(([
    //                           Graphic,
    //                           Point,
    //                           SpatialReference,
    //                           FeatureLayer,
    //                         ]) => {
    //     this.personnelGridList.forEach(personnelGrid => {
    //       personnelGrid.graphicType = 'personnelGrid'
    //       const attributes = JSON.parse(JSON.stringify(personnelGrid))
    //       if (personnelGrid.type === "point" && personnelGrid.coordinate) {
    //         const p = personnelGrid.coordinate.split(",");
    //         const point = new Point({
    //           x: p[0],
    //           y: p[1],
    //           spatialReference: SpatialReference.WebMercator,
    //         });
    //         const personnelGridGraphic = new Graphic({
    //           geometry: point,
    //           attributes: attributes,
    //         });
    //         this.personnelGridGraphicArr.push(personnelGridGraphic)
    //       }
    //     })
    //     this.personnelGridFeatureLayer = new FeatureLayer({
    //       source: this.personnelGridGraphicArr,
    //       fields: [
    //         {
    //           name: "ObjectID",
    //           alias: "ObjectID",
    //           type: "oid"
    //         },
    //         {
    //           name: "graphicType",
    //           alias: "graphicType",
    //           type: "string"
    //         },
    //         {
    //           name: "id",
    //           alias: "id",
    //           type: "long"
    //         },
    //         {
    //           name: "xm",
    //           alias: "xm",
    //           type: "string"
    //         },
    //         {
    //           name: "zm",
    //           alias: "zm",
    //           type: "string"
    //         },
    //         {
    //           name: "qmrq",
    //           alias: "qmrq",
    //           type: "string"
    //         },
    //         {
    //           name: "hxdd",
    //           alias: "hxdd",
    //           type: "string"
    //         },
    //         {
    //           name: "jzdd",
    //           alias: "jzdd",
    //           type: "string"
    //         },
    //         {
    //           name: "jzdcjw",
    //           alias: "jzdcjw",
    //           type: "string"
    //         },
    //         {
    //           name: "sqjzcbdx",
    //           alias: "sqjzcbdx",
    //           type: "string"
    //         },
    //         {
    //           name: "ysdj",
    //           alias: "ysdj",
    //           type: "string"
    //         },
    //         {
    //           name: "nrwghglyy",
    //           alias: "nrwghglyy",
    //           type: "string"
    //         },
    //         {
    //           name: "cjllxyxm",
    //           alias: "cjllxyxm",
    //           type: "string"
    //         },
    //         {
    //           name: "cjllxylxfs",
    //           alias: "cjllxylxfs",
    //           type: "string"
    //         },
    //         {
    //           name: "wgxxyxm",
    //           alias: "wgxxyxm",
    //           type: "string"
    //         },
    //         {
    //           name: "wgxxylxfs",
    //           alias: "wgxxylxfs",
    //           type: "string"
    //         },
    //         {
    //           name: "bz",
    //           alias: "bz",
    //           type: "string"
    //         }
    //       ],
    //       outFields: ["*"],
    //       // renderer: {
    //       //   type: "simple",
    //       //   symbol: {
    //       //     type: "picture-marker",
    //       //     url: "http://**************/images/icon/icon_llryzddx_red.png",
    //       //     width: "30px",
    //       //     height: "38px"
    //       //   }
    //       // },
    //       renderer: {
    //         type: "unique-value",
    //         field: "ysdj",
    //         defaultSymbol: {
    //           type: "picture-marker",
    //           url: "http://**************/images/icon/icon_llryzddx.png",
    //           width: "30px",
    //           height: "38px"
    //         },
    //         uniqueValueInfos: [{
    //           value: "红色",
    //           symbol: {
    //             type: "picture-marker",
    //             url: "http://**************/images/icon/icon_llryzddx_red_visited.png",
    //             width: "39px",
    //             height: "45px"
    //           }
    //         }, {
    //           value: "黄色",
    //           symbol: {
    //             type: "picture-marker",
    //             url: "http://**************/images/icon/icon_llryzddx_yellow_visited.png",
    //             width: "39px",
    //             height: "45px"
    //           }
    //         }, {
    //           value: "橙色",
    //           symbol: {
    //             type: "picture-marker",
    //             url: "http://**************/images/icon/icon_llryzddx_orange.png",
    //             width: "30px",
    //             height: "38px"
    //           }
    //         }]
    //       },
    //       popupTemplate: {
    //         title: "两类人员网格化",
    //         content: [{
    //           type: "fields",
    //           fieldInfos: [
    //             {
    //               fieldName: "xm",
    //               label: "姓名"
    //             },
    //             {
    //               fieldName: "zm",
    //               label: "罪名"
    //             },
    //           ]
    //         }],
    //         outFields: ["*"],
    //         actions: [
    //           {
    //             title: "详细",
    //             id: "popup-detail",
    //           }
    //         ]
    //       }
    //       // popupTemplate end
    //     })
    //   })
    // })
    // end  两类人员网格化

  },
  methods: {
    // 小区基础信息的icon显示
    iconSelBtn(index) {
      // 判断是哪一个图标
      if (index === 0) {
        // 出入口
        // 判断是否已显示图标
        if (this.doorMarkerShow) {
          // 已显示-移除图标
          // this.map.remove(this.doorMarker);

          this.residentialEntranceGraphicArr.forEach(item => {
            this.graphicsLayer.remove(item);
          });

          // 已显示-修改为未选中样式
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          // 修改为未选中状态
          this.doorMarkerShow = false;
        } else {
          // 未显示-添加图标
          // this.map.add(this.doorMarker);

          this.residentialEntranceGraphicArr.forEach(item => {
            this.graphicsLayer.add(item);
          });

          // 未显示-修改为选中样式
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          // 修改为选中状态
          this.doorMarkerShow = true;
        }
      } else if (index === 1) {
        // 转门
        if (this.revolvingDoorMarkerShow) {
          // this.map.remove(this.revolvingDoorMarker);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.revolvingDoorMarkerShow = false;
        } else {
          // this.map.add(this.revolvingDoorMarker);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.revolvingDoorMarkerShow = true;
        }
      } else if (index === 2) {
        // 居委
        if (this.residentCommitteeShow) {
          // this.map.remove(this.residentCommittee);
          // this.committeeGraphicArr.forEach(item => {
          //   this.graphicsLayer.remove(item);
          // });
          this.map.remove(this.committeeFeatureLayer);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.residentCommitteeShow = false;
        } else {
          // this.map.add(this.residentCommittee);
          // this.committeeGraphicArr.forEach(item => {
          //   this.graphicsLayer.add(item);
          // });
          this.map.add(this.committeeFeatureLayer);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.residentCommitteeShow = true;
        }
      } else if (index === 3) {
        // 垃圾房
        if (this.garbageRoomShow) {
          // this.map.remove(this.garbageRoom);
          this.garbageHouseGraphicArr.forEach(item => {
            this.graphicsLayer.remove(item);
          });
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.garbageRoomShow = false;
        } else {
          // this.map.add(this.garbageRoom);
          this.garbageHouseGraphicArr.forEach(item => {
            this.graphicsLayer.add(item);
          });
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.garbageRoomShow = true;
        }
      } else if (index === 4) {
        // 配电室
        if (this.powerRoomShow) {
          // this.map.remove(this.powerRoom);
          this.map.remove(this.residentialFeatureLayer);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.powerRoomShow = false;
        } else {
          // this.map.add(this.powerRoom);
          this.map.add(this.residentialFeatureLayer);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.powerRoomShow = true;
        }
      } else if (index === 5) {
        // 凉亭
        if (this.pavilionShow) {
          // this.map.remove(this.pavilion);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.pavilionShow = false;
        } else {
          // this.map.add(this.pavilion);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.pavilionShow = true;
        }
      } else if (index === 6) {
        // 健身点
        if (this.fitnessShow) {
          // this.map.remove(this.fitness);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fitnessShow = false;
        } else {
          // this.map.add(this.fitness);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fitnessShow = true;
        }
      } else if (index === 7) {
        // 消防通道
        if (this.emergencyAccessLineShow) {
          // this.map.remove(this.emergencyAccessLine);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.emergencyAccessLineShow = false;
        } else {
          // this.map.add(this.emergencyAccessLine);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.emergencyAccessLineShow = true;
        }
      }
    },
    // 绘制所有居委会图形
    drawAllCommittee() {
      this.map.add(this.committeeFeatureLayer)
      this.map.add(this.committeePointFeatureLayer)
      this.$refs.tipsWindow.checkedCommunities = ['居委会']
      this.$refs.tipsWindow.isIndeterminateCommunity = true
    },
    checkCommunityBasicInfo(value) {
      if (value === true) {
        this.map.add(this.committeeFeatureLayer);
        this.map.add(this.committeePointFeatureLayer);
        this.map.add(this.largeGarbageSiteFeatureLayer);
        this.map.add(this.constructionWasteSiteFeatureLayer);
        this.map.add(this.nonResidentialPropertyFeatureLayer);
        this.map.add(this.residentialEntranceFeatureLayer);
        this.map.add(this.garbageHouseFeatureLayer);
        this.map.add(this.residentialFeatureLayer);
        // this.map.add(this.personnelGridFeatureLayer);
      } else if (value === false) {
        this.map.remove(this.committeeFeatureLayer);
        this.map.remove(this.committeePointFeatureLayer)
        this.map.remove(this.largeGarbageSiteFeatureLayer);
        this.map.remove(this.constructionWasteSiteFeatureLayer);
        this.map.remove(this.nonResidentialPropertyFeatureLayer);
        this.map.remove(this.residentialEntranceFeatureLayer);
        this.map.remove(this.garbageHouseFeatureLayer);
        this.map.remove(this.residentialFeatureLayer);
        // this.map.remove(this.personnelGridFeatureLayer);
      } else {
        // const communityOptions = ['居委会', '小区边界线', '小区出入口', '物业管理处', '垃圾房', '大件垃圾', '建筑垃圾', '健身点', '配电室', '凉亭'];
        const communityOptions = ['居委会', '小区边界线', '小区出入口', '物业管理处', '垃圾房', '大件垃圾', '建筑垃圾', '两类人员网格化'];
        let a = new Set(value);
        let b = new Set(communityOptions);
        let arr = Array.from(new Set([...b].filter(x => !a.has(x))));
        for (let item of arr) {
          if (item === '居委会') {
            this.map.remove(this.committeeFeatureLayer);
            this.map.remove(this.committeePointFeatureLayer)
          } else if (item === '小区边界线') {
            this.map.remove(this.residentialFeatureLayer);
          } else if (item === '大件垃圾') {
            this.map.remove(this.largeGarbageSiteFeatureLayer);
          } else if (item === '建筑垃圾') {
            this.map.remove(this.constructionWasteSiteFeatureLayer);
          }  else if (item === '物业管理处') {
            this.map.remove(this.nonResidentialPropertyFeatureLayer);
          } else if (item === '小区出入口') {
            this.map.remove(this.residentialEntranceFeatureLayer);
          } else if (item === '垃圾房') {
            this.map.remove(this.garbageHouseFeatureLayer);
          } else if (item === '两类人员网格化') {
            this.map.remove(this.personnelGridFeatureLayer);
          }
        }
        if (value.length > 0) {
          for (let item of value) {
            if (item === '居委会') {
              this.map.add(this.committeeFeatureLayer);
              this.map.add(this.committeePointFeatureLayer);
            } else if (item === '小区边界线') {
              this.map.add(this.residentialFeatureLayer);
            }  else if (item === '大件垃圾') {
              this.map.add(this.largeGarbageSiteFeatureLayer);
            } else if (item === '建筑垃圾') {
              this.map.add(this.constructionWasteSiteFeatureLayer);
            }  else if (item === '物业管理处') {
              this.map.add(this.nonResidentialPropertyFeatureLayer);
            } else if (item === '小区出入口') {
              this.map.add(this.residentialEntranceFeatureLayer);
            } else if (item === '垃圾房') {
              this.map.add(this.garbageHouseFeatureLayer);
            } else if (item === '两类人员网格化') {
              this.map.add(this.personnelGridFeatureLayer);
            }
          }
        }
      }
    },

    // 重点人员默认加载两类人员网格化图层
    addPersonnelGridFeatureLayer() {
      this.map.add(this.personnelGridFeatureLayer);
    }

  }
}

</script>
