<template>

</template>

<script>

import {
  listScreenRoadStreetChief,
  listScreenCommittee,
  listCheckRecordShop,
} from "@/api/shcy/screen";
import {loadModules} from "esri-loader";
import {parseCoordinate} from "@/utils/coordinate";

// const clusterConfig = {
//   type: "cluster",
//   clusterRadius: "60px",
//   clusterMinSize: "50px",
//   clusterMaxSize: "100px",
//   labelingInfo: [{
//     deconflictionStrategy: "none",
//     labelExpressionInfo: {
//       expression: "Text($feature.cluster_count, '#,###')"
//     },
//     symbol: {
//       type: "text",
//       color: "#004a5d",
//       font: {
//         weight: "bold",
//         family: "Noto Sans",
//         size: "12px"
//       }
//     },
//     labelPlacement: "center-center",
//   }]
// };

export default {
  name: "streetChiefMixin",
  props: {
    checkLogThreeData:{
      type:Array,
      default: () => []
    },

  },
  data() {
    return {
      // 今日巡查通过商铺
      shopCheckedList: [],
      shopCheckedGraphicArr: [],
      shopCheckedFeatureLayer: null,
      // 今日巡查未通过商铺
      shopCheckedNoPassList: [],
      shopCheckedNoPassGraphicArr: [],
      shopCheckedNoPassFeatureLayer: null,

      // 本月巡查通过商铺
      shopCheckedMonthList: [],
      shopCheckedMonthGraphicArr: [],
      shopCheckedMonthFeatureLayer: null,
      // 本月巡查未通过商铺
      shopCheckedNoPassMonthList: [],
      shopCheckedNoPassMonthGraphicArr: [],
      shopCheckedNoPassMonthFeatureLayer: null,

      // 本月未巡查商铺
      monthNotCheckShopList: [],
      monthNotCheckShopGraphicArr: [],
      monthNotCheckShopFeatureLayer: null,

      // 路段
      roadStreetChiefList: [],
      roadStreetChiefGraphicArr: [],
      roadStreetChiefFeatureLayer: null,
      roadStreetChiefPointGraphicArr: [],
      roadStreetChiefPointFeatureLayer: null,

      //街长信息
      roadChiefData: {},
      isRoadCheifWindowShow: false,

      //路名信息
      roadData:{},
      isRoadWindowShow:false,


      committeeStreetChiefFeatureLayer: null,
    }
  },
  mounted() {

    listCheckRecordShop().then(response => {
      this.shopCheckedList = response.data.todayPassShop
      this.shopCheckedNoPassList = response.data.todayNotPassShop
      this.shopCheckedMonthList = response.data.monthPassShop
      this.shopCheckedNoPassMonthList = response.data.monthNotPassShop
      this.monthNotCheckShopList = response.data.monthNotCheckShop
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        if (this.shopCheckedList.length > 0) {
          this.shopCheckedList.forEach(shopChecked => {
            let shop = shopChecked.shop
            shop.graphicType = 'shop'
            const attributes = JSON.parse(JSON.stringify(shop))

            if (shop.type === "point" && shop.coordinate) {
              const p = shop.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const shopGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.shopCheckedGraphicArr.push(shopGraphic)
            }
          })
          this.shopCheckedFeatureLayer = new FeatureLayer({
            source: this.shopCheckedGraphicArr,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "shopName",
                alias: "shopName",
                type: "string"
              },
              {
                name: "shopAddress",
                alias: "shopAddress",
                type: "string"
              },
              {
                name: "shopLicense",
                alias: "shopLicense",
                type: "string"
              },
              {
                name: "shopCreditCode",
                alias: "shopCreditCode",
                type: "string"
              },
              {
                name: "shopRegisterAddress",
                alias: "shopRegisterAddress",
                type: "string"
              },
              {
                name: "shopOperatingAddress",
                alias: "shopOperatingAddress",
                type: "string"
              },
              {
                name: "shopCommittee",
                alias: "shopCommittee",
                type: "string"
              },
              {
                name: "shopCategory",
                alias: "shopCategory",
                type: "string"
              },
              {
                name: "shopSubcategory",
                alias: "shopSubcategory",
                type: "string"
              },
              {
                name: "shopLittlecategory",
                alias: "shopLittlecategory",
                type: "string"
              },
              {
                name: "shopContract",
                alias: "shopContract",
                type: "string"
              },
              {
                name: "shopContactPhone",
                alias: "shopContactPhone",
                type: "string"
              },
              {
                name: "shopHouseOwnership",
                alias: "shopHouseOwnership",
                type: "string"
              }
            ],
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/home_lzz_map_icon_xctg.png",
                width: "29px",
                height: "37px"
              }
            },
          })
        }

        if (this.shopCheckedNoPassList.length > 0) {
          this.shopCheckedNoPassList.forEach(shopCheckedNoPass => {
            let shop = shopCheckedNoPass.shop
            shop.graphicType = 'shop'
            const attributes = JSON.parse(JSON.stringify(shop))
            if (shop.type === "point" && shop.coordinate) {
              const p = shop.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const shopGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.shopCheckedNoPassGraphicArr.push(shopGraphic)
            }
          })
          this.shopCheckedNoPassFeatureLayer = new FeatureLayer({
            source: this.shopCheckedNoPassGraphicArr,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "shopName",
                alias: "shopName",
                type: "string"
              },
              {
                name: "shopAddress",
                alias: "shopAddress",
                type: "string"
              },
              {
                name: "shopLicense",
                alias: "shopLicense",
                type: "string"
              },
              {
                name: "shopCreditCode",
                alias: "shopCreditCode",
                type: "string"
              },
              {
                name: "shopRegisterAddress",
                alias: "shopRegisterAddress",
                type: "string"
              },
              {
                name: "shopOperatingAddress",
                alias: "shopOperatingAddress",
                type: "string"
              },
              {
                name: "shopCommittee",
                alias: "shopCommittee",
                type: "string"
              },
              {
                name: "shopCategory",
                alias: "shopCategory",
                type: "string"
              },
              {
                name: "shopSubcategory",
                alias: "shopSubcategory",
                type: "string"
              },
              {
                name: "shopLittlecategory",
                alias: "shopLittlecategory",
                type: "string"
              },
              {
                name: "shopContract",
                alias: "shopContract",
                type: "string"
              },
              {
                name: "shopContactPhone",
                alias: "shopContactPhone",
                type: "string"
              },
              {
                name: "shopHouseOwnership",
                alias: "shopHouseOwnership",
                type: "string"
              }
            ],
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/home_lzz_map_icon_xcwtg.png",
                width: "29px",
                height: "37px"
              }
            },
          })
        }



        // 本月巡检商铺
        if (this.shopCheckedMonthList.length > 0) {
          this.shopCheckedMonthList.forEach(shopCheckedMonth => {
            let shop = shopCheckedMonth.shop
            shop.graphicType = 'shop'
            const attributes = JSON.parse(JSON.stringify(shop))
            if (shop.type === "point" && shop.coordinate) {
              const p = shop.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const shopGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.shopCheckedMonthGraphicArr.push(shopGraphic)
            }
          })
          this.shopCheckedMonthFeatureLayer = new FeatureLayer({
            source: this.shopCheckedMonthGraphicArr,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "shopName",
                alias: "shopName",
                type: "string"
              },
              {
                name: "shopAddress",
                alias: "shopAddress",
                type: "string"
              },
              {
                name: "shopLicense",
                alias: "shopLicense",
                type: "string"
              },
              {
                name: "shopCreditCode",
                alias: "shopCreditCode",
                type: "string"
              },
              {
                name: "shopRegisterAddress",
                alias: "shopRegisterAddress",
                type: "string"
              },
              {
                name: "shopOperatingAddress",
                alias: "shopOperatingAddress",
                type: "string"
              },
              {
                name: "shopCommittee",
                alias: "shopCommittee",
                type: "string"
              },
              {
                name: "shopCategory",
                alias: "shopCategory",
                type: "string"
              },
              {
                name: "shopSubcategory",
                alias: "shopSubcategory",
                type: "string"
              },
              {
                name: "shopLittlecategory",
                alias: "shopLittlecategory",
                type: "string"
              },
              {
                name: "shopContract",
                alias: "shopContract",
                type: "string"
              },
              {
                name: "shopContactPhone",
                alias: "shopContactPhone",
                type: "string"
              },
              {
                name: "shopHouseOwnership",
                alias: "shopHouseOwnership",
                type: "string"
              }
            ],
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/home_lzz_map_icon_xctg.png",
                width: "29px",
                height: "37px"
              }
            },
          })
        }

        if (this.shopCheckedNoPassMonthList.length > 0) {
          this.shopCheckedNoPassMonthList.forEach(shopCheckedNoPassMonth => {
            let shop = shopCheckedNoPassMonth.shop
            shop.graphicType = 'shop'
            const attributes = JSON.parse(JSON.stringify(shop))
            if (shop.type === "point" && shop.coordinate) {
              const p = shop.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const shopGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.shopCheckedNoPassMonthGraphicArr.push(shopGraphic)
            }
          })
          this.shopCheckedNoPassMonthFeatureLayer = new FeatureLayer({
            source: this.shopCheckedNoPassMonthGraphicArr,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "shopName",
                alias: "shopName",
                type: "string"
              },
              {
                name: "shopAddress",
                alias: "shopAddress",
                type: "string"
              },
              {
                name: "shopLicense",
                alias: "shopLicense",
                type: "string"
              },
              {
                name: "shopCreditCode",
                alias: "shopCreditCode",
                type: "string"
              },
              {
                name: "shopRegisterAddress",
                alias: "shopRegisterAddress",
                type: "string"
              },
              {
                name: "shopOperatingAddress",
                alias: "shopOperatingAddress",
                type: "string"
              },
              {
                name: "shopCommittee",
                alias: "shopCommittee",
                type: "string"
              },
              {
                name: "shopCategory",
                alias: "shopCategory",
                type: "string"
              },
              {
                name: "shopSubcategory",
                alias: "shopSubcategory",
                type: "string"
              },
              {
                name: "shopLittlecategory",
                alias: "shopLittlecategory",
                type: "string"
              },
              {
                name: "shopContract",
                alias: "shopContract",
                type: "string"
              },
              {
                name: "shopContactPhone",
                alias: "shopContactPhone",
                type: "string"
              },
              {
                name: "shopHouseOwnership",
                alias: "shopHouseOwnership",
                type: "string"
              }
            ],
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/home_lzz_map_icon_xcwtg.png",
                width: "29px",
                height: "37px"
              }
            },
          })
        }

        if (this.monthNotCheckShopList.length > 0) {
          this.monthNotCheckShopList.forEach(monthNotCheckShop => {
            let shop = monthNotCheckShop
            shop.graphicType = 'shop'
            const attributes = JSON.parse(JSON.stringify(shop))
            if (shop.type === "point" && shop.coordinate) {
              const p = shop.coordinate.split(",");
              const point = new Point({
                x: p[0],
                y: p[1],
                spatialReference: SpatialReference.WebMercator,
              });
              const shopGraphic = new Graphic({
                geometry: point,
                attributes: attributes,
              });
              this.monthNotCheckShopGraphicArr.push(shopGraphic)
            }
          })
          this.monthNotCheckShopFeatureLayer = new FeatureLayer({
            source: this.monthNotCheckShopGraphicArr,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "shopName",
                alias: "shopName",
                type: "string"
              },
              {
                name: "shopAddress",
                alias: "shopAddress",
                type: "string"
              },
              {
                name: "shopLicense",
                alias: "shopLicense",
                type: "string"
              },
              {
                name: "shopCreditCode",
                alias: "shopCreditCode",
                type: "string"
              },
              {
                name: "shopRegisterAddress",
                alias: "shopRegisterAddress",
                type: "string"
              },
              {
                name: "shopOperatingAddress",
                alias: "shopOperatingAddress",
                type: "string"
              },
              {
                name: "shopCommittee",
                alias: "shopCommittee",
                type: "string"
              },
              {
                name: "shopCategory",
                alias: "shopCategory",
                type: "string"
              },
              {
                name: "shopSubcategory",
                alias: "shopSubcategory",
                type: "string"
              },
              {
                name: "shopLittlecategory",
                alias: "shopLittlecategory",
                type: "string"
              },
              {
                name: "shopContract",
                alias: "shopContract",
                type: "string"
              },
              {
                name: "shopContactPhone",
                alias: "shopContactPhone",
                type: "string"
              },
              {
                name: "shopHouseOwnership",
                alias: "shopHouseOwnership",
                type: "string"
              }
            ],
            outFields: ["*"],
            renderer: {
              type: "simple",
              symbol: {
                type: "picture-marker",
                url: "http://**************/images/icon/home_lzz_map_icon_wxj.png",
                width: "29px",
                height: "37px"
              }
            },
            // featureReduction: clusterConfig,
          })
        }

      })
    })

    listScreenRoadStreetChief(this.queryParams).then(response => {
      this.roadStreetChiefList = response.data
      loadModules([
          "esri/Graphic",
          "esri/geometry/Point",
          "esri/geometry/Polyline",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Point,
                              Polyline,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let roadStreetChiefPointRenderer = {
          type: "unique-value",
          field: "isKeynoteRoad",
          defaultSymbol: {
            type: "picture-marker",
            url: "http://**************/images/icon/icon_streetchief_non_key.png",
            width: "36px",
            height: "46px"
          },
          uniqueValueInfos: []
        }

        this.roadStreetChiefList.forEach(roadStreetChief => {
          roadStreetChief.graphicType = 'roadStreetChiefPoint'
          const attributes = JSON.parse(JSON.stringify(roadStreetChief))
          if (roadStreetChief.type2 === "point" && roadStreetChief.coordinate2) {
            let url = "http://**************/images/icon/icon_streetchief_non_key.png";
            let width = "36px"
            let height = "46px"
            if (roadStreetChief.isKeynoteRoad === '是') {
              url = "http://**************/images/icon/icon_streetchief.png";
              width = "45px"
              height = "58px"
            }
            let uniqueValueInfo = {
              value: roadStreetChief.isKeynoteRoad,
              symbol: {
                type: "picture-marker",
                url: url,
                width: width,
                height: height,
              }
            }
            roadStreetChiefPointRenderer.uniqueValueInfos.push(uniqueValueInfo)
            const p = roadStreetChief.coordinate2.split(",");
            const point = new Point({
              x: p[0],
              y: p[1],
              spatialReference: SpatialReference.WebMercator,
            });
            const roadStreetChiefPointGraphic = new Graphic({
              geometry: point,
              attributes: attributes,
            });
            this.roadStreetChiefPointGraphicArr.push(roadStreetChiefPointGraphic)
          }
        })
        this.roadStreetChiefPointFeatureLayer = new FeatureLayer({
          source: this.roadStreetChiefPointGraphicArr,
          fields: [
            {
              name: "ObjectID",
              alias: "ObjectID",
              type: "oid"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "roadName",
              alias: "roadName",
              type: "string"
            },
            {
              name: "streetChief",
              alias: "streetChief",
              type: "string"
            },
            {
              name: "chiefPhone",
              alias: "chiefPhone",
              type: "string"
            },
            {
              name: "type",
              alias: "type",
              type: "string"
            },
            {
              name: "coordinate",
              alias: "coordinate",
              type: "string"
            },
            {
              name: "streetSubChief",
              alias: "streetSubChief",
              type: "string"
            },
            {
              name: "subChiefPhone",
              alias: "subChiefPhone",
              type: "string"
            },
            {
              name: "streetThirdChief",
              alias: "streetThirdChief",
              type: "string"
            },
            {
              name: "thirdChiefPhone",
              alias: "thirdChiefPhone",
              type: "string"
            },
            {
              name: "type2",
              alias: "type2",
              type: "string"
            },
            {
              name: "coordinate2",
              alias: "coordinate2",
              type: "string"
            },
            {
              name: "graphicType",
              alias: "graphicType",
              type: "string"
            },
            {
              name: "roadId",
              alias: "roadId",
              type: "string"
            },
            {
              name: "isKeynoteRoad",
              alias: "isKeynoteRoad",
              type: "string"
            },
          ],
          outFields: ["*"],
          renderer: roadStreetChiefPointRenderer,
          popupTemplate: {
            title: "街长信息",
            content: [{
              type: "fields",
              fieldInfos: [
                {
                  fieldName: "roadName",
                  label: "路名"
                },
                {
                  fieldName: "streetChief",
                  label: "街长"
                },
              ]
            }],
            outFields: ["*"],
            actions: [
              {
                title: "详细",
                id: "popup-detail",
              }
            ]
          },
        })
      })
    })

    //居委会信息
    listScreenCommittee(this.queryParams).then(response => {
      const committeeList = response.data
      const committeeStreetChiefGraphicArr = []
      loadModules([
          "esri/Graphic",
          "esri/geometry/Polygon",
          "esri/geometry/SpatialReference",
          "esri/layers/FeatureLayer",
        ],
        this.options).then(([
                              Graphic,
                              Polygon,
                              SpatialReference,
                              FeatureLayer,
                            ]) => {

        let committeeStreetChiefRenderer = {
          type: "unique-value",
          field: "committeeName",
          defaultSymbol: {
            type: "simple-fill",
            color: [52, 125, 244, 0.2],
            outline: {
              color: [57, 133, 246, 0],
              width: 2
            }
          },
          uniqueValueInfos: []
        }

        committeeList.forEach(committee => {
          const streetFillColor = committee.streetFillColor ? committee.streetFillColor.split(",") : [52, 125, 244, 0.2]
          const streetOutlineColor = committee.streetOutlineColor ? committee.streetOutlineColor.split(",") : [57, 133, 246, 0]

          let uniqueValueInfo = {
            value: committee.committeeName,
            symbol: {
              type: "simple-fill",
              color: streetFillColor,
              outline: {
                color: streetOutlineColor,
                width: 2
              }
            }
          }
          committeeStreetChiefRenderer.uniqueValueInfos.push(uniqueValueInfo)
          const attributes = JSON.parse(JSON.stringify(committee))
          if (committee.coordinate) {
            if (committee.type === "polygon") {
              const r = parseCoordinate(committee.coordinate);
              const polygon = new Polygon({
                rings: r,
                spatialReference: SpatialReference.WebMercator,
              });
              const committeeStreetChiefGraphic = new Graphic({
                geometry: polygon,
                attributes: attributes,
              })
              committeeStreetChiefGraphicArr.push(committeeStreetChiefGraphic)
            }
          }
        })
        const fields = [
          {
            name: "ObjectID",
            alias: "ObjectID",
            type: "oid"
          },
          {
            name: "committeeName",
            alias: "committeeName",
            type: "string"
          }
        ]
        this.committeeStreetChiefFeatureLayer = new FeatureLayer({
          source: committeeStreetChiefGraphicArr,
          fields: fields,
          renderer: committeeStreetChiefRenderer,
        })
        const labelClass = {
          symbol: {
            type: "text",
            color: "white",
            font: {
              size: 14,
              weight: "bold"
            }
          },
          labelPlacement: "always-horizontal",
          labelExpressionInfo: {
            expression: "$feature.committeeName"
          }
        };
        this.committeeStreetChiefFeatureLayer.labelingInfo = [labelClass];

      })
    })

  },
  methods: {

    checkedShop(type) {
      if (type === 'pass') {
        if (this.shopCheckedList.length > 0) {
          this.map.add(this.shopCheckedFeatureLayer)
        }
      } else if (type === 'noPass') {
        if (this.shopCheckedNoPassList.length > 0) {
          this.map.add(this.shopCheckedNoPassFeatureLayer)
        }
      } else if (type === 'remove') {
        this.map.remove(this.shopCheckedFeatureLayer)
        this.map.remove(this.shopCheckedNoPassFeatureLayer)
      }
    },
    checkedShopMonth(type) {
      if (type === 'pass') {
        if (this.shopCheckedMonthList.length > 0) {
          this.map.add(this.shopCheckedMonthFeatureLayer)
        }
      } else if (type === 'noPass') {
        if (this.shopCheckedNoPassMonthList.length > 0) {
          this.map.add(this.shopCheckedNoPassMonthFeatureLayer)
        }
      } else if (type === 'noCheck') {
        if (this.monthNotCheckShopList.length > 0) {
          this.map.add(this.monthNotCheckShopFeatureLayer)
        }
      } else if (type === 'remove') {
        this.map.remove(this.shopCheckedMonthFeatureLayer)
        this.map.remove(this.shopCheckedNoPassMonthFeatureLayer)
        this.map.remove(this.monthNotCheckShopFeatureLayer)
      }
    },
    addRoadStreetChief() {
      this.map.add(this.roadStreetChiefPointFeatureLayer)
    },
    removeRoadStreetChief(){
      this.map.remove(this.roadStreetChiefPointFeatureLayer)
    },
    addCommitteeStreetChief() {
      this.map.add(this.committeeStreetChiefFeatureLayer)
    },
    drawRoad(attributes) {
      // 判断路段是否已经绘制
      const graphic = this.graphicsLayer.graphics.items.find(item=>item.attributes.roadName === attributes.roadName)
      if (graphic) {
        this.graphicsLayer.remove(graphic)
        return
      }

      // 根据路段坐标绘制路段
      loadModules(["esri/Graphic",
        "esri/geometry/Point",
        "esri/geometry/Polyline",
        "esri/geometry/SpatialReference",], this.options).then(([Graphic,
                                                                        Point,
                                                                        Polyline,
                                                                        SpatialReference,]) => {
        const roadStreetChief = JSON.parse(JSON.stringify(attributes))
        roadStreetChief.graphicType = 'roadStreetChief'
        const centerPoint = new Point({
          x: roadStreetChief.coordinate2.split(",")[0],
          y: roadStreetChief.coordinate2.split(",")[1],
          spatialReference: 3857,
        })
        this.view.goTo({
          target: centerPoint,
          zoom: 10,
        }, {
          duration: 500,
          easing: "ease-in-out"
        })
        if (roadStreetChief.type === "polyline" && roadStreetChief.coordinate) {
          const p = parseCoordinate(roadStreetChief.coordinate)
          const polyline = new Polyline({
            paths: p,
            spatialReference: SpatialReference.WebMercator,
          })
          const g = new Graphic({
            geometry: polyline,
            attributes: roadStreetChief,
            fields: [
              {
                name: "ObjectID",
                alias: "ObjectID",
                type: "oid"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "roadName",
                alias: "roadName",
                type: "string"
              },
              {
                name: "streetChief",
                alias: "streetChief",
                type: "string"
              },
              {
                name: "chiefPhone",
                alias: "chiefPhone",
                type: "string"
              },
              {
                name: "type",
                alias: "type",
                type: "string"
              },
              {
                name: "coordinate",
                alias: "coordinate",
                type: "string"
              },
              {
                name: "streetSubChief",
                alias: "streetSubChief",
                type: "string"
              },
              {
                name: "subChiefPhone",
                alias: "subChiefPhone",
                type: "string"
              },
              {
                name: "type2",
                alias: "type2",
                type: "string"
              },
              {
                name: "coordinate2",
                alias: "coordinate2",
                type: "string"
              },
              {
                name: "graphicType",
                alias: "graphicType",
                type: "string"
              },
              {
                name: "roadId",
                alias: "roadId",
                type: "string"
              },
              {
                name: "isKeynoteRoad",
                alias: "isKeynoteRoad",
                type: "string"
              },
            ],
            outFields: ["*"],
            symbol: {
              type: "simple-line",
              color: [77, 238, 250],
              width: 6
            },
            popupTemplate: {
              title: "路段信息",
              content: [{
                type: "fields",
                fieldInfos: [
                  {
                    fieldName: "roadName",
                    label: "路名"
                  },
                  {
                    fieldName: "streetChief",
                    label: "街长"
                  },
                  // {
                  //   fieldName: "chiefPhone",
                  //   label: "联系方式"
                  // },
                  // {
                  //   fieldName: "streetSubChief",
                  //   label: "第二街长"
                  // },
                  // {
                  //   fieldName: "subChiefPhone",
                  //   label: "联系电话"
                  // },


                ]
              }],
              outFields: ["*"],
              actions: [
                {
                  title: "详细",
                  id: "popup-detail",
                }
              ]
            },

          })
          this.graphicsLayer.add(g)
        }
      });
    },
    drawShop(roadName) {
      const streetChiefShopList = this.shopList.filter(item => item.chiefRoad === roadName)
      // const shopCheckLogThreeList = this.
      // 判断 streetChiefShopList 是否为空
      if (streetChiefShopList.length === 0) {
        return
      }

      // 判断商铺是否已经绘制
      const graphicList = this.graphicsLayer.graphics.items.filter(item => item.attributes.chiefRoad === roadName)
      // debugger
      // console.log(" 判断商铺是否已经绘制")
      // console.log(graphicList)
      // 判断 graphicList 是否为空
      if (graphicList.length > 0) {
        graphicList.forEach(graphic => {
          this.graphicsLayer.remove(graphic)
        })
        return
      }

      loadModules(["esri/Graphic",
        "esri/geometry/Point",
        "esri/geometry/SpatialReference",], this.options).then(([Graphic,
                                                                  Point,
                                                                  SpatialReference,]) => {
         streetChiefShopList.forEach(shop => {
           shop.graphicType = 'shopChecked'
           const attributes = JSON.parse(JSON.stringify(shop))

           if (shop.type === "point" && shop.coordinate) {
             const p = shop.coordinate.split(",");
             const point = new Point({
               x: p[0],
               y: p[1],
               spatialReference: SpatialReference.WebMercator,
             });

             // 商铺默认样式
             let pointSymbol = {
               type: "picture-marker",
               url: "http://**************/images/icon/xianzhaung_icon_yanjieshangpu.png",
               width: "29px",
               height: "37px"
             }

             // 判断 monthNotCheckShopList 是否包含 id 为 shop.id 的商铺
             const isNotCheck = this.monthNotCheckShopList.some(item => item.id === shop.id)
             const isChecked = this.shopCheckedMonthList.some(item => item.shop.id === shop.id)
             const isCheckedNoPass = this.shopCheckedNoPassMonthList.some(item => item.shop.id === shop.id)


             if (isNotCheck) {
               pointSymbol = {
                 type: "picture-marker",
                 url: "http://**************/images/icon/home_lzz_map_icon_wxj.png",
                 width: "29px",
                 height: "37px"
               }
             } else if (isChecked) {
               pointSymbol = {
                 type: "picture-marker",
                 url: "http://**************/images/icon/home_lzz_map_icon_xctg.png",
                 width: "29px",
                 height: "37px"
               }
             } else if (isCheckedNoPass) {
               pointSymbol = {
                 type: "picture-marker",
                 url: "http://**************/images/icon/home_lzz_map_icon_xcwtg.png",
                 width: "29px",
                 height: "37px"
               }
             }

             const shopGraphic = new Graphic({
               geometry: point,
               attributes: attributes,
               fields: [
                 {
                   name: "ObjectID",
                   alias: "ObjectID",
                   type: "oid"
                 },
                 {
                   name: "graphicType",
                   alias: "graphicType",
                   type: "string"
                 },
                 {
                   name: "shopName",
                   alias: "shopName",
                   type: "string"
                 },
                 {
                   name: "shopAddress",
                   alias: "shopAddress",
                   type: "string"
                 },
                 {
                   name: "shopLicense",
                   alias: "shopLicense",
                   type: "string"
                 },
                 {
                   name: "shopCreditCode",
                   alias: "shopCreditCode",
                   type: "string"
                 },
                 {
                   name: "shopRegisterAddress",
                   alias: "shopRegisterAddress",
                   type: "string"
                 },
                 {
                   name: "shopOperatingAddress",
                   alias: "shopOperatingAddress",
                   type: "string"
                 },
                 {
                   name: "shopCommittee",
                   alias: "shopCommittee",
                   type: "string"
                 },
                 {
                   name: "shopCategory",
                   alias: "shopCategory",
                   type: "string"
                 },
                 {
                   name: "shopSubcategory",
                   alias: "shopSubcategory",
                   type: "string"
                 },
                 {
                   name: "shopLittlecategory",
                   alias: "shopLittlecategory",
                   type: "string"
                 },
                 {
                   name: "shopContract",
                   alias: "shopContract",
                   type: "string"
                 },
                 {
                   name: "shopContactPhone",
                   alias: "shopContactPhone",
                   type: "string"
                 },
                 {
                   name: "shopHouseOwnership",
                   alias: "shopHouseOwnership",
                   type: "string"
                 }
               ],
               symbol: pointSymbol,
               popupTemplate: {
               title: "沿街商铺",
                 content: [{
                 type: "fields",
                 fieldInfos: [
                   {
                     fieldName: "shopName",
                     label: "商铺名称"
                   },
                   {
                     fieldName: "shopCommittee",
                     label: "所属居委会"
                   },
                 ]
               }],
                 outFields: ["*"],
                 actions: [
                 {
                   title: "详细",
                   id: "popup-detail",
                 }
               ]
             }
             });
             this.graphicsLayer.add(shopGraphic)
           }

         })

      });

    },

  }
}
</script>

