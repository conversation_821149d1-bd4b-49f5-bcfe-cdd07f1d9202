<template>
  <div id="container" @click.stop>

    <!-- 沿街商铺弹窗 -->
<!--    <transition name="el-zoom-in-center">-->
<!--      <ShopWindow-->
<!--        :shopData = popData-->
<!--        :checkLogData = shopcheckLogThreeList-->
<!--        @closeWindow="closeShopWindow"-->
<!--        ref="shopWindow"-->
<!--        v-show="isShopWindowShow"-->
<!--      >-->
<!--      </ShopWindow>-->
<!--    </transition>-->
    <!-- 沿街商铺弹窗 -->
    <transition name="el-zoom-in-center">
      <ShopWindow
        :shopData = popData
        :checkLogData = shopcheckLogThreeList
        @closeWindow="closeShopWindow"
        ref="shopWindow"
        v-show="isShopWindowShow"
      >
      </ShopWindow>
    </transition>

    <!-- 房地信息弹窗 -->
    <transition name="el-zoom-in-center">
      <PremisesSurveyWindow
        :parcelInformationData="popData"
        @closeWindow="closePremisesSurveyWindow"
        ref="premisesSurveyWindow"
        v-show="isPremisesSurveyWindowShow"
      >
      </PremisesSurveyWindow>
    </transition>

    <!-- 厂房仓库弹窗 -->
    <transition name="el-zoom-in-center">
      <FactoryWarehouseWindow
        :factoryWarehouseData="popData"
        @closeWindow="closeFactoryWarehouseWindow"
        ref="factoryWarehouseWindow"
        v-show="isFactoryWarehouseWindowShow"
      >
      </FactoryWarehouseWindow>
    </transition>

    <!--    井盖信息弹窗 -->
    <transition name="el-zoom-in-center">
      <CoversWindow
        :coverData ="popData"
        @closeWindow ="closeCoverWindow"
        ref="coversWindow"
        v-show="isCoverWindowShow"
      >
      </CoversWindow>
    </transition>

    <!--   非住宅物业弹窗 -->
    <transition name="el-zoom-in-center">
      <PropertyContractWindow
        :propertyContractData ="popData"
        @closeWindow ="closePropertyContractWindow"
        ref="propertyContractWindow"
        v-show="isPropertyContractWindowShow"
      >
      </PropertyContractWindow>
    </transition>

    <!-- 公寓租赁房弹窗 -->
    <transition name="el-zoom-in-center">
      <ApartmentRentalWindow
        :apartmentRentalData="popData"
        @closeWindow ="closeApartmentRentalWindow"
        ref="apartmentRentalWindow"
        v-show="isApartmentRentalWindowShow"
      >
      </ApartmentRentalWindow>
    </transition>

    <!-- 娱乐场所弹窗 -->
    <transition name="el-zoom-in-center">
      <EntainmentWindow
        :entertainmentData="popData"
        @closeWindow ="closeEntainmentWindow"
        ref="entertainmentWindow"
        v-show="isEntertainmentWindowShow"
      >
      </EntainmentWindow>
    </transition>

    <!-- 小区出入口所弹窗 -->
    <transition name="el-zoom-in-center">
      <EntranceWindow
        :entranceData="popData"
        @closeWindow ="closeEntranceWindow"
        ref="entranceWindow"
        v-show="isResidentialEntranceWindow"
      >
      </EntranceWindow>
    </transition>

    <!--    物业管理处弹窗 -->
    <transition name="el-zoom-in-center">
      <PropertyManageWindow
        :propertyManageData="popData"
        @closeWindow ="closePropertyManageWindow"
        ref="propertyManageWindow"
        v-show="isNonResidentialPropertyWindow"
      >
      </PropertyManageWindow>
    </transition>

    <!--    垃圾房弹窗 -->
    <transition name="el-zoom-in-center">
      <GarbageHouseWindow
        :garbageHouseData="popData"
        @closeWindow ="closeGarbageHouseWindow"
        ref="garbageHouseWindow"
        v-show="isGarbageHouseWindow"
      >
      </GarbageHouseWindow>
    </transition>

    <!-- 两类人员网格化弹窗 -->
    <transition name="el-zoom-in-center">
      <PersonnelGridWindow
        :personnelGridData="popData"
        :visitRecords="visitRecords"
        @closeWindow ="closePersonnelGridWindow"
        ref="personnelGridWindow"
        v-show="isPersonnelGridWindow"
      >
      </PersonnelGridWindow>
    </transition>

    <!-- 综合网格工作站弹窗 -->
    <transition name="el-zoom-in-center">
      <ComprehensiveGridWindow
        @closeWindow ="closeComprehensiveGridWindow"
        ref="comprehensiveGridWindow"
        v-show="isComprehensiveGridWindowShow"
        :gridId="gridId"
      >
      </ComprehensiveGridWindow>
    </transition>

    <!--    街长信息弹窗-->
    <transition name="el-zoom-in-center">
      <RoadChiefWindow
        :roadChiefData="popData"
        @closeWindow ="closeRoadChiefWindow"
        ref="roadChiefWindow"
        v-show="isRoadCheifWindowShow"
      >
      </RoadChiefWindow>
    </transition>

    <!-- 路名信息弹窗-->
    <transition name="el-zoom-in-center">
      <RoadWindow
        :roadData="popData"
        @closeWindow ="closeRoadWindow"
        ref="roadWindow"
        v-show="isRoadWindowShow"
      >
      </RoadWindow>
    </transition>

    <!-- 监控弹窗-->
    <transition name="el-zoom-in-center">
      <camera-window
        :cameraIndexCode="cameraIndexCode"
        @closeWindow ="closeCameraWindow"
        ref="cameraWindow"
        v-show="isCameraWindowShow"
      >
      </camera-window>
    </transition>

    <!-- 搜索框弹窗 -->
    <transition name="el-zoom-in-center">
      <SearchWindow @searchMap="searchMap" v-show="isInputShow"></SearchWindow>
    </transition>

    <!-- 搜索监控框弹窗 -->
    <transition name="el-zoom-in-center">
      <SearchCameraWindow @searchCamera="searchCamera" v-show="isSearchCameraShow"></SearchCameraWindow>
    </transition>

    <!-- 标识指示弹窗 -->
    <transition name="el-zoom-in-center">
      <TipsWindow ref="tipsWindow" @checkedTips="checkedTips" v-show="isTipsShow"></TipsWindow>
    </transition>

    <!-- 报警记录弹窗 -->
    <transition name="el-zoom-in-center">
      <AlarmRecordWindow
        :alarmRecordData="alarmRecordData"
        @closeWindow ="closeAlarmRecordWindow"
        ref="alarmRecordWindow"
        v-show="isAlarmRecordWindowShow"
      >
      </AlarmRecordWindow>
    </transition>

    <!-- Icc设备弹窗 -->
    <transition name="el-zoom-in-center">
      <IccDeviceWindow
        :iccDeviceData="iccDeviceData"
        @closeWindow ="closeIccDeviceWindow"
        ref="iccDeviceWindow"
        v-show="isIccDeviceWindowShow"
      >
      </IccDeviceWindow>
    </transition>

    <!-- Icc设备实时预览弹窗 -->
    <transition name="el-zoom-in-center">
      <IccDevicePreviewWindow
        :device-name="iccDeviceName"
        :previewUrl="iccDevicePreviewUrl"
        @closeWindow ="closeIccDevicePreviewWindow"
        ref="iccDevicePreviewWindow"
        v-show="isIccDevicePreviewWindowShow"
      >
      </IccDevicePreviewWindow>
    </transition>

    <!-- 防汛防台重点区域弹窗 -->
    <transition name="el-zoom-in-center">
      <FloodControlKeyAreaWindow
        :floodControlKeyAreaData="popData"
        @closeWindow ="closeFloodControlKeyAreaWindow"
        ref="floodControlKeyAreaWindow"
        v-show="isFloodControlKeyAreaWindowShow"
      >
      </FloodControlKeyAreaWindow>
    </transition>

    <!-- 防汛防台重点区域关联视频弹窗 -->
    <transition name="el-zoom-in-center">
      <FloodControlKeyAreaCameraWindow
        :floodControlKeyAreaCameraData="popData"
        @closeWindow ="closeFloodControlKeyAreaCameraWindow"
        ref="floodControlKeyAreaCameraWindow"
        v-if="isFloodControlKeyAreaCameraWindowShow"
      >
      </FloodControlKeyAreaCameraWindow>
    </transition>

    <!-- 液位报警装置关联视频弹窗 -->
    <transition name="el-zoom-in-center">
      <LiquidLevelDeviceCameraWindow
        :liquidLevelDeviceCameraData="popData"
        @closeWindow ="closeLiquidLevelDeviceCameraWindow"
        ref="liquidLevelDeviceCameraWindow"
        v-if="isLiquidLevelDeviceCameraWindowShow"
      >
      </LiquidLevelDeviceCameraWindow>
    </transition>

    <!-- 海康视频监控预览 -->
    <transition name="el-zoom-in-center">
      <HikPreviewWindow
        :previewUrl="previewUrl"
        :currentCameraIndexCode="currentCameraIndexCode"
        @closeWindow ="closeHikPreviewWindow"
        ref="hikPreviewWindow"
        v-if="isHikPreviewWindowShow"
      >
      </HikPreviewWindow>
    </transition>

    <!-- 视频监控分组 -->
    <transition name="el-zoom-in-center">
      <CameraGroupWindow
        :previewUrl="previewUrl"
        @closeWindow ="closeCameraGroupWindow"
        ref="cameraGroupWindow"
        v-if="isCameraGroupWindowShow"
      >
      </CameraGroupWindow>
    </transition>

    <!-- 框选查找的监控 -->
    <transition name="el-zoom-in-center">
      <CameraFindWindow
        :findCameraList="findCameraList"
        @closeWindow ="closeCameraFindWindow"
        ref="cameraFindWindow"
        v-if="isCameraFindWindowShow"
      >
      </CameraFindWindow>
    </transition>

    <!-- 突发事件详情弹窗 -->
    <transition name="el-zoom-in-center">
      <EmergentWindow
        :emergent="popData"
        @closeWindow ="closeEmergentWindow"
        ref="emergentWindow"
        v-show="isEmergentWindowShow"
      >
      </EmergentWindow>
    </transition>

    <!-- 地图操作浮窗 -->
    <!-- 还原 -->
    <div @click="winRestore()" class="floatingWin floatingWin-restore" title="刷新">刷新</div>
    <!-- 搜索 -->
    <div @click="winSearch()" :class="isSketchActive ? 'floatingWin floatingWin-search-press' : 'floatingWin floatingWin-search'" title="查找">查找</div>
    <!-- 视频预案 -->
    <div @click="winPatternSpot()" :class="isClickWin ? 'floatingWin floatingWin-patternSpot-press' : 'floatingWin floatingWin-patternSpot'" title="视频预案">视频预案</div>
    <!-- 卫星地图 电子地图 切换 -->
    <div @click="winSwitch()" :class="isSatelliteLayer ? 'floatingWin floatingWin-switch-press' : 'floatingWin floatingWin-switch'" title="视图切换">视图切换</div>
    <!-- 标识指示 -->
    <div @click="winTips()" :class="isTipsShow ? 'floatingWin floatingWin-tips-press' : 'floatingWin floatingWin-tips'" title="标识指示">标识指示</div>
    <!-- 监控 -->
    <div @click="winSurveillance()" :class="isSurveillanceShow ? 'floatingWin floatingWin-surveillance-press' : 'floatingWin floatingWin-surveillance'" title="监控">监控</div>
    <!-- 疫情防控 -->
<!--    <div @click="illnessControl()" class="floatingWin floatingWin-illnessControl"></div>-->

    <!-- 现状使用主体中的按钮选项 -->
    <div class="currentUserSelBtn" v-show="currentUserSelBtnShow">
      <div
        class="currentUser-item"
        v-for="(item, index) in currentUserSelBtnData"
        :key="item.id"
        @click="currentUserSelBtn(index)"
      >
        <span class="currentUser" :ref="'currentUser0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <!-- 绿化主体中的按钮选项 -->
    <div class="greeningSelBtn" v-show="greeningSelBtnShow">
      <div
        class="greening-item"
        v-for="(item, index) in greeningSelBtnData"
        :key="item.id"
        @click="greeningSelBtn(index)"
      >
        <span class="greening" :ref="'greening0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <!-- 小区基础信息中的icon按钮选项 -->
    <div class="iconSelBtn" v-show="iconSelBtnShow">
      <div
        class="icon-item"
        v-for="(item, index) in iconSelBtnData"
        :key="item.id"
        @click="iconSelBtn(index)"
      >
        <span class="icon" :ref="'icon0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <!-- 管网数据中的管网按钮选项 -->
    <div class="pipeSelBtn" v-show="pipeSelBtnShow">
      <div
        class="pipe-item"
        v-for="(item, index) in pipeSelBtnData"
        :key="item.id"
        @click="pipeSelBtn(index)"
      >
        <span class="pipe" :ref="'pipe0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>

    <!-- 防汛防台下方按钮选项 -->
    <div class="fxftSelBtn" v-show="fxftSelBtnShow">
      <div
        class="fxft-item"
        v-for="(item, index) in fxftSelBtnData"
        :key="item.id"
        @click="fxftSelBtn(index)"
      >
        <span class="fxft-check" :ref="'fxft0' + index"></span>
        <span class="fxft-title">{{ item.title }}</span>
      </div>
    </div>

  </div>
</template>

<script>

import ShopWindow from "@/components/ScreenAgsWindowInfo/ShopWindow";
import PremisesSurveyWindow from "@/components/ScreenAgsWindowInfo/PremisesSurveyWindow";
import FactoryWarehouseWindow from "@/components/ScreenAgsWindowInfo/FactoryWarehouseWindow";
import SearchWindow from "@/components/ScreenAgsWindowInfo/SearchWindow";
import SearchCameraWindow from "@/components/ScreenAgsWindowInfo/SearchCameraWindow";
import TipsWindow from "@/components/ScreenAgsWindowInfo/TipsWindow";
import CoversWindow from "@/components/ScreenAgsWindowInfo/CoversWindow";

import {loadModules} from "esri-loader";
import axios from "axios";

import DataMixin from "./mixin/dataMixin";
import CurrentUserDBMixin from "./mixin/currentUserDBMixin";
import PremisesSurveyDBMixin from "./mixin/premisesSurveyDBMixin";
import GreeningDBMixin from "./mixin/greeningDBMixin";
import CommunityBasicInfoMixin from "./mixin/communityBasicInfoMixin";
import PipeNetworkMixin from "./mixin/pipeNetworkMixin";
import StreetChiefMixin from "./mixin/streetChiefMixin";
import PropertyContractWindow from "@/components/ScreenAgsWindowInfo/PropertyContractWindow";
import ApartmentRentalWindow from "@/components/ScreenAgsWindowInfo/ApartmentRentalWindow";
import EntainmentWindow from "@/components/ScreenAgsWindowInfo/EntainmentWindow";
import EntranceWindow from "@/components/ScreenAgsWindowInfo/EntranceWindow";
import GarbageHouseWindow from "@/components/ScreenAgsWindowInfo/GarbageHouseWindow";
import PersonnelGridWindow from "@/components/ScreenAgsWindowInfo/PersonnelGridWindow";
import PropertyManageWindow from "@/components/ScreenAgsWindowInfo/PropertyManageWindow";
import RoadChiefWindow from "@/components/ScreenAgsWindowInfo/RoadChiefWindow";
import RoadWindow  from "@/components/ScreenAgsWindowInfo/RoadWindow";
import CameraWindow  from "@/components/ScreenAgsWindowInfo/CameraWindow";
import AlarmRecordWindow from "@/components/ScreenAgsWindowInfo/AlarmRecordWindow";
import IccDeviceWindow from "@/components/ScreenAgsWindowInfo/IccDeviceWindow";
import FloodControlKeyAreaWindow from "@/components/ScreenAgsWindowInfo/FloodControlKeyAreaWindow";
import FloodControlKeyAreaCameraWindow from "@/components/ScreenAgsWindowInfo/FloodControlKeyAreaCameraWindow";
import LiquidLevelDeviceCameraWindow from "@/components/ScreenAgsWindowInfo/LiquidLevelDeviceCameraWindow.vue";
import HikPreviewWindow from "@/components/ScreenAgsWindowInfo/HikPreviewWindow";
import CameraGroupWindow from "@/components/ScreenAgsWindowInfo/CameraGroupWindow";
import IccDevicePreviewWindow from "@/components/ScreenAgsWindowInfo/IccDevicePreviewWindow.vue";
import CameraFindWindow from "@/components/ScreenAgsWindowInfo/CameraFindWindow.vue";

import {getThreedayShopCheckLog} from "@/api/shcy/shopCheckLog";
import {getAlarmRecordByImei, getHikPreviewUrl, getHikPreviewUrlWs, getHjzzMonitorVideo} from "@/api/shcy/screen";
import emergentMixin from "./mixin/emergentMixin";
import EmergentWindow from "@/components/ScreenAgsWindowInfo/EmergentWindow.vue";
import ComprehensiveGridWindow from "@/components/ScreenAgsWindowInfo/ComprehensiveGridWindow.vue";


export default {
  props: ["btnTitle"],
  components: {
    EmergentWindow,
    PropertyContractWindow,
    TipsWindow,
    ShopWindow,
    PremisesSurveyWindow,
    FactoryWarehouseWindow,
    SearchWindow,
    SearchCameraWindow,
    CoversWindow,
    ApartmentRentalWindow,
    EntainmentWindow,
    EntranceWindow,
    GarbageHouseWindow,
    PersonnelGridWindow,
    PropertyManageWindow,
    RoadChiefWindow,
    RoadWindow,
    CameraWindow,
    AlarmRecordWindow,
    IccDeviceWindow,
    FloodControlKeyAreaWindow,
    FloodControlKeyAreaCameraWindow,
    HikPreviewWindow,
    CameraGroupWindow,
    IccDevicePreviewWindow,
    LiquidLevelDeviceCameraWindow,
    CameraFindWindow,
    ComprehensiveGridWindow,
  },
  mixins: [DataMixin, CurrentUserDBMixin, PremisesSurveyDBMixin, GreeningDBMixin, CommunityBasicInfoMixin, PipeNetworkMixin, StreetChiefMixin, emergentMixin],
  data() {
    return {
      map: null,
      view: null,
      centerPoint: null,
      zwLayer: null,
      airLayer: null,
      shjdFeatureLayer: null,
      graphicsLayer: null,
      findGraphicsLayer: null,
      popData:{},
      visitRecords:[],
      //查询所有检查列表
      shopcheckLogThreeList:[],
      options:{
        url: 'http://**************/arcgis9080/arcgis_js_api/javascript/4.21/init.js',
        css: 'http://**************/arcgis9080/arcgis_js_api/javascript/4.21/esri/themes/dark/main.css'
      },
      isSketchActive: false,
      sketch: null,
      // 框选的cameraList
      findCameraList: [],
    }
  },
  mounted() {
    this.initMap()
    document.addEventListener('click', this.closePop)
  },
  methods: {
    initMap() {
      axios.get("http://**************/arcgis/api/auth/get_token?user_key=PacaHWGx6awBNifTckMxrwXkd44nMF66").then(response => {
        const jwt = response.data.data
        loadModules([
            "esri/Map",
            "esri/views/MapView",
            "esri/layers/TileLayer",
            "esri/Graphic",
            "esri/layers/GraphicsLayer",
            "esri/layers/FeatureLayer",
            "esri/geometry/Point",
            "esri/geometry/Polyline",
            "esri/geometry/Polygon",
            "esri/geometry/SpatialReference",
            "esri/widgets/Sketch",
          ],
          this.options).then(([
                           Map,
                           MapView,
                           TileLayer,
                           Graphic,
                           GraphicsLayer,
                           FeatureLayer,
                           Point,
                           Polyline,
                           Polygon,
                           SpatialReference,
                           Sketch,
                         ]) => {
          this.zwLayer = new TileLayer({
            url: "http://**************/arcgis9080/ags_tile_svc/basemap_zw_black_3857/MapServer",
            customParameters: {
              jwt: jwt,
            }
          });
          this.airLayer = new TileLayer({
            url: "http://**************/arcgis9080/ags_tile_svc/air2018_3857/MapServer",
            customParameters: {
              jwt: jwt,
            }
          });
          this.map = new Map();
          this.map.add(this.zwLayer)

          // 地图中心点
          this.centerPoint = new Point({
            x: -12954,
            y: -56261,
            spatialReference: 3857,
          })

          this.view = new MapView({
            map: this.map,
            zoom: 6,
            center: this.centerPoint,
            container: "container",
            ui: {
              components: [], // 这将移除所有默认的 UI 组件，包括版权信息
            },
          });

          // 石化街道轮廓图
          const statesRenderer = {
            type: "simple",
            symbol: {
              type: "simple-fill",
              color: [0, 0, 0, 0],
              outline: {
                color: [255, 181, 39, 0.7],
                width: 5
              }
            }
          };
          this.shjdFeatureLayer = new FeatureLayer({
            url:
              "http://**************/server/geoscene/rest/services/bianjie/FeatureServer/0",
            renderer: statesRenderer,
          });
          this.map.add(this.shjdFeatureLayer);

          this.graphicsLayer = new GraphicsLayer();
          this.map.add(this.graphicsLayer);

          this.findGraphicsLayer = new GraphicsLayer();
          this.map.add(this.findGraphicsLayer);

          this.sketch = new Sketch({
            view: this.view,
            layer: this.findGraphicsLayer,
            creationMode: 'single',
          })

          // 默认绘制居委会图形
          // 1秒后加载数据
          setTimeout(() => {
            // this.drawAllCommittee()
            this.drawAllZds()
          }, 1000);

          // 地图点击事件
          this.view.on('click', e => {
            this.view.hitTest(e).then(res => {
              this.isTipsShow = false
              if (res.results.length !== 0) {
                const attributes = res.results[0].graphic.attributes
                const graphicType = attributes.graphicType
                if (graphicType && graphicType === 'roadStreetChiefPoint') {
                  // 绘制街长路段
                  this.drawRoad(attributes)
                  // 绘制街长商铺
                  this.drawShop(attributes.roadName)
                } else if (graphicType && graphicType === 'iccDevice') {
                  this.iccDeviceName = attributes.deviceName
                  let query = {}
                  if (this.iccDeviceName === '环江路垃圾房') {
                    query.channelCode = '1000010$1$0$0'
                  } else if (this.iccDeviceName === '卫九路11号') {
                    query.channelCode = '1000009$1$0$0'
                  } else if (this.iccDeviceName === '龙胜路垃圾房') {
                    query.channelCode = '1000018$1$0$0'
                  } else if (this.iccDeviceName === '卫九路大堤路') {
                    query.channelCode = '1000027$1$0$0'
                  } else if (this.iccDeviceName === '龙胜路垃圾房旁侧') {
                    query.channelCode = '1000024$1$0$0'
                  }
                  getHjzzMonitorVideo(query).then(res=>{
                    this.iccDevicePreviewUrl = res.data
                    this.isIccDevicePreviewWindowShow = true;
                  })
                } else if (graphicType && graphicType === 'camera') {
                  this.isCameraFindWindowShow = true;
                } else if (graphicType && graphicType === 'comprehensiveGrid') {
                  this.gridId = attributes.id
                  this.isComprehensiveGridWindowShow = true;
                }
              }
            })
          })

          // popup-detail 点击事件
          this.view.popup.on("trigger-action", (event) => {
            if (event.action.id === "popup-detail") {
              this.view.popup.close();
              const attributes = this.view.popup.selectedFeature.attributes;
              this.popupDetail(attributes);
            } else if (event.action.id === "popup-play") {
              this.view.popup.close();
              this.isSearchCameraShow = false;
              this.currentCameraIndexCode = this.view.popup.selectedFeature.attributes.cameraIndexCode
              getHikPreviewUrlWs(this.view.popup.selectedFeature.attributes.cameraIndexCode).then(res => {
                this.previewUrl = res.data
                this.isHikPreviewWindowShow = true;
              })
            } else if (event.action.id === "popup-video") {
              this.view.popup.close();
              const attributes = this.view.popup.selectedFeature.attributes;
              this.popData = JSON.parse(JSON.stringify(attributes))
              this.isFloodControlKeyAreaCameraWindowShow = true;
            } else if (event.action.id === "popup-camera") {
              this.view.popup.close();
              const attributes = this.view.popup.selectedFeature.attributes;
              this.popData = JSON.parse(JSON.stringify(attributes))
              this.isLiquidLevelDeviceCameraWindowShow = true;
            } else if (event.action.id === "popup-pump-station-area") {
              this.view.popup.close();
              const attributes = this.view.popup.selectedFeature.attributes;
              const res = JSON.parse(JSON.stringify(attributes))
              const ids = res.pumpStationArea
              this.addPumpStationAreaLayer(ids)
            }
          });

          this.view.on('mouse-wheel', ()=>{
            if (this.view.zoom > 8) {
              this.map.add(this.cadFeatureLayer1)
            } else {
              this.map.remove(this.cadFeatureLayer1)
            }
          })

          let that = this
          this.sketch.on('create', function (event) {
            if (event.state === 'complete') {
              // 清空图层
              // that.graphicsLayer.removeAll()
              // that.findCameraList = []

              let geometry = event.graphic.geometry
              let query = that.cameraFeatureLayer.createQuery()
              query.geometry = geometry
              query.spatialRelationship = 'intersects' // 查询与绘制几何图形相交的要素

              // 执行查询
              that.cameraFeatureLayer
                .queryFeatures(query)
                .then(function (result) {
                  // console.log(result.features.length)
                  if (result.features.length > 0) {
                    result.features.forEach(function (feature) {
                      let attributes = feature.attributes
                      attributes.graphicType = 'camera'
                      const g = new Graphic({
                        geometry: feature.geometry,
                        attributes: attributes,
                        symbol: {
                          type: 'picture-marker',
                          url: 'http://**************/images/icon/xianzhaung_icon_jiankong.png',
                          width: '29px',
                          height: '37px',
                        },
                        // popupTemplate: {
                        //   title: '{name}',
                        //   content: [
                        //     {
                        //       type: 'fields',
                        //       fieldInfos: [
                        //         {
                        //           fieldName: 'cameraIndexCode',
                        //           label: '监控点编号',
                        //         },
                        //         {
                        //           fieldName: 'name',
                        //           label: '监控点名称',
                        //         },
                        //         {
                        //           fieldName: 'cameraTypeName',
                        //           label: '监控点类型',
                        //         },
                        //         {
                        //           fieldName: 'positionName',
                        //           label: '监控点位置',
                        //         },
                        //       ],
                        //     },
                        //   ],
                        //   outFields: ['*'],
                        //   actions: [
                        //     {
                        //       title: '播放',
                        //       id: 'popup-play',
                        //       image:
                        //         'http://**************/images/icon/play-button.png',
                        //     },
                        //   ],
                        // },
                      })
                      that.findGraphicsLayer.add(g)
                      // 将attributes中的name和cameraIndexCode添加到findCameraList
                      that.findCameraList.push({
                        name: attributes.name,
                        cameraIndexCode: attributes.cameraIndexCode
                      })
                    })
                  }
                })
            }
          })

        })
      })
    },

    // 悬浮离开或者随意点击后 关闭弹窗
    windowLeave() {
      // this.isGarbageShow = false;
      // this.isEntryExitShow = false;
      // this.isLandInfoShow = false;
      // this.isGreeningShow = false;
      // this.isPipeInfoShow = false;
      // this.isWellCoverShow = false;
      //
      // if (this.infoWindow) {
      //   this.infoWindow.close();
      // }
      // this.infoWindow = null;
    },
    // 关闭沿街商铺弹窗
    closeShopWindow(value) {
      this.isShopWindowShow = value;
    },
    // 关闭房地信息弹窗
    closePremisesSurveyWindow(value) {
      this.isPremisesSurveyWindowShow = value;
    },
    // 关闭厂房仓库弹窗
    closeFactoryWarehouseWindow(value) {
      this.isFactoryWarehouseWindowShow = value;
    },
    //关闭井盖信息弹窗
    closeCoverWindow(value){
      this.isCoverWindowShow = value;
    },
    //关闭非住宅物业信息弹窗
    closePropertyContractWindow(value){
      this.isPropertyContractWindowShow = value;
    },
    //关闭公寓租赁房信息弹窗
    closeApartmentRentalWindow(value){
      this.isApartmentRentalWindowShow = value;
    },
    //关闭文化娱乐场所信息弹窗
    closeEntainmentWindow(value){
      this.isEntertainmentWindowShow = value;
    },
    //关闭小区出入口信息弹窗
    closeEntranceWindow(value){
      this.isResidentialEntranceWindow = value;
    },
    //关闭物业管理处信息弹窗
    closePropertyManageWindow(value){
      this.isNonResidentialPropertyWindow = value;
    },
    //关闭垃圾房信息弹窗
    closeGarbageHouseWindow(value){
      this.isGarbageHouseWindow = value;
    },
    //关闭两类人员网格化弹窗
    closePersonnelGridWindow(value){
      this.isPersonnelGridWindow = value;
    },
    //关闭综合网格工作站弹窗
    closeComprehensiveGridWindow(value){
      this.isComprehensiveGridWindowShow = value;
    },
    //关闭街长信息弹窗
    closeRoadChiefWindow(value){
      this.isRoadCheifWindowShow = value;
    },
    //关闭路名信息弹窗
    closeRoadWindow(value){
      this.isRoadWindowShow = value;
    },
    //关闭监控弹窗
    closeCameraWindow(value){
      this.isCameraWindowShow = value;
    },
    //关闭报警记录弹窗
    closeAlarmRecordWindow(value){
      this.isAlarmRecordWindowShow = value;
    },
    //关闭Icc设备弹窗
    closeIccDeviceWindow(value){
      this.isIccDeviceWindowShow = value;
    },
    closeIccDevicePreviewWindow(value){
      this.isIccDevicePreviewWindowShow = value;
    },
    // 关闭防汛防台重点区域详情
    closeFloodControlKeyAreaWindow(value){
      this.isFloodControlKeyAreaWindowShow = value;
    },
    closeFloodControlKeyAreaCameraWindow(value){
      this.isFloodControlKeyAreaCameraWindowShow = value;
    },
    closeLiquidLevelDeviceCameraWindow(value){
      this.isLiquidLevelDeviceCameraWindowShow = value;
    },
    // 关闭海康视频监控预览
    closeHikPreviewWindow(value){
      this.isHikPreviewWindowShow = value;
    },

    closeCameraGroupWindow(value){
      this.isClickWin = false;
      this.isCameraGroupWindowShow = value;
    },

    closeCameraFindWindow(value){
      this.isCameraFindWindowShow = value;
    },

    // 点击下方按钮-重置选项
    reset(goToCenter = true) {
      if (goToCenter) {
        this.view.goTo({
          target: this.centerPoint,
          zoom: 6,
        }, {
          duration: 500,
          easing: "ease-in-out"
        })
      }

      this.view.popup.close();

      this.isSketchActive = false;
      this.view.ui.remove(this.sketch);

      this.isShopWindowShow = false;
      this.isPremisesSurveyWindowShow = false;
      this.isFactoryWarehouseWindowShow = false;
      this.isCoverWindowShow = false;
      this.isPropertyContractWindowShow = false;
      this.isApartmentRentalWindowShow = false;
      this.isEntertainmentWindowShow = false;
      this.isNonResidentialPropertyWindow = false;
      this.isGarbageHouseWindow= false;
      this.isResidentialEntranceWindow = false;
      this.isRoadCheifWindowShow = false;
      this.isRoadWindowShow = false;
      this.isPersonnelGridWindow = false;
      this.isComprehensiveGridWindowShow = false;

      this.isClickWin = false;

      // 疫情防控按钮，标记为按钮未点击
      this.isIllnessControl = false;

      // 隐藏搜索框
      this.isInputShow = false;
      // 隐藏监控搜索框
      this.isSearchCameraShow = false;
      // 隐藏标识指示
      this.isTipsShow = false;
      // 隐藏监控
      this.isSurveillanceShow = false;

      this.isCameraWindowShow = false
      this.$refs.cameraWindow.videoDisplay = false;

      this.ispatternSpotShow = false;

      // 未显示卫星图层
      // this.isSatelliteLayer = false;

      // 没有添加遮罩物
      this.isAllData = false;
      this.isRoadLine = false;
      this.isPremisesSurvey = false;
      this.isGreening = false;
      this.isCommunityInfo = false;
      this.isPipeNetwork = false;
      this.isCurrentUser = false;

      // 移除图形
      this.graphicsLayer.removeAll()
      this.removeAllFeatureLayer()
      this.findCameraList = []

      this.findGraphicsLayer.removeAll()

      // 重置标识指示checkbox
      this.$refs.tipsWindow.checkAllCurrentUser = false
      this.$refs.tipsWindow.checkedCurrentUsers = []
      this.$refs.tipsWindow.isIndeterminate = false
      this.$refs.tipsWindow.checkAllCommunity = false
      this.$refs.tipsWindow.checkedCommunities = []
      this.$refs.tipsWindow.isIndeterminateCommunity = false
      this.$refs.tipsWindow.checkAllGreening = false
      this.$refs.tipsWindow.checkedGreenings = []
      this.$refs.tipsWindow.isIndeterminateGreening = false
      this.$refs.tipsWindow.checkAllPipeNetwork = false
      this.$refs.tipsWindow.checkedPipeNetworks = []
      this.$refs.tipsWindow.isIndeterminatePipeNetwork = false
      this.$refs.tipsWindow.checkAllZd = false
      this.$refs.tipsWindow.checkedZds = []
      this.$refs.tipsWindow.isIndeterminateZd = false
      this.$refs.tipsWindow.checkAllRiver = false
      this.$refs.tipsWindow.checkedRivers = []
      this.$refs.tipsWindow.isIndeterminateRiver = false

      this.isShowEmergentFeatureLayer = false

      this.$refs["fxft00"][0].style.backgroundColor = "#c7c7c6";
      this.fxbwShow = false;
      this.$refs["fxft01"][0].style.backgroundColor = "#c7c7c6";
      this.ywcxShow = false
      this.$refs["fxft02"][0].style.backgroundColor = "#c7c7c6";
      this.fxzdbwjkShow = false
      this.$refs["fxft03"][0].style.backgroundColor = "#c7c7c6";
      this.gwzxtShow = false
      this.$refs["fxft04"][0].style.backgroundColor = "#c7c7c6";
      this.szgwShow = false

    },
    removeAllFeatureLayer() {
      // 移除现状使用主体
      this.map.remove(this.shopFeatureLayer)
      this.map.remove(this.propertyContractFeatureLayer)
      this.map.remove(this.entertainmentFeatureLayer)
      this.map.remove(this.apartmentRentalFeatureLayer)
      this.map.remove(this.premisesSurveyFeatureLayer)
      this.map.remove(this.premisesSurvey26FeatureLayer)
      // this.map.remove(this.premisesSurveyZsh1FeatureLayer)
      // this.map.remove(this.premisesSurveyZsh2FeatureLayer)
      // this.map.remove(this.premisesSurveyZsh3FeatureLayer)
      // this.map.remove(this.premisesSurveyLo1FeatureLayer)
      // this.map.remove(this.premisesSurveyLo2FeatureLayer)
      // this.map.remove(this.premisesSurveyLo3FeatureLayer)
      // this.map.remove(this.premisesSurveyJh1FeatureLayer)
      this.map.remove(this.premisesSurveyProdFeatureLayer)
      this.map.remove(this.premisesSurveyProdEGQFeatureLayer)
      this.map.remove(this.kindergartenFeatureLayer)
      this.map.remove(this.primarySchoolFeatureLayer)
      this.map.remove(this.juniorMiddleSchoolFeatureLayer)
      this.map.remove(this.vocationalSchoolFeatureLayer)
      this.map.remove(this.highSchoolFeatureLayer)
      this.map.remove(this.functionalSchoolFeatureLayer)
      this.map.remove(this.supplementarySchoolFeatureLayer)
      this.map.remove(this.domesticAssetsFeatureLayer)
      this.map.remove(this.factoryWarehouseFeatureLayer)
      this.map.remove(this.committeeGridFeatureLayer)
      this.map.remove(this.comprehensiveGridFeatureLayer)

      // 移除监控资源
      this.map.remove(this.cameraFeatureLayer)

      // 移除绿化主体
      // this.map.remove(this.greenSpaceFeatureLayer)
      // this.map.remove(this.suburbanStreetTreeFeatureLayer)
      // this.map.remove(this.greenFeatureLayer0)
      // this.map.remove(this.greenFeatureLayer1)
      // this.map.remove(this.greenFeatureLayer4)
      // this.map.remove(this.greenFeatureLayer5)
      // this.map.remove(this.greenFeatureLayer6)
      // this.map.remove(this.greenFeatureLayer7)
      // this.map.remove(this.greenFeatureLayer8)
      // this.map.remove(this.greenFeatureLayer9)
      // this.map.remove(this.commercialHousingGreeningFeatureLayer)
      // this.map.remove(this.afterSalePublicHousingGreeningFeatureLayer)

      // 移除绿化主体
      this.map.remove(this.danweilvdiFeatureLayer0)
      this.map.remove(this.danweilvdiFeatureLayer1)
      this.map.remove(this.danweilvdiFeatureLayer2)
      this.map.remove(this.danweilvdiFeatureLayer3)
      this.map.remove(this.gonggonglvdiFeatureLayer0)
      this.map.remove(this.gonggonglvdiFeatureLayer1)
      this.map.remove(this.gonggonglvdiFeatureLayer2)
      this.map.remove(this.gonggonglvdiFeatureLayer3)
      this.map.remove(this.xingdaoshuFeatureLayer0)
      this.map.remove(this.xingdaoshuFeatureLayer1)
      this.map.remove(this.xingdaoshuFeatureLayer2)
      this.map.remove(this.xingdaoshuFeatureLayer3)
      this.map.remove(this.xingdaoshuFeatureLayer4)
      this.map.remove(this.shangpinfanglvhuaFeatureLayer0)
      this.map.remove(this.shangpinfanglvhuaFeatureLayer1)
      this.map.remove(this.shouhougongfanglvhuaFeatureLayer0)
      this.map.remove(this.greenGridFeatureLayer)

      // 移除小区基础信息
      this.map.remove(this.committeeFeatureLayer)
      this.map.remove(this.committeePointFeatureLayer)
      this.map.remove(this.largeGarbageSiteFeatureLayer)
      this.map.remove(this.constructionWasteSiteFeatureLayer)
      this.map.remove(this.nonResidentialPropertyFeatureLayer)
      this.map.remove(this.residentialEntranceFeatureLayer)
      this.map.remove(this.garbageHouseFeatureLayer)
      this.map.remove(this.residentialFeatureLayer)
      this.map.remove(this.personnelGridFeatureLayer)

      // 移除排水信息
      this.map.remove(this.coverFeatureLayer)
      this.map.remove(this.hedaoFeatureLayer0)
      this.map.remove(this.hedaoFeatureLayer1)
      this.map.remove(this.hedaoFeatureLayer2)
      this.map.remove(this.hedaoFeatureLayer3)
      this.map.remove(this.hedaoFeatureLayer4)
      this.map.remove(this.hedaoFeatureLayer5)
      this.map.remove(this.hedaoFeatureLayer6)
      this.map.remove(this.pumpStationFeatureLayer)
      this.map.remove(this.pumpStationFeatureLayer1)
      this.map.remove(this.pumpStationPointFeatureLayer)
      this.map.remove(this.pumpStationPolylineFeatureLayer)
      this.map.remove(this.pumpStationPolygonFeatureLayer)

      this.map.remove(this.pumpStationAreaPointFeatureLayer)
      this.map.remove(this.pumpStationAreaPolylineFeatureLayer)
      this.map.remove(this.pumpStationAreaPolygonFeatureLayer)

      // 移除 CAD
      this.map.remove(this.cadFeatureLayer0);
      this.map.remove(this.cadFeatureLayer1);
      this.map.remove(this.cadFeatureLayer2);
      this.map.remove(this.cadFeatureLayer3);
      this.map.remove(this.cadFeatureLayer4);

      this.map.remove(this.ysFeatureLayer);
      this.map.remove(this.wsFeatureLayer);

      // 移除街长制图层
      this.map.remove(this.shopCheckedFeatureLayer)
      this.map.remove(this.shopCheckedNoPassFeatureLayer)
      this.map.remove(this.shopCheckedMonthFeatureLayer)
      this.map.remove(this.shopCheckedNoPassMonthFeatureLayer)
      this.map.remove(this.monthNotCheckShopFeatureLayer)
      this.map.remove(this.roadStreetChiefFeatureLayer)
      this.map.remove(this.roadStreetChiefPointFeatureLayer)
      this.map.remove(this.committeeStreetChiefFeatureLayer)

      // 移除液压设备图层
      this.map.remove(this.liquidLevelDeviceAreaFeatureLayer)
      this.map.remove(this.liquidLevelDeviceAreaPointFeatureLayer)
      this.map.remove(this.liquidLevelDeviceFeatureLayer)
      this.map.remove(this.liquidLevelDeviceOfflineFeatureLayer)
      this.map.remove(this.liquidLevelDeviceAlarmFeatureLayer)
      // 移除icc设备图层
      this.map.remove(this.iccDeviceFeatureLayer)
      // 移除防汛重点部位监控图层
      this.map.remove(this.fxzdbwCameraFeatureLayer)
      this.map.remove(this.floodReserveInfoFeatureLayer)
      this.map.remove(this.floodShelterInfoFeatureLayer)
      // 移除水位传感器设备图层
      this.map.remove(this.sensorDeviceFeatureLayer)

      // 移除emergent图层
      this.map.remove(this.emergentFeatureLayer)

      // 移除紧急事件图层
      this.map.remove(this.urgentCaseFeatureLayer)
    },
    // 获取按钮传来的数据
    getParentBtnTitle(title) {
      // 悬浮离开或者随意点击后 关闭弹窗
      this.windowLeave();
      // 通过title值来决定缩放等级事件的判断
      this.bottomBtnTitle = title;

      if (title === "全部数据") {
        this.getSelData();
      } else if (title === "现状使用主体") {
        if (!this.isCurrentUser) {
          this.currentUserDB();
        }
        // if (!this.isRoadLine) {
        //   this.roadLineDB();
        // }
      } else if (title === "房地调查数据库") {
        if (!this.isPremisesSurvey) {
          this.premisesSurveyDB();
        }
      } else if (title === "绿化主体") {
        if (!this.isGreening) {
          this.greeningDB();
        }
      } else if (title === "小区基础信息") {
        if (!this.isCommunityInfo) {
          this.communityBasicInfo();
        }
      } else if (title === "管网数据") {
        if (!this.isPipeNetwork) {
          this.pipeNetwork();
        }
      }
    },

    // 全部数据
    getSelData() {
      this.reset();
      this.drawAllCommittee()
    },
    // 现状使用主体
    currentUserDB() {
      this.reset();
      // 显示icon选项按钮条
      this.currentUserSelBtnShow = true;
      // 表示已点击按钮，防止重复点击重复触发
      this.isCurrentUser = true;
    },
    // 房地调查数据库
    premisesSurveyDB() {
      this.reset();
      this.map.add(this.premisesSurveyFeatureLayer)
      // 表示已点击按钮，防止重复点击重复触发
      this.isPremisesSurvey = true;
    },
    // 绿化主体
    greeningDB() {
      this.reset();
      this.greeningSelBtnShow = true;
      // // 表示已点击按钮，防止重复点击重复触发
      this.isGreening = true;
    },
    // 点击绿化图斑
    greeningSpot() {
      this.reset();
    },
    // 小区基础信息
    communityBasicInfo() {
      this.reset();
      // 添加icon选项按钮
      this.iconSelBtnShow = true;
      // 表示已点击按钮，防止重复点击重复触发
      this.isCommunityInfo = true;
    },
    // 管网数据
    pipeNetwork() {
      this.reset();
      // 添加管网选项按钮
      this.pipeSelBtnShow = true;
      // 表示已点击按钮，防止重复点击重复触发
      this.isPipeNetwork = true;
    },

    // ------浮窗功能------
    // 还原
    winRestore() {
      // 关闭信息弹窗
      if (this.infoWindow) {
        this.infoWindow.close();
      }
      this.infoWindow = null;
      this.bottomBtnTitle = "还原";
      this.reset();
      this.$emit("resetJbgkPosition", -1);
    },
    // 网格 视频监控
    winPatternSpot() {
      // this.reset();
      this.isClickWin = !this.isClickWin;
      // 显示视频监控分组弹窗
      this.isCameraGroupWindowShow = true;

    },
    // 搜索
    winSearch() {
      // 移除图形
      this.findGraphicsLayer.removeAll()
      this.removeAllFeatureLayer()
      this.findCameraList = []
      // this.isTipsShow = false;
      // this.isInputShow = !this.isInputShow;
      this.toggleSketch();
    },
    // 切换视图
    winSwitch() {
      this.reset(false)
      if (this.isSatelliteLayer) {
        this.map.remove(this.airLayer);
        this.map.add(this.zwLayer);
      } else {
        this.map.remove(this.zwLayer);
        this.map.add(this.airLayer);
      }
      this.map.add(this.shjdFeatureLayer);
      this.map.add(this.graphicsLayer);
      this.isSatelliteLayer = !this.isSatelliteLayer;
    },
    // 标识指示
    winTips() {
      this.isInputShow = false;
      this.isTipsShow = !this.isTipsShow;
    },
    // 监控
    winSurveillance() {
      this.graphicsLayer.removeAll();
      this.isTipsShow = false;
      this.isSearchCameraShow = !this.isSearchCameraShow;
      if (this.isSurveillanceShow) {
        this.isSearchCameraShow = false
        this.map.remove(this.cameraFeatureLayer);
      } else {
        this.map.add(this.cameraFeatureLayer);
      }
      this.isSurveillanceShow = !this.isSurveillanceShow;
    },
    // 疫情防控
    illnessControl() {
      this.isIllnessControl = !this.isIllnessControl;
    },
    // 地图查找
    searchMap(data) {
      this.reset()
      const {searchType, keyword} = data
      if (searchType === '全部') {
        this.searchShop(keyword)
        this.searchPremisesSurvey(keyword)
      } else if (searchType === '沿街商铺') {
        this.searchShop(keyword)
      } else if (searchType === '房地信息') {
        this.searchPremisesSurvey(keyword)
      }
    },
    searchCamera(data) {
      this.reset()
      const {searchType, keyword} = data
      this.searchCameraGraphic(searchType, keyword)
    },
    checkedTips(data){
      const {type, value} = data
      if (type === '现状使用主体') {
        this.checkCurrentUser(value)
      } else if  (type === '小区基础信息') {
        this.checkCommunityBasicInfo(value)
      } else if  (type === '绿化信息') {
        this.checkGreening(value)
      } else if  (type === '排水信息') {
         this.checkPipeNetwork(value)
      } else if  (type === '房地信息') {
        this.checkZd(value)
      } else if  (type === '河道信息') {
        this.checkRiver(value)
      } else if  (type === '公寓租赁房') {
        this.checkApartmentRental()
      }
    },
    popupDetail(attributes) {
      const myAttributes = JSON.parse(JSON.stringify(attributes))
      this.popData = myAttributes
      const graphicType = myAttributes.graphicType
      // 井盖
      if (graphicType === 'cover') {
        this.isCoverWindowShow = true
      }
      // 非住宅物业
      else if (graphicType === 'propertyContract') {
        this.isPropertyContractWindowShow  = true
      }
      // 公寓租赁房
      else if (graphicType === 'apartmentRental') {
        this.isApartmentRentalWindowShow  = true
      }
      // 文化娱乐场所
      else if (graphicType === 'entertainment') {
        this.isEntertainmentWindowShow  = true
      }
      // 沿街商铺
      else if(graphicType === 'shop'){
        this.isShopWindowShow = true
      }
      //巡查商铺
      else if(graphicType === 'shopChecked') {
        const id = this.popData.id
        if(id !== undefined){
          this.shopcheckLogThreeList = [];
          getThreedayShopCheckLog(id).then(response=>{
            this.shopcheckLogThreeList = response.data
            this.isShopWindowShow = true
          })
        }
      }
      //物业管理处
      else if(graphicType === 'nonResidentialProperty'){
        this.isNonResidentialPropertyWindow = true
      }
      //垃圾房
      else if(graphicType === 'garbageHouse'){
        this.isGarbageHouseWindow = true;
      }
      // 突发事件
      else if(graphicType === 'emergent'){
        this.isEmergentWindowShow = true
      }
      // 两类人员网格化
      else if(graphicType === 'personnelGrid'){
        this.isPersonnelGridWindow = true;
        if (this.popData.id === 2) {
          this.visitRecords = [
            {
              date: '2023-10-18',
              condition: '该对象前两天去医院化疗，10月16日第二次化疗结束，出院回到石化家里。'
            },
            {
              date: '2023-10-8',
              condition: '目前该对象在家中休息，身体状况较差。'
            },
            {
              date: '2023-9-26',
              condition: '该对象在中西医结合医院虹口院区肿瘤科9月21日第一次化疗结束，现在出院回到石化家里。'
            },
            {
              date: '2023-9-13',
              condition: '当日与司法所民警和社工到中西医结合医院看望该对象，获知该对象患有两个癌症，已经扩散，预后在6个月至1年。'
            },
            {
              date: '2023-8-24',
              condition: '经过电话了解8月10日下午前往东方肝胆医院进行治疗，8月14日医生确诊胆囊恶性肿瘤。'
            },
            {
              date: '2023-8-11',
              condition: '经了解其近期身体不好，昨天刚刚去是市区医院看病，目前还在医院，据家属说是胆囊癌。'
            }
          ]
        } else if (this.popData.id === 12) {
          this.visitRecords = [
            {
              date: '2023-10-18',
              condition: '其妻子的户籍在四川省大竹县莲印乡龙潭村4组，妻子外地退休，退休工资不高，王某无工作，靠低保维持生活，夫妻两人每天基本在家，家里有2桌棋牌桌，平时会有朋友过来打牌，赚点外快补贴家用，王某生活正常，情绪比较稳定，未发现有不良行为的迹象。'
            },
            {
              date: '2023-9-20',
              condition: '王某夫妻两人每天基本在家，生活正常，情绪稳定。'
            },
            {
              date: '2023-8-17',
              condition: '王某夫妻两人每天基本在家，家里有一桌人在打麻将，生活正常，情绪比较稳定，未发现有不良行为的迹象。'
            },
            {
              date: '2023-7-21',
              condition: '王某家里的棋牌室因天气炎热无人光顾，其和妻子两人靠低保生活，生活比较困难，希望居委会在适当的时候能够给予补助，情绪比较稳定，未发现有不良行为的迹象。'
            },
            {
              date: '2023-6-20',
              condition: '和协管员一起上门告知6月20日选区人大补选，让王宝龙到时不要忘记，因王宝龙家里有一辆电瓶车，对其宣传消防安全，不要违规飞线充电，家里放了两桌棋牌桌，但没看到有人打牌，告知其要注意安全，不要扰民，目前生活、思想状态、情绪比较稳定，未发现有不良行为的迹象。'
            },
            {
              date: '2023-5-16',
              condition: '目前享受低保待遇，家有妻子，无子女，妻子为外来人员，目前户籍已迁来上海，但退休工资低，全家生活拮据。通过居委会了解到其在家里放了四桌麻将台，已经和社区民警反映过，属于无证经营，其说生活困难补贴一点家用。居委会告知其麻将桌仅限于平时娱乐，嘱咐其不能扰民，要谨记教训，不能做违法乱纪的事情。'
            }
          ]
        } else if (this.popData.id === 14) {
          this.visitRecords = [
            {
              date: '2023-10-17',
              condition: '近期其表现较为平稳，偶尔出现有类似情绪机动的情况，未发现其有盗窃行为。'
            },
            {
              date: '2023-10-7',
              condition: '今日与社区民警一同上门，其在家，表现较为平稳，了解了其日常情况，对其进行了教育引导。'
            },
            {
              date: '2023-9-26',
              condition: '近期其表现较为平稳，目前中介老板和她的关系较好，平时能够按时给钱。'
            },
            {
              date: '2023-9-19',
              condition: '近期表现平稳，偶尔有情绪激动的情况，对其进行了劝解。'
            },
            {
              date: '2023-9-5',
              condition: '这两天情绪比较不稳定，其他情况还好，未发现其有盗窃行为。'
            },
            {
              date: '2023-8-30',
              condition: '近期表现平稳，和人交流比较正常，和中介老板关系还好，老板能够按约定每月给她钱。'
            }
          ]
        } else if (this.popData.id === 8) {
          this.visitRecords = [
            {
              date: '2023-10-11',
              condition: '无业，与前妻、女儿共同居住，今年年初大病后身体情况较差，长期在家养病，很小外出，享受低保待遇，目前未发现其他异常情况。'
            },
            {
              date: '2023-9-19',
              condition: '目前退休，前段时间生病，还在恢复中，生活稳定，居委会帮助其申请了市民帮扶，暂未发现有不良行为的迹象。'
            },
            {
              date: '2023-8-16',
              condition: '前段时间大病初愈，目前身体状况欠佳，最近帮助其办理低保复审时表示生活状况没有变化，思想状态尚可。'
            },
            {
              date: '2023-7-20',
              condition: '无业，前段时间大病初愈，目前身体情况欠佳，不讲外出情况，与前妻同住，前妻表示近期生活较为稳定，没有异常。'
            },
            {
              date: '2023-6-19',
              condition: '无业，现在和前妻以及女儿生活在一起，依靠低保以及补助生活，近期生病住院治疗，已帮助其解决一部分医疗报销，目前身体不佳。'
            },
            {
              date: '2023-5-17',
              condition: '该对象无业，和前妻以及女儿生活在一起，依靠低保以及补助生活，近期在医院治疗。经济压力较大，已通过民政条线帮助其解决医药报销。'
            }
          ]
        } else if (this.popData.id === 4) {
          this.visitRecords = [
            {
              date: '2023-10-17',
              condition: '其姐姐告知，沈某8月25日在下楼梯时一脚踩空从楼梯上摔下来，造成左腿髌骨骨折，后住院治疗，于9月7日出院。目前在康复医院接受康复治疗，仍在恢复期。'
            },
            {
              date: '2023-9-12',
              condition: '其姐姐告知，沈某8月25日在下楼梯时一脚踩空从楼梯上摔下来，造成左腿髌骨骨折，后住院治疗，于9月7日出院。目前在康复医院接受康复治疗。'
            },
            {
              date: '2023-8-15',
              condition: '走访沈伟人姐姐家，正巧碰见沈伟人在其姐姐家。志愿者询问其近况，其告知最近感觉大腿发麻，身体不适，志愿者提醒其尽快去医院看看。'
            },
            {
              date: '2023-7-18',
              condition: '走访沈某户籍地临潮三村35号203室，该房屋是沈某姐姐名下房屋，沈某平时居住石化七村。其姐姐告知，近期天气炎热，沈某身体不好，且有颈椎病，经常头晕，基本在家不出门。'
            },
            {
              date: '2023-6-22',
              condition: '沈某经常来居委会咨询一些问题，志愿者与其闲聊，了解近况，沈某生活正常，情绪稳定。'
            },
            {
              date: '2023-5-4',
              condition: '沈某来居委会签收粮油帮困卡，志愿者与其聊家常，了解其思想动态。沈某近期生活平稳，经常去同小区35号203室，其姐姐家串门。'
            }
          ];
        } else if (this.popData.id === 3) {
          this.visitRecords = [
            {
              date: '2023-10-17',
              condition: '人户分离，实际居住在金山卫镇西静路1151弄59号101室，在上海欣联工程服务中心上班。最近生活平稳，思想稳定。'
            },
            {
              date: '2023-9-15',
              condition: '黄某人户分离，居住在金山卫镇，思想稳定，近期找到工作，已对其进行节前安全维稳宣传。'
            },
            {
              date: '2023-8-17',
              condition: '居住在金山卫镇，思想稳定，近期找到工作，正在培训中。'
            },
            {
              date: '2023-8-2',
              condition: '民政口反馈，黄皓淳申请并通过了低保金，帮教小组将持续关注其情况。'
            },
            {
              date: '2023-7-17',
              condition: '目前与母亲一起居住在山康，家庭和睦，平时打打零工，享受低保，无邻里矛盾和不良交往。'
            },
            {
              date: '2023-6-20',
              condition: '居住在金山卫镇西静路1151号101室。近期来居委会低保复审，黄某生活平稳无异常。'
            }
          ];
        } else if (this.popData.id === 13) {
          this.visitRecords = [
            {
              date: '2023-10-19',
              condition: '了解到其还是居住在紫薇苑，目前在北隋唐河路开了一个酒吧（小辣椒），收入较为稳定，与其沟通顺畅，情绪稳定，无异常。'
            },
            {
              date: '2023-10-18',
              condition: '其依旧居住在紫薇苑，目前在北隋唐河路开了一个酒吧（小辣椒），收入较为稳定，与其沟通顺畅，情绪稳定，对帮教态度较好，未发现有不良行为迹象。'
            },
            {
              date: '2023-8-17',
              condition: '依旧居住在紫薇苑，已经不做快递业务，目前在北隋唐河路开了一个酒吧（小辣椒），收入较为稳定，与其沟通，情绪稳定，对帮教态度较好。'
            },
            {
              date: '2023-7-21',
              condition: '依旧居住在紫薇苑，已经不做快递业务，目前在北隋唐河路开了一个酒吧（小辣椒），收入较为稳定，与其沟通，情绪稳定，对帮教态度较好。'
            },
            {
              date: '2023-6-15',
              condition: '其依旧居住在紫薇苑，目前从事收快递业务，收入来源稳定，与其本人沟通顺畅，情绪还算稳定。'
            },
            {
              date: '2023-5-15',
              condition: '目前从事收快递业务，现在小区里进行快递业务，因其在小区飞线充电充快递电瓶车，存在安全隐患。社区民警及治保主任已和凌某多次沟通，并签署“禁止电动自行车私拉电线充电告知书”。并让其找门面进行快递业务不要影响周边居民生活，其态度诚恳答应了,但近3个月还是存在小区充电现象，凌某目前收入来源较稳定，与其沟通顺畅，情绪稳定。'
            }
          ]
        } else if (this.popData.id === 9) {
          this.visitRecords = [
            {
              date: '2023-10-9',
              condition: '和该对象电话联系，其表示最近生活没什么变化。其询问重阳节到了，居委会是不是回发重阳节的东一，和其解释，他还没有到慰问的年龄。'
            },
            {
              date: '2023-9-7',
              condition: '在小区巡逻时碰到周正鸿，其正要去超市，和其聊了会天，看其正在玩手机，提醒不要点陌生的链接，不要和陌生的电话聊太久，避免被诈骗。其表示自己玩手机还是很小心，谢谢提醒。'
            },
            {
              date: '2023-8-16',
              condition: '走访该对象，通过谈话了解到其近期生活情况，目前没有变化，较少出门，能配合帮教工作，志愿者嘱咐其近期天气炎热，注意防暑降温'
            },
            {
              date: '2023-7-13',
              condition: '志愿者在小区内环境整治，走访到周正鸿，其出门去超市购买日用品，表示近期生活平稳，帮教态度尚可。'
            },
            {
              date: '2023-6-25',
              condition: '该对象为集体户口，目前住在一村，近期被野狗咬伤，已经去医院打了狂犬疫苗，目前在恢复中。近期生活比较困难，希望能够申请补助，司法所告知其将来会为其考虑的。其平时会出去买个菜自己烧饭，偶尔会和朋友出去聚个餐，生活很稳定。'
            },
            {
              date: '2023-5-18',
              condition: '一个人住在一村52号408室，走访的时候其还在睡觉，经过谈话了解到其近期生活无变化，帮教态度端正。'
            }
          ]
        } else if (this.popData.id === 7) {
          this.visitRecords = [
            {
              date: '2023-10-11',
              condition: '该对象人户分离住在四村，志愿者联系其母亲，了解到其依旧在姐姐哪里帮忙，经济来源依靠低保，性格暴躁，但生活较为平稳。'
            },
            {
              date: '2023-9-19',
              condition: '该对象人户分离，其母亲家中装修，其也在帮忙、协助装修，也没有去姐姐店里干活，近期表现尚可。'
            },
            {
              date: '2023-8-16',
              condition: '该对象目前居住在四村，人户分离，近期母亲家里装修，父亲身体不佳卧床休息，最近常回就行其母亲借住的家中照顾父亲，其母亲表示脾气依旧暴躁，已经多次因装修事宜和邻居争吵。'
            },
            {
              date: '2023-7-20',
              condition: '该对象人户分离，近期其母亲家中装修，父亲身体不佳，卧床休息，最近常回九村其母亲借住家中照顾父亲，母亲表示脾气依旧暴躁，已经多次因为装修事宜和邻居争吵。'
            },
            {
              date: '2023-6-19',
              condition: '该对象人户分离住在四村，通过其母亲及电话了解其近期情况，本人反馈情况良好，在其姐姐店里帮忙，母亲表示近期生活正常，平时有喝酒的习惯，在店里与姐姐发生矛盾后，就会旷工几天后再去，目前为发现有不良行为的迹象。'
            },
            {
              date: '2023-5-17',
              condition: '该对象人户分离居住在四村，通过其母亲及电话了解本人近期情况，其本人反馈情况良好，在其姐姐店里帮忙，其母亲表示仍旧有酗酒的现象。'
            }
          ]
        } else if (this.popData.id === 6) {
          this.visitRecords = [
            {
              date: '2023-10-23',
              condition: '孙某近期情况稳定，平时在家较少外出，前段时间出现幻听，最近吃了一段时间药后有好转。上月司法所为其申请年终慰问款已到账，孙某表示感谢。'
            },
            {
              date: '2023-9-22',
              condition: '孙某近期情况稳定，平时在家较少外出，前段时间出现幻听，最近吃了一段时间药后有好转。社工对其进行节前安全维稳宣传，孙某表示感谢。'
            },
            {
              date: '2023-8-21',
              condition: '今日联系孙某，孙某告知最近感冒了，其他没有症状。社工善意提醒其，最近二阳很多，去公共场所做好防护，孙某表示感谢。'
            },
            {
              date: '2023-7-17',
              condition: '电话联系孙某本人，孙某告知社工，最近晚上睡眠不好，白天人有点虚，胃口还好。每天晚上自己做饭，有时候喝点小酒，偶尔去金山卫绿地小区朋友家玩，生活比较稳定。'
            },
            {
              date: '2023-6-23',
              condition: '孙某目前还是居住在卫一路公寓房，基本在家不外出，有时候去大食堂吃饭。孙某告知社工，前段时间自己出现耳鸣幻听，感觉耳边一直有人说话，本月去医院看了，医生也配了药，吃一段时间看看有没有用。'
            },
            {
              date: '2023-5-25',
              condition: '今日与社区民警雍警官关于孙某情况进行沟通，得知孙某5月3日酒后在饭店与人发生冲突，动手打了对方，因伤势不重，最后和解处理。社工电话联系孙某，孙某对当日发生的事情如实告知，社工对其再三提醒，少喝酒，保持头脑清醒。孙某表示明白，并告知因原来合租的朋友家人来了，所以现在搬出去住了，目前住在石化卫一路128号公寓房，一个人住，房租500元一个月。'
            }
          ]
        } else if (this.popData.id === 11) {
          this.visitRecords = [
            {
              date: '2023-10-18',
              condition: '在国庆及亚运会重要节点，为了保障东泉社区社区和谐稳定，居委工作人员由其父亲上门与傅佩佩会谈，得知其最近依旧在东泉居住，心态良好无不良交往，目前依旧自由职业。付佩佩近期与家人共同居住，思想稳定，无不良交往。工作人员告知若有需要或想寻找工作，可以和居委多沟通交流。并于10月17日与其父沟通，提醒其关心女儿思想动态。'
            },
            {
              date: '2023-9-19',
              condition: '临近国庆、中秋重要节点，为了保障东泉社区社区和谐稳定，志愿者上门与傅佩佩及其父亲会谈，得知其最近依旧在东泉居住，心态良好无不良交往。目前依旧通过自由职业。傅佩佩近期与家人共同居住，思想稳定，无不良交往。工作人员告知若有需要或想寻找工作，可以和居委多沟通交流。并和傅师傅沟通，提醒其关心女儿思想动态。'
            },
            {
              date: '2023-8-15',
              condition: '为了保障社区稳定，志愿者走访傅佩佩家庭，其近期心态良好无不良交往，在家从事自由职业，经济情况稳定；同时嘱咐其父亲要做好傅佩佩思想工作，保持良好心态，并告知其若有需求，可以和居委会、司法所沟通，最后要求傅佩佩遵纪守法，远离犯罪。'
            },
            {
              date: '2023-7-3',
              condition: '为了保障特殊时期社区和谐稳定，居委工作人员上门傅佩佩谈话，其最近依旧在东泉居住，心态良好无不良交往。目前依旧在家从事自由职业，经济情况稳定，不缺少日用品。近期与家人共同居住，无不良交往。工作人员告知若有需要，可以和居委多沟通交流，并提醒其遵纪守法。'
            },
            {
              date: '2023-6-2',
              condition: '为了保障各类考试期间社区和谐稳定，居委工作人员在居委会与傅佩佩电话交谈，得知其最近依旧在东泉居住，心态良好无不良交往。目前依旧通过一对一补课获得收入。傅佩佩近期与家人共同居住，思想稳定，无不良交往。工作人员告知若有需要，可以和居委多沟通交流，并关心其思想动态。'
            },
            {
              date: '2023-5-12',
              condition: '为了保障敏感节点社区和谐稳定，居委志愿者在居委会与该对象面谈，得知其最近依旧在东泉居住，心态良好无不良交往。目前主要通过一对一不可获得收入。其与家人共同居住，思想稳定，近期无不良交往，志愿者告知其若有需要，可以和居委多沟通交流。'
            }
          ]
        } else {
          this.visitRecords = [{
              date: '',
              condition: '',
            }]
        }
      }
      //小区出入口
      else if(graphicType === 'residentialEntrance'){
        this.isResidentialEntranceWindow = true;
      }
      //街长信息
      else if(graphicType === 'roadStreetChiefPoint'){
        this.isRoadCheifWindowShow = true;
      }
      //路名信息
      else if(graphicType === 'roadStreetChief'){
        this.isRoadWindowShow = true;
      }
      // 报警记录
      else if(graphicType === 'liquidLevelDeviceAlarm'){
        const query = {
          deviceImei: myAttributes.deviceImei
        }
        getAlarmRecordByImei(query).then(response => {
          const {deviceAttrValue, alarmTime} = response.data
          const item = {
            attrName: '报警时间',
            value: alarmTime
          }
          this.alarmRecordData = JSON.parse(deviceAttrValue)
          this.alarmRecordData.unshift(item)
          this.alarmRecordData = this.alarmRecordData.map(record => {
            if (record.attrName === '井盖状态') {
              record.attrName = '报警原因';
            }
            return record;
          });

          this.isAlarmRecordWindowShow = true
        })
      }
      //icc设备
      else if(graphicType === 'iccDevice'){
        this.isIccDeviceWindowShow = true;
      }
      // 防汛防台重点区域
      else if(graphicType === 'liquidLevelDeviceArea'){
        this.isFloodControlKeyAreaWindowShow = true;
      }

      // 房地信息 =》 先根据宗地编号（LOT_NUMBER）判断 再根据现状使用主体（现状使）判断
      let lotNumber = attributes.lot_number
      let xianzhuangshi = attributes.现状使
      if (lotNumber && lotNumber !== '非宗地') {
        const arr = this.parcelInformationList.filter(item => item.lotNumber === lotNumber)
        if (arr.length !== 0) {
          this.popData = arr[0]
          this.isPremisesSurveyWindowShow = true
        }
      } else {
        if (xianzhuangshi) {
          const arr = this.parcelInformationList.filter(item => item.rightToUse === xianzhuangshi)
          if (arr.length !== 0) {
            this.popData = arr[0]
            this.isPremisesSurveyWindowShow = true
          }
        }
      }

      // 厂房仓库
      let unitName = attributes.单位名
      let areaName = attributes.建筑类
      // 如果建筑类为空，则根据单位名称查询， 如果建筑类不为空，则根据单位名和建筑类查询
      if (areaName && areaName.trim()) {
        const arr = this.factoryWarehouseList.filter(item => item.unitName === unitName && item.areaName === areaName)
        if (arr.length !== 0) {
          this.popData = arr[0]
          this.isFactoryWarehouseWindowShow = true
        }
      } else {
        const arr = this.factoryWarehouseList.filter(item => item.unitName === unitName)
        if (arr.length !== 0) {
          this.popData = arr[0]
          this.isFactoryWarehouseWindowShow = true
        }
      }

    },
    closePop(e) {
      if(this.isTipsShow) {
        this.isTipsShow = false
      }
      if (this.isInputShow) {
        this.isInputShow = false
      }
      if (this.isCameraWindowShow) {
        this.isCameraWindowShow = false
        this.$refs.cameraWindow.videoDisplay = false;
      }
    },
    toggleSketch() {
      this.isSketchActive = !this.isSketchActive;
      if (this.isSketchActive) {
        this.view.ui.add(this.sketch, 'manual');
        this.sketch.container.style.left = '800px';
        this.sketch.container.style.top = '250px';
      } else {
        this.view.ui.remove(this.sketch);
      }
    },
  },
  beforeDestroy() {
    this.view.destroy();
    document.removeEventListener('click', this.closePop)
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>

<style>
.esri-popup__main-container {
  max-height: 100% !important;
}
.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: auto 0px Highlight !important;
  outline: auto 0px -webkit-focus-ring-color !important;
}
</style>
