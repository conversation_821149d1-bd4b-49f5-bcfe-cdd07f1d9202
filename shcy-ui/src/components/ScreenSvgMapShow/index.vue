<template>
  <div class="svg-demo">
    <div class="background">
      <object :data="svgSrc" type="image/svg+xml" class="svg-image" ref="svgObject"></object>
    </div>

    <!-- top5 -->
    <div class="top5">
      <div class="top5-title">{{ top5Title }} TOP5</div>
      <div class="top5-content">
        <!-- 这里可以添加 top5 的具体内容 -->
        <div class="top5-item" v-for="(item, index) in top5Data" :key="index">
          <div class="top5-item-name" @click="handleNameClick(item.name)">{{ item.name }}</div>
          <div class="top5-item-value">{{ item.value }}</div>
        </div>
      </div>
    </div>

    <div class="button-group">
      <button
        v-for="(btn, index) in buttons"
        :key="index"
        @click="handleButtonClick(btn)"
        :class="{ 'active': btn.active }"
      >
        {{ btn.text }}
      </button>
    </div>

    <!-- fenbu -->
    <div class="fenbu">
      <div class="fenbu-title">事件投诉热度分布</div>
      <div class="fenbu-content">
        <div class="fenbu-item" v-for="(item, index) in fenbuData" :key="index">{{ item }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCaseNumberByResidentialarea } from "@/api/shcy/screenAsjgl";

export default {
  name: 'ScreenSvgMapShow',
  data() {
    return {
      svgSrc: require('@/assets/svg/asjgl.svg'),
      buttons: [
        { text: '案件总数', type: '1', active: false },
        { text: '重复案件', type: '2', active: false },
        { text: '重点案件', type: '3', active: false },
        { text: '紧急案件', type: '4', active: false },
      ],
      caseNumber: {},
      committees: {
        "滨一居委会": ["cls-56"],
        "滨二居委会": ["cls-58"],
        "紫卫居委会": ["cls-52"],
        "东村居委会": ["cls-60", "cls-80"],
        "山龙居委会": ["cls-62"],
        "卫清居委会": ["cls-64"],
        "辰凯居委会": ["cls-66"],
        "山鑫居委会": ["cls-68"],
        "合生居委会": ["cls-70"],
        "桥园居委会": ["cls-72"],
        "东礁一居委会": ["cls-74"],
        "东礁二居委会": ["cls-76"],
        "东泉居委会": ["cls-78"],
        "四村居委会": ["cls-82"],
        "临蒙居委会": ["cls-84"],
        "海棠居委会": ["cls-86"],
        "梅州居委会": ["cls-88"],
        "临三居委会": ["cls-90"],
        "七村居委会": ["cls-92"],
        "柳城居委会": ["cls-94"],
        "九村居委会": ["cls-96"],
        "十村居委会": ["cls-98"],
        "三村居委会": ["cls-100"],
        "十三村居委会": ["cls-102"],
        "合浦居委会": ["cls-104", "cls-108"],
        "十二村居委会": ["cls-106"],
      },
      caseData: {},
      fenbuData: ['0-40', '40-60', '60-80', '80-120', '120以上'], // 默认显示案件总数的分布
      fenbuRanges: {
        '1': ['0-40', '40-60', '60-80', '80-120', '120以上'],    // 案件总数
        '2': ['0-5', '5-10', '10-15', '15-20', '20以上'],        // 重复案件
        '3': ['0-10', '10-20', '20-30', '30-40', '40以上'],      // 重点案件
        '4': ['0-2', '2-4', '4-6', '6-8', '8以上'],             // 紧急任务
      },
      top5Data: [],
      top5Title: '',
    }
  },
  methods: {
    handleButtonClick(selectedBtn) {
      // 触发父组件事件
      this.$emit('buttonClick', selectedBtn.type);

      // Update button states
      this.buttons.forEach(btn => {
        btn.active = false;
      });
      selectedBtn.active = true;

      // Update fenbu data based on button type
      this.updateFenbuData(selectedBtn.type);

      // Update SVG colors based on the selected button type
      this.updateSvgColors(selectedBtn.type);

      // Update top5 data based on the selected button type

      this.top5Title = selectedBtn.text;
      // 对 top5 数据进行排序 取前5个
      this.top5Data = Object.entries(this.caseData[selectedBtn.type])
        .map(([name, value]) => ({ name, value }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 5);
    },
    updateSvgColors(type) {
      const svgObject = this.$refs.svgObject;
      const svgDocument = svgObject.contentDocument;
      if (svgDocument) {
        Object.keys(this.committees).forEach(key => {
          const classNames = this.committees[key];
          const caseNumber = this.caseData[type]?.[key] || 0;
          classNames.forEach(className => {
            const targetElements = svgDocument.getElementsByClassName(className);
            if (targetElements.length > 0) {
              Array.from(targetElements).forEach(element => {
                if (type === '1') {
                  element.style.fill = this.getType1Color(caseNumber);
                  element.style.stroke = this.getType1Color(caseNumber);
                } else if (type === '2') {
                  element.style.fill = this.getType2Color(caseNumber);
                  element.style.stroke = this.getType2Color(caseNumber);
                } else if (type === '3') {
                  element.style.fill = this.getType3Color(caseNumber);
                  element.style.stroke = this.getType3Color(caseNumber);
                } else if (type === '4') {
                  element.style.fill = this.getType4Color(caseNumber);
                  element.style.stroke = this.getType4Color(caseNumber);
                }
              });
            }
          });
        });
      }
    },
    async fetchCaseData() {
      try {
        const types = ['1', '2', '3', '4'];
        for (const type of types) {
          const res = await getCaseNumberByResidentialarea({ type });
          this.caseData[type] = res.data;
        }
        // Initialize with the first button's data
        this.updateFenbuData(this.buttons[0].type);
        this.updateSvgColors(this.buttons[0].type);
        this.buttons[0].active = true;
        this.top5Title = this.buttons[0].text;
        // 初始化 top5 数据进行排序
        this.top5Data = Object.entries(this.caseData['1'])
          .map(([name, value]) => ({ name, value }))
          .sort((a, b) => b.value - a.value)
          .slice(0, 5);
      } catch (error) {
        console.error('Error fetching case data:', error);
      }
    },
    // 更新分布数据
    updateFenbuData(type) {
      this.fenbuData = this.fenbuRanges[type];
    },
    // 案件总数颜色判断
    getType1Color(value) {
      if (value <= 40) return '#72ddfe'
      if (40 < value && value <= 60) return '#28f07e'
      if (60 < value && value <= 80) return '#f7b119'
      if (80 < value && value <= 120) return '#ff7905'
      return '#e60000'
    },
    // 重复案件颜色判断
    getType2Color(value) {
      if (value <= 5) return '#72ddfe'
      if (5 < value && value <= 10) return '#28f07e'
      if (10 < value && value <= 15) return '#f7b119'
      if (15 < value && value <= 20) return '#ff7905'
      return '#e60000'
    },
    // 重点案件颜色判断
    getType3Color(value) {
      if (value <= 10) return '#72ddfe'
      if (10 < value && value <= 20) return '#28f07e'
      if (20 < value && value <= 30) return '#f7b119'
      if (30 < value && value <= 40) return '#ff7905'
      return '#e60000'
    },
    // 紧急案件颜色判断
    getType4Color(value) {
      if (value <= 2) return '#72ddfe'
      if (2 < value && value <= 4) return '#28f07e'
      if (4 < value && value <= 6) return '#f7b119'
      if (6 < value && value <= 8) return '#ff7905'
      return '#e60000'
    },
    handleNameClick(name) {
      this.$emit('nameClick', name);
    },
  },
  mounted() {
    this.fetchCaseData();
  }
}
</script>

<style lang="scss" scoped>
.svg-demo {
  width: 100%;
  height: 100%;
  position: relative;

  .background {
    width: 100%;
    height: 100%;
    background-image: url('~@/assets/svg/asjgl_bg.png');
    background-size: cover;
    background-position: center;
    position: relative;
  }

  .svg-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
    height: auto;
  }

  .top5 {
    width: 200px;
    height: 240px;
    background-image: url('~@/assets/svg/top5.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    right: 350px;
    bottom: 450px;  // 调整位置，确保在按钮组上方

    .top5-title {
      padding: 12px 0 0 28px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 30px;
    }

    .top5-content {
      display: flex;
      flex-direction: column;
      margin-left: 50px;
      margin-top: 10px;

      .top5-item {
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 13px;
        color: #FFFFFF;
        line-height: 35px;

        .top5-item-name {
          cursor: pointer;  // 添加鼠标指向样式

          &:hover {
            color: #72ddfe;  // 鼠标悬停时变色
            text-decoration: underline;  // 鼠标悬停时添加下划线
          }
        }
      }
    }
  }

  .button-group {
    position: absolute;
    bottom: 200px;
    right: 400px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    button {
      width: 120px;
      height: 40px;
      border: none;
      background-color: transparent; // 设置背景颜色为透明
      background-image: url('~@/assets/svg/btn_asj.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;

      // 字体样式
      font-family: 'Source Han Sans CN', sans-serif;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 40px;
      font-style: italic;

      &:hover {
        transform: translateX(-5px);
      }

      &.active {
        background-image: url('~@/assets/svg/btn_asj_active.png');
      }
    }
  }

  .fenbu {
    width: 560px;
    height: 130px;
    background-image: url('~@/assets/svg/fenbu.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    left: 250px;
    bottom: 250px;

    .fenbu-title {
      padding:12px 0 0 28px;

      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 30px;
    }

    .fenbu-content {
      display: flex;
      gap: 0px;
      margin-left: 40px;

      .fenbu-item {
        width: 80px;
        margin: 6px 20px 0 12px;
        font-size: 13px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
}
</style>
