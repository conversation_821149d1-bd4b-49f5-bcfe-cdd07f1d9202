<template>
  <div class="comprehensive-grid-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ `${comprehensiveGrid.gridName} ${comprehensiveGrid.gridNumber}` }}</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="覆盖范围" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.coverageArea }}
        </el-descriptions-item>
        <el-descriptions-item label="覆盖居民区（个数）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.residentialAreas }}
        </el-descriptions-item>
        <el-descriptions-item label="面积（平方千米）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.areaSize }}
        </el-descriptions-item>
        <el-descriptions-item label="人口规模（万人）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.population }}
        </el-descriptions-item>
        <el-descriptions-item label="市场主体（家）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.marketEntities }}
        </el-descriptions-item>
        <el-descriptions-item label="新就业群（人）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ comprehensiveGrid.newEmployment }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="title-box" style="margin-top:16px;">
      <span class="ring"></span>
      <span class="window-title">工作力量</span>
    </div>
    <div class="table-img1" style="margin-top:16px;">
      <el-table
        :data="gridWorkforce"
        :header-cell-style="{background:'#2D2D64',color:'#fff',fontSize:'12px', fontWeight:'normal',borderColor:'#656594',borderWidth: '2px',height: '40px'}"
        :cell-style="{background:'#252545',color:'#fff',borderColor:'#656594',borderWidth: '2px', height: '40px'}"
        border
        style="width: 100%;">
        <el-table-column
          label="序号"
          :resizable="false">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="identity"
          label="身份"
          :resizable="false">
        </el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          :resizable="false">
        </el-table-column>
        <el-table-column
          prop="position"
          label="现任职务"
          :resizable="false">
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import { getComprehensiveGridById } from "@/api/shcy/screen";
import { getGridWorkforceList } from "@/api/shcy/screen";
export default {
  name: "ComprehensiveGridWindow",
  props: ["gridId"],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      comprehensiveGrid: {},
      gridWorkforce: [],
    }
  },
  watch: {
    gridId: {
      handler(newVal) {
        this.getComprehensiveGrid(newVal)
        this.getGridWorkforce(newVal)
      }
    },
  },
  methods: {
    getComprehensiveGrid(gridId) {
      getComprehensiveGridById(gridId).then(res => {
        this.comprehensiveGrid = res.data
      })
    },
    getGridWorkforce(gridId) {
      const params = {
        gridId: gridId
      }
      getGridWorkforceList(params).then(res => {
        this.gridWorkforce = res.data
      })
    },
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.comprehensive-grid-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1000px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 100%;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .table-img1 {
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;
  }

  .table-img1::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}


</style>
