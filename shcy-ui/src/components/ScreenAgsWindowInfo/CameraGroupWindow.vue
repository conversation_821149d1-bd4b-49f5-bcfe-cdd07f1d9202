<template>
  <div class="camera-group-container" ref="cameraGroupWindow">
    <div class="box-title">
      <span class="ring"></span>
      <span class="window-title">视频预览</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="box-content">

        <el-aside class="left-sp" style="width:282px;" >
          <div class="sp-search">
            <el-form :model="queryParams" ref="queryForm" >
            <el-input v-model="queryParams.inputValue" placeholder="请输入关键词" @input="handleInput" @keyup.enter.native="handleInput"></el-input>
            </el-form>
          </div >
<!--          <div class="sp-search-search">-->
<!--            <el-button type="text" size="medium" @click="handleQuery">搜索</el-button>-->
<!--          </div>-->
          <el-collapse accordion style="margin-top:65px;height:auto;border: 2px solid #656594;overflow:hidden" >
            <el-collapse-item  v-for="(item, index) in leftCameraList"  v-show="filter.length === 0" >
                <template slot="title" ><div  @click="play123(item.groupType)" class="left-sp-title">{{ item.groupName }}</div></template>
                <div style="height:310px">
                  <div  class="left-sp-title" style="cursor: pointer" v-for="(item1, index) in leftCameraList[index].cameraDTOList" @click="changeCamera123(item1)">{{ item1.name }}</div>
                </div>
            </el-collapse-item>
              <div style="height:310px" v-show="filter.length > 0" class="filter-css">
                <div  class="left-sp-title" style="cursor: pointer" v-for="(item, index) in filter" @click="changeCamera123(item)">{{ item.name }}</div>
              </div>
          </el-collapse>
        </el-aside>
        <el-container style="width:1126px;float:left;height:690px;">
            <el-main style="padding:20px 0px 0px 20px">
              <div class="flex-parent" >
                <div id="player" style="width: 1106px; height: 595px"></div>
              </div>
              <div style="width:100%;height:30px;float:bottom;padding:0px;margin-top:80px;background:transparent">
                <img src="./../../assets/screen_display_img/wholefull.png" style="float:right;margin:10px;width:20px;cursor: pointer" title="整体全屏" @click="wholeFullScreen"/>
                <img src="./../../assets/screen_display_img/44.png" style="float:right;margin:10px;width:20px;cursor: pointer" title="4x4" @click="arrangeWindow(4)"/>
                <img src="./../../assets/screen_display_img/33.png" style="float:right;margin:10px;width:20px;cursor: pointer" title="3x3" @click="arrangeWindow(3)"/>
                <img src="./../../assets/screen_display_img/22.png" style="float:right;margin:10px;width:20px;cursor: pointer" title="2x2" @click="arrangeWindow(2)"/>
              </div>
            </el-main>
        </el-container>
      </div>
    </div>
</template>

<script>
const IS_MOVE_DEVICE = document.body.clientWidth < 992 // 是否移动设备
const MSE_IS_SUPPORT = !!window.MediaSource // 是否支持mse

import {getGroupCamera, getGroupCameras} from "@/api/shcy/cameras";
import {getHikPreviewUrl, getHikPreviewUrlWs} from "@/api/shcy/screen"
export default {
  name: "CameraGroupWindow",
  props: ["previewUrl"],
  data() {
    return {
      display: false,
      cameraList: [],
      inputValue: '',
      leftCameraList:[],
      active:'',
      activeUrl:'',
      filter:[],
      cameraIndex:'',
      twoCamera:true,
      fourCamera:false,
      queryParams:{
        inputValue:undefined,
      },

      // 默认第一组 防汛重点区域
      cameraGroup: 1,

      isMoveDevice: IS_MOVE_DEVICE,
      player: null,
      splitNum: IS_MOVE_DEVICE ? 1 : 2,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? 'mse' : 'decoder',
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      urls: {
        realplay: '',
      },
      muted: true,

    }
  },
  mounted() {

    this.$el.style.setProperty('display', 'block')
    this.init()
    this.createPlayer()

    this.$nextTick(() => {
      this.play123(this.cameraGroup)
    })
  },
  created(){
    this.getCamera();
  },
  computed: {
    mode: function() {
      return this.tabActive === 'mse' ? 0 : 1
    }
  },
  methods: {

    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener('resize', () => {
        this.player.JS_Resize()
      })
    },
    createPlayer() {
      this.player = new window.JSPlugin({
        szId: 'player',
        szBasePath: "./",
        iMaxSplit: 4,
        iCurrentSplit: IS_MOVE_DEVICE ? 1 : 2,
        openDebug: true,
        oStyle: {
          borderSelect: IS_MOVE_DEVICE ? '#000' : '#FFCC00',
        }
      })

      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
          console.log('windowSelect callback: ', iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
          console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
        },
        windowEventOver: function (iWndIndex) {  //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {  //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {  //全屏切换回调
          console.log('fullScreen callback: ', bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
          console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {  //性能不足回调
          console.log('performanceLack callback: ');
        }
      });
    },
    /* 预览&对讲 */
    realplay() {
      let { player, mode, urls } = this,
        index = player.currentWindowIndex,
        playURL = urls.realplay

      player.JS_Play(playURL, { playURL, mode }, index).then(
        () => { console.log('realplay success') },
        e => { console.error(e) }
      )
    },


    showItem(index){
      if(this.fourCamera)
      {
        return index<16;
      }
      else if(this.twoCamera)
      {
        return index<4;
      }
    },
    changeCameraSize(type){
      if(type=="four")
      {
        this.fourCamera=true;
        this.twoCamera=false;
      }
      else if(type=="two")
      {
        this.twoCamera=true;
        this.fourCamera=false;
      }
    },
    handleInput() {
      this.filter=[];
      this.inputValue = this.queryParams.inputValue;
      if(this.inputValue == "")
      {
        this.filter=[]
      }
      else {
        this.leftCameraList.filter(item=>{
          item.cameraDTOList.filter(item1=>{
            if(item1.name.indexOf(this.inputValue)!==-1)
            {
              this.filter.push(item1)
            }

          })
        })
      }
    },
    getCamera(){
      getGroupCamera().then(res => {
          if (res.code === 200) {
            this.leftCameraList =res.data;
          }
        })
    },
    play(type) {
      getGroupCameras(type).then(res => {
        if (res.code === 200) {
          this.cameraList = res.data;
          if(this.cameraList.length <16 )
          {
              for(var i=this.cameraList.length;i<16;i++)
              {
                var form={
                  id:null,
                    name:null,
                    url:null
                };
                form.id=i;
                this.cameraList[i]=form;
              }

          }
        }
      })
    },

    // 播放视频
    play123(type) {
      this.stopAllPlay()
      this.cameraGroup = type
      getGroupCameras(type).then(res => {
        if (res.code === 200) {
          this.cameraList = res.data;

          // 判断this.splitNum为2 this.cameraLis取前4个，为3 取前9个，为4 取前16个
          if (this.splitNum === 2) {
            this.cameraList = this.cameraList.slice(0, 4)
          } else if (this.splitNum === 3) {
            this.cameraList = this.cameraList.slice(0, 9)
          } else if (this.splitNum === 4) {
            this.cameraList = this.cameraList.slice(0, 16)
          }
          let { player, mode, urls } = this,
            index = player.currentWindowIndex,
            playURL = urls.realplay
          // 遍历这4个后播放
          this.cameraList.forEach((item, index) => {
            this.player.JS_Play(item.url, { playURL: item.url, mode: 0 }, index).then(
              () => { console.log('realplay success') },
              e => { console.error(e) }
            )
          })

        }
      })
    },

    changeCamera123(item){
      getHikPreviewUrlWs(item.cameraIndexCode).then(res => {
        if (res.code === 200) {
          item.url = res.data
          let { player, mode, urls } = this,
          index = player.currentWindowIndex,
          playURL = item.url
          this.player.JS_Play(item.url, { playURL: item.url, mode: 0 }, index).then(
            () => { console.log('realplay success') },
            e => { console.error(e) }
          )
        }
      })
    },

    arrangeWindow(splitNum) {
      this.splitNum = splitNum
      this.player.JS_ArrangeWindow(splitNum).then(
        () => {
          console.log(`arrangeWindow to ${splitNum}x${splitNum} success`)
          this.play123(this.cameraGroup)
        },
        e => { console.error(e) }
      )
    },
    wholeFullScreen() {
      this.player.JS_FullScreenDisplay(true).then(
        () => { console.log(`wholeFullScreen success`) },
        e => { console.error(e) }
      )
    },

    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
    showBorder(item,index){
        this.active=item.id;
        this.cameraIndex=index;
        this.activeUrl=item.url;
    },
    changeCamera(item){
      getHikPreviewUrlWs(item.cameraIndexCode).then(res => {
        if (res.code === 200) {
          item.url=res.data
        }
      })
      this.cameraList = [...this.cameraList.slice(0, this.cameraIndex), item, ...this.cameraList.slice( this.cameraIndex + 1)];
      router.go(0);
    },

    stopAllPlay() {
      this.player.JS_StopRealPlayAll().then(
          () => { console.log('stopAllPlay success') },
          e => { console.error(e) }
      )
    },
  },
  beforeDestroy() {
    this.stopAllPlay()
  }
};
</script>

<style lang="scss" scoped>
.filter-css{
  font-size: 13px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 38px;
}
.cameraActive{
  border:1px solid orange
}
.uncameraActive{
  border:0px;
}
.sp-search{
  width: 282px;
  height: 46px;
  background: #181839;
  border: 2px solid #656594;
  color:#fff;
  float:left;
  ::v-deep .el-input--medium .el-input__inner {
    height: 45px;
    line-height: 45px;
    margin-bottom: 0px;
    border:0px;
    background:transparent;
  }
}
.sp-search-search{
  width: 60px;
  height: 46px;
  background: #181839;
  float:left;
  margin-left:5px;
  border: 2px solid #656594;
  ::v-deep .el-button--medium{
    height: 45px;
    line-height: 45px;
    margin-bottom: 0px;
    border:0px;
    background:transparent;
    color: #fff;
    padding:0px 14px;
  }
}
.sp-search-search:hover{
  background:#656594;
}
.left-sp{
  width:282px;
  margin-top:25px;
  float:left;
  height:auto;
  background: #181839;
  border: 0px solid #656594;
  padding: 0px 0px;
  float:left;
  overflow: hidden;
}

.el-collapse {
  border:0px;
}
.left-sp-title{
  margin-left:10px;
}

 ::v-deep .el-collapse-item__header {
  width: 100%;
  height: 42px;
  background: #2D2D64;
  border-bottom: 2px solid #656594;
  cursor: pointer;
  font-size: 13px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 38px;
}

 ::v-deep .el-collapse-item__content {
   font-size: 13px;
   font-family: Source Han Sans CN;
   font-weight: 400;
   color: #FFFFFF;
   line-height: 38px;
   height: 310px;
   overflow-y: scroll;
   background: #181839;
   border: 0px solid #656594;
   scrollbar-color: transparent transparent; //兼容火狐
   &::-webkit-scrollbar {
     display: none; //隐藏滚动条
   }
 }

 ::v-deep .el-collapse-item__wrap {
   will-change: height;
   background-color: #FFFFFF;
   overflow: hidden;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   border-bottom: 0px solid #e6ebf5;
 }

.camera-group-container {
  z-index: 1000;
  position: absolute;
  top: 53%;
  left: 47%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1480px;
  max-height: 1033px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .box-title {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN, sans-serif;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .box-content {
    width: 100%;
    margin-top: 5px;
    max-height: 1000px;

    .flex-parent {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
      height:550px;
      .flex-child {
        width: 250px;
        margin: 5px 10px
      }
      .flex-child-two{
        width: 500px;
        margin: 5px 10px
      }
    }
  }
}

::v-deep #dhHlsVideoWrapper {
  width: auto !important;
}
</style>
