<template>
  <div class="iccDevice-container" ref="iccDeviceDetailWindow">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">报警详情</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <div class="icc-video">
        <video muted="muted" class="video" :src="src" type="video/mp4" :poster="url" autoplay="autoplay" controls="controls" loop="-1" width="750px">
          <p>你的浏览器不支持video标签.</p>
        </video>
      </div>
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="发现时间" :label-style="this.labelStyle" :content-style="this.contentStyle">
          2023-08-30 16:41:39
        </el-descriptions-item>
        <el-descriptions-item label="事件地点" :label-style="this.labelStyle" :content-style="this.contentStyle">
          环江路垃圾投放点前端IPC
        </el-descriptions-item>
        <el-descriptions-item label="关联视频" :label-style="this.labelStyle" :content-style="this.contentStyle">
          <a href="javascript:void(0);" @click="downloadVideo">下载视频</a>
        </el-descriptions-item>
        <el-descriptions-item label="抓拍照片" :label-style="this.labelStyle" :content-style="this.contentStyle">
          <el-image
            style="height: 50px"
            :src="url"
            :preview-src-list="srcList">
          </el-image>
        </el-descriptions-item>
        <el-descriptions-item label="车牌信息" :label-style="this.labelStyle" :content-style="this.contentStyle">
          苏CU38P3
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>

export default {
  name: "IccDeviceWindow",
  props: ["iccDeviceData"],
  data() {
    return {
      display: false,
      src: 'https://jyzhkc.oss-cn-shanghai.aliyuncs.com/9decc1ff5af3d5dfaf3194e2762f9d58.mp4',
      url: 'https://pic.rmb.bdstatic.com/bjh/user/fe857d42b8a302de30696569dad66692.jpeg',
      srcList: [
        'https://pic.rmb.bdstatic.com/bjh/user/fe857d42b8a302de30696569dad66692.jpeg'
      ],
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
    downVideo(url, name) {
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'arraybuffer';    // 返回类型blob
      xhr.onload = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          // 转换一个blob链接
          let u = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }))
          let a = document.createElement('a');
          a.download = name;
          a.href = u;
          a.style.display = 'none'
          document.body.appendChild(a)
          a.click();
          a.remove();
        }
      };
      xhr.send()
    },
    downloadVideo() {
      //调用  点击按钮实现mp4格式视频下载
      this.downVideo(this.src, '关联视频');
    }
  },
};
</script>

<style lang="scss" scoped>
// 容器
.iccDevice-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 800px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 750px;
    max-height: 650px;
    overflow-y: scroll;

  }

  .icc-video {

  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}

video {
  object-fit: fill;
}
</style>
