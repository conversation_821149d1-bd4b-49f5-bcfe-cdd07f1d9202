<template>
  <div class="container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">突发事件详情</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-content">
      <el-descriptions title="" :column="2" border>
        <el-descriptions-item label="案件状态" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.statusname }}
        </el-descriptions-item>
        <el-descriptions-item label="任务号" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.taskid }}
        </el-descriptions-item>
        <el-descriptions-item label="案件来源" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.infosourcename }}
        </el-descriptions-item>
        <el-descriptions-item label="发现时间" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.discovertime }}
        </el-descriptions-item>
        <el-descriptions-item label="案件属性" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.infotypename }}
        </el-descriptions-item>
        <el-descriptions-item label="案件大类" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.infobcname }}
        </el-descriptions-item>
        <el-descriptions-item label="案件小类" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.infoscname }}
        </el-descriptions-item>
        <el-descriptions-item label="发生地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.address }}
        </el-descriptions-item>
        <el-descriptions-item label="案件图片" :label-style="this.labelStyle" :content-style="this.contentStyle"
                              :span="2">
          <el-image style="width: 200px; margin-right: 10px;"
                    v-if="imagefilenamesArr"
                    lazy :src="imagefilenamesArr[0]"
                    :preview-src-list="imagefilenamesArr" fit="contain"></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="核查照片" :label-style="this.labelStyle" :content-style="this.contentStyle"
                              :span="2">
          <el-image style="width: 200px; margin-right: 10px;"
                    v-if="checkimagesArr"
                    lazy :src="checkimagesArr[0]"
                    :preview-src-list="checkimagesArr" fit="contain"></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="监督员姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.reporter }}
        </el-descriptions-item>
        <el-descriptions-item label="问题描述" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.description }}
        </el-descriptions-item>
        <el-descriptions-item label="主责部门" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.executedeptname }}
        </el-descriptions-item>
        <el-descriptions-item label="结案时间" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ emergent.endtime }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>

export default {
  name: "EmergentWindow",
  props: {
    emergent: {
      type: Object,
      default: {}
    },
  },
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  computed: {
    imagefilenamesArr() {
      // this.emergent.imagefilenames使用,分隔字符串
      // 判断this.emergent.imagefilenames是不是字符串类型
      if (typeof this.emergent.imagefilenames === 'string') {
        return this.emergent.imagefilenames ? this.emergent.imagefilenames.split(",") : [];
      } else {
        return this.emergent.imagefilenames
      }
    },
    checkimagesArr() {
      // this.emergent.checkimages使用,分隔字符串
      if (typeof this.emergent.checkimages === 'string') {
        return this.emergent.checkimages ? this.emergent.checkimages.split(",") : [];
      } else {
        return this.emergent.checkimages
      }
    },
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  //left: 950px;
  //transform: translate(0%, -50%);
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 800px;
  height: 600px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("~@/assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-content {
    width: 750px;
    max-height: 515px;
    overflow-y: scroll;
    margin-top: 16px;
  }

  .table-content::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}

</style>
