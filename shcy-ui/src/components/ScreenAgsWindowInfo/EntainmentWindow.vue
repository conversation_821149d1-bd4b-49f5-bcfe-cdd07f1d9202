<template>
  <div class="premisesSurvey-container" ref="currentUse">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">文化娱乐场所</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="营业执照名称" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.entertainmentData.licenseName }}
        </el-descriptions-item>
        <el-descriptions-item label="对外经营名称" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.entertainmentData.operateName }}
        </el-descriptions-item>
        <el-descriptions-item label="法人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.entertainmentData.legalPerson }}
        </el-descriptions-item>
        <el-descriptions-item label="联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.entertainmentData.contactPhone }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  name: "EntertainmentWindow",
  props: ["entertainmentData"],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.premisesSurvey-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 870px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 820px;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}
</style>
