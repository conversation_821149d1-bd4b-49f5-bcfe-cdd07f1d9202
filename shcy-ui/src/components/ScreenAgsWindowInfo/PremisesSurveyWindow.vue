<template>
  <div class="premisesSurvey-container" ref="currentUse">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">房地信息</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="地块类型" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.lotNumber == null ? '非宗地' : '宗地' }}
        </el-descriptions-item>
        <el-descriptions-item label="土地性质" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.landRights }}
        </el-descriptions-item>
        <el-descriptions-item label="土地用途（试行）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.landUseTrial }}
        </el-descriptions-item>
        <el-descriptions-item label="所辖居委" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.belongCommittee }}
        </el-descriptions-item>
        <el-descriptions-item label="权属单位" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.ownership }}
        </el-descriptions-item>
        <el-descriptions-item label="是否国有资产" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.isGyzc }}
        </el-descriptions-item>
        <el-descriptions-item label="权属联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.ownershipContact }}
        </el-descriptions-item>
        <el-descriptions-item label="权属联系电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.ownershipPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="物业公司" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.propertyCompany }}
        </el-descriptions-item>
        <el-descriptions-item label="物业联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.propertyContact }}
        </el-descriptions-item>
        <el-descriptions-item label="物业联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.propertyPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="土地面积（㎡）" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.shapeArea }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions v-if="this.parcelInformationData.rightToUse1" class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse1 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType1 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress1 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact1 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone1 }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions v-if="this.parcelInformationData.rightToUse2" class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse2 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType2 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress2 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact2 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone2 }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions v-if="this.parcelInformationData.rightToUse3" class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体3" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse3 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型3" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType3 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址3" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress3 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人3" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact3 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话3" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone3 }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions v-if="this.parcelInformationData.rightToUse4" class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体4" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse4 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型4" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType4 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址4" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress4 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人4" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact4 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话4" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone4 }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions v-if="this.parcelInformationData.rightToUse5" class="descriptions-mt16" title="" :column="2" border>
        <el-descriptions-item label="现状使用主体5" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.rightToUse5 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型5" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitType5 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位地址5" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitAddress5 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系人5" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitContact5 }}
        </el-descriptions-item>
        <el-descriptions-item label="单位联系电话5" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.parcelInformationData.unitPhone5 }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

  </div>
</template>

<script>
export default {
  name: "PremisesSurveyWindow",
  props: ["parcelInformationData"],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.premisesSurvey-container {
  z-index: 1001;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1350px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 100%;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}
</style>
