<template>
  <div class="box-container">
    <div class="box-title">
      <span class="ring"></span>
      <span class="window-title">{{ deviceName }} - 实时预览</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="box-content">
      <FLVPlayer :videoUrl="previewUrl"></FLVPlayer>
    </div>
  </div>
</template>

<script>
import FLVPlayer from "@/components/FLVPlayer/index.vue";

export default {
  name: "IccDevicePreviewWindow",
  components: { FLVPlayer },
  props: ["previewUrl", "deviceName"],
  data() {
    return {
      display: false,
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.box-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 900px;
  max-height: 933px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .box-title {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN, sans-serif;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .box-content {
    width: 850px;
    margin-top:16px;
    max-height: 650px;
  }


}

</style>
