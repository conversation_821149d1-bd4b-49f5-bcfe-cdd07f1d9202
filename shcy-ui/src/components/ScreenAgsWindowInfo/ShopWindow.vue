<!-- 现状使用主体-沿街商铺 -->
<template>
  <div class="currentUse-container" ref="currentUse">
    <!-- 标题1 （商铺信息）-->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">沿街商铺情况</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img descriptions-mt16">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="商铺名称" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopName }}
        </el-descriptions-item>
        <el-descriptions-item label="营业执照" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopLicense }}
        </el-descriptions-item>
        <el-descriptions-item label="商铺地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="统一社会信用代码" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopCreditCode }}
        </el-descriptions-item>
        <el-descriptions-item label="注册地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopRegisterAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="经营地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopOperatingAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="行业大类" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopCategory }}
        </el-descriptions-item>
        <el-descriptions-item label="行业中类" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopSubcategory }}
        </el-descriptions-item>
        <el-descriptions-item label="行业小类" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopLittlecategory }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopContract }}
        </el-descriptions-item>
        <el-descriptions-item label="房屋权属" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopHouseOwnership }}
        </el-descriptions-item>
        <el-descriptions-item label="电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shoplAndlordPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="国有资产" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.isStateAssets }}
        </el-descriptions-item>
        <el-descriptions-item label="房东" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopLandlordContract }}
        </el-descriptions-item>
        <el-descriptions-item label="管辖物业" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shopGoverningProperty }}
        </el-descriptions-item>
        <el-descriptions-item label="房东电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.shoplAndlordPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="是否与小区物业一致" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.shopData.isSameCommunityProperty }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 标题2(巡查记录) -->
    <div v-show="checkLogData.length !==0">
    <div class="title-box" style="margin-top: 20px">
      <span class="ring"></span>
      <span class="window-title">商铺最近三次巡查记录</span>
    </div>
    <div class="table-img descriptions-mt16">
      <el-table
        :data="checkLogData"
        height="220"
        border
        style="width: 100%">
        <el-table-column
          prop="checkCrossDoorOperation"
          label="是否存在跨门经营"
          :style=labelStyle
          style="text-align: center">
        </el-table-column>
        <el-table-column
          prop="checkMqsbls"
          label="门责制是否落实"
          :style=labelStyle
          style="text-align: center"
          >
        </el-table-column>
        <el-table-column
          prop="checkThreeOnePlace"
          label="是否三合一场所"
          :style=labelStyle
          style="text-align: center">
        </el-table-column>
        <el-table-column
          prop="checkDate"
          label="巡查时间"
          :style=labelStyle
          style="text-align: center">
        </el-table-column>
      </el-table>
    </div>

    </div>
  </div>
</template>

<script>
export default {
  name: "ShopWindow",
  props: ['shopData','checkLogData'],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '16px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '16px',
        width: '25%',
      }
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },

  },
};
</script>

<style lang="scss" scoped>
// 容器
.currentUse-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1350px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 100%;
  }

  .descriptions-mt16{
    margin-top: 20px;
  }
}

::v-deep .el-table tbody tr:hover>td {
  background-color:#656594 !important //修改成自己想要的颜色即可
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}

::v-deep .el-table {
  color: #ffffff;
  border: 2px solid #656594;
}
::v-deep .el-table--border .el-table__cell{
  color: #ffffff;
}


::v-deep.el-table tr {
  background-color: #656594;
}

::v-deep .el-table__body-wrapper {
  background-color: #2D2D64;
}



::v-deep .el-table__header tr,
::v-deep .el-table th, .el-table tr{
  background-color:#252545;
}

</style>
