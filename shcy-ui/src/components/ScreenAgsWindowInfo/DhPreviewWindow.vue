<template>
  <div class="dh-preview-container">
    <div class="box-title">
      <span class="ring"></span>
      <span class="window-title">视频预览</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="box-content">
      <div class="flex-parent">
        <div class="flex-child" v-for="(item, index) in previewUrls"
             :key="index">
          <HLSPlayer :videoUrl="item" :timeout="500"></HLSPlayer>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import HLSPlayer from "@/components/HLSPlayer/index.vue";

export default {
  name: "DhPreviewWindow",
  components: {
    HLSPlayer,
  },
  props: ["previewUrls"],
  data() {
    return {
      display: false
    }
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    }
  }
};
</script>

<style lang="scss" scoped>
// 容器
.dh-preview-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 900px;
  max-height: 933px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .box-title {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN, sans-serif;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .box-content {
    width: 850px;
    margin-top: 16px;
    max-height: 650px;

    .flex-parent {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;

      max-height: 473px;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        width:4px;
        background-color:#F5F5F5;
      }
      /*定义滚动条轨道：内阴影+圆角*/
      &::-webkit-scrollbar-track {
        background-color:#F5F5F5;
      }
      /*定义滑块：内阴影+圆角*/
      &::-webkit-scrollbar-thumb {
        background-color:#555;
      }

      .flex-child {
        width: 100%;
      }
    }
  }

}
</style>
