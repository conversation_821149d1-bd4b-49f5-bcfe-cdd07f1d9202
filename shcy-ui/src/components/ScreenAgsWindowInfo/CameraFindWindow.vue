<template>
  <div class="camera-group-container" ref="cameraFindWindow">
    <div class="box-title">
      <span class="ring"></span>
      <span class="window-title">自定义</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="box-content">
        <el-aside class="left-sp" style="width:282px;" >
          <div class="sp-search">
            <el-form :model="queryParams" ref="queryForm" >
            <el-input v-model="queryParams.inputValue" placeholder="请输入关键词" @input="handleInput" @keyup.enter.native="handleInput"></el-input>
            </el-form>
          </div >
          <el-collapse accordion value="1" style="margin-top:65px;height:auto;border: 2px solid #656594;overflow:hidden" >
            <el-collapse-item name="1" v-show="filter.length === 0" >
                <template slot="title" ><div class="left-sp-title">框选视频</div></template>
                <div style="height:478px">
                  <div 
                    class="left-sp-title" 
                    :class="{ 'selected': selectedItem === item }"
                    style="cursor: pointer" 
                    v-for="(item, index) in findCameraList" 
                    @click="changeCamera123(item)"
                  >
                    {{ item.name }}
                  </div>
                </div>
            </el-collapse-item>
              <div style="height:478px" v-show="filter.length > 0" class="filter-css">
                <div 
                  class="left-sp-title" 
                  :class="{ 'selected': selectedItem === item }"
                  style="cursor: pointer" 
                  v-for="(item, index) in filter" 
                  @click="changeCamera123(item)"
                >
                  {{ item.name }}
                </div>
              </div>
          </el-collapse>
        </el-aside>
        <el-container style="width:1126px;float:left;height:690px;">
            <el-main style="padding:20px 0px 0px 20px">
              <div class="flex-parent" >
                <div id="player" style="width: 1106px; height: 595px"></div>
              </div>
              <div class="navigation-icons">
                <div v-if="currentPage > 1" class="icon prev" aria-label="Previous page" @click="prevPage" title="上一页"></div>
                <div v-if="findCameraList.length > currentPage * 9" class="icon next" aria-label="Next page" @click="nextPage" title="下一页"></div>
              </div>
            </el-main>
        </el-container>
      </div>
    </div>
</template>

<script>
const IS_MOVE_DEVICE = document.body.clientWidth < 992 // 是否移动设备
const MSE_IS_SUPPORT = !!window.MediaSource // 是否支持mse

import {getHikPreviewUrlWs} from "@/api/shcy/screen"
export default {
  name: "CameraFindWindow",
  props: ["findCameraList"],
  data() {
    return {
      display: false,
      cameraList: [],
      inputValue: '',
      filter:[],
      cameraIndex:'',
      queryParams:{
        inputValue:undefined,
      },
      isMoveDevice: IS_MOVE_DEVICE,
      player: null,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? 'mse' : 'decoder',
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      urls: {
        realplay: '',
      },
      muted: true,
      currentPage: 1,
      selectedItem: null,
    }
  },
  mounted() {
    this.$el.style.setProperty('display', 'block')
    this.init()
    this.createPlayer()
    this.$nextTick(() => {
      this.play123()
    })
  },
  computed: {
    mode: function() {
      return this.tabActive === 'mse' ? 0 : 1
    }
  },
  methods: {
    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener('resize', () => {
        this.player.JS_Resize()
      })
    },
    createPlayer() {
      this.player = new window.JSPlugin({
        szId: 'player',
        szBasePath: "./",
        iMaxSplit: 4,
        iCurrentSplit: IS_MOVE_DEVICE ? 1 : 3,
        openDebug: true,
        oStyle: {
          borderSelect: IS_MOVE_DEVICE ? '#000' : '#FFCC00',
        }
      })
      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
          console.log('windowSelect callback: ', iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
          console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
        },
        windowEventOver: function (iWndIndex) {  //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {  //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {  //全屏切换回调
          console.log('fullScreen callback: ', bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
          console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {  //性能不足回调
          console.log('performanceLack callback: ');
        }
      });
    },
    /* 预览&对讲 */
    realplay() {
      let { player, mode, urls } = this,
        index = player.currentWindowIndex,
        playURL = urls.realplay

      player.JS_Play(playURL, { playURL, mode }, index).then(
        () => { console.log('realplay success') },
        e => { console.error(e) }
      )
    },

    handleInput() {
      this.filter=[];
      this.inputValue = this.queryParams.inputValue;
      if(this.inputValue == "")
      {
        this.filter=[]
      }
      else {
        this.findCameraList.filter(item=>{
            if(item.name.indexOf(this.inputValue)!==-1)
            {
              this.filter.push(item)
            }
        })
      }
    },

    // 播放视频
    play123() {
      this.stopAllPlay()
      this.cameraList = this.findCameraList;
      const startIndex = (this.currentPage - 1) * 9;
      const endIndex = this.currentPage * 9;
      this.cameraList = this.cameraList.slice(startIndex, endIndex);
      let { player, mode, urls } = this,
        index = player.currentWindowIndex,
        playURL = urls.realplay
      // 遍历这4个后播放
      this.cameraList.forEach((item, index) => {
        getHikPreviewUrlWs(item.cameraIndexCode).then(res => {
          if (res.code === 200) {
            this.player.JS_Play(res.data, { playURL: res.data, mode: 0 }, index).then(
              () => { console.log('realplay success') },
              e => { console.error(e) }
            )
          }
        })
      })
    },

    changeCamera123(item){
      this.selectedItem = item;
      this.player.JS_Stop(this.player.currentWindowIndex)
      getHikPreviewUrlWs(item.cameraIndexCode).then(res => {
        if (res.code === 200) {
          item.url = res.data
          let { player, mode, urls } = this,
          index = player.currentWindowIndex,
          playURL = item.url
          this.player.JS_Play(item.url, { playURL: item.url, mode: 0 }, index).then(
            () => { console.log('realplay success') },
            e => { console.error(e) }
          )
        }
      })
    },

    wholeFullScreen() {
      this.player.JS_FullScreenDisplay(true).then(
        () => { console.log(`wholeFullScreen success`) },
        e => { console.error(e) }
      )
    },

    closeWindow() {
      this.$emit("closeWindow", this.display);
    },

    changeCamera(item){
      getHikPreviewUrlWs(item.cameraIndexCode).then(res => {
        if (res.code === 200) {
          item.url=res.data
        }
      })
      this.cameraList = [...this.cameraList.slice(0, this.cameraIndex), item, ...this.cameraList.slice( this.cameraIndex + 1)];
      router.go(0);
    },

    stopAllPlay() {
      this.player.JS_StopRealPlayAll().then(
          () => { console.log('stopAllPlay success') },
          e => { console.error(e) }
      )
    },
    nextPage() {
        this.currentPage++;
        this.play123();
      },
    prevPage() {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.play123();
        }
      },
  },
  beforeDestroy() {
    this.stopAllPlay()
  }
};
</script>

<style lang="scss" scoped>
.filter-css{
  font-size: 13px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 38px;
}
.cameraActive{
  border:1px solid orange
}
.uncameraActive{
  border:0px;
}
.sp-search{
  width: 282px;
  height: 46px;
  background: #181839;
  border: 2px solid #656594;
  color:#fff;
  float:left;
  ::v-deep .el-input--medium .el-input__inner {
    height: 45px;
    line-height: 45px;
    margin-bottom: 0px;
    border:0px;
    background:transparent;
  }
}
.sp-search-search{
  width: 60px;
  height: 46px;
  background: #181839;
  float:left;
  margin-left:5px;
  border: 2px solid #656594;
  ::v-deep .el-button--medium{
    height: 45px;
    line-height: 45px;
    margin-bottom: 0px;
    border:0px;
    background:transparent;
    color: #fff;
    padding:0px 14px;
  }
}
.sp-search-search:hover{
  background:#656594;
}
.left-sp{
  width:282px;
  margin-top:25px;
  float:left;
  height:auto;
  background: #181839;
  border: 0px solid #656594;
  padding: 0px 0px;
  float:left;
  overflow: hidden;
}

.el-collapse {
  border:0px;
}
.left-sp-title{
  padding-left:10px;
  transition: background-color 0.3s ease;
  
  &.selected {
    background-color: #3a3a7a; // 选中时的背景色
    color: #28dfae; // 选中时的文字颜色
    font-weight: bold;
  }
  
  &:hover {
    background-color: #2d2d64; // 鼠标悬停时的背景色
  }
}

 ::v-deep .el-collapse-item__header {
  width: 100%;
  height: 42px;
  background: #2D2D64;
  border-bottom: 2px solid #656594;
  cursor: pointer;
  font-size: 13px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 38px;
}

 ::v-deep .el-collapse-item__content {
   font-size: 13px;
   font-family: Source Han Sans CN;
   font-weight: 400;
   color: #FFFFFF;
   line-height: 38px;
   height: 478px;
   overflow-y: scroll;
   background: #181839;
   border: 0px solid #656594;
   scrollbar-color: transparent transparent; //兼容火狐
   &::-webkit-scrollbar {
     display: none; //隐藏滚动条
   }
 }

 ::v-deep .el-collapse-item__wrap {
   will-change: height;
   background-color: #FFFFFF;
   overflow: hidden;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   border-bottom: 0px solid #e6ebf5;
 }

.camera-group-container {
  z-index: 1000;
  position: absolute;
  top: 53%;
  left: 47%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1480px;
  max-height: 1033px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .box-title {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN, sans-serif;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .box-content {
    width: 100%;
    margin-top: 5px;
    max-height: 1000px;

    .flex-parent {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
      height:550px;
      .flex-child {
        width: 250px;
        margin: 5px 10px
      }
      .flex-child-two{
        width: 500px;
        margin: 5px 10px
      }
    }
  }
}

::v-deep #dhHlsVideoWrapper {
  width: auto !important;
}

.navigation-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 60px;
}

.icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.icon:hover {
  background-color: #e0e0e0;
}

.icon::before {
  content: '';
  width: 10px;
  height: 10px;
  border-top: 2px solid #333;
  border-right: 2px solid #333;
  transition: border-color 0.3s ease;
}

.prev::before {
  transform: rotate(-135deg);
  margin-left: 4px;
}

.next::before {
  transform: rotate(45deg);
  margin-right: 4px;
}

.icon:hover::before {
  border-color: #000;
}

</style>
