<template>
  <div class="camera-container" ref="currentUseCamera">
    <!-- 标题 -->
<!--    <div class="title-box">-->
<!--      <span class="ring"></span>-->
<!--      <span class="window-title">监控视频预览</span>-->
<!--      <div class="close" @click="closeWindow()"></div>-->
<!--    </div>-->
<!--    `http://localhost:5500/demo_window_simple_preview.html?cameraIndexCode=${this.cameraIndexCode}`-->
    <div class="content-box"  v-if="videoDisplay">
      <iframe
        id="main"
        ref="iframe"
        :src="`http://**************/hik?cameraIndexCode=${this.cameraIndexCode}`"
        width="100%"
        height="500px"
        frameborder="0">
      </iframe>
    </div>

  </div>
</template>

<script>
export default {
  name: "CameraWindow",
  props: ["cameraIndexCode"],
  data() {
    return {
      display: false,
      videoDisplay: false,
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
      this.videoDisplay = false;
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.camera-container {
  z-index: 1000;
  position: absolute;
  //top: 233px;
  //left: 371px;
  //padding: 25px;
  //width: 100px;
  //max-height: 833px;
  //background-image: linear-gradient(209deg, #2d2d64, #030315);
  //border: 2px solid #ffffff;
  //border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .content-box {
    //margin-top: 20px;
    //width: 100%;
    //height: 500px;
  }

}


</style>
