<!-- 标识弹窗 -->
<template>
  <div class="tips-box">

    <el-checkbox :indeterminate="isIndeterminateZd" v-model="checkAllZd" @change="handleCheckAllZdChange" class="checkbox check-all">房地信息</el-checkbox>
    <el-checkbox-group v-model="checkedZds" @change="handleCheckedZdChange" class="checkbox-group">
      <el-checkbox v-for="zd in zds" :label="zd" :key="zd" class="checkbox check-child">
        {{zd}}
        <el-image v-if="zd === '房地信息'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_fdxx.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="zd === '综合网格'"
                  class="tips-image"
                  :src="`http://**************/images/icon/map_icon_zhwg.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="zd === '厂房仓库'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_cfck.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="zd === '生产型企业'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_scqy.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="zd === '生产型企业-二工区'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_scqy-egq.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>
    </el-checkbox-group>
    <el-divider style="margin-top: 10px"></el-divider>

    <el-checkbox :indeterminate="isIndeterminate" v-model="checkAllCurrentUser" @change="handleCheckAllChange" class="checkbox check-all">现状使用主体</el-checkbox>
    <el-checkbox-group v-model="checkedCurrentUsers" @change="handleCheckedCurrentUserChange" class="checkbox-group">
      <el-checkbox v-for="currentUser in currentUsers" :label="currentUser" :key="currentUser" class="checkbox check-child">
        {{currentUser}}
        <el-image v-if="currentUser === '沿街商铺'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_yanjieshangpu.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '非住宅物业'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_feizhuzhaiwuye.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '文化娱乐场所'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_wenhuayule.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '监控'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_jiankong.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '宾旅馆'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_binbglvguan.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '商业网点'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_shangyewangdian.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '农贸市场'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_nongmao.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '学校'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_xuexiao.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '养老机构'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_yanglao.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '宗教场所'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_zongjiao.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '隔离酒店'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_gelijiudian.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="currentUser === '公寓租赁房'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xianzhaung_icon_gongyuzulingfang.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>

    </el-checkbox-group>

    <el-divider style="margin-top: 10px"></el-divider>

    <el-checkbox :indeterminate="isIndeterminateGreening" v-model="checkAllGreening" @change="handleCheckAllGreeningChange" class="checkbox check-all">绿化信息</el-checkbox>
    <el-checkbox-group v-model="checkedGreenings" @change="handleCheckedGreeningChange" class="checkbox-group">
      <el-checkbox v-for="greening in greenings" :label="greening" :key="greening" class="checkbox check-child">
        {{greening}}
        <el-image v-if="greening === '单位绿地'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvhua_icon_danwei.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="greening === '公共绿地'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvdi_icon_gonggong.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="greening === '行道树'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvhua_icon_xingdaoshu.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="greening === '商品房绿化'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvdi_icon_shangpin.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="greening === '售后公房绿化'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvhua_icon_shouhou.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="greening === '绿化网格'"
                  class="tips-image"
                  :src="`http://**************/images/icon/lvdi_icon_wangge.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>
    </el-checkbox-group>

    <el-divider></el-divider>

    <el-checkbox :indeterminate="isIndeterminateCommunity" v-model="checkAllCommunity" @change="handleCheckAllCommunityChange" class="checkbox check-all">小区基础信息</el-checkbox>
    <el-checkbox-group v-model="checkedCommunities" @change="handleCheckedCommunityChange" class="checkbox-group">
      <el-checkbox v-for="community in communities" :label="community" :key="community" class="checkbox check-child">
        {{community}}
        <el-image v-if="community === '居委会'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_juwei.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '小区边界线'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_xqbjx.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '小区出入口'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_churukou.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '物业管理处'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_wuye.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '垃圾房'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_lajifang.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '大件垃圾'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_dajianlaji.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '建筑垃圾'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_jianzhulaji.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '健身点'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_jianshen.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '配电室'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_peidian.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '凉亭'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_liangting.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '党建'"
                  class="tips-image"
                  :src="`http://**************/images/icon/xiaoqu_icon_dangjian.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="community === '两类人员网格化'"
                  class="tips-image"
                  :src="`http://**************/images/icon/icon_llryzddx.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>
    </el-checkbox-group>

    <el-divider></el-divider>

    <el-checkbox :indeterminate="isIndeterminatePipeNetwork" v-model="checkAllPipeNetwork" @change="handleCheckAllPipeNetworkChange" class="checkbox check-all">排水信息</el-checkbox>
    <el-checkbox-group v-model="checkedPipeNetworks" @change="handleCheckedPipeNetworkChange" class="checkbox-group">
      <el-checkbox v-for="pipeNetwork in pipeNetworks" :label="pipeNetwork" :key="pipeNetwork" class="checkbox check-child">
        {{pipeNetwork}}
        <el-image v-if="pipeNetwork === '市政管网'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_shizhenguanwang.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="pipeNetwork === '河道'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_hedao.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="pipeNetwork === '排涝泵站'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_plbz.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="pipeNetwork === '排污泵站'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_pwbz.png`"
                  :fit="`cover`"></el-image>
        <el-image v-if="pipeNetwork === '液位告警'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_ywcxgzsb.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>
    </el-checkbox-group>

    <el-divider></el-divider>

    <el-checkbox :indeterminate="isIndeterminateRiver" v-model="checkAllRiver" @change="handleCheckAllRiverChange" class="checkbox check-all">河道信息</el-checkbox>
    <el-checkbox-group v-model="checkedRivers" @change="handleCheckedRiverChange" class="checkbox-group">
      <el-checkbox v-for="river in rivers" :label="river" :key="river" class="checkbox check-child">
        {{river}}
        <el-image v-if="river === '河道'"
                  class="tips-image"
                  :src="`http://**************/images/icon/guanwang_icon_hedao.png`"
                  :fit="`cover`"></el-image>
      </el-checkbox>
    </el-checkbox-group>

  </div>
</template>

<script>
// const zdOptions = ['房地信息', '综合网格', '厂房仓库', '生产型企业', '生产型企业-二工区'];
const zdOptions = ['房地信息', '综合网格'];
// const currentUserOptions = ['沿街商铺', '非住宅物业', '文化娱乐场所', '农贸市场'];
const currentUserOptions = ['沿街商铺', '非住宅物业', '文化娱乐场所', '公寓租赁房'];
const greeningOptions = ['单位绿地', '公共绿地', '行道树', '商品房绿化', '售后公房绿化', '绿化网格'];
// const communityOptions = ['居委会', '小区边界线', '小区出入口', '物业管理处', '垃圾房', '大件垃圾', '建筑垃圾', '健身点', '配电室', '凉亭'];
const communityOptions = ['居委会', '小区边界线', '小区出入口', '物业管理处', '垃圾房', '大件垃圾', '建筑垃圾'];
const pipeNetworkOptions = ['市政管网', '排涝泵站', '排污泵站', '液位告警'];
const riverOptions = ['河道'];
export default {
  name: "TipsWindow",
  data() {
    return {

      checkAllZd: false,
      checkedZds: [],
      zds: zdOptions,
      isIndeterminateZd: false,

      checkAllCurrentUser: false,
      checkedCurrentUsers: [],
      currentUsers: currentUserOptions,
      isIndeterminate: false,

      checkAllCommunity: false,
      checkedCommunities: [],
      communities: communityOptions,
      isIndeterminateCommunity: false,

      checkAllGreening: false,
      checkedGreenings: [],
      greenings: greeningOptions,
      isIndeterminateGreening: false,

      checkAllPipeNetwork: false,
      checkedPipeNetworks: [],
      pipeNetworks: pipeNetworkOptions,
      isIndeterminatePipeNetwork: false,

      checkAllRiver: false,
      checkedRivers: [],
      rivers: riverOptions,
      isIndeterminateRiver: false,
    };
  },
  methods: {

    handleCheckAllZdChange(val) {
      this.checkedZds = val ? zdOptions : [];
      this.isIndeterminateZd = false;
      let data = {
        type: '房地信息',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedZdChange(value) {
      let checkedCount = value.length;
      this.checkAllZd = checkedCount === this.zds.length;
      this.isIndeterminateZd = checkedCount > 0 && checkedCount < this.zds.length;
      let data = {
        type: '房地信息',
        value: value,
      }
      this.$emit('checkedTips', data);
    },

    handleCheckAllChange(val) {
      this.checkedCurrentUsers = val ? currentUserOptions : [];
      this.isIndeterminate = false;
      let data = {
        type: '现状使用主体',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedCurrentUserChange(value) {
      let checkedCount = value.length;
      this.checkAllCurrentUser = checkedCount === this.currentUsers.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.currentUsers.length;
      let data = {
        type: '现状使用主体',
        value: value,
      }
      this.$emit('checkedTips', data);
    },

    handleCheckAllCommunityChange(val) {
      this.checkedCommunities = val ? communityOptions : [];
      this.isIndeterminateCommunity = false;
      let data = {
        type: '小区基础信息',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedCommunityChange(value) {
      let checkedCount = value.length;
      this.checkAllCommunity = checkedCount === this.communities.length;
      this.isIndeterminateCommunity = checkedCount > 0 && checkedCount < this.communities.length;
      let data = {
        type: '小区基础信息',
        value: value,
      }
      this.$emit('checkedTips', data);
    },

    handleCheckAllGreeningChange(val) {
      this.checkedGreenings = val ? greeningOptions : [];
      this.isIndeterminateGreening = false;
      let data = {
        type: '绿化信息',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedGreeningChange(value) {
      let checkedCount = value.length;
      this.checkAllGreening = checkedCount === this.greenings.length;
      this.isIndeterminateGreening = checkedCount > 0 && checkedCount < this.greenings.length;
      let data = {
        type: '绿化信息',
        value: value,
      }
      this.$emit('checkedTips', data);
    },

    handleCheckAllPipeNetworkChange(val) {
      this.checkedPipeNetworks = val ? pipeNetworkOptions : [];
      this.isIndeterminatePipeNetwork = false;
      let data = {
        type: '排水信息',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedPipeNetworkChange(value) {
      let checkedCount = value.length;
      this.checkAllPipeNetwork = checkedCount === this.pipeNetworks.length;
      this.isIndeterminatePipeNetwork = checkedCount > 0 && checkedCount < this.pipeNetworks.length;
      let data = {
        type: '排水信息',
        value: value,
      }
      this.$emit('checkedTips', data);
    },

    handleCheckAllRiverChange(val) {
      this.checkedRivers = val ? riverOptions : [];
      this.isIndeterminateRiver = false;
      let data = {
        type: '河道信息',
        value: val,
      }
      this.$emit('checkedTips', data);
    },
    handleCheckedRiverChange(value) {
      let checkedCount = value.length;
      this.checkAllRiver = checkedCount === this.rivers.length;
      this.isIndeterminateRiver = checkedCount > 0 && checkedCount < this.rivers.length;
      let data = {
        type: '河道信息',
        value: value,
      }
      this.$emit('checkedTips', data);
    }
  }
}
</script>

<style scoped>
.tips-box {
  position: absolute;
  top: 205px;
  right: 366px;
  width: 756px;
  height: 860px;
  background: linear-gradient(209deg, #2D2D64, #030315);
  border: 2px solid #FFFFFF;
  border-radius: 3px;
  padding: 6px 12px 6px 12px;
  z-index: 9999;
}

.check-all {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 32px;
}

.check-child {
  width: 120px;
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #C1DBFF;
  height:70px;
  line-height: 50px;
}

.tips-image {
  position: absolute;
  height:32px;
  top: 36px;
  left: 24px;
}

::v-deep .el-divider--horizontal {
  margin: 16px 0;
}

.checkbox-group >>>label.checkbox.check-child.el-checkbox{
  margin-right: 0;
}
</style>
