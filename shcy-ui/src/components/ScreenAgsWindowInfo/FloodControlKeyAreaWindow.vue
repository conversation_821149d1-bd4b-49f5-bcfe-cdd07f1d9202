<template>
  <div class="floodControlKeyArea-container" ref="floodControlKeyAreaDetail">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">灾害风险部位</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="风险部位" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.riskSite }}
        </el-descriptions-item>
        <el-descriptions-item label="风险等级" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.riskLevel }}
        </el-descriptions-item>
        <el-descriptions-item label="脆弱性区域" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.vulnerableArea }}
        </el-descriptions-item>
        <el-descriptions-item label="区域面积" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.areaSize }}
        </el-descriptions-item>
        <el-descriptions-item label="风险概况" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.riskOverview }}
        </el-descriptions-item>
        <el-descriptions-item label="受影响范围" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.affectedRange }}
        </el-descriptions-item>
        <el-descriptions-item label="防控期" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.controlPeriod }}
        </el-descriptions-item>
        <el-descriptions-item label="风险评级" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.riskRating }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="应对措施1" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.countermeasure1 }}
        </el-descriptions-item>
        <el-descriptions-item label="责任部门" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.responsibleDepartment1 }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.contactPerson1 }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.contactPhone1 }}
        </el-descriptions-item>
        <el-descriptions-item label="启动条件" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.startCondition1 }}
        </el-descriptions-item>
        <el-descriptions-item label="" :label-style="this.labelStyle" :content-style="this.contentStyle">
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="table-img" style="margin-top:16px;" v-show="floodControlKeyAreaData.countermeasure2">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="应对措施2" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.countermeasure2 }}
        </el-descriptions-item>
        <el-descriptions-item label="责任部门" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.responsibleDepartment2 }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.contactPerson2 }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.contactPhone2 }}
        </el-descriptions-item>
        <el-descriptions-item label="启动条件" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.floodControlKeyAreaData.startCondition2 }}
        </el-descriptions-item>
        <el-descriptions-item label="" :label-style="this.labelStyle" :content-style="this.contentStyle">
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div style="margin-top:16px;display: flex;justify-content:center" v-if="floodControlKeyAreaData.riskSite === '石化一村' || floodControlKeyAreaData.riskSite === '石化一村点位'">
      <el-button type="danger" @click="dispatch">紧急任务派遣</el-button>
    </div>

  </div>
</template>

<script>
import {handleUrgentTask} from "@/api/shcy/urgentTask";

export default {
  name: "FloodControlKeyAreaWindow",
  props: ["floodControlKeyAreaData"],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    };
  },
  mounted() {
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
    dispatch() {
      let data = {}
      handleUrgentTask(data).then(res=>{
        this.$modal.msgSuccess("派遣成功");
        this.closeWindow();
      })
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.floodControlKeyArea-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 870px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 820px;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}
</style>
