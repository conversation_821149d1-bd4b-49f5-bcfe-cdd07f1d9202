<!-- 搜索监控弹窗 -->
<template>
  <div class="search-camera-container">
    <div class="sel-camera-box">
      <ScreenDisplaySelect
        class="sel"
        :selStyle="selStyle"
        :seldata="selData"
        :selDefault="selDefault"
        @handleSelect="handleSelect"
      />
      <div class="search-camera-box">
        <input class="input-search" type="search" v-model="keyword" placeholder="请输入小区、路名进行搜索" />
        <span class="input-search-icon" @click="isSearch()">
          <i class="search-icon"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script>

import ScreenDisplaySelect from "../ScreenDisplaySelect/index.vue";
import {
  getAllMonitoringTypeName,
} from "@/api/shcy/screen";

export default {
  name: "SearchCameraWindow",
  components: {
    ScreenDisplaySelect,
  },
  data() {
    return {
      selStyle: {
        left: "-2px",
        top: "38px",
      },
      selData: [],
      selDefault: "全部",
      searchType: "全部",
      keyword: "",
    }
  },
  created() {
    this.getCameraTypeNameList();
  },
  methods: {
    isSearch() {
      let data = {
        searchType: this.searchType,
        keyword: this.keyword,
      }
      this.$emit("searchCamera", data);
    },
    handleSelect(value) {
      this.searchType = value;
    },
    getCameraTypeNameList() {
      getAllMonitoringTypeName().then(response => {
        this.selData = response.data;
        this.selData.unshift("全部");
      });
    },
  }
}

</script>

<style lang="scss" scoped>
// 搜索框
.search-camera-container {
  position: absolute;
  top: 226px;
  left: 600px;
  width: 900px;
  background: linear-gradient(209deg, #2D2D64, #030315);
  border: 2px solid #FFFFFF;
  border-radius: 3px;
  z-index: 9998;

  // 下拉列表
  .sel-camera-box {
    margin: 16px;
    width: 100%;
    display: flex;

    .sel {
      margin-right: 26px;
    }

    // 搜索框
    .search-camera-box {
      display: flex;
      width: 554px;
      height: 41px;
      border: 2px solid #656594;

      .input-search {
        padding: 0 0 0 50px;
        width: 513px;
        // height: 124px;
        outline: none;
        border: none;
        border-right: 2px solid #656594;
        background-color: #181839;
        font-size: 12px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #8f8fa7;
        line-height: 41px;
      }

      .input-search-icon {
        position: relative;
        display: inline-block;
        width: 41px;
        // height: 124px;
        background-color: #2d2d64;
        cursor: pointer;

        .search-icon {
          position: absolute;
          top: 13px;
          left: 13px;
          display: block;
          width: 12px;
          height: 14px;
          background: url("../../assets/screen_display_img/bottom_icon_search.png")
          no-repeat;
          background-size: contain;
        }
      }
    }
  }

}

</style>
