<!-- 搜索弹窗 -->
<template>
  <div class="search-container">
    <div class="sel-box">
      <ScreenDisplaySelect
        class="sel"
        :selStyle="selStyle"
        :seldata="selData"
        :selDefault="selDefault"
        @handleSelect="handleSelect"
      />
      <div class="search-box">
        <input class="input-search" type="search" v-model="keyword" placeholder="请输入关键词" />
        <span class="input-search-icon" @click="isSearch()">
          <i class="search-icon"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script>

import ScreenDisplaySelect from "../ScreenDisplaySelect/index.vue";

export default {
  name: "SearchWindow",
  components: {
    ScreenDisplaySelect,
  },
  data() {
    return {
      selStyle: {
        left: "-2px",
        top: "38px",
      },
      selData: [
        "全部",
        "沿街商铺",
        "房地信息",
      ],
      selDefault: "全部",
      searchType: "全部",
      keyword: "",
    }
  },
  methods: {
    isSearch() {
      let data = {
        searchType: this.searchType,
        keyword: this.keyword,
      }
      this.$emit("searchMap", data);
    },
    handleSelect(value) {
      this.searchType = value;
    },
  }
}

</script>

<style lang="scss" scoped>
// 搜索框
.search-container {
  // display: none;
  position: absolute;
  top: 336px;
  left: 600px;
  width: 900px;
  background: linear-gradient(209deg, #2D2D64, #030315);
  border: 2px solid #FFFFFF;
  border-radius: 3px;
  z-index: 9999;

  // 下拉列表
  .sel-box {
    margin: 16px;
    width: 100%;
    display: flex;

    .sel {
      margin-right: 26px;
    }

    // 搜索框
    .search-box {
      display: flex;
      width: 554px;
      height: 41px;
      border: 2px solid #656594;

      .input-search {
        padding: 0 0 0 50px;
        width: 513px;
        // height: 124px;
        outline: none;
        border: none;
        border-right: 2px solid #656594;
        background-color: #181839;
        font-size: 12px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #8f8fa7;
        line-height: 41px;
      }

      .input-search-icon {
        position: relative;
        display: inline-block;
        width: 41px;
        // height: 124px;
        background-color: #2d2d64;
        cursor: pointer;

        .search-icon {
          position: absolute;
          top: 13px;
          left: 13px;
          display: block;
          width: 12px;
          height: 14px;
          background: url("../../assets/screen_display_img/bottom_icon_search.png")
          no-repeat;
          background-size: contain;
        }
      }
    }
  }

}

</style>
