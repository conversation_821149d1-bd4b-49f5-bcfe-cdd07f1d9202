<template>
  <div class="alarmRecord-container" ref="alarmRecord">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">报警记录</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-table
        :data="alarmRecordData"
        :header-cell-style="{background:'#2D2D64',color:'#fff',fontSize:'12px', fontWeight:'normal',borderColor:'#656594',borderWidth: '2px',height: '40px'}"
        :cell-style="{background:'#252545',color:'#fff',borderColor:'#656594',borderWidth: '2px', height: '40px'}"
        border
        style="width: 100%;">
        <el-table-column
          prop="attrName"
          label="报警属性名称"
          :resizable="false">
        </el-table-column>
        <el-table-column
          prop="value"
          label="报警属性值"
          :resizable="false">
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
export default {
  name: "AlarmRecordWindow",
  props: ["alarmRecordData"],
  data() {
    return {
      display: false,
    };
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.alarmRecord-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 870px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 820px;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}
</style>
