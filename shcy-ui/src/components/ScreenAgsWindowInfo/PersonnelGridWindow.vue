<template>
  <div class="personnel-grid-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">网格化管理对象及联络人员名单</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="table-img" style="margin-top:16px;">
      <el-descriptions class="margin-top" title="" :column="2" border>
        <el-descriptions-item label="姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.xm }}
        </el-descriptions-item>
        <el-descriptions-item label="罪名" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.zm }}
        </el-descriptions-item>
        <el-descriptions-item label="期满日期" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.qmrq }}
        </el-descriptions-item>
        <el-descriptions-item label="户籍地地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ hxdd }}
        </el-descriptions-item>
        <el-descriptions-item label="居住地地址" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ jzdd }}
        </el-descriptions-item>
        <el-descriptions-item label="居住地村（居）委" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.jzdcjw }}
        </el-descriptions-item>
        <el-descriptions-item label="社区矫正重点对象/社区矫正重要对象/重点关注安置帮教对象" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.sqjzcbdx }}
        </el-descriptions-item>
        <el-descriptions-item label="颜色等级" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.ysdj }}
        </el-descriptions-item>
        <el-descriptions-item label="村（居）联络员姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.cjllxyxm }}
        </el-descriptions-item>
        <el-descriptions-item label="村（居）联络员联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ cjllxylxfs }}
        </el-descriptions-item>
        <el-descriptions-item label="网格信息员姓名" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.wgxxyxm }}
        </el-descriptions-item>
        <el-descriptions-item label="网格信息员联系方式" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ wgxxylxfs }}
        </el-descriptions-item>
        <el-descriptions-item label="纳入网格化管理原因" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.nrwghglyy }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :label-style="this.labelStyle" :content-style="this.contentStyle">
          {{ this.personnelGridData.bz }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="title-box" style="margin-top:16px;">
      <span class="ring"></span>
      <span class="window-title">巡访记录</span>
    </div>
    <div class="table-img1" style="margin-top:16px;">
      <el-table
        :data="visitRecords"
        :header-cell-style="{background:'#2D2D64',color:'#fff',fontSize:'12px', fontWeight:'normal',borderColor:'#656594',borderWidth: '2px',height: '40px'}"
        :cell-style="{background:'#252545',color:'#fff',borderColor:'#656594',borderWidth: '2px', height: '40px'}"
        border
        style="width: 100%;">
        <el-table-column
          prop="date"
          label="巡访时间"
          width="186"
          :resizable="false">
        </el-table-column>
        <el-table-column
          prop="condition"
          label="巡访情况"
          width="558"
          :resizable="false">
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
export default {
  name: "PersonnelGridWindow",
  props: ["personnelGridData", "visitRecords"],
  data() {
    return {
      display: false,
      labelStyle: {
        'background-color': '#2D2D64',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      },
      contentStyle: {
        'background-color': '#252545',
        color: '#fff',
        'font-size': '12px',
        width: '25%',
      }
    }
  },
  computed: {
    hxdd() {
      // 将this.personnelGridData.hxdd中的数字替换成*代替实现数据脱敏
      if (this.personnelGridData.hxdd) {
        return this.personnelGridData.hxdd.replace(/\d/g, "*");
      } else {
        return "";
      }
    },
    jzdd() {
      if (this.personnelGridData.jzdd) {
        return this.personnelGridData.jzdd.replace(/\d/g, "*");
      } else {
        return "";
      }
    },
    cjllxylxfs() {
      // 将this.personnelGridData.cjllxylxfs中的最后四位数字替换成*代替实现数据脱敏
      if (this.personnelGridData.cjllxylxfs) {
        return this.personnelGridData.cjllxylxfs.replace(/\d{4}$/, "****");
      } else {
        return "";
      }
    },
    wgxxylxfs() {
      // 将this.personnelGridData.wgxxylxfs中的最后四位数字替换成*代替实现数据脱敏
      if (this.personnelGridData.wgxxylxfs) {
        return this.personnelGridData.wgxxylxfs.replace(/\d{4}$/, "****");
      } else {
        return "";
      }
    }
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
  },
};
</script>

<style lang="scss" scoped>
// 容器
.personnel-grid-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 800px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .table-img {
    width: 100%;
    max-height: 650px;
    overflow-y: scroll;
  }

  .table-img::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .table-img1 {
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;
  }

  .table-img1::-webkit-scrollbar {
    width: 0;
    height: 10px;
  }

  .descriptions-mt16{
    margin-top: 16px;
  }
}

::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
  border: 2px solid #656594;
}

.el-table--border:after,
.el-table--group:after,
.el-table:before {
  background-color: #656594;
}

.el-table--border,
.el-table--group {
  border-color: #656594;
}


</style>
