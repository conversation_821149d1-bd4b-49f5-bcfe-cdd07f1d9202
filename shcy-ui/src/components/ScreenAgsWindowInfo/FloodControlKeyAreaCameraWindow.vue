<template>
  <div class="box-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ floodControlKeyAreaCameraData.riskSite }}</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <div class="box-content">
      <div class="flex-parent">
        <template v-if="floodControlKeyAreaCameraData.riskSite === '志田冷库点位' || floodControlKeyAreaCameraData.riskSite === '志田冷库'">
          <div class="flex-child">
            <FLVPlayer :videoUrl="ztlkqjUrl" ref="ztlkqj"></FLVPlayer>
          </div>
          <div class="flex-child">
            <FLVPlayer :videoUrl="ztlkxjUrl" ref="ztlkxj"></FLVPlayer>
          </div>
        </template>
        <template v-else>
          <!--<div class="flex-child" v-for="(item, index) in cameraList"
               :key="item.id">
            <HLSPlayer :videoUrl="item.url"></HLSPlayer>
          </div>-->
          <div id="fxftPlayer" style="width: 1106px; height: 595px"></div>
        </template>
      </div>
    </div>

    <div style="margin-top:16px;display: flex;justify-content:center"
         v-if="floodControlKeyAreaCameraData.riskSite !== '石化一村' && floodControlKeyAreaCameraData.riskSite !== '石化一村点位'">
      <el-button type="danger" @click="dispatch">突发事件派遣</el-button>
    </div>

  </div>
</template>

<script>

const IS_MOVE_DEVICE = document.body.clientWidth < 992 // 是否移动设备
const MSE_IS_SUPPORT = !!window.MediaSource // 是否支持mse

import HLSPlayer from "@/components/HLSPlayer/index.vue";
import FLVPlayer from "@/components/FLVPlayer/index.vue";
import {getLinkCameras} from "@/api/shcy/cameras";
import {handleEmergency} from "@/api/shcy/emergencyAlertEventInfo";
import {getHjzzMonitorVideo} from "@/api/shcy/screen.js";

export default {
  name: "FloodControlKeyAreaCameraWindow",
  components: {HLSPlayer, FLVPlayer},
  props: ["floodControlKeyAreaCameraData"],
  data() {
    return {
      display: false,
      cameraList: [],
      cameraIndexCodes: this.floodControlKeyAreaCameraData.cameras,
      ztlkqjUrl: null,
      ztlkxjUrl: null,

      isMoveDevice: IS_MOVE_DEVICE,
      player: null,
      splitNum: IS_MOVE_DEVICE ? 1 : 2,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? 'mse' : 'decoder',
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      urls: {
        realplay: '',
      },
      muted: true,

    };
  },
  mounted() {
    // 判断是否是志田冷库
    if (this.floodControlKeyAreaCameraData.riskSite === '志田冷库点位' || this.floodControlKeyAreaCameraData.riskSite === '志田冷库') {
      this.getZtlkqj();
      this.getZtlkxj();
    } else {
      // this.getLinkCameras();

      this.$el.style.setProperty('display', 'block')
      this.init()
      this.createPlayer()

      this.$nextTick(() => {
        this.play123()
      })

    }
  },
  methods: {

    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener('resize', () => {
        this.player.JS_Resize()
      })
    },
    createPlayer() {
      this.player = new window.JSPlugin({
        szId: 'fxftPlayer',
        szBasePath: "./",
        iMaxSplit: 4,
        iCurrentSplit: IS_MOVE_DEVICE ? 1 : 2,
        openDebug: true,
        oStyle: {
          borderSelect: IS_MOVE_DEVICE ? '#000' : '#FFCC00',
        }
      })

      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
          console.log('windowSelect callback: ', iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
          console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
        },
        windowEventOver: function (iWndIndex) {  //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {  //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {  //全屏切换回调
          console.log('fullScreen callback: ', bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
          console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {  //性能不足回调
          console.log('performanceLack callback: ');
        }
      });
    },
    /* 预览&对讲 */
    realplay() {
      let { player, mode, urls } = this,
        index = player.currentWindowIndex,
        playURL = urls.realplay

      player.JS_Play(playURL, { playURL, mode }, index).then(
        () => { console.log('realplay success') },
        e => { console.error(e) }
      )
    },

    // 播放视频
    play123() {
      if (this.cameraIndexCodes) {
        // 获取this.cameraIndexCodes用逗号分割后数组的个数
        const length = this.cameraIndexCodes.split(',').length
        if (length <= 1) {
          this.splitNum = 1
        } else if (length <= 4) {
          this.splitNum = 2
        } else if (length <= 9) {
          this.splitNum = 3
        } else {
          this.splitNum = 4
        }
        this.player.JS_ArrangeWindow(this.splitNum).then(
          () => {
            getLinkCameras(this.cameraIndexCodes).then((res) => {
              if (res.code === 200) {
                this.cameraList = res.data;
                let {player, mode, urls} = this,
                  index = player.currentWindowIndex,
                  playURL = urls.realplay
                // 遍历这4个后播放
                this.cameraList.forEach((item, index) => {
                  this.player.JS_Play(item.url, {playURL: item.url, mode: 0}, index).then(
                    () => {
                      console.log('realplay success')
                    },
                    e => {
                      console.error(e)
                    }
                  )
                })
              }
            })
          },
          e => { console.error(e) }
        )
      }
    },
    getLinkCameras() {
      if (this.cameraIndexCodes) {
        getLinkCameras(this.cameraIndexCodes).then((res) => {
          if (res.code === 200) {
            this.cameraList = res.data;
          }
        });
      }
    },
    getZtlkqj() {
      let query = {channelCode: '1000023$1$0$0'}
      getHjzzMonitorVideo(query).then(res => {
        this.ztlkqjUrl = res.data
      })
    },
    getZtlkxj() {
      let query = {channelCode: '1000023$1$0$1'}
      getHjzzMonitorVideo(query).then(res => {
        this.ztlkxjUrl = res.data
      })
    },
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
    dispatch() {
      let data = {}
      data.alarmLocation = this.floodControlKeyAreaCameraData.riskSite;
      handleEmergency(data).then(res => {
        this.$modal.msgSuccess("派遣成功");
        this.closeWindow();
      })
    },
    stopAllPlay() {
      this.player.JS_StopRealPlayAll().then(
          () => { console.log('stopAllPlay success') },
          e => { console.error(e) }
      )
    },
  },
  beforeDestroy() {
    this.stopAllPlay()
  }
};
</script>

<style lang="scss" scoped>
// 容器
.box-container {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 25px;
  width: 1100px;
  max-height: 833px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 2px solid #ffffff;
  border-radius: 3px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 20px;
    // 圆环
    .ring {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 10px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 20px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
      background-size: contain;
    }
  }

  .box-content {
    width: 100%;
    margin-top: 16px;
    max-height: 800px;

    .flex-parent {
      //display: flex;
      //flex-wrap: wrap;
      //justify-content: flex-start;
      //align-items: flex-start;

      display: flex;
      //flex-direction: column;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: center;
      margin: 0 auto;

      .flex-child {
        width: 522px;
        //margin-top: 10px
      }
    }
  }


}

</style>
