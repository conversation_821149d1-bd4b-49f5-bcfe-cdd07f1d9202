<template>
  <div class="flv-player">
    <video ref="flvVideo" width="100%" controls preload="auto" autoplay="autoplay" muted></video>
  </div>
</template>
<script>
export default {
  name: "FLVPlayer",
  props: {
    videoUrl: {
      type: String,
      default: ''
    },
    timeout: {
      type: Number,
      default: 2000
    },
    instantly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      video: null,
      flvPlayer: null
    };
  },
  created() {
  },
  mounted() {
    if (this.instantly) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.playVideo();
        }, this.timeout);
      })
    }
  },
  watch: {
    videoUrl() {
      this.playVideo();
    }
  },
  methods: {
    playVideo() {
      this.video =  this.$refs.flvVideo;
      this.flvPlayer = dhflvjs.createPlayer({
        type: 'flv',
        url: this.videoUrl
      });
      this.flvPlayer.attachMediaElement(this.video);
      this.flvPlayer.load();
      this.flvPlayer.play();
    },
    playHjzzVideo(videoUrl) {
      this.video =  this.$refs.flvVideo;
      this.flvPlayer = dhflvjs.createPlayer({
        type: 'flv',
        url: videoUrl
      });
      this.flvPlayer.attachMediaElement(this.video);
      this.flvPlayer.load();
      this.flvPlayer.play();
    }
  },
  beforeDestroy() {
    this.video.pause();
    this.flvPlayer.destroy();
  }
};
</script>
<style lang="scss" scoped>
.flv-player {
  width: 100%;
  position: relative;
}

::v-deep #dhFlvVideoWrapper {
  height: auto !important;
}
</style>
