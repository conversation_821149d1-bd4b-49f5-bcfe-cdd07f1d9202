#container {
  position: relative;
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  border-radius: 1300px;

  .amap-layer {
    position: fixed;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  // 小区基础信息弹窗
  .basicInfoWindow {
    width: 717px;
    height: 738px;
    background: url("https://pic.rmb.bdstatic.com/bjh/175a6805a353c9e1cb813fe307de9e83.png") no-repeat;
    background-size: contain;
    z-index: 9999;
  }

  // 临蒙居委会弹窗-截图
  .buildingNumWindow {
    width: 717px;
    height: 738px;
    background: url("https://pic.rmb.bdstatic.com/bjh/175a6805a353c9e1cb813fe307de9e83.png") no-repeat;
    background-size: contain;
    z-index: 9999;
  }

  // 地图操作浮窗
  .floatingWin {
    position: absolute;
    width: 260px;
    height: 260px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
    font-size: 40px;
    line-height: 260px;
  }

  // 还原
  .floatingWin-restore {
    right: 700px;
    bottom: 1990px;
    background: url("https://pic.rmb.bdstatic.com/bjh/8ed1f7f4f9b93bd2a9f8b810be4b8f99.png") no-repeat;
    background-size: contain;
  }

  // 图斑切换
  .floatingWin-patternSpot {
    right: 600px;
    bottom: 1450px;
    background: url("https://pic.rmb.bdstatic.com/bjh/724e35b35116b8cb040158cbd328ffcd.png") no-repeat;
    background-size: contain;
  }

  // 搜索按钮
  .floatingWin-search {
    right: 640px;
    bottom: 1720px;
    border-radius: 50%;
    background: url("https://pic.rmb.bdstatic.com/bjh/f22ea581aa97f89c2423b39386e0c407.png") no-repeat;
    background-size: contain;
  }

  // 搜索框
  .search-container {
    // display: none;
    position: absolute;
    top: 1010px;
    left: 1800px;
    width: 2700px;
    background: linear-gradient(209deg, #2D2D64, #030315);
    border: 4px solid #FFFFFF;
    border-radius: 10px;
    z-index: 9999;

    // 搜索框
    .search-input {
      padding: 70px 100px;
      width: 100%;
      outline: none;
      border: none;
      border-bottom: 4px solid #FFFFFF;
      background-color: transparent;
      font-size: 46px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #acacac;
    }

    // 搜索历史
    .search-history {
      padding: 0 100px 70px 100px;
      width: 100%;
      height: 1090px;

      .search-title {
        display: flex;
        justify-content: space-between;
        font-size: 46px;
        color: #acacac;

        .clear {
          cursor: pointer;
        }
      }

      .search-keyword {
        display: inline-block;
        margin-right: 70px;
        margin-bottom: 70px;
        font-size: 46px;
        padding: 30px 50px;
        background-color: #343475;
        border-radius: 100px;
        color: #acacac;
        cursor: pointer;
      }

    }
  }



  // 图层切换
  .floatingWin-switch {
    right: 640px;
    bottom: 1180px;
    background: url("https://pic.rmb.bdstatic.com/bjh/0d484b8e185146a457faa47cee270342.png") no-repeat;
    background-size: contain;
  }

  // 疫情防控
  .floatingWin-illnessControl {
    right: 800px;
    bottom: 640px;
    background: url("https://s1.ax1x.com/2022/10/18/xrAIrn.png") no-repeat;
    background-size: contain;
  }

  // 标注示例
  .floatingWin-tips {
    right: 700px;
    bottom: 910px;
    background: url("https://pic.rmb.bdstatic.com/bjh/af255b22baecb1a175723302f52819d7.png") no-repeat;
    background-size: contain;

    .tops-box {
      position: absolute;
      top: -200px;
      right: 360px;
      width: 930px;
      height: 710px;
      background: url("https://pic.rmb.bdstatic.com/bjh/0130afd02cb34effb2486fd56b29ee4e.png") no-repeat;
      background-size: contain;
    }
  }

  // 小区基础信息中的icon按钮选项
  .iconSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 1730px;
    bottom: 360px;
    width: 2800px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .icon-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .icon {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #2BFBD9;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

  }

  // 管网数据中的管网按钮选项
  .pipeSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 2630px;
    bottom: 360px;
    width: 1000px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .pipe-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .pipe {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #2BFBD9;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

    // .pipe-item:hover .pipe{
    //   background: #c7c7c6;
    // }
  }

  // 现状使用主体数据中的按钮选项
  .currentUserSelBtn {
    display: flex;
    align-items: center;
    position: absolute;
    left: 1630px;
    bottom: 360px;
    width: 3000px;
    height: 170px;
    background: #186FE8;
    border: 10px solid #2DC6ED;
    border-radius: 80px;
    z-index: 999;

    .currentUser-item {
      flex-grow: 1;
      text-align: center;
      cursor: pointer;

      span {
        display: inline-block;
      }

      .currentUser {
        margin-right: 20px;
        width: 52px;
        height: 52px;
        line-height: 170px;
        border: 10px solid #FFFFFF;
        background: #c7c7c6;
        border-radius: 50%;
        vertical-align: -10px;
      }

      .title {
        font-size: 48px;
        line-height: 170px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
      }

    }

    // .pipe-item:hover .pipe{
    //   background: #c7c7c6;
    // }
  }



  ::v-deep .amap-marker-label {
    padding-bottom: 8px;
    font-size: 24px;
    color: #fff;
    border: 0;
    background-color: transparent;
  }

  /* 可以设置不同的进入和离开动画 */
  /* 设置持续时间和动画函数 */
  .slide-fade-enter-active {
    transition: all 0.5s ease;
  }

  .slide-fade-leave-active {
    transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter,
  .slide-fade-leave-to {
    // transform: translateX(260px);
    width: 0;
    opacity: 0;
  }
}
