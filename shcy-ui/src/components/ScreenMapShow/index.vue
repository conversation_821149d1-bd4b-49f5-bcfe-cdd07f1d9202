<template>
  <div id="container">
    <!-- 垃圾房-弹窗组件 -->
    <GarbageRoomWindow
      :garbageData="garbageData"
      ref="garbageRoomWindow"
      v-show="isGarbageShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 党建库-党建办事处-委员会 弹窗组件-->
    <PartyBuildingWindow
      :partyData="partyData"
      ref="partyBuildingWindow"
      v-show="isPartyBuildingShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 监控-弹窗组件 -->
    <MonitorWindow
      :monitorData="monitorData"
      ref="monitorWindow"
      v-show="isMonitorShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 隔离酒店-弹窗组件 -->
    <IsolateHoteWindow
      :isolateHoteData="isolateHoteData"
      ref="isolateHoteWindow"
      v-show="isIsolateHoteShow"
      @mouseleave.native="windowLeave()"
    />

    <!--大件垃圾和建筑垃圾-弹窗组件 -->
    <GarbageOtherWindow
      :garbageOtherData="garbageOtherData"
      ref="garbageOtherWindow"
      v-show="isGarbageOtherShow"
      @mouseleave.native="windowLeave()"
    />
    <!-- 出入口-弹窗组件 -->
    <EntryExitWindow
      :entryExitData="entryExitData"
      ref="entryExitWindow"
      v-show="isEntryExitShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 出入口-弹窗组件 -->
    <BodybuildingWindow
      ref="bodybuildingWindow"
      v-show="isBodybuildingShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 用地信息-弹窗组件 -->
    <LandInfoWindow
      :landInfoData="landInfoData"
      ref="landInfoWindow"
      v-show="isLandInfoShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 绿化信息-弹窗组件 -->
    <GreeningWindow
      :greeningData="greeningData"
      ref="greeningWindow"
      v-show="isGreeningShow"
      @mouseleave.native="windowLeave()"
    />

    <!-- 管网数据信息-弹窗组件 -->
    <transition name="el-zoom-in-top">
      <PipeInfoWindow
        :pipeData="pipeData"
        ref="PipeInfoWindow"
        v-show="isPipeInfoShow"
        @mouseleave.native="windowLeave()"
      />
    </transition>

    <!-- 井盖数据信息-弹窗组件 -->
    <transition name="el-zoom-in-top">
      <WellCoverWindow
        :wellCoverData="wellCoverData"
        ref="wellCoverWindow"
        v-show="isWellCoverShow"
        @mouseleave.native="windowLeave()"
      />
    </transition>

    <!-- 绿化图斑信息-弹窗组件 -->
    <transition name="el-zoom-in-top">
      <GreeningSpotWindow
        :greeningSpotData="greeningSpotData"
        ref="greeningSpotWindow"
        v-show="isGreeningSpotShow"
        @mouseleave.native="windowLeave()"
      />
    </transition>
    <!--  -->

    <!-- 现状使用主体-大弹窗 -->
    <!-- <transition name="el-zoom-in-center">
      <CurrentUseWindow
        :currentUserData="currentUserData"
        @closeWindow="closeCurrentUseWindow"
        ref="currentUseWindow"
        v-show="isCurrentUserShow"
      />
    </transition> -->
    <transition name="el-zoom-in-center">
      <CurrentUseWindowTwo
        :currentUserData="streetShopData"
        @closeWindow="closeCurrentUseWindow"
        ref="currentUseWindow"
        v-show="isCurrentUserShow"
      />
    </transition>

    <!-- 林长制巡查事件上报记录-大弹窗 -->
    <transition name="el-zoom-in-center">
      <PatrolEventWindow v-show="closePatrolEventWindow" />
    </transition>

    <!-- 紧急通道-弹窗组件 -->
    <EmergencyLoadWindow
      ref="emergencyLoadWindow"
      v-show="isEmergencyLoadShow"
      @mouseleave.native="windowLeave()"
    />
    <!-- 小区基础信息弹窗 -->
    <div
      class="basicInfoWindow"
      ref="basicInfoWindow"
      v-show="isBasicInfoShow"
      @mouseleave="windowLeave()"
    ></div>

    <!-- 楼号信息弹窗 -->
    <!-- <div
      class="buildingNumWindow"
      ref="buildingNumWindow"
      v-show="isBuildingNumShow"
      @mouseleave="windowLeave()"
    ></div> -->

    <!-- 地图操作浮窗 -->
    <!-- 还原 -->
    <div @click="winRestore()" class="floatingWin floatingWin-restore"></div>
    <!-- 搜索 -->
    <div @click="winSearch()" class="floatingWin floatingWin-search"></div>
    <!-- 搜索框 -->
    <transition name="el-zoom-in-center">
      <div class="search-container" v-show="isInputShow">
        <input
          class="search-input"
          ref="search_input"
          type="search"
          placeholder="请输入关键字"
        />
        <!-- v-show="isInputShow"
        @blur="isInputShow = false" -->
        <div class="search-history">
          <p class="search-title">
            <span class="title">搜索历史</span>
            <span class="clear">清空</span>
          </p>
          <span class="search-keyword">临潮一村</span>
          <span class="search-keyword">鹦鹉洲生态湿地</span>
          <span class="search-keyword">临蒙居委</span>
          <span class="search-keyword">七彩艺术酒店</span>
          <span class="search-keyword">海棠居委</span>
          <span class="search-keyword">临潮幼儿园</span>
          <span class="search-keyword">海棠居委</span>
          <span class="search-keyword">石化第一小学</span>
          <span class="search-keyword">滨海公园</span>
        </div>
      </div>
    </transition>
    <!-- 网格-图斑 -->
    <div
      @click="winPatternSpot()"
      class="floatingWin floatingWin-patternSpot"
    ></div>
    <!-- 卫星地图 电子地图 切换 -->
    <div @click="winSwitch()" class="floatingWin floatingWin-switch"></div>
    <!-- 标识指示 -->
    <div @click="winTips()" class="floatingWin floatingWin-tips">
      <transition name="el-zoom-in-center">
        <div class="tops-box" v-show="isTipsShow"></div>
      </transition>
    </div>
    <!-- 疫情防控 -->
    <div
      @click="illnessControl()"
      class="floatingWin floatingWin-illnessControl"
    ></div>

    <!-- 小区基础信息中的icon按钮选项 -->
    <div class="iconSelBtn" v-show="iconSelBtnShow">
      <div
        class="icon-item"
        v-for="(item, index) in iconSelBtnData"
        :key="item.id"
        @click="iconSelBtn(index)"
      >
        <span class="icon" :ref="'icon0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <!-- 管网数据中的管网按钮选项 -->
    <div class="pipeSelBtn" v-show="pipeSelBtnShow">
      <div
        class="pipe-item"
        v-for="(item, index) in pipeSelBtnData"
        :key="item.id"
        @click="pipeSelBtn(index)"
      >
        <span class="pipe" :ref="'pipe0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
    <!-- 现状使用主体中的按钮选项 -->
    <div class="currentUserSelBtn" v-show="currentUserSelBtnShow">
      <div
        class="currentUser-item"
        v-for="(item, index) in currentUserSelBtnData"
        :key="item.id"
        @click="currentUserSelBtn(index)"
      >
        <span class="currentUser" :ref="'currentUser0' + index"></span>
        <span class="title">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script>
// 垃圾房-弹窗组件
import GarbageRoomWindow from "../ScreenWindowInfo/GarbageRoomWindow.vue";
// 监控-弹窗组件
import MonitorWindow from "../ScreenWindowInfo/MonitorWindow.vue";
// 大件垃圾和建筑垃圾-弹窗组件
import GarbageOtherWindow from "../ScreenWindowInfo/GarbageOtherWindow.vue";
// 出入口-弹窗组件
import EntryExitWindow from "../ScreenWindowInfo/EntryExitWindow.vue";
// 绿化信息-弹窗组件
import GreeningWindow from "../ScreenWindowInfo/GreeningWindow.vue";
// 用地信息-弹窗组件
import LandInfoWindow from "../ScreenWindowInfo/LandInfoWindow.vue";
// 现状使用主体
import CurrentUseWindow from "../ScreenWindowInfo/CurrentUseWindow.vue";
import CurrentUseWindowTwo from "../ScreenWindowInfo/CurrentUseWindowTwo.vue";
// 紧急通道-弹窗
import EmergencyLoadWindow from "../ScreenWindowInfo/EmergencyLoadWindow.vue";
// 管网数据信息-弹窗组件
import PipeInfoWindow from "../ScreenWindowInfo/PipeInfoWindow.vue";
// 井盖数据信息-弹窗组件
import WellCoverWindow from "../ScreenWindowInfo/WellCoverWindow.vue";
// 林长制巡查事件上报记录-弹窗组件
import PatrolEventWindow from "../ScreenWindowInfo/PatrolEventWindow.vue";
// 绿化图斑信息-弹窗组件
import GreeningSpotWindow from "../ScreenWindowInfo/GreeningSpotWindow.vue";
// 隔离酒店-弹窗组件
import IsolateHoteWindow from "../ScreenWindowInfo/IsolateHoteWindow.vue";
// 健身点-弹窗组件
import BodybuildingWindow from "../ScreenWindowInfo/BodybuildingWindow.vue";
// 党建库-党建办事处-弹窗组件
import PartyBuildingWindow from "../ScreenWindowInfo/PartyBuildingWindow.vue";

// 高德地图loader引入
import AMapLoader from "@amap/amap-jsapi-loader";
import { timeout } from "q";
export default {
  props: ["btnTitle"],
  components: {
    GarbageRoomWindow,
    MonitorWindow,
    GarbageOtherWindow,
    EntryExitWindow,
    GreeningWindow,
    LandInfoWindow,
    CurrentUseWindow,
    CurrentUseWindowTwo,
    EmergencyLoadWindow,
    PipeInfoWindow,
    WellCoverWindow,
    PatrolEventWindow,
    GreeningSpotWindow,
    IsolateHoteWindow,
    BodybuildingWindow,
    PartyBuildingWindow,
  },
  computed: {
    closePatrolEventWindow() {
      return this.$store.state.patrolEvent.display;
    },
  },
  watch: {
    closePatrolEventWindow(newVal, oldVal) {
      // console.log(newVal, oldVal);
      //
      if (!newVal) {
        // 是否未读
        if (!this.isReaded) {
          // 未读状态则....
          console.log(this.greeningArr.indexOf(this.eventTips));
          this.greeningArr.splice(3, 1);
          this.map.remove(this.eventTips);
          // 设置为已读
          this.isReaded = true;
        }
      }
    },
  },
  data() {
    return {
      map: null,
      AMap: null,
      // 是否已填加“全部数据”遮罩物
      isAllData: false,
      // 是否已填加“道路红线”遮罩物
      isRoadLine: false,
      // 是否已填加“房地调查”遮罩物
      isPremisesSurvey: false,
      // 是否已填加“绿化区域”遮罩物
      isGreening: false,
      // 是否已填加“小区基础信息”遮罩物
      isCommunityInfo: false,
      // 是否已填加“管网数据”遮罩物
      isPipeNetwork: false,
      // 是否已填加“现状使用主体”遮罩物
      isCurrentUser: false,

      // 覆盖物对象-道路红线
      roadLineArr: [],
      // 覆盖物对象-现状使用主体
      currentUserArr: [],
      // 覆盖物对象-房地调查
      premisesSurveyArr: [],
      // 覆盖物对象-绿化
      greeningArr: [],
      // 覆盖物对象-出入口、垃圾房、凉亭、配电室
      communityInfoArr: [],
      // 覆盖物对象-官网数据
      pipeNetworkArr: [],
      // 文本标记-点标记-点聚合
      gather_num_arr: [],

      // 点聚合对象-隆安路
      gether_big_num_arr: null,

      // 信息弹窗对象
      infoWindow: null,

      // ------垃圾房组件------
      // 是否显示垃圾房组件
      isGarbageShow: false,
      // 垃圾房数据
      garbageRoomInfo: [
        {
          title: "垃圾房",
          community: "临潮一村",
          position: "临潮一村40号",
          refuseClassify: "四分类",
          openTime: "6:30--8:30  ，17:00--19:30",
        },
        {
          title: "垃圾房",
          // 所属小区
          community: "临潮一村",
          // 地址
          position: "临潮一村45号",
          // 分类投放了垃圾种类
          refuseClassify: "两分类",
          // 投放点开放时间
          openTime: "24小时",
        },
        {
          title: "垃圾房",
          community: "临潮一村",
          position: "临潮一村62号",
          refuseClassify: "两分类",
          openTime: "26:30--8:30  ，17:00--19:30",
        },
        {
          title: "垃圾房",
          community: "临潮一村",
          position: "临潮一村69号",
          refuseClassify: "两分类",
          openTime: "24小时",
        },
      ],

      // 传入垃圾房组件中的数据
      garbageData: {
        title: "垃圾房",
        community: "临潮一村",
        position: "临潮一村62号",
        refuseClassify: "两分类",
        openTime: "26:30--8:30  ，17:00--19:30",
      },

      // 是否显示建筑垃圾和大件垃圾组件
      isGarbageOtherShow: false,
      // 建筑垃圾和大件垃圾
      garbageOtherInfo: [
        {
          title: "堆放点 - 建筑垃圾",
        },
        {
          title: "堆放点 - 大件垃圾",
        },
      ],
      // 传入建筑垃圾和大件垃圾组件中的数据
      garbageOtherData: {
        title: "堆放点 - 建筑垃圾",
      },

      // ------出入口组件------
      // 是否显示出入口组件
      isEntryExitShow: false,
      // 出入口数据
      entryExitInfo: [
        {
          entryExitTitle: "1号门",
          // 位置
          position: "1号",
          // 门卫
          guard: "有",
          // 非机动车通行
          nonMotor: "是",
          // 行人
          pedestrian: "是",
          // 开放时间
          openTime: "24小时",
        },
        {
          entryExitTitle: "2号门",
          position: "2号",
          guard: "无",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
        {
          entryExitTitle: "3号门",
          position: "3号",
          guard: "有",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
        {
          entryExitTitle: "转门",
          position: "临一10号楼旁",
          guard: "有",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
        {
          entryExitTitle: "转门",
          position: "临二4号楼旁",
          guard: "有",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
        {
          entryExitTitle: "转门",
          position: "临一72号楼旁",
          guard: "有",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
        {
          entryExitTitle: "转门",
          position: "临一80号楼旁",
          guard: "有",
          nonMotor: "是",
          pedestrian: "是",
          openTime: "24小时",
        },
      ],
      // 传入出入口组件中的数据
      entryExitData: {
        entryExitTitle: "2号门",
        position: "2号",
        guard: "无",
        nonMotor: "是",
        pedestrian: "是",
        openTime: "24小时",
      },

      // ------居委会------
      // 是否显示居委会弹窗
      isBasicInfoShow: false,

      // ------用地信息组件------
      // 是否显示用地信息组件
      isLandInfoShow: false,
      // 用地信息数据
      landInfo: [
        {
          index: 0,
          // 用地名称
          title: "石化第一小学",
          // 地块面积
          plotArea: "7330.43",
          // 所在小区域
          area: "临潮一村",
          // 土地用途（试行）
          landUse: "教育用地",
          // 宗地号
          landNumber: "18*****010*****0",
        },
        {
          index: 1,
          title: "临潮幼儿园",
          plotArea: "6966",
          area: "临潮一村",
          landUse: "教育用地",
          landNumber: "18*****010****20",
        },
        {
          index: 2,
          title: "石化街道综合行政执法队",
          plotArea: "809.82",
          area: "临潮二村",
          landUse: "机关团体用地",
          landNumber: "18*****010****04",
        },
        {
          index: 3,
          title: "临潮一村-28号楼",
          plotArea: "711",
          area: "临潮一村",
          landUse: "住宅用地",
          landNumber: "18*****010****11",
        },
      ],
      // 传入用地信息组件中的数据
      landInfoData: {
        title: "临潮幼儿园",
        plotArea: "6966",
        area: "临潮一村",
        landUse: "教育用地",
        landNumber: "18*****010****20",
      },

      // ------绿化信息组件------
      // 是否显示绿化信息组件
      isGreeningShow: false,
      // 绿化信息数据
      greeningInfo: [
        {
          // 绿化编号
          greeningNumber: "街心花园0001",
          // 类型
          type: "公共绿地",
          // 权属单位
          ownershipUnit: "区园林所",
          // 养护单位
          maintainUnit: "绿建公司",
          // 养护责任人
          maintainPerson: "张三",
          // 联系方式
          phoneNumber: "17106683344",
        },
        {
          greeningNumber: "金山一小0001",
          type: "单位绿化",
          ownershipUnit: "区园林所",
          maintainUnit: "绿建公司",
          maintainPerson: "李四",
          phoneNumber: "15751848043",
        },
        {
          greeningNumber: "临潮一村0001",
          type: "售后公房绿化",
          ownershipUnit: "区园林所",
          maintainUnit: "绿建公司",
          maintainPerson: "王五",
          phoneNumber: "15952643888",
        },
        {
          greeningNumber: "机非隔离绿化0001",
          type: "公共绿地",
          ownershipUnit: "区园林所",
          maintainUnit: "绿建公司",
          maintainPerson: "孙晓明",
          phoneNumber: "14543938470",
        },
      ],
      // 传入绿化信息组件中的数据
      greeningData: {
        greeningNumber: "金山一小0001",
        type: "单位绿化",
        ownershipUnit: "区园林所",
        maintainUnit: "绿建公司",
        maintainPerson: "李四",
        phoneNumber: "15751848043",
      },

      // 卫星图层
      satelliteLayer: null,
      isSatelliteLayer: false,
      // 楼块显示
      buildingsLayer: null,

      // ------现状使用主体组件------
      // 是否显示组件
      isCurrentUserShow: false,
      // 现状使用主体（沿街商铺数据）
      currentUserInfo: {
        // 隆安路
        num01: [
          {
            committee: "临蒙居委会",
            shopName: "东方有线",
            license: "金山东方有线网络有限公司",
            address: "隆安路105号",
            category: "公用事业",
            generalCategory: "通讯营业厅",
            subclass: "",
            contacts: "金晓燕",
            tel: "15800982038",
            ownership: "城建公司",
            employNum: 9,
            placeCode: "是",
            longitude: 121.350778,
            latitude: 30.718799,
          },
          {
            committee: "临蒙居委会",
            shopName: "上海燃气",
            license: "金山天燃气公司石化办事处",
            address: "隆安路119号",
            category: "公用事业",
            generalCategory: "水电气营业厅",
            subclass: "",
            contacts: "袁欣",
            tel: "33692237",
            ownership: "城建公司",
            employNum: 5,
            placeCode: "是",
            longitude: 121.350575,
            latitude: 30.718754,
          },
          {
            committee: "临蒙居委会",
            shopName: "峰影搬家",
            license: "上海峰影搬场有限公司",
            address: "隆安路125号",
            category: "服务业",
            generalCategory: "上门服务类",
            subclass: "搬家公司",
            contacts: "马影",
            tel: "15021573312",
            ownership: "城建公司",
            employNum: 3,
            placeCode: "是",
            longitude: 121.350352,
            latitude: 30.718711,
          },
          {
            committee: "临蒙居委会",
            shopName: "一扫光",
            license: "上海鸿逸实业有限公司",
            address: "隆安路135号",
            category: "",
            generalCategory: "",
            subclass: "",
            contacts: "包益红",
            tel: "19102183732",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "是",
            longitude: 121.350097,
            latitude: 30.718654,
          },
        ],
        num02: [
          {
            committee: "临蒙居委会",
            shopName: "石电能源",
            license: "上海石化公用事业部供电营业室",
            address: "隆安路159号",
            category: "公用事业",
            generalCategory: "水电气营业厅",
            subclass: "",
            contacts: "朱永强",
            tel: "57952001",
            ownership: "城建公司",
            employNum: 5,
            placeCode: "异常",
            longitude: 121.3476,
            latitude: 30.718199,
            taxNum: "121000002236375133D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "朱永强",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },{
                name: "谢叶飞",
                gender: "男",
                age: 22,
                telNum: "15618395547",
                liveAddress: "临潮一村24号楼302",
                testTime: "2022/10/24 8:04:44",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },{
                name: "慕灵韵",
                gender: "女",
                age: 33,
                telNum: "13888549324",
                liveAddress: "临潮一村17号楼103",
                testTime: "2022/10/24 9:21:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "聂梦竹",
                gender: "男",
                age: 18,
                telNum: "13733682107",
                liveAddress: "临潮一村12号楼303",
                testTime: "2022/10/24 8:11:11",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "储芳林",
                gender: "女",
                age: 42,
                telNum: "15800658214",
                liveAddress: "临潮一村23号楼201",
                testTime: "2022/10/24 9:22:21",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "宇浩门窗店",
            license: "金山区宇浩塑钢门窗店",
            address: "隆安路161号",
            category: "零售业",
            generalCategory: "五金店",
            subclass: "",
            contacts: "余超",
            tel: "15921250106",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.347733,
            latitude: 30.718233,
            taxNum: "12100001125246133D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "申贝丽",
                gender: "女",
                age: 32,
                telNum: "13733682107",
                liveAddress: "临潮一村1号楼102",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "苏秀媛",
                gender: "女",
                age: 43,
                telNum: "15703826237",
                liveAddress: "临潮一村1号楼302",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "陈奎装潢经营部",
            license: "上海市金山区陈奎装潢经营部",
            address: "隆安路163号",
            category: "零售业",
            generalCategory: "装潢店",
            subclass: "",
            contacts: "陈雯雯",
            tel: "18049715352",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.347829,
            latitude: 30.718247,
            taxNum: "12100000425374553D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "丁涵畅",
                gender: "男",
                age: 32,
                telNum: "15800658214",
                liveAddress: "临潮一村25号楼402",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "居莹然",
                gender: "女",
                age: 22,
                telNum: "13888549324",
                liveAddress: "临潮一村7号楼602",
                testTime: "2022/10/24 8:14:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "古玩店--未开张",
            license: "",
            address: "隆安路165号",
            category: "零售业",
            generalCategory: "工艺品店",
            subclass: "",
            contacts: "",
            tel: "",
            ownership: "城建公司",
            employNum: "",
            placeCode: "阴性",
            longitude: 121.347986,
            latitude: 30.718282,
            taxNum: "12100000224336133D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "浦平绿",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村11号楼101",
                testTime: "2022/10/24 7:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "酷漫通讯器材经营部",
            license: "上海市金山区酷漫通讯器材经营部",
            address: "隆安路167号",
            category: "零售业",
            generalCategory: "家电销售店",
            subclass: "",
            contacts: "舒育明",
            tel: "13661983228",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.348163,
            latitude: 30.718306,
            taxNum: "12100000477453533D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "权安静",
                gender: "男",
                age: 42,
                telNum: "13733682107",
                liveAddress: "临潮一村13号楼302",
                testTime: "2022/10/24 8:11:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
              {
                name: "田思迪",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村13号楼302",
                testTime: "2022/10/24 8:11:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "绘新房地产经纪事务所",
            license: "上海绘新房地产经济事务所",
            address: "隆安路171号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "袁巧珍",
            tel: "13585895621",
            ownership: "",
            employNum: 7,
            placeCode: "异常",
            longitude: 121.348336,
            latitude: 30.718351,
            taxNum: "12100000789565133D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "丁涵畅",
                gender: "男",
                age: 19,
                telNum: "13815972179",
                liveAddress: "临潮一村12号楼402",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },
              {
                name: "瞿访蕊",
                gender: "女",
                age: 41,
                telNum: "15703826237",
                liveAddress: "临潮一村14号楼702",
                testTime: "2022/10/24 8:25:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "邵怡悦",
                gender: "男",
                age: 35,
                telNum: "15618395547",
                liveAddress: "临潮一村17号楼402",
                testTime: "2022/10/24 8:26:54",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },{
                name: "关妙婧",
                gender: "男",
                age: 42,
                telNum: "13888549324",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:27:09",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "通千兰",
                gender: "男",
                age: 23,
                telNum: "13733682107",
                liveAddress: "临潮一村14号楼302",
                testTime: "2022/10/24 8:29:23",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },{
                name: "富丝雨",
                gender: "男",
                age: 34,
                telNum: "15800658214",
                liveAddress: "临潮一村2号楼702",
                testTime: "2022/10/24 8:31:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "公清霁",
                gender: "男",
                age: 33,
                telNum: "13815972179",
                liveAddress: "临潮一村4号楼802",
                testTime: "2022/10/24 8:33:24",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "益丰大药房隆安路店",
            license: "益丰大药房有限责任公司",
            address: "隆安路175号",
            category: "医疗卫生",
            generalCategory: "药房",
            subclass: "",
            contacts: "马雪丽",
            tel: "67297583",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.348505,
            latitude: 30.718385,
            taxNum: "12100000421325343D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "韶迷伤",
                gender: "男",
                age: 34,
                telNum: "15703826237",
                liveAddress: "临潮一村17号楼202",
                testTime: "2022/10/24 8:14:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "朱永强",
                gender: "男",
                age: 25,
                telNum: "13733682107",
                liveAddress: "临潮一村27号楼402",
                testTime: "2022/10/24 8:54:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "御堂足浴",
            license: "上海玙千实业有限公司",
            address: "隆安路181号",
            category: "服务业",
            generalCategory: "养生服务类",
            subclass: "足浴店",
            contacts: "刘琳敏",
            tel: "18221605119",
            ownership: "城建公司",
            employNum: 3,
            placeCode: "阴性",
            longitude: 121.34867,
            latitude: 30.71842,
            taxNum: "12100000423467343D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "利尔柳",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 9:24:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
              {
                name: "饶智菱",
                gender: "男",
                age: 22,
                telNum: "15618395547",
                liveAddress: "临潮一村12号楼402",
                testTime: "2022/10/24 8:14:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "陈恨之",
                gender: "男",
                age: 34,
                telNum: "13733682107",
                liveAddress: "临潮一村23号楼602",
                testTime: "2022/10/24 10:13:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "城建公司租赁处",
            license: "上海石化城市建设综合开发有限公司",
            address: "隆安路185号",
            category: "其他商铺",
            generalCategory: "办公场所",
            subclass: "",
            contacts: "周旋",
            tel: "15301882393",
            ownership: "城建公司",
            employNum: 4,
            placeCode: "异常",
            longitude: 121.348799,
            latitude: 30.718451,
            taxNum: "12100000423456755D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "郑恨寒",
                gender: "男",
                age: 18,
                telNum: "15618395547",
                liveAddress: "临潮一村13号楼802",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },{
                name: "熊翠绿",
                gender: "女",
                age: 22,
                telNum: "13815972179",
                liveAddress: "临潮一村12号楼303",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "盖青香",
                gender: "男",
                age: 26,
                telNum: "13733682107",
                liveAddress: "临潮一村23号楼301",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "鄂童童",
                gender: "女",
                age: 32,
                telNum: "15618395547",
                liveAddress: "临潮一村12号楼402",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "梓一地产",
            license: "上海梓一地产有限公司",
            address: "隆安路195号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "腾饮子",
            tel: "18817205077",
            ownership: "",
            employNum: 3,
            placeCode: "阴性",
            longitude: 121.348968,
            latitude: 30.718486,
            taxNum: "12100000425023233D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "周梦竹",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村12号楼202",
                testTime: "2022/10/24 8:14:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "余晓桐",
                gender: "男",
                age: 25,
                telNum: "15618395547",
                liveAddress: "临潮一村23号楼301",
                testTime: "2022/10/24 8:24:38",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "曾凝远",
                gender: "男",
                age: 19,
                telNum: "15800658214",
                liveAddress: "临潮一村13号楼402",
                testTime: "2022/10/24 8:33:33",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "广盛装潢",
            license: "",
            address: "隆安路197号",
            category: "零售业",
            generalCategory: "装潢店",
            subclass: "",
            contacts: "杨广军",
            tel: "13162943965",
            ownership: "",
            employNum: "1",
            placeCode: "阴性",
            longitude: 121.349121,
            latitude: 30.718517,
            taxNum: "1210000042235233D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "朱永强",
                gender: "男",
                age: 32,
                telNum: "15703826237",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "异常",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "富翔足浴有限公司",
            license: "富翔足浴有限公司",
            address: "隆安路199号",
            category: "服务业",
            generalCategory: "养生服务类",
            subclass: "足浴店",
            contacts: "杨景萍",
            tel: "17721070066",
            ownership: "",
            employNum: 4,
            placeCode: "阴性",
            longitude: 121.34933,
            latitude: 30.718555,
            taxNum: "12100000425023453D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "韶迷伤",
                gender: "男",
                age: 22,
                telNum: "15618395547",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:24:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "富丝雨",
                gender: "男",
                age: 34,
                telNum: "13888549324",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:25:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "暨舒荣",
                gender: "男",
                age: 23,
                telNum: "13733682107",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:26:25",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "宁迎海",
                gender: "男",
                age: 19,
                telNum: "15800658214",
                liveAddress: "临潮一村7号楼202",
                testTime: "2022/10/24 8:30:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
          {
            committee: "临蒙居委会",
            shopName: "捷强超市",
            license: "上海捷强百货连锁集团",
            address: "隆安路201号",
            category: "零售业",
            generalCategory: "食品流通店",
            subclass: "杂货店",
            contacts: "曹伟英",
            tel: "67353209",
            ownership: "",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.349563,
            latitude: 30.718583,
            taxNum: "12100000425263533D",
            licenseImg: "https://s1.ax1x.com/2022/10/23/xgDxhj.png",
            employ: [
              {
                name: "陆棠华",
                gender: "男",
                age: 32,
                telNum: "13888549324",
                liveAddress: "临潮一村17号楼202",
                testTime: "2022/10/24 9:23:18",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },{
                name: "弓真文",
                gender: "男",
                age: 19,
                telNum: "15703826237",
                liveAddress: "临潮一村23号楼102",
                testTime: "2022/10/24 8:14:08",
                suiShenCodeStatus: "阴性",
                tripCodeStatus: "上海市",
              },
            ],
          },
        ],
        num03: [
          {
            committee: "临蒙居委会",
            shopName: "百音艺校",
            license: "上海市金山区百音文化艺术进修学校",
            address: "隆安路233号",
            category: "文体类",
            generalCategory: "培训机构",
            subclass: "",
            contacts: "蒋颖",
            tel: "57281020",
            ownership: "市场公司",
            employNum: "",
            placeCode: "阴性",
            longitude: 121.34733,
            latitude: 30.718109,
          },
          {
            committee: "临蒙居委会",
            shopName: "盟林房产",
            license: "",
            address: "隆安路235号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "",
            tel: "15618171768",
            ownership: "市场公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.347432,
            latitude: 30.71781,
          },
          {
            committee: "临蒙居委会",
            shopName: "上海来看房产经纪事务所",
            license: "",
            address: "隆安路241号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "张卫红",
            tel: "15921862425",
            ownership: "市场公司",
            employNum: "",
            placeCode: "阴性",
            longitude: 121.347171,
            latitude: 30.717757,
          },
          {
            committee: "临蒙居委会",
            shopName: "上海翔峰房地产经纪事务所",
            license: "",
            address: "隆安路245号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "宋向英",
            tel: "18964730313",
            ownership: "市场公司",
            employNum: "",
            placeCode: "阴性",
            longitude: 121.346932,
            latitude: 30.717854,
          },
          {
            committee: "临蒙居委会",
            shopName: "闵望房产",
            license: "",
            address: "隆安路247号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "封海明",
            tel: "15800474603",
            ownership: "市场公司",
            employNum: "",
            placeCode: "阴性",
            longitude: 121.34667,
            latitude: 30.717786,
          },
          {
            committee: "临蒙居委会",
            shopName: "唐家品牌折扣店",
            license: "唐丽服装店",
            address: "隆安路249号",
            category: "零售业",
            generalCategory: "鞋服店",
            subclass: "",
            contacts: "唐丽",
            tel: "13761006855",
            ownership: "市场公司",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.346465,
            latitude: 30.717727,
          },
          {
            committee: "临蒙居委会",
            shopName: "天一综合商店",
            license: "天一综合商店",
            address: "隆安路255号",
            category: "零售业",
            generalCategory: "食品流通店",
            subclass: "杂货店",
            contacts: "赵宏宇",
            tel: "57952028",
            ownership: "城建公司",
            employNum: 5,
            placeCode: "阴性",
            longitude: 121.346181,
            latitude: 30.717708,
          },
          {
            committee: "临蒙居委会",
            shopName: "竹之优生态竹纺店",
            license: "陈昌立水果店",
            address: "隆安路257号",
            category: "零售业",
            generalCategory: "家具店",
            subclass: "",
            contacts: "熊国华",
            tel: "18721237806",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.346545,
            latitude: 30.718084,
          },
          {
            committee: "临蒙居委会",
            shopName: "东升果行",
            license: "小不点食品店",
            address: "隆安路259号",
            category: "零售业",
            generalCategory: "食品流通店",
            subclass: "水果店",
            contacts: "周建峰",
            tel: "57947814",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.346465,
            latitude: 30.718324,
          },
          {
            committee: "临蒙居委会",
            shopName: "喜桃房产",
            license: "上海金山喜桃房产",
            address: "隆安路261号",
            category: "零售业",
            generalCategory: "房产中介",
            subclass: "",
            contacts: "栾春冬",
            tel: "15001773458",
            ownership: "城建公司",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.346846,
            latitude: 30.718407,
          },
          {
            committee: "临蒙居委会",
            shopName: "建设银行",
            license: "建设银行隆安支行",
            address: "隆安路271号",
            category: "金融类",
            generalCategory: "银行网点",
            subclass: "",
            contacts: "吴玉苓",
            tel: "57938978",
            ownership: "",
            employNum: 18,
            placeCode: "阴性",
            longitude: 121.347188,
            latitude: 30.718466,
          },
          {
            committee: "临蒙居委会",
            shopName: "临潮小商品市场",
            license: "金源综合市场经营管理有限公司石化隆安市场分公司",
            address: "临潮一村1号门",
            category: "其他商铺",
            generalCategory: "小商品市场",
            subclass: "",
            contacts: "吴玲辉",
            tel: "18901853331",
            ownership: "市场公司",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.347239,
            latitude: 30.718177,
          },
          {
            committee: "临蒙居委会",
            shopName: "诗怡理发店",
            license: "金山石化诗怡理发店",
            address: "临潮一村1号门",
            category: "服务业",
            generalCategory: "美容服务类",
            subclass: "理发店",
            contacts: "黄碧",
            tel: "18918966352",
            ownership: "聚慧",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.347051,
            latitude: 30.718026,
          },
        ],
        // 临潮街
        num04: [
          {
            committee: "临蒙居委会",
            shopName: "永华理发店",
            license: "金山区石化永华理发店",
            address: "临潮街200号",
            category: "服务业",
            generalCategory: "美容服务类",
            subclass: "理发店",
            contacts: "李桂华",
            tel: "18964131866",
            ownership: "商业集团",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350432,
            latitude: 30.716628,
          },
          {
            committee: "临蒙居委会",
            shopName: "庆习百货-水站",
            license: "上海市金山区庆习百货",
            address: "临潮街202号",
            category: "零售业",
            generalCategory: "其他",
            subclass: "",
            contacts: "吕庆习",
            tel: "13817049238",
            ownership: "商业集团",
            employNum: 2,
            placeCode: "阴性",
            longitude: 121.350295,
            latitude: 30.716612,
          },
          {
            committee: "临蒙居委会",
            shopName: "晓敏发屋",
            license: "金山区石化街道晓敏发屋",
            address: "临潮街216号",
            category: "服务业",
            generalCategory: "美容服务类",
            subclass: "理发店",
            contacts: "周艳敏",
            tel: "13045630008",
            ownership: "商业集团",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350125,
            latitude: 30.716584,
          },
          {
            committee: "临蒙居委会",
            shopName: "妈妈驿站",
            license: "上海舒圆贸易有限公司",
            address: "临潮街218号",
            category: "服务类",
            generalCategory: "快递服务类",
            subclass: "快递驿站",
            contacts: "李方奎",
            tel: "18217321316",
            ownership: "商业集团",
            employNum: 3,
            placeCode: "阴性",
            longitude: 121.349886,
            latitude: 30.71649,
          },
          {
            committee: "临蒙居委会",
            shopName: "临潮烟酒晨光文具店",
            license: "金山区石化飞跃佳乐杂货商店",
            address: "临潮街529号",
            category: "",
            generalCategory: "",
            subclass: "",
            contacts: "汪耀钢",
            tel: "13917176805",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.349918,
            latitude: 30.716289,
          },
          {
            committee: "临蒙居委会",
            shopName: "临潮打印店",
            license: "上海市金山区欣晨日用百货经营部",
            address: "临潮街535号",
            category: "零售业",
            generalCategory: "其他",
            subclass: "",
            contacts: "刘鸣",
            tel: "13641818393",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350038,
            latitude: 30.716004,
          },
          {
            committee: "临蒙居委会",
            shopName: "朱阿姨裁缝铺",
            license: "上海市金山区朱惠缝纫经营部",
            address: "临潮街537号",
            category: "服务业",
            generalCategory: "维修服务类",
            subclass: "缝纫店",
            contacts: "朱惠珍",
            tel: "18802150278",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350084,
            latitude: 30.715678,
          },
          {
            committee: "临蒙居委会",
            shopName: "盼盼车行",
            license: "上海市金山区张盼自行车修理店",
            address: "临潮街537号",
            category: "服务业",
            generalCategory: "维修服务类",
            subclass: "非机动车维修店",
            contacts: "张善超",
            tel: "13524653978",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350417,
            latitude: 30.715594,
          },
          {
            committee: "临蒙居委会",
            shopName: "逸清商行",
            license: "金山区石化街道逸清商行",
            address: "临潮街541号",
            category: "",
            generalCategory: "",
            subclass: "",
            contacts: "张善超",
            tel: "13524653978",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350384,
            latitude: 30.715328,
          },
        ],
        // 临潮二村
        num05: [
          {
            committee: "临蒙居委会",
            shopName: "德山装潢部",
            license: "上海市金山区德山装潢部",
            address: "临潮二村151号",
            category: "零售业",
            generalCategory: "装潢店",
            subclass: "",
            contacts: "杨德山",
            tel: "13818035008",
            ownership: "",
            employNum: 4,
            placeCode: "阴性",
            longitude: 121.351279,
            latitude: 30.716734,
          },
          {
            committee: "临蒙居委会",
            shopName: "韵达超市-快递服务",
            license: "上海鸿徽物流有限公司",
            address: "临潮二村165号",
            category: "服务类",
            generalCategory: "快递服务类",
            subclass: "快递驿站",
            contacts: "李标",
            tel: "13817049238",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.351091,
            latitude: 30.716729,
          },
          {
            committee: "临蒙居委会",
            shopName: "元锦理发店",
            license: "金山石化元锦理发店",
            address: "临潮二村175号",
            category: "服务业",
            generalCategory: "美容服务类",
            subclass: "理发店",
            contacts: "唐书燕",
            tel: "13661777402",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350846,
            latitude: 30.716724,
          },
          {
            committee: "临蒙居委会",
            shopName: "淑洁美容美发店",
            license: "金山石化淑洁美容美发店",
            address: "临潮二村178号",
            category: "服务业",
            generalCategory: "美容服务类",
            subclass: "美容美发店",
            contacts: "刘书杰",
            tel: "13916789138",
            ownership: "",
            employNum: 1,
            placeCode: "阴性",
            longitude: 121.350567,
            latitude: 30.71669,
          },
        ],
      },
      // 传入现状使用主体组件中的数据
      currentUserData: [
        // 公共事业
        {
          committee: "临蒙居委会",
          shopName: "东方有线",
          license: "金山东方有线网络有限公司",
          address: "隆安路105号",
          category: "公用事业",
          generalCategory: "通讯营业厅",
          subclass: "",
          contacts: "金晓燕",
          tel: "15800982038",
          ownership: "城建公司",
          employNum: 9,
          placeCode: "是",
        },
      ],
      streetShopData: {
        committee: "临蒙居委会",
        shopName: "临潮烟酒晨光文具店",
        license: "金山东方有线网络有限公司",
        address: "隆安路105号",
        category: "公用事业",
        generalCategory: "通讯营业厅",
        subclass: "",
        contacts: "金晓燕",
        tel: "15800982038",
        ownership: "城建公司",
        employNum: 9,
        placeCode: "阴性",
        taxNum: '12100000425006133D',
        licenseImg: 'https://s1.ax1x.com/2022/10/23/xgDxhj.png',
        employ: [
          {
            name: '朱永强',
            gender: '男',
            age: 32,
            telNum: "15703826237",
            liveAddress: '临潮一村7号楼202',
            testTime: '2022/10/24 8:24:08',
            suiShenCodeStatus: '异常',
            tripCodeStatus: '上海市'
          },
        ]
      },
      // 是否改变了地图样式
      isHideBuilding: true,

      // ------紧急通道------
      isEmergencyLoadShow: false,

      // ------楼号数据------
      buildingNum: [
        {
          title: "6",
          lnglat: [121.347723, 30.718123],
        },
        {
          title: "7",
          lnglat: [121.347852, 30.718148],
        },
        {
          title: "8",
          lnglat: [121.347988, 30.718171],
        },
        {
          title: "9",
          lnglat: [121.348389, 30.718216],
        },
        {
          title: "10",
          lnglat: [121.348514, 30.718235],
        },
        {
          title: "11",
          lnglat: [121.346375, 30.717583],
        },
        {
          title: "12",
          lnglat: [121.346637, 30.717616],
        },
        {
          title: "13",
          lnglat: [121.346796, 30.717648],
        },
        {
          title: "14",
          lnglat: [121.347183, 30.717586],
        },
        {
          title: "15",
          lnglat: [121.347312, 30.717612],
        },
        {
          title: "16",
          lnglat: [121.347467, 30.717638],
        },
        {
          title: "17",
          lnglat: [121.347775, 30.717876],
        },
        {
          title: "18",
          lnglat: [121.347941, 30.717899],
        },
        {
          title: "19",
          lnglat: [121.34812, 30.717974],
        },
        {
          title: "20",
          lnglat: [121.348408, 30.717967],
        },
        {
          title: "21",
          lnglat: [121.348571, 30.717997],
        },
        {
          title: "22",
          lnglat: [121.346439, 30.717315],
        },
        {
          title: "23",
          lnglat: [121.346682, 30.717351],
        },
        {
          title: "24",
          lnglat: [121.346815, 30.717371],
        },
        {
          title: "25",
          lnglat: [121.346963, 30.717403],
        },
        {
          title: "26",
          lnglat: [121.347862, 30.717609],
        },
        {
          title: "27",
          lnglat: [121.348017, 30.717631],
        },
        {
          title: "28",
          lnglat: [121.348438, 30.717703],
        },
        {
          title: "29",
          lnglat: [121.348609, 30.717736],
        },
        {
          title: "30",
          lnglat: [121.346481, 30.717044],
        },
        {
          title: "31",
          lnglat: [121.346743, 30.717084],
        },
        {
          title: "32",
          lnglat: [121.346872, 30.717106],
        },
        {
          title: "33",
          lnglat: [121.34702, 30.717123],
        },
        {
          title: "34",
          lnglat: [121.34755, 30.717333],
        },
        {
          title: "35",
          lnglat: [121.347384, 30.717338],
        },
        {
          title: "36",
          lnglat: [121.347315, 30.717218],
        },
        {
          title: "37",
          lnglat: [121.34789, 30.717315],
        },
        {
          title: "38",
          lnglat: [121.348055, 30.717337],
        },
        {
          title: "39",
          lnglat: [121.348221, 30.717408],
        },
        {
          title: "40",
          lnglat: [121.34852, 30.717458],
        },
        {
          title: "41",
          lnglat: [121.34868, 30.717486],
        },
        {
          title: "42",
          lnglat: [121.346479, 30.716722],
        },
        {
          title: "43",
          lnglat: [121.346636, 30.716796],
        },
        {
          title: "44",
          lnglat: [121.346794, 30.71682],
        },
        {
          title: "45",
          lnglat: [121.347051, 30.716792],
        },
        {
          title: "48",
          lnglat: [121.346538, 30.716445],
        },
        {
          title: "49",
          lnglat: [121.346705, 30.71652],
        },
        {
          title: "50",
          lnglat: [121.346855, 30.716542],
        },
        {
          title: "51",
          lnglat: [121.347125, 30.716514],
        },
        {
          title: "52",
          lnglat: [121.346956, 30.716307],
        },
        {
          title: "53",
          lnglat: [121.347185, 30.716237],
        },
        {
          title: "57",
          lnglat: [121.347809, 30.715939],
        },
        {
          title: "58",
          lnglat: [121.34793, 30.715939],
        },
        {
          title: "59",
          lnglat: [121.348044, 30.716073],
        },
        {
          title: "60",
          lnglat: [121.34812, 30.716171],
        },
        {
          title: "61",
          lnglat: [121.348268, 30.716226],
        },
        {
          title: "62",
          lnglat: [121.348913, 30.71621],
        },
        {
          title: "63",
          lnglat: [121.348848, 30.716132],
        },
        {
          title: "64",
          lnglat: [121.348761, 30.716011],
        },
        {
          title: "65",
          lnglat: [121.348624, 30.715936],
        },
        {
          title: "67",
          lnglat: [121.349337, 30.716038],
        },
        {
          title: "68",
          lnglat: [121.349474, 30.71607],
        },
        {
          title: "69",
          lnglat: [121.349554, 30.716194],
        },
        {
          title: "70",
          lnglat: [121.349627, 30.716308],
        },
        {
          title: "71",
          lnglat: [121.349776, 30.716346],
        },
        {
          title: "72",
          lnglat: [121.348613, 30.715686],
        },
        {
          title: "73",
          lnglat: [121.348915, 30.715634],
        },
        {
          title: "74",
          lnglat: [121.349184, 30.715616],
        },
        {
          title: "75",
          lnglat: [121.349434, 30.715575],
        },
        {
          title: "76",
          lnglat: [121.349566, 30.715582],
        },
        {
          title: "77",
          lnglat: [121.349747, 30.715779],
        },
        {
          title: "78",
          lnglat: [121.349896, 30.715793],
        },
        {
          title: "79",
          lnglat: [121.349824, 30.715516],
        },
        {
          title: "80",
          lnglat: [121.349949, 30.715533],
        },
      ],
      // 所有的标注实例
      mapTextObjArr: [],

      // ------监控点位数据------
      controlData: [
        {
          controlCode: "31011623001181025009",
          controlId: 1,
          controlLnglat: [121.34898, 30.718072],
          controlName: "沪杭公路戚家墩路（北向南）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/3988905f9f82a942262afcbaa9417281.jpeg",
        },
        {
          controlCode: "31011623001181004014",
          controlId: 2,
          controlLnglat: [121.349543, 30.718043],
          controlName: "沪杭/戚家墩(东-西)HG",
          controlType: "治安卡口断面",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/85619ef5e8ddf9b43caac4048b451134.jpeg",
        },
        {
          controlCode: "31011623001181025012",
          controlId: 3,
          controlLnglat: [121.349382, 30.717948],
          controlName: "沪杭公路戚家墩路（东向西）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/0c68a15200e63eae9db5a482cb719d21.jpeg",
        },
        {
          controlCode: "31011623001180006012",
          controlId: 4,
          controlLnglat: [121.349297, 30.717821],
          controlName: "沪杭/戚家墩H",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/7eb678e31a5ad5367b5e52b71fff7b8e.jpeg",
        },
        {
          controlCode: "31011623001180008006",
          controlId: 5,
          controlLnglat: [121.34921, 30.71766],
          controlName: "海岸线停车场H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/21772ce3b7ec26471b81f5fa3dc612c5.jpeg",
        },
        {
          controlCode: "31011623001180011007",
          controlId: 6,
          controlLnglat: [121.349166, 30.717609],
          controlName: "城市沙滩停车场H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/dc0c17588fdd09643ff644243c881dba.jpeg",
        },
        {
          controlCode: "31011623001180018010",
          controlId: 7,
          controlLnglat: [121.348781, 30.717635],
          controlName: "戚家墩停车场1H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/de576cec5b8400a9fc888a9e8994e04c.jpeg",
        },
        {
          controlCode: "31011623001181025010",
          controlId: 8,
          controlLnglat: [121.34867, 30.717651],
          controlName: "沪杭公路戚家墩路（西向东）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/298309e2a7328545702e9032395ec349.jpeg",
        },
        {
          controlCode: "31011623001180018011",
          controlId: 9,
          controlLnglat: [121.348791, 30.717523],
          controlName: "戚家墩停车场2H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/1e34d92dd295ddac98d48b23e98bd414.jpeg",
        },
        {
          controlCode: "31011623001181003018",
          controlId: 10,
          controlLnglat: [121.349015, 30.717476],
          controlName: "海岸线戚家墩出入口HG",
          controlType: "旅游景区",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/ac801421820e85521207f9d73b6cf7db.jpeg",
        },
        {
          controlCode: "31011623001180011008",
          controlId: 11,
          controlLnglat: [121.350448, 30.717422],
          controlName: "沙滩停车场东南H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/b062b737b65b952f728f51cdba0e5d15.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 12,
          controlLnglat: [121.349656, 30.718661],
          controlName: "沪杭公路戚家墩路（北向南）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/a8c082ec1cce5d5a71f074b97bc10186.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 13,
          controlLnglat: [121.347596, 30.718481],
          controlName: "沪杭/戚家墩(东-西)HG",
          controlType: "治安卡口断面",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/2b0812f893ca1fb8a989d46cf0d21beb.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 14,
          controlLnglat: [121.347375, 30.718436],
          controlName: "沪杭公路戚家墩路（东向西）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/3988905f9f82a942262afcbaa9417281.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 15,
          controlLnglat: [121.347556, 30.718252],
          controlName: "沪杭/戚家墩H",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/85619ef5e8ddf9b43caac4048b451134.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 16,
          controlLnglat: [121.346172, 30.717962],
          controlName: "海岸线停车场H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/0c68a15200e63eae9db5a482cb719d21.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 17,
          controlLnglat: [121.34618, 30.716053],
          controlName: "城市沙滩停车场H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/7eb678e31a5ad5367b5e52b71fff7b8e.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 18,
          controlLnglat: [121.346152, 30.715655],
          controlName: "戚家墩停车场1H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/21772ce3b7ec26471b81f5fa3dc612c5.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 19,
          controlLnglat: [121.346398, 30.715451],
          controlName: "沪杭公路戚家墩路（西向东）HG",
          controlType: "交通干线",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/dc0c17588fdd09643ff644243c881dba.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 20,
          controlLnglat: [121.346631, 30.715544],
          controlName: "戚家墩停车场2H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/de576cec5b8400a9fc888a9e8994e04c.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 21,
          controlLnglat: [121.346699, 30.715862],
          controlName: "海岸线戚家墩出入口HG",
          controlType: "旅游景区",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/298309e2a7328545702e9032395ec349.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 22,
          controlLnglat: [121.345701, 30.718246],
          controlName: "沙滩停车场东南H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/1e34d92dd295ddac98d48b23e98bd414.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 23,
          controlLnglat: [121.346112, 30.718297],
          controlName: "海岸线戚家墩出入口HG",
          controlType: "旅游景区",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/ac801421820e85521207f9d73b6cf7db.jpeg",
        },
        {
          controlCode: "310116230011800*****",
          controlId: 24,
          controlLnglat: [121.345786, 30.717893],
          controlName: "沙滩停车场东南H",
          controlType: "治安复杂区域",
          controlImage:
            "https://pic.rmb.bdstatic.com/bjh/b062b737b65b952f728f51cdba0e5d15.jpeg",
        },
      ],
      // 是否显示监控弹窗
      isMonitorShow: false,
      monitorData: {
        controlCode: "31011623001181025009",
        controlId: 1,
        controlLnglat: [121.34898, 30.718072],
        controlName: "沪杭公路戚家墩路（北向南）HG",
        controlType: "交通干线",
      },
      // 监控
      controlArr: [],
      // 隔离酒店
      isolateHoteArr: [],

      // ------管网数据------
      // 管网弹窗显示
      isPipeInfoShow: false,
      pipeData: {
        pipeType: "雨水",
        pipeMaterial: "砼",
        pipeShape: "圆形",
        pipeWidth: "600(毫米)",
        pipeHeight: "600(毫米)",
        buryMethod: "直埋",
        whereRoad: "沪杭公路",
        adjoinRoad: "蒙山路-戚家墩路",
        pipeLength: "47.843(米)",
        buryDate: "201203",
        abandonDate: "",
        detectProperty: "普查",
        detectDepartment: "上海勘察设计研究院（集团）有限公司",
        detectDate: "201706",
        supervisor: "上海市地质调查研究院",
      },

      // ---雨水管网---
      // 管网显示
      rainPipeShow: true,
      // 管网实例
      rainPipeArr: [],
      // 管网数据
      rainPipeInfo: [
        {
          // 管线类型
          pipeType: "雨水",
          // 管线材质
          pipeMaterial: "砼",
          // 管线形状
          pipeShape: "圆形",
          // 管线宽度
          pipeWidth: "600(毫米)",
          // 管线高度
          pipeHeight: "600(毫米)",
          // 埋设方式
          buryMethod: "直埋",
          // 所在道路
          whereRoad: "蒙山路",
          // 相邻道路
          adjoinRoad: "蒙山路-戚家墩路",
          // 管道长度
          pipeLength: "39.565(米)",
          // 埋设年份
          buryDate: "201203",
          // 废弃年代
          abandonDate: "",
          // 探测性质
          detectProperty: "普查",
          // 探测单位名称
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          // 探测年份
          detectDate: "201706",
          // 监理单位名称
          supervisor: "上海市地质调查研究院",
        },
        {
          pipeType: "雨水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "600(毫米)",
          pipeHeight: "600(毫米)",
          buryMethod: "直埋",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          pipeLength: "47.843(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          pipeType: "雨水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "800(毫米)",
          pipeHeight: "800(毫米)",
          buryMethod: "直埋",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          pipeLength: "28.752(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
      ],

      // ---污水管网---
      // 管网显示
      sewagePipeShow: true,
      // 管网实例
      sewagePipe: null,
      // 管网数据
      sewagePipeInfo: [
        {
          pipeType: "污水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "400(毫米)",
          pipeHeight: "400(毫米)",
          buryMethod: "直埋",
          whereRoad: "梅州街",
          adjoinRoad: "隆安路-卫零路",
          pipeLength: "39.582(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201707",
          supervisor: "上海市地质调查研究院",
        },
        {
          pipeType: "污水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "400(毫米)",
          pipeHeight: "400(毫米)",
          buryMethod: "直埋",
          whereRoad: "梅州街",
          adjoinRoad: "隆安路-卫零路",
          pipeLength: "39.988(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          pipeType: "污水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "400(毫米)",
          pipeHeight: "400(毫米)",
          buryMethod: "直埋",
          whereRoad: "隆安路",
          adjoinRoad: "蒙山路-戚家墩路",
          pipeLength: "40.377(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          pipeType: "污水",
          pipeMaterial: "砼",
          pipeShape: "圆形",
          pipeWidth: "400(毫米)",
          pipeHeight: "400(毫米)",
          buryMethod: "直埋",
          whereRoad: "临潮街",
          adjoinRoad: "隆平路-隆安路",
          pipeLength: "39.746(米)",
          buryDate: "201203",
          abandonDate: "",
          detectProperty: "普查",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
      ],

      // ------井盖------
      isWellCoverShow: false,
      wellCoverInfo: [
        {
          formalNum: "YS90",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          formalNum: "YS25",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          formalNum: "YS30",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          formalNum: "YS10",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          formalNum: "YS21",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
        {
          formalNum: "YS33",
          pipeType: "雨水",
          pointFeature: "直线点",
          attachType: "雨水簏",
          attachNuknown: "使用探测成果",
          wellShape: "",
          wellDepth: "0.0",
          whereRoad: "沪杭公路",
          adjoinRoad: "蒙山路-戚家墩路",
          buildDate: "201203",
          detectDepartment: "上海勘察设计研究院（集团）有限公司",
          detectDate: "201706",
          supervisor: "上海市地质调查研究院",
        },
      ],
      wellCoverData: {
        formalNum: "YS90",
        pipeType: "雨水",
        pointFeature: "直线点",
        attachType: "雨水簏",
        attachNuknown: "使用探测成果",
        wellShape: "",
        wellDepth: "0.0",
        whereRoad: "沪杭公路",
        adjoinRoad: "蒙山路-戚家墩路",
        buildDate: "201203",
        detectDepartment: "上海勘察设计研究院（集团）有限公司",
        detectDate: "201706",
        supervisor: "上海市地质调查研究院",
      },
      wellCoverArr: [],

      // ------监控------
      isControlInfoShow: false,

      // ------隔离酒店------
      isIsolateHoteShow: false,
      isolateHoteInfo: [
        {
          title: "七彩艺术酒店",
          address: "金山区石化街道隆安路278号",
          img: "https://pic.rmb.bdstatic.com/bjh/977b0ebde14bf6cef267006be3afdb28.jpeg",
        },
        {
          title: "锦江之星",
          address: "金山区金一东路80号",
          img: "https://pic.rmb.bdstatic.com/bjh/977b0ebde14bf6cef267006be3afdb28.jpeg",
        },
      ],
      isolateHoteData: {
        title: "七彩艺术酒店",
        address: "金山区石化街道隆安路278号",
        img: "https://pic.rmb.bdstatic.com/bjh/977b0ebde14bf6cef267006be3afdb28.jpeg",
      },

      // ------楼号------
      isBuildingNumShow: false,

      // ------图斑------
      ispatternSpotShow: false,
      patternSpot: [],

      // 是否显示标注示例
      isTipsShow: false,
      // 是否显示
      isInputShow: false,

      // 石化街道图斑数据
      shiHuaStreet: [
        [121.348043, 30.706107],
        [121.344994, 30.703521],
        [121.337165, 30.707081],
        [121.335606, 30.705693],
        [121.340736, 30.701895],
        [121.343875, 30.697188],
        [121.337879, 30.694841],
        [121.319476, 30.693062],
        [121.310817, 30.695245],
        [121.302112, 30.694552],
        [121.277547, 30.690317],
        [121.273674, 30.687279],
        [121.272124, 30.687256],
        [121.271317, 30.688361],
        [121.27198, 30.693301],
        [121.271263, 30.697299],
        [121.270011, 30.698304],
        [121.270204, 30.700778],
        [121.273672, 30.701127],
        [121.275796, 30.7011],
        [121.277994, 30.702348],
        [121.280251, 30.703094],
        [121.285152, 30.70395],
        [121.286531, 30.704261],
        [121.286396, 30.704849],
        [121.289442, 30.705619],
        [121.29505, 30.707821],
        [121.298922, 30.710435],
        [121.303943, 30.712773],
        [121.303421, 30.715858],
        [121.304414, 30.715962],
        [121.303639, 30.717128],
        [121.304099, 30.717248],
        [121.303616, 30.718615],
        [121.30411, 30.718773],
        [121.304055, 30.719438],
        [121.306645, 30.720092],
        [121.306286, 30.722088],
        [121.308818, 30.722966],
        [121.309137, 30.721825],
        [121.309789, 30.721955],
        [121.309763, 30.722419],
        [121.310133, 30.722536],
        [121.31014, 30.72328],
        [121.31126, 30.723624],
        [121.311595, 30.721992],
        [121.312305, 30.722106],
        [121.313733, 30.71707],
        [121.315677, 30.717531],
        [121.315511, 30.718057],
        [121.316252, 30.718219],
        [121.316471, 30.717715],
        [121.317203, 30.717861],
        [121.31679, 30.719138],
        [121.316384, 30.71908],
        [121.316246, 30.719571],
        [121.31475, 30.719249],
        [121.314545, 30.720065],
        [121.317002, 30.721032],
        [121.316873, 30.72133],
        [121.318693, 30.721746],
        [121.318553, 30.722236],
        [121.31955, 30.722476],
        [121.319384, 30.72319],
        [121.32068, 30.723466],
        [121.320549, 30.723746],
        [121.320981, 30.723862],
        [121.321219, 30.723198],
        [121.32258, 30.723337],
        [121.322698, 30.72286],
        [121.324221, 30.723165],
        [121.323767, 30.725541],
        [121.324366, 30.725683],
        [121.323872, 30.727322],
        [121.323228, 30.727201],
        [121.322808, 30.728276],
        [121.32414, 30.728638],
        [121.32381, 30.729842],
        [121.32683, 30.730483],
        [121.327132, 30.729273],
        [121.32788, 30.729401],
        [121.328667, 30.725933],
        [121.328933, 30.725983],
        [121.329593, 30.723235],
        [121.330735, 30.723436],
        [121.330505, 30.724406],
        [121.331594, 30.724577],
        [121.331079, 30.726448],
        [121.336602, 30.727528],
        [121.335701, 30.732087],
        [121.341399, 30.735889],
        [121.343524, 30.728979],
        [121.350455, 30.730466],
        [121.352027, 30.723439],
        [121.353241, 30.717413],
        [121.353641, 30.715511],
        [121.354327, 30.713255],
        [121.354385, 30.711946],
        // [121.348146, 30.706162],
        [121.348043, 30.706107],
      ],
      linChaoVillage: null,
      isClickWin: false,

      // 镂空绿化数组
      greenPolygonArr: [],
      // 绿化图斑数组
      greeningSpotArr: [],
      // 绿化图斑文本数组
      greeningSpotTextArr: [],
      // 是否显示绿化信息弹窗
      isGreeningSpotShow: false,
      // 绿化图斑信息
      greeningSpotInfo: [
        {
          id: 1,
          forestHead: "朱筷顺",
          deputyForestHead: "潘凤杰",
          packageArea: "临蒙居民区",
          resourceType: "居住区绿化",
          name: "临潮一村",
          area: "28214㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 2,
          forestHead: "姚德磊",
          deputyForestHead: "钱军",
          packageArea: "鹦鹉洲生态湿地",
          resourceType: "生态廊道",
          name: "湿地",
          area: "145842㎡",
          managementUnit: "上海金山文旅产业有限公司",
          managementUnitPerson: "李广利",
          managementUnitTelphone: "19376492373",
          conservationUnit: "上海金山文旅产业有限公司",
          conservationUnitPerson: "张艳军",
          conservationUnitTelphone: "10834747647",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 3,
          forestHead: "王咏梅",
          deputyForestHead: "王君毅 ",
          packageArea: "滨海公园",
          resourceType: "公园",
          name: "公共绿地",
          area: "60003㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 4,
          forestHead: "王咏梅",
          deputyForestHead: "王君毅 ",
          packageArea: "荟萃园",
          resourceType: "公园",
          name: "公共绿地",
          area: "12000㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: null,
          jobDuty: null,
        },
        {
          id: 5,
          forestHead: "姚德磊",
          deputyForestHead: "钱军",
          packageArea: "山龙绿地",
          resourceType: "公园",
          name: "公共绿地",
          area: "25645㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 6,
          forestHead: "王咏梅",
          deputyForestHead: "李怀青",
          packageArea: "梅州居民区",
          resourceType: "居住区绿化",
          name: "梅州新村",
          area: "42857㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 7,
          forestHead: "姚德磊",
          deputyForestHead: "蒋静文",
          packageArea: "柳城居民区",
          resourceType: "居住区绿化",
          name: "六村",
          area: "14996㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: null,
          jobDuty: null,
        },
        {
          id: 8,
          forestHead: "沈磊",
          deputyForestHead: "杨林娟",
          packageArea: "海棠居民区",
          resourceType: "居住区绿化",
          name: "海棠新村",
          area: "51546㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
        {
          id: 9,
          forestHead: "胡益红",
          deputyForestHead: "张慧",
          packageArea: "十村居民区",
          resourceType: "居住区绿化",
          name: "十村",
          area: "20597㎡",
          managementUnit: "上海市金山区园林管理所",
          managementUnitPerson: "钱军",
          managementUnitTelphone: "57941481",
          conservationUnit: "上海金山绿化建设养护有限公司",
          conservationUnitPerson: "张颖罡",
          conservationUnitTelphone: "57946333",
          managementStyle: "督促",
          jobDuty: null,
        },
      ],
      greeningSpotData: {},

      // 小区基础信息-icon的显示隐藏
      iconSelBtnShow: false,
      // 小区基础信息-icon选择按钮的数据
      iconSelBtnData: [
        {
          id: 1,
          title: "出入口",
        },
        {
          id: 2,
          title: "转门",
        },
        {
          id: 3,
          title: "居委",
        },
        {
          id: 4,
          title: "垃圾房",
        },
        {
          id: 5,
          title: "配电室",
        },
        {
          id: 6,
          title: "凉亭",
        },
        {
          id: 7,
          title: "健身点",
        },
        {
          id: 8,
          title: "消防通道",
        },
      ],

      // 管网数据-icon的显示隐藏
      pipeSelBtnShow: false,
      // 管网数据-icon选择按钮的数据
      pipeSelBtnData: [
        {
          id: 1,
          title: "雨水管线",
        },
        {
          id: 2,
          title: "污水管线",
        },
      ],
      // 消防通道的显示
      emergencyAccessLineShow: true,
      // 出入口的显示
      doorMarkerShow: true,
      // 转门的显示
      revolvingDoorMarkerShow: true,
      // 垃圾房的显示
      garbageRoomShow: true,
      // 凉亭的显示
      pavilionShow: true,
      // 配电室的显示
      powerRoomShow: true,
      // 健身房的显示
      fitnessShow: true,
      // 居委的显示
      residentCommitteeShow: true,

      // ------icon实例------
      emergencyAccessLine: [],
      doorMarker: [],
      revolvingDoorMarker: [],
      garbageRoom: [],
      pavilion: [],
      powerRoom: [],
      fitness: [],
      residentCommittee: [],

      // ------健身点------
      isBodybuildingShow: false,
      // ------事件上报------
      isReaded: false,
      eventTips: null,

      // ------党建库-办事处-----
      isPartyBuildingShow: false,
      partyBuildingOfficeArr: [],
      partyData: {
        title: "人民政府石化街道办事处",
        position: [121.33636, 30.725693],
        address: "上海市金山区石化九村西北3门旁",
        tel: "021-57941191",
        openTime: "周一至周五 08:30-17:00",
        introduce:
          "金山区党建服务中心作为上海市民政局授牌的公益基地,积极为全区党员参与公益活动、志愿服务搭建平台。近年来,该中心以“党建＋公益”为理念,先后组建党员志愿服务总队、纵队、支队，注册党员志愿者1.7万多名,在社区公益、四季公益等方面展示出新作为。<br/>党建＋社区公益,让志愿服务更聚人气。<br/>金山区党建服务中心依托“心联鑫”区域化党建联席会议平台,整合结对单位资源，建成“1+14+245+X”三级党建平台...",
      },
      partyInfo: [
        {
          title: "人民政府石化街道办事处",
          position: [121.33636, 30.725693],
          address: "上海市金山区卫零路485号",
          tel: "021-57941191",
          openTime: "周一至周五 08:30-17:00",
          introduce:
            "金山区党建服务中心作为上海市民政局授牌的公益基地,积极为全区党员参与公益活动、志愿服务搭建平台。近年来,该中心以“党建＋公益”为理念,先后组建党员志愿服务总队、纵队、支队，注册党员志愿者1.7万多名,在社区公益、四季公益等方面展示出新作为。<br/>党建＋社区公益,让志愿服务更聚人气。<br/>金山区党建服务中心依托“心联鑫”区域化党建联席会议平台,整合结对单位资源，建成“1+14+245+X”三级党建平台...",
        },
        {
          title: "东村居民委员会",
          position: [121.324395, 30.721022],
          address: "上海市金山区学府路221号",
          tel: "021-57940318",
          openTime: "周一至周五 08:30-17:00",
          introduce:
            "石化街道东村居民委员会是由原上海石化征地后的所有散居自然村组成，原共有11个自然村，即：卫城村、金卫村、海光村、南门村、黄弄村、陈埭村、濮黎村、陆家村、何家村、肖家村和裴弄村。",
        },
        {
          title: "社区党群服务中心",
          position: [121.340476, 30.712873],
          address: "上海市金山区新城路328号",
          tel: "021-37218462",
          openTime: "周一至周五 08:30-16:00",
          introduce:
            "石化街道社区服务中心是在上海市金山区石化街道办事处党工委、办事处领导下的事业单位，为社区居民办实事、解难事的服务机构。<br/>社区生活服务中心为民办非企业单位，采用公建民营的方式，实行政府搭台、民间运作、社会参与的运作模式，同时注重专业化、社会化、项目化的发展方向，最终实现百姓受益的目标。",
        },
        {
          title: "七村居民区党建服务站",
          position: [121.347886, 30.713143],
          address: "上海市金山区石化九村西北3门旁",
          tel: "021-49645767",
          openTime: "周一至周五 08:30-17:00",
          introduce:
            "石化街道七村居委会近年来先后荣获：金山区敬老居委会、金山区园林式小区、金山区调解工作先进集体、区级民防居委会合格单位、金山区计划生育“五佳”新居村、上海市合格职工之家、上海市平安小区、上海市文明小区、社区建设示范居委会...",
        },
        {
          title: "十村居民委员会",
          position: [121.351497, 30.712761],
          address: "上海市金山区石化十村19号",
          tel: "021-57933792",
          openTime: "周一至周五 08:30-17:00",
          introduce:
            "上海石化地区十村居委会的地址是上海市金山区石化地区石化十村19号，树立“全心全意为大家服务”的精神，仍需要我们不懈努力与不断追求。我们需要持续努力，坚持“真诚、务实、优质、高效”的作风，充分发挥我们的精神，想大家之所想，急大家之所急，提供全方位的服务。",
        },
      ],

      // ------现状使用主体-icon选择按钮的数据----
      // icon的显示隐藏
      currentUserSelBtnShow: false,
      // icon选择按钮的数据
      currentUserSelBtnData: [
        {
          id: 1,
          title: "监控",
        },
        {
          id: 2,
          title: "沿街商铺",
        },
        {
          id: 3,
          title: "宾旅馆",
        },
        {
          id: 4,
          title: "商业网点",
        },
        {
          id: 5,
          title: "农贸市场",
        },
        {
          id: 6,
          title: "学校",
        },
        {
          id: 7,
          title: "养老机构",
        },
        {
          id: 8,
          title: "宗教场所",
        },
        {
          id: 9,
          title: "隔离酒店",
        },
        // 监控、沿街商铺、宾旅馆、商业网点、农贸市场、学校、养老机构、宗教场所
      ],
      monitorIconShow: false, // 监控
      streetShopIconShow: false, // 沿街商铺
      hotelIconShow: false, // 宾旅馆
      networkIconShow: false, // 商业网点
      farmMarketIconShow: false, // 农贸市场
      schoolIconShow: false, // 学校
      nursingIconShow: false, // 养老机构
      religionIconShow: false, // 宗教场所
      isolateHoteIconShow: false, // 隔离酒店
      streetShopArr: [], // 沿街商铺集合
      streetShopOtherArr: [], // 部分显示信息的沿街商铺集合
      hotelArr: [], // 宾旅馆集合
      networkArr: [], // 商业网点集合
      farmMarketArr: [], // 农贸市场集合
      schoolArr: [], // 学校集合
      nursingArr: [], // 养老机构集合
      religionArr: [], // 宗教场所集合

      illnessControlShow: false, // 记录疫情防控按钮是否显示
      isIllnessControl: false, // 记录疫情防控按钮是否点击

      illnessControlAreaPath: [], // 疫情防控区域路径-正常
      illnessControlArea: [], // 疫情防控区域-正常

      bottomBtnTitle: "", // 记录底部按钮的title

      greenPointArr: [], // 绿色聚合点
      redPoint: null, // 红色聚合点
    };
  },
  methods: {
    initMap() {
      AMapLoader.load({
        key: window._AMapKey,
        version: "2.0",
      })
        .then((AMap) => {
          this.AMap = AMap;
          this.map = new AMap.Map("container", {
            // resizeEnable: false,
            // rotateEnable: false,
            // pitchEnable: false,
            mapStyle: "amap://styles/af4e1a73c65122f90fc32eb60d686d87",
            viewMode: "3D",
            zoom: 16,
            // zooms: [18, 20],
            // center: [121.348263, 30.717306],
            center: [121.314026, 30.712866],
            // pitch: 40,
            features: ["bg", "road", "point", "building"],
          });
          // this.map.setPitch(0);
          // this.map.setZoom(16, false, 500);
          // this.map.setCenter([121.314026, 30.712866], false, 500);

          this.map.on("click", (ev) => {
            console.log(ev.lnglat.lng, ev.lnglat.lat);
            this.windowLeave();
            this.isInputShow = false;
          });

          // (---待封装---可防抖处理)
          // this.map.on("zoomstart", () => {
          //   // console.log("zoomstart");
          //   // 移除大聚合
          //   this.map.remove(this.gether_big_num_arr);
          // });

          // (---待封装---可防抖处理)
          // this.map.on("zoomend", (ev) => {
          //   console.log(this.map.getZoom());

          //   if (this.bottomBtnTitle == "全部数据") {
          //     if (this.map.getZoom() <= 18.5) {
          //       this.map.remove(this.gather_num_arr);
          //       this.map.add(this.gether_big_num_arr);
          //     } else {
          //       this.map.add(this.gather_num_arr);
          //       this.map.remove(this.gether_big_num_arr);
          //     }
          //   } else if (this.bottomBtnTitle == "现状使用主体") {
          //     if (this.streetShopIconShow) {
          //       // 当缩放等级小于18.29时，
          //       if (this.map.getZoom() <= 18.5) {
          //         this.map.remove(this.gather_num_arr);
          //         this.map.add(this.gether_big_num_arr);
          //       } else {
          //         this.map.add(this.gather_num_arr);
          //         this.map.remove(this.gether_big_num_arr);
          //       }
          //     }
          //   } else {
          //     this.map.remove(this.gather_num_arr);
          //     this.map.remove(this.gether_big_num_arr);
          //   }
          // });

          // 创建卫星图
          this.satelliteLayer = new this.AMap.TileLayer.Satellite();

          // 初始化所有遮罩层
          this.initCovering();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 初始化所有遮罩层
    initCovering() {
      // ------道路红线------
      // #region
      /*
      // 隆安路
      var roadLinePath01 = [
        new this.AMap.LngLat("121.345946", "30.718134"),
        new this.AMap.LngLat("121.349226", "30.718641"),
      ];
      var roadLine01 = new this.AMap.Polyline({
        path: roadLinePath01,
        strokeWeight: 20,    // 线宽
        strokeColor: 'red',
        strokeOpacity: 1,
        cursor: 'pointer',
      });
      roadLine01.on('click', (ev) => {
        console.log("道路红线 - 隆安路");
      })
      // 蒙山路
      var roadLinePath02 = [
        new this.AMap.LngLat("121.345959", "30.71808"),
        new this.AMap.LngLat("121.346487", "30.715849"),
      ];
      var roadLine02 = new this.AMap.Polyline({
        path: roadLinePath02,
        strokeWeight: 20,    // 线宽
        strokeColor: 'red',
        strokeOpacity: 1,
        cursor: 'pointer',
      });
      roadLine02.on('click', (ev) => {
        console.log("道路红线 - 蒙山路");
      });

      // 沪杭公路
      var roadLinePath03 = [
        new this.AMap.LngLat("121.346502", "30.715842"),
        new this.AMap.LngLat("121.350083", "30.715297"),
      ];
      var roadLine03 = new this.AMap.Polyline({
        path: roadLinePath03,
        strokeWeight: 20,    // 线宽
        strokeColor: 'red',
        strokeOpacity: 1,
        cursor: 'pointer',
      });
      roadLine03.on('click', (ev) => {
        console.log("道路红线 - 沪杭公路");
      });
      */
      //#endregion

      //#region
      // ------临潮一村------
      var pathOne = [
        new AMap.LngLat("121.346146", "30.717641"),
        new AMap.LngLat("121.347223", "30.717832"),
        new AMap.LngLat("121.347262", "30.717688"),
        new AMap.LngLat("121.347528", "30.717718"),
        new AMap.LngLat("121.347399", "30.718283"),
        new AMap.LngLat("121.349255", "30.7186"),
        new AMap.LngLat("121.349376", "30.718071"),
        new AMap.LngLat("121.349498", "30.718098"),
        new AMap.LngLat("121.349599", "30.717651"),
        new AMap.LngLat("121.349897", "30.717684"),
        new AMap.LngLat("121.350026", "30.71762"),
        new AMap.LngLat("121.350183", "30.716994"),
        new AMap.LngLat("121.349991", "30.716944"),
        new AMap.LngLat("121.350011", "30.716826"),
        new AMap.LngLat("121.349744", "30.716775"),
        new AMap.LngLat("121.349795", "30.716563"),
        new AMap.LngLat("121.34985 ", "30.716439"),
        new AMap.LngLat("121.350136", "30.715318"),
        new AMap.LngLat("121.346816", "30.715809"),
        new AMap.LngLat("121.346491", "30.716062"),
        new AMap.LngLat("121.346146", "30.717641"),
      ];
      this.linChaoVillage = new AMap.Polygon({
        path: pathOne,
        // #70b2e2
        strokeColor: "#70b2e2", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 18, // 线宽
        strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#70b2e2", // 填充色
        fillOpacity: 0.3, // 填充透明度
      });
      this.linChaoVillage.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });

      //#endregion

      // ------图斑 临蒙居委------
      //#region
      var linMengCommiteePath = [
        [121.345964, 30.718037],
        [121.351054, 30.718853],
        [121.351464, 30.716863],
        [121.349795, 30.716596],
        [121.350129, 30.715324],
        [121.34648, 30.715839],
      ];
      var linMengCommitee = new AMap.Polygon({
        path: linMengCommiteePath,
        strokeColor: "#70b2e2", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#70b2e2", // 填充色
        fillOpacity: 0.2, // 填充透明度
        cursor: "pointer",
      });
      var linMengCommiteeText = new AMap.Text({
        text: "临蒙居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.34886, 30.717226],
        // offset: [0, 8],
      });
      linMengCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
        this.getParentBtnTitle("全部数据");
        this.$emit("getAllBtnVal", 0);
      });
      linMengCommiteeText.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
        this.getParentBtnTitle("全部数据");
        this.$emit("getAllBtnVal", 0);
      });
      // 临蒙居委图斑的移入移出
      linMengCommitee.on("mouseover", (ev) => {
        linMengCommitee.setOptions({
          fillOpacity: 0.7,
        });
      });
      linMengCommitee.on("mouseout", (ev) => {
        linMengCommitee.setOptions({
          fillOpacity: 0.2,
        });
      });
      // 临蒙居委文字的移入移出
      linMengCommiteeText.on("mouseover", (ev) => {
        linMengCommitee.setOptions({
          fillOpacity: 0.7,
        });
      });
      linMengCommiteeText.on("mouseout", (ev) => {
        linMengCommitee.setOptions({
          fillOpacity: 0.2,
        });
      });

      this.patternSpot.push(linMengCommitee, linMengCommiteeText);

      //#endregion

      // ------图斑 临三居委------
      //#region
      var linSanCommiteePath = [
        [121.351204, 30.718926],
        [121.352813, 30.7192],
        [121.35356, 30.715669],
        [121.353399, 30.715582],
        [121.353137, 30.71552],
        [121.350204, 30.71523],
        [121.349906, 30.716506],
        [121.351669, 30.716786],
      ];

      var linSanCommitee = new AMap.Polygon({
        path: linSanCommiteePath,
        strokeColor: "#70b2e2", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#70b2e2", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var linSanCommiteeText = new AMap.Text({
        text: "临三居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.35165, 30.71614],
        // offset: [0, 8],
      });
      linSanCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(linSanCommitee, linSanCommiteeText);

      //#endregion

      // ------图斑 海棠居委------
      //#region
      var haiTangCommiteePath = [
        [121.345339, 30.720793],
        [121.345924, 30.71824],
        [121.352735, 30.719293],
        [121.352113, 30.722455],
      ];
      var haiTangCommitee = new AMap.Polygon({
        path: haiTangCommiteePath,
        strokeColor: "#ecc765", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#ecc765", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var haiTangCommiteeText = new AMap.Text({
        text: "海棠居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.348976, 30.720222],
        // offset: [0, 8],
      });
      haiTangCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(haiTangCommitee, haiTangCommiteeText);

      //#endregion

      // ------图斑 东泉居委------
      //#region
      var dongQuanCommiteePath = [
        [121.344384, 30.725419],
        [121.351212, 30.726889],
        [121.352048, 30.722599],
        [121.345336, 30.720913],
      ];

      var dongQuanCommitee = new AMap.Polygon({
        path: dongQuanCommiteePath,
        strokeColor: "#bbb2d7", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#bbb2d7", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongQuanCommiteeText = new AMap.Text({
        text: "东泉居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.348097, 30.724061],
        // offset: [0, 8],
      });
      dongQuanCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongQuanCommitee, dongQuanCommiteeText);
      //#endregion

      // ------图斑 合生居委-----
      //#region
      var heShengCommiteePath = [
        [121.351311, 30.726955],
        [121.353306, 30.727893],
        [121.354854, 30.725039],
        [121.352027, 30.723567],
      ];

      var heShengCommitee = new AMap.Polygon({
        path: heShengCommiteePath,
        strokeColor: "#e8eb83", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#e8eb83", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var heShengCommiteeText = new AMap.Text({
        text: "合生居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.35288, 30.725746],
        // offset: [0, 8],
      });
      heShengCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(heShengCommitee, heShengCommiteeText);
      //#endregion

      // -----图斑 阳光城居委-----
      //#region
      var yangGuangCityCommiteePath = [
        [121.343634, 30.72892],
        [121.35046, 30.730421],
        [121.351145, 30.727128],
        [121.344366, 30.725579],
      ];

      var yangGuangCityCommitee = new AMap.Polygon({
        path: yangGuangCityCommiteePath,
        strokeColor: "#9dcf90", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9dcf90", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var yangGuangCityCommiteeText = new AMap.Text({
        text: "阳光城居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.347206, 30.728088],
        // offset: [0, 8],
      });
      yangGuangCityCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(yangGuangCityCommitee, yangGuangCityCommiteeText);
      //#endregion

      // -----图斑 紫卫居委-----
      //#region
      var ziWeiCommiteePath = [
        [121.335747, 30.732104],
        [121.341258, 30.735794],
        [121.342376, 30.732363],
        [121.336036, 30.731003],
      ];

      var ziWeiCommitee = new AMap.Polygon({
        path: ziWeiCommiteePath,
        strokeColor: "#e8eb84", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#e8eb84", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var ziWeiCommiteeText = new AMap.Text({
        text: "紫卫居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.33925, 30.732859],
        // offset: [0, 8],
      });
      ziWeiCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(ziWeiCommitee, ziWeiCommiteeText);
      //#endregion

      // -----图斑 山龙居委-----
      //#region
      var ziWeiCommiteePath = [
        [121.335999, 30.730907],
        [121.336702, 30.727734],
        [121.343366, 30.729081],
        [121.3424, 30.732241],
      ];

      var ziWeiCommitee = new AMap.Polygon({
        path: ziWeiCommiteePath,
        strokeColor: "#6fb1e1", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#6fb1e1", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var ziWeiCommiteeText = new AMap.Text({
        text: "山龙居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.339602, 30.729963],
        // offset: [0, 8],
      });
      ziWeiCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(ziWeiCommitee, ziWeiCommiteeText);
      //#endregion

      // -----图斑 卫清居委-----
      //#region
      var weiQingCommiteePath = [
        [121.336687, 30.727524],
        [121.337017, 30.725896],
        [121.338264, 30.726141],
        [121.338277, 30.726207],
        [121.3405, 30.726659],
        [121.340436, 30.72692],
        [121.34074, 30.727018],
        [121.340721, 30.727198],
        [121.343787, 30.727829],
        [121.343495, 30.728885],
      ];

      var weiQingCommitee = new AMap.Polygon({
        path: weiQingCommiteePath,
        strokeColor: "#bbb2d7", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#bbb2d7", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var weiQingCommiteeText = new AMap.Text({
        text: "卫清居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.339926, 30.727388],
        // offset: [0, 8],
      });
      weiQingCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(weiQingCommitee, weiQingCommiteeText);
      //#endregion

      // -----图斑 辰凯居委-----
      //#region
      var chenKaiCommiteePath = [
        [121.337018, 30.725812],
        [121.337421, 30.724148],
        [121.344245, 30.725548],
        [121.343763, 30.727755],
        [121.340785, 30.727175],
        [121.340794, 30.727039],
        [121.340531, 30.726896],
        [121.34054, 30.726625],
        [121.338376, 30.726158],
        [121.33835, 30.726113],
        [121.337044, 30.725834],
      ];

      var chenKaiCommitee = new AMap.Polygon({
        path: chenKaiCommiteePath,
        strokeColor: "#ed9d92", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#ed9d92", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var chenKaiCommiteeText = new AMap.Text({
        text: "辰凯居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.340574, 30.725832],
        // offset: [0, 8],
      });
      chenKaiCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(chenKaiCommitee, chenKaiCommiteeText);
      //#endregion

      // -----图斑 东礁一居委-----
      //#region
      var dongJiaoOneCommiteePath = [
        [121.337446, 30.724021],
        [121.340052, 30.724556],
        [121.340422, 30.723386],
        [121.34249, 30.723749],
        [121.342648, 30.723359],
        [121.342184, 30.722479],
        [121.342922, 30.720266],
        [121.338554, 30.719205],
      ];

      var dongJiaoOneCommitee = new AMap.Polygon({
        path: dongJiaoOneCommiteePath,
        strokeColor: "#d09dc6", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#d09dc6", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongJiaoOneCommiteeText = new AMap.Text({
        text: "东礁一居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.339927, 30.72214],
        // offset: [0, 8],
      });
      dongJiaoOneCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongJiaoOneCommitee, dongJiaoOneCommiteeText);
      //#endregion

      // -----图斑 东礁二居委-----
      //#region
      var dongJiaoTwoCommiteePath = [
        [121.340137, 30.724559],
        [121.344264, 30.725398],
        [121.345196, 30.720885],
        [121.34296, 30.720329],
        [121.342252, 30.722459],
        [121.342693, 30.723364],
        [121.342563, 30.723802],
        [121.340456, 30.723475],
      ];

      var dongJiaoTwoCommitee = new AMap.Polygon({
        path: dongJiaoTwoCommiteePath,
        strokeColor: "#9dd6e0", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9dd6e0", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongJiaoTwoCommiteeText = new AMap.Text({
        text: "东礁二居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.34239, 30.724366],
        // offset: [0, 8],
      });
      dongJiaoTwoCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongJiaoTwoCommitee, dongJiaoTwoCommiteeText);
      //#endregion

      // -----图斑 梅州居委-----
      //#region
      var meiZhouCommiteePath = [
        [121.332641, 30.718022],
        [121.332867, 30.716779],
        [121.333817, 30.71728],
        [121.338703, 30.717401],
        [121.346296, 30.715815],
        [121.345207, 30.720757],
      ];

      var meiZhouCommitee = new AMap.Polygon({
        path: meiZhouCommiteePath,
        strokeColor: "#ed9e93", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#ed9e93", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var meiZhouCommiteeText = new AMap.Text({
        text: "梅州居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.341777, 30.71844],
        // offset: [0, 8],
      });
      meiZhouCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(meiZhouCommitee, meiZhouCommiteeText);
      //#endregion

      // -----图斑 四村居委-----
      //#region
      var siCunCommiteePath = [
        [121.346328, 30.715703],
        [121.338874, 30.717199],
        [121.339832, 30.712879],
        [121.341814, 30.711335],
      ];

      var siCunCommitee = new AMap.Polygon({
        path: siCunCommiteePath,
        strokeColor: "#9cce8e", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9cce8e", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var siCunCommiteeText = new AMap.Text({
        text: "四村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.34188, 30.714792],
        // offset: [0, 8],
      });
      siCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(siCunCommitee, siCunCommiteeText);
      //#endregion

      // -----图斑 桥园居委-----
      //#region
      var qiaoYuanCommiteePath = [
        [121.331198, 30.726373],
        [121.336432, 30.727424],
        [121.337827, 30.721249],
        [121.332879, 30.719985],
      ];

      var qiaoYuanCommitee = new AMap.Polygon({
        path: qiaoYuanCommiteePath,
        strokeColor: "#e07f8a", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#e07f8a", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var qiaoYuanCommiteeText = new AMap.Text({
        text: "桥园居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.334645, 30.723945],
        // offset: [0, 8],
      });
      qiaoYuanCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(qiaoYuanCommitee, qiaoYuanCommiteeText);

      //#endregion

      // -----图斑 东村居委-----
      //#region
      var dongCunCommiteePath01 = [
        [121.332229, 30.719692],
        [121.332619, 30.71817],
        [121.338229, 30.719401],
        [121.337825, 30.72118],
      ];

      var dongCunCommitee01 = new AMap.Polygon({
        path: dongCunCommiteePath01,
        strokeColor: "#8ccdba", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#8ccdba", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongCunCommiteeText01 = new AMap.Text({
        text: "东村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.335197, 30.719627],
        // offset: [0, 8],
      });
      dongCunCommitee01.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongCunCommitee01, dongCunCommiteeText01);

      //#endregion

      // -----图斑 三村居委-----
      //#region
      var dongCunCommiteePath = [
        [121.332815, 30.716654],
        [121.333884, 30.717132],
        [121.338738, 30.71717],
        [121.33972, 30.712865],
        [121.341708, 30.711263],
        [121.335465, 30.705409],
      ];

      var dongCunCommitee = new AMap.Polygon({
        path: dongCunCommiteePath,
        strokeColor: "#9dd6e0", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9dd6e0", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongCunCommiteeText = new AMap.Text({
        text: "三村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.336764, 30.712836],
        // offset: [0, 8],
      });
      dongCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongCunCommitee, dongCunCommiteeText);
      //#endregion

      // -----图斑 合浦居委-----
      //#region
      var dongCunCommiteePath = [
        [121.341807, 30.711199],
        [121.343387, 30.709981],
        [121.344406, 30.705322],
        [121.344937, 30.704941],
        [121.344007, 30.704142],
        [121.337234, 30.706893],
      ];

      var dongCunCommitee = new AMap.Polygon({
        path: dongCunCommiteePath,
        strokeColor: "#e8eb83", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#e8eb83", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongCunCommiteeText = new AMap.Text({
        text: "合浦居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.341266, 30.707733],
        // offset: [0, 8],
      });
      dongCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongCunCommitee, dongCunCommiteeText);
      //#endregion

      // -----图斑 十二村居委-----
      //#region
      var shiErCunCommiteePath = [
        [121.343492, 30.709989],
        [121.347485, 30.710638],
        [121.348092, 30.708031],
        [121.345048, 30.70503],
        [121.344476, 30.705404],
      ];
      var shiErCunCommitee = new AMap.Polygon({
        path: shiErCunCommiteePath,
        strokeColor: "#ecc765", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#ecc765", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var shiErCunCommiteeText = new AMap.Text({
        text: "十二村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.345857, 30.708313],
        // offset: [0, 8],
      });
      shiErCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(shiErCunCommitee, shiErCunCommiteeText);
      //#endregion

      // -----图斑 十三村居委-----
      //#region
      var shiSanCunCommiteePath = [
        [121.347592, 30.710656],
        [121.348156, 30.708212],
        [121.352329, 30.71169],
        [121.351956, 30.711599],
        [121.351264, 30.71104],
        [121.350892, 30.71126],
      ];
      var shiSanCunCommitee = new AMap.Polygon({
        path: shiSanCunCommiteePath,
        strokeColor: "#9dd6e0", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9dd6e0", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var shiSanCunCommiteeText = new AMap.Text({
        text: "十三村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.349109, 30.71021],
        // offset: [0, 8],
      });
      shiSanCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(shiSanCunCommitee, shiSanCunCommiteeText);
      //#endregion

      // -----图斑 柳城居委-----
      //#region
      var liuChengCommiteePath = [
        [121.346408, 30.715639],
        [121.341884, 30.711288],
        [121.343422, 30.71012],
        [121.347457, 30.710747],
      ];
      var liuChengCommitee = new AMap.Polygon({
        path: liuChengCommiteePath,
        strokeColor: "#d09dc6", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#d09dc6", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var liuChengCommiteeText = new AMap.Text({
        text: "柳城居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.3449, 30.711938],
        // offset: [0, 8],
      });
      liuChengCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(liuChengCommitee, liuChengCommiteeText);
      //#endregion

      // -----图斑 七村居委-----
      //#region
      var qiCunCommiteePath = [
        [121.346493, 30.715668],
        [121.350152, 30.715162],
        [121.350526, 30.71348],
        [121.347073, 30.712949],
      ];
      var qiCunCommitee = new AMap.Polygon({
        path: qiCunCommiteePath,
        strokeColor: "#bbb2d7", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#bbb2d7", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var qiCunCommiteeText = new AMap.Text({
        text: "七村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.348384, 30.714261],
        // offset: [0, 8],
      });
      qiCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(qiCunCommitee, qiCunCommiteeText);
      //#endregion

      // -----图斑 九村居委-----
      //#region
      var jiuCunCommiteePath = [
        [121.347066, 30.712863],
        [121.350534, 30.713384],
        [121.350911, 30.711376],
        [121.347572, 30.710804],
      ];
      var jiuCunCommitee = new AMap.Polygon({
        path: jiuCunCommiteePath,
        strokeColor: "#e07f8a", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#e07f8a", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var jiuCunCommiteeText = new AMap.Text({
        text: "九村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.348936, 30.712072],
        // offset: [0, 8],
      });
      jiuCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(jiuCunCommitee, jiuCunCommiteeText);
      //#endregion

      // -----图斑 十村居委-----
      //#region
      var shiCunCommiteePath = [
        [121.350281, 30.715141],
        [121.353504, 30.715507],
        [121.353951, 30.71338],
        [121.352319, 30.711799],
        [121.351903, 30.711709],
        [121.351248, 30.7112],
        [121.35103, 30.711388],
      ];

      var shiCunCommitee = new AMap.Polygon({
        path: shiCunCommiteePath,
        strokeColor: "#ed9d92", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#ed9d92", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var shiCunCommiteeText = new AMap.Text({
        text: "十村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.352253, 30.713661],
        // offset: [0, 8],
      });
      shiCunCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(shiCunCommitee, shiCunCommiteeText);
      //#endregion

      // -----图斑 东村居委-----
      //#region
      var dongCunCommiteePath02 = [
        [121.326291, 30.727258],
        [121.32668, 30.726968],
        [121.326797, 30.726421],
        [121.326407, 30.725897],
        [121.32389, 30.725339],
        [121.323993, 30.72506],
        [121.328614, 30.725997],
        [121.328276, 30.727548],
        [121.327563, 30.727425],
      ];

      var dongCunCommitee02 = new AMap.Polygon({
        path: dongCunCommiteePath02,
        strokeColor: "#8ccdba", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#8ccdba", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var dongCunCommiteeText02 = new AMap.Text({
        text: "东村居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.327508, 30.726571],
        // offset: [0, 8],
      });
      dongCunCommitee02.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(dongCunCommitee02, dongCunCommiteeText02);

      //#endregion

      // -----图斑 滨一居委-----
      //#region
      var binYiCommiteePath = [
        [121.324014, 30.72982],
        [121.326926, 30.730497],
        [121.327264, 30.729337],
        [121.324345, 30.728717],
        [121.324279, 30.728683],
        [121.324729, 30.72742],
        [121.323942, 30.727397],
        [121.323591, 30.727568],
        [121.323207, 30.72746],
        [121.322883, 30.728256],
        [121.324246, 30.728637],
      ];

      var binYiCommitee = new AMap.Polygon({
        path: binYiCommiteePath,
        strokeColor: "#d09dc6", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#d09dc6", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var binYiCommiteeText = new AMap.Text({
        text: "滨一居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.325602, 30.729526],
        // offset: [0, 8],
      });
      binYiCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(binYiCommitee, binYiCommiteeText);
      //#endregion

      // -----图斑 滨二居委-----
      //#region
      var binErCommiteePath = [
        [121.32438, 30.728655],
        [121.324821, 30.727384],
        [121.326217, 30.727394],
        [121.328221, 30.72764],
        [121.327804, 30.729352],
      ];

      var binErCommitee = new AMap.Polygon({
        path: binErCommiteePath,
        strokeColor: "#9dd6e0", // 线颜色
        strokeOpacity: 1, // 线透明度
        strokeWeight: 6, // 线宽
        // strokeStyle: "dashed",
        strokeDasharray: [50, 50],
        fillColor: "#9dd6e0", // 填充色
        fillOpacity: 0.2, // 填充透明度
      });
      var binErCommiteeText = new AMap.Text({
        text: "滨二居委",
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "60px",
          color: "#FFF",
        },
        position: [121.326365, 30.728191],
        // offset: [0, 8],
      });
      binErCommitee.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.windowLeave();
      });
      this.patternSpot.push(binErCommitee, binErCommiteeText);
      //#endregion

      // 应急通道
      //#region
      var emergencyAccessPath01 = new this.AMap.Polyline({
        path: [
          new this.AMap.LngLat("121.34632 ", "30.716833"),
          new this.AMap.LngLat("121.346454", "30.716856"),
          new this.AMap.LngLat("121.346902", "30.716918"),
          new this.AMap.LngLat("121.347199", "30.716985"),
          new this.AMap.LngLat("121.347322", "30.717005"),
          new this.AMap.LngLat("121.347499", "30.717103"),
          new this.AMap.LngLat("121.347689", "30.717146"),
          new this.AMap.LngLat("121.348807", "30.71733 "),
          new this.AMap.LngLat("121.348905", "30.716911"),
          new this.AMap.LngLat("121.348973", "30.716652"),
          new this.AMap.LngLat("121.349031", "30.716389"),
          new this.AMap.LngLat("121.349474", "30.716426"),
          new this.AMap.LngLat("121.349636", "30.716474"),
          new this.AMap.LngLat("121.349864", "30.716518"),
        ],
      });
      var emergencyAccessPath02 = new this.AMap.Polyline({
        path: [
          new this.AMap.LngLat("121.348556", "30.718483"),
          new this.AMap.LngLat("121.348807", "30.71733 "),
        ],
      });

      var emergencyAccessLine = new AMap.OverlayGroup([
        emergencyAccessPath01,
        emergencyAccessPath02,
      ]);

      emergencyAccessLine.setOptions({
        strokeWeight: 20, // 线宽
        strokeColor: "red",
        strokeOpacity: 1,
        cursor: "pointer",
      });

      // 点击应急通道对象，生成遮罩物
      emergencyAccessLine.on("click", (ev) => {
        this.windowLeave();
        if (!this.infoWindow) {
          this.isEmergencyLoadShow = true;
          this.$nextTick(() => {
            this.infoWindow = new AMap.InfoWindow({
              isCustom: true, // 使用自定义窗体
              anchor: "middle-left",
              content: this.$refs.emergencyLoadWindow.$el, // 传入 dom 对象，或者 html 字符串
              offset: new AMap.Pixel(0, 0),
            });
            var position = new AMap.LngLat(121.348237, 30.717235);
            this.infoWindow.open(this.map, position);
          });
        }
      });
      //#endregion

      // ------房地调查数据库------
      //#region
      // ------石化第一小学------
      var shihuaPrimarySchoolPath = [
        new AMap.LngLat("121.347698", "30.717147"),
        new AMap.LngLat("121.348801", "30.717329"),
        new AMap.LngLat("121.348897", "30.716908"),
        new AMap.LngLat("121.348965", "30.716655"),
        new AMap.LngLat("121.349023", "30.716387"),
        new AMap.LngLat("121.348936", "30.716378"),
        new AMap.LngLat("121.348633", "30.716324"),
        new AMap.LngLat("121.348478", "30.716319"),
        new AMap.LngLat("121.348387", "30.716337"),
        new AMap.LngLat("121.347896", "30.716588"),
        new AMap.LngLat("121.3478", "30.716701"),
        new AMap.LngLat("121.347771", "30.71682"),
        new AMap.LngLat("121.347747", "30.716943"),
        new AMap.LngLat("121.347698", "30.717147"),
      ];
      var shihuaPrimarySchool = new this.AMap.Polygon({
        path: shihuaPrimarySchoolPath,
        strokeColor: "#DF3D52", // 线颜色
        cursor: "pointer",
        strokeWeight: 4, // 线宽
        fillColor: "#DF3D52", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
      });
      // 石化第一小学 - 标注
      var shihuaPrimarySchoolText = new AMap.Text({
        text: "石化第一小学",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "36px",
          color: "#FFF",
        },
        position: [121.348407, 30.716843],
        // offset: [0, 8],
      });
      // 石化小学的点击事件
      shihuaPrimarySchool.on("click", (ev) => {
        var position = new AMap.LngLat(121.348369, 30.716825);
        var offset = new AMap.Pixel(400, 0);
        this.landInfoHandle(0, "middle-left", offset, position);
      });
      // 石化小学的鼠标进入范围事件
      shihuaPrimarySchool.on("mouseover", (ev) => {
        shihuaPrimarySchool.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 石化小学的鼠标移除范围事件
      shihuaPrimarySchool.on("mouseout", (ev) => {
        shihuaPrimarySchool.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------临潮幼儿园------
      var linchaoKindergartenPath = [
        new AMap.LngLat("121.347655", "30.717109"),
        new AMap.LngLat("121.347513", "30.717077"),
        new AMap.LngLat("121.347333", "30.716976"),
        new AMap.LngLat("121.347173", "30.716947"),
        new AMap.LngLat("121.347361", "30.716037"),
        new AMap.LngLat("121.347687", "30.715902"),
        new AMap.LngLat("121.347741", "30.716025"),
        new AMap.LngLat("121.347813", "30.716013"),
        new AMap.LngLat("121.348124", "30.716438"),
        new AMap.LngLat("121.347773", "30.7166"),
        new AMap.LngLat("121.347655", "30.717109"),
      ];
      var linchaoKindergarten = new this.AMap.Polygon({
        path: linchaoKindergartenPath,
        strokeColor: "#DF3D52", // 线颜色
        // strokeOpacity: 0.2, // 线透明度
        strokeWeight: 4, // 线宽
        fillColor: "#DF3D52", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
      });
      // 临潮幼儿园 - 标注
      var linchaoKindergartenText = new AMap.Text({
        text: "临潮幼儿园",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "36px",
          color: "#FFF",
        },
        position: [121.347551, 30.716516],
        // offset: [0, 8],
      });
      // 临潮幼儿园的点击事件
      linchaoKindergarten.on("click", (ev) => {
        var position = new AMap.LngLat(121.347607, 30.716571);
        var offset = new AMap.Pixel(300, 0);
        this.landInfoHandle(1, "middle-left", offset, position);
      });
      // 临潮幼儿园的鼠标进入范围事件
      linchaoKindergarten.on("mouseover", (ev) => {
        linchaoKindergarten.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 临潮幼儿园的鼠标移除范围事件
      linchaoKindergarten.on("mouseout", (ev) => {
        linchaoKindergarten.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------石化街道综合行政执法队------
      var enforceLawTeamPath = [
        new AMap.LngLat("121.35008 ", "30.718402"),
        new AMap.LngLat("121.350104", "30.718286"),
        new AMap.LngLat("121.349674", "30.718213"),
        new AMap.LngLat("121.349651", "30.718328"),
      ];
      var enforceLawTeam = new this.AMap.Polygon({
        path: enforceLawTeamPath,
        strokeColor: "#DF3D52", // 线颜色
        strokeWeight: 4, // 线宽
        fillColor: "#DF3D52", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
      });
      // 综合行政执法队 - 标注
      var enforceLawTeamText = new AMap.Text({
        text: "石化街道综合行政执法队",
        anchor: "center", // 设置文本标记锚点
        cursor: "pointer",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "36px",
          color: "#FFF",
        },
        angle: -10,
        position: [121.349857, 30.718307],
        offset: [10, -50],
        zIndex: 1,
      });
      // 综合行政执法队的点击事件
      enforceLawTeam.on("click", (ev) => {
        var position = new AMap.LngLat(121.349857, 30.718307);
        var offset = new AMap.Pixel(100, 0);
        this.landInfoHandle(2, "top-left", offset, position);
      });
      // 综合行政执法队的鼠标进入范围事件
      enforceLawTeam.on("mouseover", (ev) => {
        enforceLawTeam.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 综合行政执法队的鼠标移除范围事件
      enforceLawTeam.on("mouseout", (ev) => {
        enforceLawTeam.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------28号楼------
      var floorInfo28Path = [
        [121.3483, 30.71771],
        [121.348321, 30.717603],
        [121.348721, 30.717663],
        [121.348694, 30.717782],
      ];

      var floorInfo28 = new this.AMap.Polygon({
        path: floorInfo28Path,
        strokeColor: "#DF3D52", // 线颜色
        strokeWeight: 4, // 线宽
        fillColor: "#DF3D52", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
      });
      // 28号楼的点击事件
      floorInfo28.on("click", (ev) => {
        var position = new AMap.LngLat(121.348519, 30.717699);
        var offset = new AMap.Pixel(150, 0);
        this.landInfoHandle(3, "top-left", offset, position);
      });
      // 28号楼的鼠标进入范围事件
      floorInfo28.on("mouseover", (ev) => {
        floorInfo28.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 28号楼的鼠标移除范围事件
      floorInfo28.on("mouseout", (ev) => {
        floorInfo28.setOptions({
          fillOpacity: 0.35,
        });
      });

      //#endregion

      // ------绿化------
      //#region
      // ------公共绿化 - 街心花园------
      var publicGreenPath01 = [
        [121.346463, 30.716277],
        [121.346507, 30.716075],
        [121.346826, 30.715823],
        [121.347604, 30.715694],
        [121.347685, 30.715893],
        [121.347304, 30.716061],
        [121.347288, 30.716125],
        [121.346992, 30.716144],
        [121.346536, 30.71629],
      ];

      var publicGreenPolygon01 = new this.AMap.Polygon({
        path: publicGreenPath01,
        strokeColor: "#39E389", // 线颜色
        // strokeOpacity: 0.2, // 线透明度
        strokeWeight: 4, // 线宽
        fillColor: "#39E389", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
        zIndex: 100,
      });
      // 公共绿化的点击事件
      publicGreenPolygon01.on("click", (ev) => {
        var position = new AMap.LngLat(121.347079, 30.715984);
        var offset = new AMap.Pixel(250, 0);
        this.greeningHandle(0, "middle-left", offset, position);
        console.log(ev.lnglat.lng, ev.lnglat.lat);
      });
      publicGreenPolygon01.on("mouseover", (ev) => {
        publicGreenPolygon01.setOptions({
          fillOpacity: 0.7,
        });
      });
      publicGreenPolygon01.on("mouseout", (ev) => {
        publicGreenPolygon01.setOptions({
          fillOpacity: 0.35,
        });
      });

      // 事件上报提示
      var map_icon_eventTips = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/92ae2d84d6d38c65e6dfd71e07d4101f.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      this.eventTips = new AMap.Marker({
        position: new AMap.LngLat(121.347008, 30.715974),
        icon: map_icon_eventTips,
        offset: new AMap.Pixel(-42, -109),
      });

      // 未读则添加图标
      // if (!this.isReaded) {
      //   console.log("阅读状态" + this.isReaded);
      //   this.greeningArr.push(this.eventTips);
      // }

      // ------单位绿化------
      var unitGreenPath = [
        [121.348139, 30.716488],
        [121.348149, 30.716507],
        [121.348166, 30.716516],
        [121.348194, 30.716514],
        [121.348297, 30.716458],
        [121.348408, 30.716391],
        [121.348482, 30.716378],
        [121.348626, 30.716376],
        [121.348863, 30.716422],
        [121.348896, 30.716427],
        [121.348915, 30.716447],
        [121.348922, 30.716464],
        [121.348881, 30.716634],
        [121.348813, 30.716894],
        [121.348745, 30.717181],
        [121.348732, 30.717209],
        [121.348705, 30.717227],
        [121.348682, 30.717241],
        [121.348653, 30.717245],
        [121.347921, 30.717126],
        [121.347777, 30.717103],
        [121.347758, 30.717112],
        [121.347757, 30.717131],
        [121.347772, 30.717141],
        [121.348785, 30.717308],
        [121.34888, 30.716905],
        [121.348948, 30.716648],
        [121.348991, 30.716452],
        [121.348998, 30.716417],
        [121.348999, 30.716397],
        [121.348981, 30.716396],
        [121.348934, 30.716392],
        [121.348628, 30.716333],
        [121.34848, 30.716335],
        [121.348397, 30.716348],
        [121.348139, 30.716487],
      ];
      var unitGreen = new this.AMap.Polygon({
        path: unitGreenPath,
        strokeColor: "#39E389", // 线颜色
        // strokeOpacity: 0.2, // 线透明度
        strokeWeight: 4, // 线宽
        fillColor: "#39E389", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
      });
      // 公共绿化的点击事件
      unitGreen.on("click", (ev) => {
        var position = new AMap.LngLat(121.348856, 30.716839);
        var offset = new AMap.Pixel(100, 0);
        this.greeningHandle(1, "middle-left", offset, position);
      });
      unitGreen.on("mouseover", (ev) => {
        unitGreen.setOptions({
          fillOpacity: 0.7,
        });
      });
      unitGreen.on("mouseout", (ev) => {
        unitGreen.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------售后公房绿化------
      var afterSaleGreenPath = [
        [121.346991, 30.717475],
        [121.347073, 30.717488],
        [121.347111, 30.717355],
        [121.347139, 30.717234],
        [121.346287, 30.71709],
        [121.346231, 30.71735],
        [121.346441, 30.717386],
        [121.346447, 30.717349],
        [121.346379, 30.717338],
        [121.346397, 30.717223],
        [121.346571, 30.717258],
        [121.346554, 30.717367],
        [121.346494, 30.717358],
        [121.346486, 30.717393],
        [121.34665, 30.71742],
        [121.34666, 30.717377],
        [121.346624, 30.71737],
        [121.346643, 30.717267],
        [121.34708, 30.717339],
        [121.347062, 30.717448],
        [121.346981, 30.717433],
        [121.346973, 30.717463],
        [121.346932, 30.717456],
        [121.346939, 30.717425],
        [121.34684, 30.717407],
        [121.346834, 30.717441],
        [121.346786, 30.717432],
        [121.346792, 30.7174],
        [121.346715, 30.717386],
        [121.346706, 30.717429],
        [121.346991, 30.717475],
      ];
      var afterSaleGreen = new this.AMap.Polygon({
        path: afterSaleGreenPath,
        strokeColor: "#39E389", // 线颜色
        // strokeOpacity: 0.2, // 线透明度
        strokeWeight: 4, // 线宽
        fillColor: "#39E389", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
        cursor: "pointer",
      });
      // 公共绿化的点击事件
      afterSaleGreen.on("click", (ev) => {
        var position = new AMap.LngLat(121.346745, 30.71724);
        var offset = new AMap.Pixel(220, 0);
        this.greeningHandle(2, "middle-left", offset, position);
      });
      afterSaleGreen.on("mouseover", (ev) => {
        afterSaleGreen.setOptions({
          fillOpacity: 0.7,
        });
      });
      afterSaleGreen.on("mouseout", (ev) => {
        afterSaleGreen.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------公共绿化 - 机非隔离绿化------
      var publicGreenPath02 = [
        new AMap.LngLat("121.34588 ", "30.718171"),
        new AMap.LngLat("121.345899", "30.718086"),
        new AMap.LngLat("121.350876", "30.718843"),
        new AMap.LngLat("121.350857", "30.718937"),
      ];
      var publicGreenPolygon02 = new AMap.Polygon({
        path: publicGreenPath02,
        strokeColor: "#39E389", // 线颜色
        strokeWeight: 4, // 线宽
        fillColor: "#39E389", // 填充色
        fillOpacity: 0.35, // 填充透明度
        strokeStyle: "dashed", // 虚线轮廓
      });
      publicGreenPolygon02.on("click", (ev) => {
        var position = new AMap.LngLat(121.347758, 30.718399);
        var offset = new AMap.Pixel(0, 20);
        this.greeningHandle(3, "top-left", offset, position);
      });
      publicGreenPolygon02.on("mouseover", (ev) => {
        publicGreenPolygon02.setOptions({
          fillOpacity: 0.7,
        });
      });
      publicGreenPolygon02.on("mouseout", (ev) => {
        publicGreenPolygon02.setOptions({
          fillOpacity: 0.35,
        });
      });
      //#endregion

      // ------镂空绿化------
      //#region

      // 72-76 79-80
      let pointers_72_80 = {
        outer: [
          [121.348481, 30.715784],
          [121.348347, 30.715561],
          [121.349866, 30.71535],
          [121.35011, 30.715385],
          [121.350053, 30.71562],
          [121.349974, 30.715623],
          [121.349936, 30.715629],
          [121.349938, 30.71561],
          [121.349903, 30.715608],
          [121.349894, 30.715634],
          [121.349819, 30.715644],
          [121.349825, 30.715594],
          [121.349779, 30.71559],
          [121.349768, 30.715653],
          [121.349566, 30.71568],
          [121.349567, 30.715658],
          [121.349524, 30.715653],
          [121.349519, 30.715687],
          [121.349426, 30.715697],
          [121.349431, 30.715646],
          [121.349392, 30.715643],
          [121.349386, 30.715702],
          [121.349181, 30.715721],
          [121.349186, 30.715688],
          [121.349139, 30.715682],
          [121.349127, 30.715721],
          [121.348959, 30.715727],
          [121.348907, 30.715734],
          [121.348911, 30.715715],
          [121.348865, 30.715707],
          [121.348859, 30.715746],
          [121.348685, 30.715789],
          [121.348622, 30.715784],
          [121.348624, 30.715765],
          [121.348592, 30.715761],
          [121.348587, 30.715782],
          [121.348494, 30.715774],
        ],
        inner: [
          [121.348527, 30.715751],
          [121.348547, 30.71562],
          [121.348713, 30.715643],
          [121.348704, 30.715697],
          [121.348819, 30.715629],
          [121.348824, 30.715579],
          [121.349003, 30.715609],
          [121.348999, 30.715655],
          [121.349095, 30.715577],
          [121.349104, 30.715533],
          [121.349276, 30.715562],
          [121.349269, 30.715607],
          [121.349334, 30.715557],
          [121.349339, 30.715506],
          [121.349648, 30.715541],
          [121.349641, 30.715586],
          [121.349723, 30.715508],
          [121.349728, 30.715448],
          [121.350023, 30.715482],
          [121.350004, 30.715609],
          [121.349708, 30.715577],

          [121.349717, 30.715536],
          [121.349636, 30.715604],
          [121.349627, 30.715658],
          [121.349329, 30.715633],
          [121.34933, 30.71558],
          [121.349263, 30.715631],
          [121.349248, 30.715693],
          [121.349084, 30.715669],
          [121.349089, 30.7156],
          [121.348995, 30.715672],
          [121.348986, 30.715722],
          [121.348961, 30.715723],
          [121.348826, 30.715699],
          [121.348826, 30.715646],
          [121.3487, 30.715717],
          [121.348689, 30.715776],
        ],
      };
      // 77-78
      let pointers_77_78 = {
        outer: [
          [121.349658, 30.715851],
          [121.349593, 30.715795],
          [121.349618, 30.715698],
          [121.349964, 30.715657],
          [121.350044, 30.715655],
          [121.34999, 30.715877],
        ],
        inner: [
          [121.349639, 30.715834],
          [121.349654, 30.715713],
          [121.349957, 30.715743],
          [121.349946, 30.715869],
        ],
      };
      // 67_71
      let pointers_67_71 = {
        outer: [
          [121.349165, 30.716125],
          [121.349185, 30.715995],
          [121.349273, 30.715958],
          [121.349434, 30.715988],
          [121.349567, 30.716142],
          [121.349782, 30.716227],
          [121.349792, 30.716197],
          [121.349593, 30.71611],
          [121.349514, 30.716025],
          [121.34966, 30.715926],
          [121.349668, 30.715881],
          [121.349976, 30.715919],
          [121.349842, 30.716424],
          [121.349815, 30.716494],
          [121.34964, 30.71646],
          [121.349448, 30.716409],
          [121.349468, 30.716306],
          [121.349412, 30.716196],
          [121.349338, 30.716149],
        ],
        inner: [
          [121.349202, 30.716103],
          [121.349318, 30.716133],
          [121.349326, 30.716108],
          [121.349412, 30.716128],
          [121.349591, 30.716391],
          [121.349778, 30.716418],
          [121.349847, 30.716335],
          [121.349764, 30.716261],
          [121.349736, 30.716308],
          [121.349668, 30.716299],
          [121.349469, 30.716031],
          [121.349287, 30.715986],
          [121.349271, 30.716035],
          [121.349216, 30.716021],
        ],
      };
      // 62_65
      let pointers_62_65 = {
        outer: [
          [121.34846, 30.716012],
          [121.348603, 30.716035],
          [121.348665, 30.716068],
          [121.348678, 30.71613],
          [121.34865, 30.716311],
          [121.348939, 30.716364],
          [121.349407, 30.716402],
          [121.349428, 30.716314],
          [121.349383, 30.716221],
          [121.349324, 30.716185],
          [121.349125, 30.716148],
          [121.348854, 30.715776],
          [121.348685, 30.715819],
          [121.348504, 30.715807],
          [121.348476, 30.715837],
        ],
        inner: [
          [121.348477, 30.716002],
          [121.348494, 30.715905],
          [121.348565, 30.715916],
          [121.348575, 30.715857],
          [121.348741, 30.715885],
          [121.348958, 30.716182],
          [121.349024, 30.716196],
          [121.348989, 30.716297],
          [121.348861, 30.716244],
          [121.348658, 30.715971],
          [121.348618, 30.715968],
          [121.348608, 30.716032],
          [121.348475, 30.716002],
        ],
      };
      // 57_61
      let pointers_57_61 = {
        outer: [
          [121.348392, 30.716326],
          [121.348352, 30.716293],
          [121.348179, 30.71628],
          [121.348098, 30.716253],
          [121.348067, 30.716234],
          [121.34797, 30.716072],
          [121.347923, 30.716032],
          [121.347852, 30.716003],
          [121.347755, 30.716006],
          [121.347619, 30.715682],
          [121.348306, 30.715567],
          [121.348456, 30.715804],
          [121.348438, 30.715827],
          [121.348426, 30.716021],
          [121.348461, 30.716311],
          [121.348446, 30.716315],
          [121.348367, 30.716221],
          [121.34833, 30.716247],
          [121.348404, 30.716321],
        ],
        inner: [
          [121.347729, 30.715905],
          [121.347952, 30.715848],
          [121.348191, 30.716154],
          [121.348278, 30.716167],
          [121.348329, 30.716126],
          [121.348389, 30.716204],
          [121.348282, 30.716283],
          [121.348199, 30.716277],
          [121.348124, 30.716238],
          [121.347892, 30.715961],
          [121.347768, 30.715993],
        ],
      };
      let pointers_jieXin = {
        outer: [
          [121.346463, 30.716277],
          [121.346507, 30.716075],
          [121.346826, 30.715823],
          [121.347604, 30.715694],
          [121.347685, 30.715893],
          [121.347304, 30.716061],
          [121.347288, 30.716125],
          [121.346992, 30.716144],
          [121.346536, 30.71629],
        ],
        inner: [
          [121.347165, 30.715961],
          [121.346979, 30.715913],
          [121.346885, 30.716044],
        ],
      };
      let pointers_48_52 = {
        outer: [
          [121.346401, 30.716551],
          [121.346974, 30.716635],
          [121.347113, 30.716152],
          [121.346993, 30.716159],
          [121.346543, 30.716304],
          [121.346461, 30.716291],
          [121.346401, 30.716551],
        ],
        inner: [
          [121.346476, 30.716484],
          [121.346494, 30.71636],
          [121.346511, 30.716362],
          [121.34652, 30.716326],
          [121.346944, 30.716378],
          [121.346945, 30.71635],
          [121.346884, 30.71634],
          [121.3469, 30.716226],
          [121.347063, 30.716255],
          [121.347042, 30.716368],
          [121.346984, 30.716359],
          [121.346981, 30.716382],
          [121.347038, 30.716389],
          [121.347029, 30.716416],
          [121.34653, 30.716354],
          [121.346528, 30.716367],
          [121.346668, 30.71639],
          [121.346662, 30.716434],
          [121.346989, 30.716481],
          [121.346972, 30.716605],
          [121.346618, 30.71655],
          [121.346627, 30.716505],
          [121.346474, 30.716485],
        ],
      };
      let pointers_22_25 = {
        outer: [
          [121.346991, 30.717475],
          [121.347073, 30.717488],
          [121.347111, 30.717355],
          [121.347139, 30.717234],
          [121.346287, 30.71709],
          [121.346231, 30.71735],
          [121.346441, 30.717386],
          [121.346447, 30.717349],
          [121.346379, 30.717338],
          [121.346397, 30.717223],
          [121.346571, 30.717258],
          [121.346554, 30.717367],
          [121.346494, 30.717358],
          [121.346486, 30.717393],
          [121.34665, 30.71742],
          [121.34666, 30.717377],
          [121.346624, 30.71737],
          [121.346643, 30.717267],
          [121.34708, 30.717339],
          [121.347062, 30.717448],
          [121.346981, 30.717433],
          [121.346973, 30.717463],
          [121.346932, 30.717456],
          [121.346939, 30.717425],
          [121.34684, 30.717407],
          [121.346834, 30.717441],
          [121.346786, 30.717432],
          [121.346792, 30.7174],
          [121.346715, 30.717386],
          [121.346706, 30.717429],
          [121.346991, 30.717475],
        ],
        inner: [
          [121.346632, 30.717204],
          [121.346779, 30.717198],
          [121.346908, 30.717265],
        ],
      };
      let pointers_14_16_34_36 = {
        outer: [
          [121.347083, 30.71763],
          [121.347106, 30.717509],
          [121.347148, 30.717362],
          [121.347181, 30.717226],
          [121.347222, 30.717008],
          [121.347318, 30.717026],
          [121.347492, 30.717121],
          [121.347676, 30.717159],
          [121.347547, 30.717727],
          [121.347086, 30.717631],
        ],
        inner: [
          [121.347107, 30.717605],
          [121.34713, 30.717492],
          [121.347172, 30.717356],
          [121.34744, 30.71741],
          [121.347446, 30.717375],
          [121.347361, 30.717359],
          [121.347228, 30.717132],
          [121.347335, 30.717087],
          [121.347444, 30.717279],
          [121.347508, 30.717289],
          [121.347532, 30.717242],
          [121.347583, 30.717228],
          [121.347647, 30.717323],
          [121.347556, 30.71739],
          [121.347499, 30.717379],
          [121.347493, 30.717419],
          [121.347609, 30.717443],
          [121.347603, 30.717471],
          [121.347185, 30.717388],
          [121.347151, 30.717495],
          [121.347578, 30.717576],
          [121.347553, 30.717691],
          [121.347115, 30.717608],
        ],
      };
      let pointers_17_19_26_27 = {
        outer: [
          [121.347538, 30.717945],
          [121.347572, 30.717756],
          [121.347642, 30.717465],
          [121.347663, 30.717379],
          [121.348291, 30.717491],
          [121.348256, 30.71777],
          [121.348191, 30.718058],
          [121.34754, 30.717948],
        ],
        inner: [
          [121.347698, 30.717901],
          [121.348023, 30.71795],
          [121.348015, 30.717993],
          [121.348191, 30.718024],
          [121.34822, 30.717898],
          [121.348065, 30.717872],
          [121.348074, 30.717834],
          [121.347736, 30.717775],
          [121.347763, 30.717624],
          [121.34812, 30.717679],
          [121.348138, 30.71757],
          [121.347773, 30.717513],
          [121.347755, 30.717621],
          [121.347728, 30.71777],
          [121.347721, 30.717776],
          [121.347698, 30.717902],
        ],
      };
      let pointers_yiXiao = {
        outer: [
          [121.348139, 30.716488],
          [121.348149, 30.716507],
          [121.348166, 30.716516],
          [121.348194, 30.716514],
          [121.348297, 30.716458],
          [121.348408, 30.716391],
          [121.348482, 30.716378],
          [121.348626, 30.716376],
          [121.348863, 30.716422],
          [121.348896, 30.716427],
          [121.348915, 30.716447],
          [121.348922, 30.716464],
          [121.348881, 30.716634],
          [121.348813, 30.716894],
          [121.348745, 30.717181],
          [121.348732, 30.717209],
          [121.348705, 30.717227],
          [121.348682, 30.717241],
          [121.348653, 30.717245],
          [121.347921, 30.717126],
          [121.347777, 30.717103],
          [121.347758, 30.717112],
          [121.347757, 30.717131],
          [121.347772, 30.717141],
          [121.348785, 30.717308],
          [121.34888, 30.716905],
          [121.348948, 30.716648],
          [121.348991, 30.716452],
          [121.348998, 30.716417],
          [121.348999, 30.716397],
          [121.348981, 30.716396],
          [121.348934, 30.716392],
          [121.348628, 30.716333],
          [121.34848, 30.716335],
          [121.348397, 30.716348],
          [121.348139, 30.716487],
        ],
        inner: [
          [121.348891, 30.716775],
          [121.348881, 30.716814],
          [121.348856, 30.716789],
        ],
      };
      let pointers_23_24_33_34 = {
        outer: [
          [121.348687, 30.717964],
          [121.348719, 30.717775],
          [121.348813, 30.717346],
          [121.348949, 30.71742],
          [121.349258, 30.717478],
          [121.349197, 30.717507],
          [121.349169, 30.717621],
          [121.349172, 30.717735],
          [121.348943, 30.717725],
          [121.348945, 30.717749],
          [121.34918, 30.717758],
          [121.349226, 30.717776],
          [121.349193, 30.717993],
          [121.34918, 30.718017],
          [121.349157, 30.718026],
          [121.348686, 30.717965],
        ],
        inner: [
          [121.348848, 30.717966],
          [121.348963, 30.717989],
          [121.348974, 30.717935],
          [121.34904, 30.717948],
          [121.349184, 30.717979],
          [121.349202, 30.717887],
          [121.349052, 30.717847],
          [121.349032, 30.717932],
          [121.348978, 30.717922],
          [121.348992, 30.717849],
          [121.348885, 30.717827],
          [121.348903, 30.717699],
          [121.349022, 30.717721],
          [121.349026, 30.717689],
          [121.349154, 30.71771],
          [121.349166, 30.717618],
          [121.349022, 30.717601],
          [121.349015, 30.717624],
          [121.348882, 30.717604],
          [121.348869, 30.71769],
          [121.348885, 30.717693],
          [121.348867, 30.717823],
          [121.348873, 30.717834],
          [121.348847, 30.717963],
        ],
      };
      let pointers_gongLu = {
        outer: [
          [121.345909, 30.718068],
          [121.347439, 30.718289],
          [121.348556, 30.718486],
          [121.34854, 30.718566],
          [121.347424, 30.718378],
          [121.345893, 30.718151],
        ],
        inner: [
          [121.345905, 30.718139],
          [121.347383, 30.718359],
          [121.347481, 30.718374],
          [121.348531, 30.718554],
          [121.348536, 30.718536],
          [121.347484, 30.718355],
          [121.347489, 30.718333],
          [121.34854, 30.718516],
          [121.348546, 30.718497],
          [121.347496, 30.718316],
          [121.347396, 30.718299],
          [121.345914, 30.718081],
          [121.345911, 30.718101],
          [121.347393, 30.718316],
          [121.347386, 30.718343],
          [121.345905, 30.718123],
        ],
      };

      let polygonOptions = {
        strokeColor: "#39E389",
        strokeWeight: 4,
        fillColor: "#39E389",
        fillOpacity: 0.35,
        strokeStyle: "dashed",
      };

      let polygon_72_80 = new AMap.Polygon(polygonOptions);
      let polygon_77_78 = new AMap.Polygon(polygonOptions);
      let polygon_67_71 = new AMap.Polygon(polygonOptions);
      let polygon_62_65 = new AMap.Polygon(polygonOptions);
      let polygon_57_61 = new AMap.Polygon(polygonOptions);
      // let polygon_jieXin = new AMap.Polygon(polygonOptions);
      let polygon_48_52 = new AMap.Polygon(polygonOptions);
      // let polygon_22_25 = new AMap.Polygon(polygonOptions);
      let polygon_14_16_34_36 = new AMap.Polygon(polygonOptions);
      let polygon_17_19_26_27 = new AMap.Polygon(polygonOptions);
      // let polygon_yiXiao = new AMap.Polygon(polygonOptions);
      let polygon_23_24_33_34 = new AMap.Polygon(polygonOptions);
      let polygon_gongLu = new AMap.Polygon(polygonOptions);

      polygon_72_80.setPath([pointers_72_80.outer, pointers_72_80.inner]);
      polygon_77_78.setPath([pointers_77_78.outer, pointers_77_78.inner]);
      polygon_67_71.setPath([pointers_67_71.outer, pointers_67_71.inner]);
      polygon_62_65.setPath([pointers_62_65.outer, pointers_62_65.inner]);
      polygon_57_61.setPath([pointers_57_61.outer, pointers_57_61.inner]);
      // polygon_jieXin.setPath([pointers_jieXin.outer, pointers_jieXin.inner]);
      polygon_48_52.setPath([pointers_48_52.outer, pointers_48_52.inner]);
      // polygon_22_25.setPath([pointers_22_25.outer, pointers_22_25.inner]);
      polygon_14_16_34_36.setPath([
        pointers_14_16_34_36.outer,
        pointers_14_16_34_36.inner,
      ]);
      polygon_17_19_26_27.setPath([
        pointers_17_19_26_27.outer,
        pointers_17_19_26_27.inner,
      ]);
      // polygon_yiXiao.setPath([pointers_yiXiao.outer, pointers_yiXiao.inner]);
      polygon_23_24_33_34.setPath([
        pointers_23_24_33_34.outer,
        pointers_23_24_33_34.inner,
      ]);
      polygon_gongLu.setPath([pointers_gongLu.outer, pointers_gongLu.inner]);

      // 非机隔离绿化，点击弹窗
      polygon_gongLu.on("click", (ev) => {
        var position = new AMap.LngLat(121.347758, 30.718399);
        var offset = new AMap.Pixel(0, 20);
        this.greeningHandle(3, "top-left", offset, position);
      });

      this.greenPolygonArr.push(
        polygon_72_80,
        polygon_77_78,
        polygon_67_71,
        polygon_62_65,
        polygon_57_61,
        // polygon_jieXin,
        polygon_48_52,
        // polygon_22_25,
        polygon_14_16_34_36,
        polygon_17_19_26_27,
        // polygon_yiXiao,
        polygon_23_24_33_34,
        polygon_gongLu
      );

      // item.on("mouseover", (ev) => {
      //   item.setOptions({
      //     fillOpacity: 0.7,
      //   });
      // });
      // item.on("mouseout", (ev) => {
      //   item.setOptions({
      //     fillOpacity: 0.35,
      //   });
      // });

      this.greenPolygonArr.forEach((item) => {
        item.on("mouseover", (ev) => {
          item.setOptions({
            fillOpacity: 0.7,
          });
        });
        item.on("mouseout", (ev) => {
          item.setOptions({
            fillOpacity: 0.35,
          });
        });
      });

      //#endregion

      // 绿化图斑遮盖物
      //#region

      // 鹦鹉洲
      let yingWuZhouPath = [
        [121.335865, 30.705725],
        [121.337072, 30.706857],
        [121.34092, 30.705073],
        [121.34085, 30.704942],
        [121.340827, 30.704802],
        [121.340827, 30.704666],
        [121.340832, 30.704528],
        [121.3409, 30.704354],
        [121.340933, 30.704167],
        [121.340944, 30.703953],
        [121.340932, 30.703706],
        [121.340897, 30.703422],
        [121.340859, 30.70325],
        [121.340811, 30.703119],
        [121.340784, 30.703002],
        [121.340723, 30.702806],
        [121.340706, 30.702727],
        [121.340692, 30.702589],
        [121.34068, 30.702479],
        [121.340687, 30.702362],
        [121.340697, 30.70225],
        [121.340719, 30.702153],
        [121.340728, 30.702099],
        [121.340747, 30.702022],
        [121.340762, 30.701952],
        [121.340775, 30.701893],
        [121.340791, 30.701802],
        [121.34089, 30.701541],
        [121.341032, 30.701291],
        [121.341167, 30.701082],
        [121.341273, 30.700882],
        [121.341329, 30.7007],
        [121.341342, 30.700441],
        [121.341353, 30.700301],
        [121.341438, 30.7001],
        [121.341648, 30.699903],
        [121.341733, 30.699856],
        [121.342146, 30.699678],
        [121.342421, 30.69956],
        [121.342627, 30.699296],
        [121.34275, 30.699094],
        [121.342716, 30.699016],
        [121.342018, 30.698772],
        [121.341506, 30.69848],
        [121.340624, 30.698165],
        [121.340469, 30.698142],
        [121.33888, 30.697568],
        [121.338481, 30.697655],
        [121.338234, 30.69782],
        [121.338064, 30.698052],
        [121.340122, 30.699713],
        [121.340181, 30.69989],
        [121.339734, 30.701955],
        [121.339386, 30.70216],
        [121.338746, 30.702324],
        [121.338395, 30.703921],
        [121.338321, 30.704063],
        [121.33818, 30.704173],
        [121.335901, 30.705638],
        [121.335862, 30.705723],
      ];

      // 滨海公园
      let bingHaiParkPath = [
        [121.343941, 30.707766],
        [121.344454, 30.705444],
        [121.344931, 30.705055],
        [121.344983, 30.70505],
        [121.34505, 30.705081],
        [121.346901, 30.706819],
        [121.348147, 30.708117],
        [121.348168, 30.70816],
        [121.348163, 30.708241],
        [121.348011, 30.708369],
        [121.347939, 30.708388],
        [121.347841, 30.708383],
        [121.343942, 30.707769],
      ];

      // 荟萃园
      let huiCuiParkPath = [
        [121.3409, 30.711767],
        [121.337892, 30.708947],
        [121.337569, 30.708675],
        [121.337712, 30.708583],
        [121.33852, 30.70824],
        [121.341752, 30.711266],
        [121.341061, 30.71182],
        [121.341006, 30.711787],
        [121.340936, 30.711769],
        [121.3409, 30.711767],
      ];

      // 山龙绿地
      let shanLongParkPath = [
        [121.336297, 30.730919],
        [121.336702, 30.731015],
        [121.336772, 30.730742],
        [121.338262, 30.731056],
        [121.338194, 30.731336],
        [121.338469, 30.731388],
        [121.338588, 30.731327],
        [121.338703, 30.730845],
        [121.337415, 30.729962],
        [121.337138, 30.729877],
        [121.336586, 30.729782],
        [121.336464, 30.729798],
        [121.336345, 30.730272],
        [121.336239, 30.730883],
      ];

      // 梅州新村
      // meiZhouCommiteePath

      // 柳城居民区
      // liuChengCommiteePath

      // 海棠居民区
      // haiTangCommiteePath

      // 十村居民区
      // shiCunCommiteePath

      let spotPolygonOpt = {
        strokeColor: "#39E389", // 线颜色
        // strokeOpacity: 1, // 线透明度
        strokeWeight: 10, // 线宽
        strokeStyle: "dashed",
        fillColor: "#39E389", // 填充色
        fillOpacity: 0.35, // 填充透明度
        cursor: "pointer",
      };

      // ------绿化图斑------
      // 临潮一村
      let linChaoOneSpot = new AMap.Polygon(spotPolygonOpt);
      // 海棠居委
      let haiTangSpot = new AMap.Polygon(spotPolygonOpt);
      // 十村居委
      let shiCunSpot = new AMap.Polygon(spotPolygonOpt);
      // 鹦鹉洲
      let yingWuZhouSpot = new AMap.Polygon(spotPolygonOpt);
      // 滨海公园
      let bingHaiParkSpot = new AMap.Polygon(spotPolygonOpt);
      // 荟萃园
      let huiCuiParkSpot = new AMap.Polygon(spotPolygonOpt);
      // 山龙绿地
      let shanLongParkSpot = new AMap.Polygon(spotPolygonOpt);
      // 梅州居民区
      let meiZhouCommiteeSpot = new AMap.Polygon(spotPolygonOpt);
      // 柳城居民区
      let liuChengCommiteeSpot = new AMap.Polygon(spotPolygonOpt);
      // 海棠居民区
      let haiTangCommiteeSpot = new AMap.Polygon(spotPolygonOpt);
      // 十村居民区
      let shiCunCommiteeSpot = new AMap.Polygon(spotPolygonOpt);

      linChaoOneSpot.setPath(pathOne);
      // haiTangSpot.setPath(haiTangCommiteePath);
      // shiCunSpot.setPath(shiCunCommiteePath);
      yingWuZhouSpot.setPath(yingWuZhouPath);
      bingHaiParkSpot.setPath(bingHaiParkPath);
      huiCuiParkSpot.setPath(huiCuiParkPath);
      shanLongParkSpot.setPath(shanLongParkPath);
      meiZhouCommiteeSpot.setPath(meiZhouCommiteePath);
      liuChengCommiteeSpot.setPath(liuChengCommiteePath);
      haiTangCommiteeSpot.setPath(haiTangCommiteePath);
      shiCunCommiteeSpot.setPath(shiCunCommiteePath);

      this.createSpotText("临潮一村", [121.348348, 30.716954]);
      this.createSpotText("鹦鹉洲 生态湿地", [121.339003, 30.704856]);
      this.createSpotText("滨海公园", [121.345755, 30.707156]);
      this.createSpotText("荟萃园", [121.339689, 30.710039]);
      this.createSpotText("山龙绿地", [121.337343, 30.730554]);
      this.createSpotText("梅州居民区", [121.342379, 30.718347]);
      this.createSpotText("柳城居民区", [121.345159, 30.71233]);
      this.createSpotText("海棠居民区", [121.348959, 30.720334]);
      this.createSpotText("十村居民区", [121.352221, 30.713767]);

      // 点击临潮一村的绿化图斑
      linChaoOneSpot.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.greeningSpot();
      });
      // 点击临潮一村的绿化图斑文本
      this.greeningSpotTextArr[0].on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
        this.greeningSpot();
      });

      // 绿化图斑数组
      this.greeningSpotArr.push(
        linChaoOneSpot,
        yingWuZhouSpot,
        // haiTangSpot,
        // shiCunGpot,
        bingHaiParkSpot,
        huiCuiParkSpot,
        shanLongParkSpot,
        meiZhouCommiteeSpot,
        liuChengCommiteeSpot,
        haiTangCommiteeSpot,
        shiCunCommiteeSpot
      );

      // 给所有绿化图斑都添加移入移出事件
      this.greeningSpotArr.forEach((item, index) => {
        item.on("click", (ev) => {
          console.log(ev.lnglat.lng, ev.lnglat.lat);
        });
        item.on("mouseover", (ev) => {
          item.setOptions({
            fillOpacity: 0.7,
          });
          this.greeningSpotData = this.greeningSpotInfo[index];
          this.isGreeningSpotShow = true;
        });
        item.on("mouseout", (ev) => {
          item.setOptions({
            fillOpacity: 0.35,
          });
          this.isGreeningSpotShow = false;
        });
        // 给所有绿化图斑文本都添加移入移出事件
        this.greeningSpotTextArr[index].on("click", (ev) => {
          console.log(ev.lnglat.lng, ev.lnglat.lat);
        });

        this.greeningSpotTextArr[index].on("mouseover", (ev) => {
          item.setOptions({
            fillOpacity: 0.7,
          });
          this.greeningSpotData = this.greeningSpotInfo[index];
          this.isGreeningSpotShow = true;
        });
        this.greeningSpotTextArr[index].on("mouseout", (ev) => {
          item.setOptions({
            fillOpacity: 0.35,
          });
          this.isGreeningSpotShow = false;
        });
      });

      //#endregion

      // ------小区基础信息------
      // ------居委会-遮盖物------
      //#region
      var map_icon_residentCommittee = new AMap.Icon({
        size: new AMap.Size(84, 109),
        image:
          "https://pic.rmb.bdstatic.com/bjh/bc8116626e4b3430f0ca15ddb0957a8b.png",
        imageSize: new AMap.Size(84, 109),
        imageOffset: new AMap.Pixel(0, 0),
      });
      var residentCommittee = new AMap.Marker({
        position: new AMap.LngLat(121.348502, 30.717795),
        icon: map_icon_residentCommittee,
        offset: new AMap.Pixel(-42, -109),
      });
      residentCommittee.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>居委会</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      residentCommittee.on("click", (ev) => {
        this.committeeClick();
      });

      // ------出入口 icon------
      //#region
      var map_icon_churukou = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(81, 81),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/a26fd8917be205c6e21088d84934f512.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(81, 81),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 1号门 车辆进口
      var doorMarker01 = new AMap.Marker({
        position: new AMap.LngLat(121.347448, 30.718289),
        icon: map_icon_churukou,
        offset: new AMap.Pixel(-41, -41),
      });
      doorMarker01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>出入口</div>", //设置文本标注内容
      });
      doorMarker01.on("click", () => {
        this.entryExitHandle(0, doorMarker01, "top-left");
      });

      // 2号门 电瓶车通道
      var doorMarker02 = new AMap.Marker({
        position: new AMap.LngLat(121.346322, 30.716834),
        icon: map_icon_churukou,
        offset: new AMap.Pixel(-41, -41),
      });
      doorMarker02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>出入口</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      doorMarker02.on("click", () => {
        this.entryExitHandle(1, doorMarker02, "top-left");
      });

      // 3号门 车辆出口
      var doorMarker03 = new AMap.Marker({
        position: new AMap.LngLat(121.349836, 30.716513),
        icon: map_icon_churukou,
        offset: new AMap.Pixel(-41, -41),
      });
      doorMarker03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>出入口</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      doorMarker03.on("click", () => {
        this.entryExitHandle(2, doorMarker03, "top-left");
      });

      var map_icon_churukou_swing = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(81, 81),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/bc09b874b85f2f1608edaddbd7119e68.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(81, 81),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 转门1（临一10号楼旁）
      var revolvingDoorMarker01 = new AMap.Marker({
        position: new AMap.LngLat(121.348556, 30.718483),
        icon: map_icon_churukou_swing,
        offset: new AMap.Pixel(-41, -41),
      });
      revolvingDoorMarker01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>转门</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      revolvingDoorMarker01.on("click", () => {
        this.entryExitHandle(3, revolvingDoorMarker01, "top-left");
      });

      // 转门2（临二4号楼旁）
      var revolvingDoorMarker02 = new AMap.Marker({
        position: new AMap.LngLat(121.349163, 30.718585),
        icon: map_icon_churukou_swing,
        offset: new AMap.Pixel(-41, -41),
      });
      revolvingDoorMarker02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>转门</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      revolvingDoorMarker02.on("click", () => {
        this.entryExitHandle(4, revolvingDoorMarker02, "top-left");
      });

      // 转门3（临一72号楼旁）
      var revolvingDoorMarker03 = new AMap.Marker({
        position: new AMap.LngLat(121.348338, 30.715576),
        icon: map_icon_churukou_swing,
        offset: new AMap.Pixel(-41, -41),
      });
      revolvingDoorMarker03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>转门</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      revolvingDoorMarker03.on("click", () => {
        this.entryExitHandle(5, revolvingDoorMarker03, "bottom-left");
      });

      // 转门4（临一80号楼旁）
      var revolvingDoorMarker04 = new AMap.Marker({
        position: new AMap.LngLat(121.350061, 30.715636),
        icon: map_icon_churukou_swing,
        offset: new AMap.Pixel(-41, -41),
      });
      revolvingDoorMarker04.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>转门</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      revolvingDoorMarker04.on("click", () => {
        this.entryExitHandle(6, revolvingDoorMarker04, "bottom-left");
      });

      //#endregion

      // ------垃圾房 icon------
      //#region
      var map_icon_garbage_room = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/cd8fcacc5d7e0e1bb92f016380c3fd70.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 垃圾房1-临潮一村40号
      var garbageRoom01 = new AMap.Marker({
        position: new AMap.LngLat(121.348346, 30.717463),
        icon: map_icon_garbage_room,
        offset: new AMap.Pixel(-42, -109),
      });
      garbageRoom01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>垃圾房</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      garbageRoom01.on("click", (ev) => {
        this.garbageRoomHandle(0, garbageRoom01, "middle-left");
      });

      // 垃圾房2-临潮一村45号
      var garbageRoom02 = new AMap.Marker({
        position: new AMap.LngLat(121.346961, 30.716904),
        icon: map_icon_garbage_room,
        offset: new AMap.Pixel(-42, -109),
      });
      garbageRoom02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>垃圾房</div>", //设置文本标注内容
      });
      garbageRoom02.on("click", (ev) => {
        this.garbageRoomHandle(1, garbageRoom02, "bottom-left");
      });

      // 垃圾房3-临潮一村62号
      var garbageRoom03 = new AMap.Marker({
        position: new AMap.LngLat(121.348415, 30.715978),
        icon: map_icon_garbage_room,
        offset: new AMap.Pixel(-42, -109),
      });
      garbageRoom03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>垃圾房</div>", //设置文本标注内容
      });
      garbageRoom03.on("click", (ev) => {
        this.garbageRoomHandle(2, garbageRoom03, "bottom-left");
      });

      // 垃圾房4-临潮一村69号
      var garbageRoom04 = new AMap.Marker({
        position: new AMap.LngLat(121.349416, 30.716319),
        icon: map_icon_garbage_room,
        offset: new AMap.Pixel(-42, -109),
      });
      garbageRoom04.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>垃圾房</div>", //设置文本标注内容
      });
      garbageRoom04.on("click", (ev) => {
        this.windowLeave();
        this.garbageRoomHandle(3, garbageRoom04, "bottom-left");
      });

      // ------建筑垃圾和大件垃圾------
      // 创建一个建筑垃圾Icon
      var buildGarbageIcon = new AMap.Icon({
        size: new AMap.Size(60, 60),
        image:
          "https://pic.rmb.bdstatic.com/bjh/9ef5a812b3a62c29088cca6b1c6e80b6.png",
        imageSize: new AMap.Size(60, 60),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 将 icon 传入 marker
      var buildGarbage01 = new AMap.Marker({
        position: new AMap.LngLat(121.34831, 30.715988),
        icon: buildGarbageIcon,
        offset: new AMap.Pixel(-20, -30),
      });
      // 建筑垃圾01的点击事件
      buildGarbage01.on("click", (ev) => {
        var position = new AMap.LngLat(121.34831, 30.715988);
        var offset = new AMap.Pixel(100, 0);
        this.garbageOtherWindow(0, "middle-left", offset, position);
      });
      // 建筑垃圾01的鼠标进入范围事件
      buildGarbage01.on("mouseover", (ev) => {
        buildGarbage01.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 建筑垃圾01的鼠标移除范围事件
      buildGarbage01.on("mouseout", (ev) => {
        buildGarbage01.setOptions({
          fillOpacity: 0.35,
        });
      });

      // 建筑垃圾02
      var buildGarbage02 = new AMap.Marker({
        position: new AMap.LngLat(121.348316, 30.717446),
        icon: buildGarbageIcon,
        offset: new AMap.Pixel(-30, -30),
      });
      // 建筑垃圾02的点击事件
      buildGarbage02.on("click", (ev) => {
        var position = new AMap.LngLat(121.348316, 30.717446);
        var offset = new AMap.Pixel(100, 0);
        this.garbageOtherWindow(0, "middle-left", offset, position);
      });
      // 建筑垃圾02的鼠标进入范围事件
      buildGarbage02.on("mouseover", (ev) => {
        buildGarbage02.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 建筑垃圾02的鼠标移除范围事件
      buildGarbage02.on("mouseout", (ev) => {
        buildGarbage02.setOptions({
          fillOpacity: 0.35,
        });
      });

      // 大件垃圾01
      var bulkyGarbageIcon = new AMap.Icon({
        size: new AMap.Size(60, 60),
        image:
          "https://pic.rmb.bdstatic.com/bjh/2ab124586abc75122d125c76f563d7fb.png",
        imageSize: new AMap.Size(60, 60),
        imageOffset: new AMap.Pixel(0, 0),
      });

      var bulkyGarbage01 = new AMap.Marker({
        position: new AMap.LngLat(121.348401, 30.717426),
        icon: bulkyGarbageIcon,
        offset: new AMap.Pixel(-30, -30),
      });
      // 大件垃圾01的点击事件
      bulkyGarbage01.on("click", (ev) => {
        var position = new AMap.LngLat(121.348401, 30.717426);
        var offset = new AMap.Pixel(100, 0);
        this.garbageOtherWindow(1, "middle-left", offset, position);
      });
      // 大件垃圾01的鼠标进入范围事件
      bulkyGarbage01.on("mouseover", (ev) => {
        bulkyGarbage01.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 大件垃圾01的鼠标移除范围事件
      bulkyGarbage01.on("mouseout", (ev) => {
        bulkyGarbage01.setOptions({
          fillOpacity: 0.35,
        });
      });

      // 大件垃圾02
      var bulkyGarbage02 = new AMap.Marker({
        position: new AMap.LngLat(121.348407, 30.715944),
        icon: bulkyGarbageIcon,
        offset: new AMap.Pixel(-30, -30),
      });
      // 大件垃圾02的点击事件
      bulkyGarbage02.on("click", (ev) => {
        var position = new AMap.LngLat(121.348407, 30.715944);
        var offset = new AMap.Pixel(100, 0);
        this.garbageOtherWindow(1, "middle-left", offset, position);
      });
      // 大件垃圾02的鼠标进入范围事件
      bulkyGarbage02.on("mouseover", (ev) => {
        bulkyGarbage02.setOptions({
          fillOpacity: 0.7,
        });
      });
      // 大件垃圾02的鼠标移除范围事件
      bulkyGarbage02.on("mouseout", (ev) => {
        bulkyGarbage02.setOptions({
          fillOpacity: 0.35,
        });
      });

      // ------凉亭 icon------
      var map_icon_pavilion = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/db22c987b1b39bd012b85fb00156d719.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 凉亭1
      var pavilion01 = new AMap.Marker({
        position: new AMap.LngLat(121.349237, 30.718298),
        icon: map_icon_pavilion,
        offset: new AMap.Pixel(-42, -109),
      });
      pavilion01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>凉亭</div>", //设置文本标注内容
      });
      pavilion01.on("click", (ev) => {
        this.windowLeave();
      });
      // 凉亭2
      var pavilion02 = new AMap.Marker({
        position: new AMap.LngLat(121.349053, 30.715998),
        icon: map_icon_pavilion,
        offset: new AMap.Pixel(-42, -109),
      });
      pavilion02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
        content: "<div>凉亭</div>", //设置文本标注内容
      });
      pavilion02.on("click", (ev) => {
        this.windowLeave();
      });

      // ------配电房 icon------
      var map_icon_power_room = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/2b9d4b2df7598c8e3bcdfca2201a4f5e.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 配电房1
      var powerRoom01 = new AMap.Marker({
        position: new AMap.LngLat(121.348197, 30.717793),
        icon: map_icon_power_room,
        offset: new AMap.Pixel(-42, -109),
      });
      powerRoom01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>配电房</div>", //设置文本标注内容
      });
      powerRoom01.on("click", (ev) => {
        this.windowLeave();
      });

      // 配电房2
      var powerRoom02 = new AMap.Marker({
        position: new AMap.LngLat(121.348223, 30.716032),
        icon: map_icon_power_room,
        offset: new AMap.Pixel(-42, -109),
      });
      powerRoom02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>配电房</div>", //设置文本标注内容
      });
      powerRoom02.on("click", (ev) => {
        this.windowLeave();
      });

      // ------健身点 icon------
      var map_icon_fitness = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image:
          "https://pic.rmb.bdstatic.com/bjh/ed95d3e2e1e8162adc54f7ed2cddf1db.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 健身点
      var fitness = new AMap.Marker({
        position: new AMap.LngLat(121.350253, 30.718258),
        icon: map_icon_fitness,
        offset: new AMap.Pixel(-42, -109),
      });
      fitness.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), //设置文本标注偏移量
        content: "<div>健身点</div>", //设置文本标注内容
      });
      fitness.on("click", (ev) => {
        this.windowLeave();
        this.isBodybuildingShow = true;
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true,
            anchor: "top-left",
            content: this.$refs.bodybuildingWindow.$el,
            offset: new AMap.Pixel(100, -120),
          });
          this.infoWindow.open(
            this.map,
            new AMap.LngLat(121.350253, 30.718258)
          );
        });
      });

      var map_icon_isolateHotel = new AMap.Icon({
        size: new AMap.Size(84, 109),
        image:
          "https://pic.rmb.bdstatic.com/bjh/be81982c50e86fd2a0bf33cc12379363.png",
        imageSize: new AMap.Size(84, 109),
        imageOffset: new AMap.Pixel(0, 0),
      });
      // 隔离酒店-七彩艺术酒店
      var isolateHotel01 = new AMap.Marker({
        position: new AMap.LngLat(121.346289, 30.718498),
        icon: map_icon_isolateHotel,
        offset: new AMap.Pixel(-42, -109),
      });
      isolateHotel01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-10, -25), // 设置文本标注偏移量
        content: "<div>隔离酒店</div>", // 设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      isolateHotel01.on("click", (ev) => {
        this.isIsolateHoteShow = true;
        this.isolateHoteData = this.isolateHoteInfo[0];
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true,
            anchor: "top-left",
            content: this.$refs.isolateHoteWindow.$el,
            offset: new AMap.Pixel(100, -120),
          });
          this.infoWindow.open(
            this.map,
            new AMap.LngLat(121.346289, 30.718498)
          );
        });
      });

      // 隔离酒店-锦江之星
      var isolateHotel02 = new AMap.Marker({
        position: new AMap.LngLat(121.340063, 30.713729),
        icon: map_icon_isolateHotel,
        offset: new AMap.Pixel(-42, -109),
      });
      isolateHotel02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-10, -25), //设置文本标注偏移量
        content: "<div>隔离酒店</div>", //设置文本标注内容
      });
      // 鼠标点击创建-自定义信息弹窗
      isolateHotel02.on("click", (ev) => {
        this.isIsolateHoteShow = true;
        this.isolateHoteData = this.isolateHoteInfo[1];
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true,
            anchor: "top-left",
            content: this.$refs.isolateHoteWindow.$el,
            offset: new AMap.Pixel(100, -120),
          });
          this.infoWindow.open(
            this.map,
            new AMap.LngLat(121.340063, 30.713729)
          );
        });
      });

      this.isolateHoteArr.push(isolateHotel01, isolateHotel02);

      //#endregion

      // ------现状使用主体 - 沿街商铺------
      //#region
      // 隆安路1
      var gather_num01 = new AMap.Text({
        text: "4",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "100px",
          height: "100px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "40px",
          "font-weight": "bold",
          "line-height": "80px",
          color: "#fff",
        },
        position: [121.349656, 30.718564],
      });
      gather_num01.on("click", (ev) => {
        this.windowLeave();
        this.isCurrentUserShow = true;
        this.currentUserData = this.currentUserInfo.num01;
      });

      // 隆安路2
      var gather_num02 = new AMap.Text({
        text: "13",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "100px",
          height: "100px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "40px",
          "font-weight": "bold",
          "line-height": "80px",
          color: "#fff",
        },
        position: [121.348401, 30.718367],
      });
      gather_num02.on("click", (ev) => {
        this.windowLeave();
        this.isCurrentUserShow = true;
        this.currentUserData = this.currentUserInfo.num02;
      });

      // 隆安路3
      var gather_num03 = new AMap.Text({
        text: "13",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "100px",
          height: "100px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "40px",
          "font-weight": "bold",
          "line-height": "80px",
          color: "#fff",
        },
        position: [121.347279, 30.71818],
      });
      gather_num03.on("click", (ev) => {
        this.windowLeave();
        this.isCurrentUserShow = true;
        this.currentUserData = this.currentUserInfo.num03;
      });

      // 临潮街
      var gather_num04 = new AMap.Text({
        text: "9",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "100px",
          height: "100px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "40px",
          "font-weight": "bold",
          "line-height": "80px",
          color: "#fff",
        },
        position: [121.349878, 30.716696],
      });
      gather_num04.on("click", (ev) => {
        this.windowLeave();
        this.isCurrentUserShow = true;
        this.currentUserData = this.currentUserInfo.num04;
      });

      // 临潮二村
      var gather_num05 = new AMap.Text({
        text: "4",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "100px",
          height: "100px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "40px",
          "font-weight": "bold",
          "line-height": "80px",
          color: "#fff",
        },
        position: [121.350698, 30.716796],
      });
      gather_num05.on("click", (ev) => {
        this.windowLeave();
        this.isCurrentUserShow = true;
        this.currentUserData = this.currentUserInfo.num05;
      });

      this.redPoint = gather_num02;
      this.greenPointArr.push(
        gather_num01,
        gather_num03,
        gather_num04,
        gather_num05
      );

      // ------隆安路的点聚合------
      var gather_num_longAn = new AMap.Text({
        text: "30",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "130px",
          height: "130px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "50px",
          "font-weight": "bold",
          "line-height": "110px",
          color: "#fff",
        },
        position: [121.348401, 30.718367],
      });
      gather_num_longAn.on("click", () => {
        // 设置中心点、缩放等级
        this.map.setCenter([121.348401, 30.718367], false, 500);
        this.map.setZoom(20, false, 500);
      });

      // 临潮街的点聚合
      var gather_num_linChao = new AMap.Text({
        text: "13",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        cursor: "pointer",
        style: {
          "border-radius": "100px",
          "background-color": "rgba(255,174,17,0.5000)",
          width: "130px",
          height: "130px",
          "border-width": "10px",
          "border-color": "#FFAE11",
          "text-align": "center",
          "font-size": "50px",
          "font-weight": "bold",
          "line-height": "110px",
          color: "#fff",
        },
        position: [121.350283, 30.716675],
      });
      gather_num_linChao.on("click", () => {
        // 设置中心点、缩放等级
        this.map.setCenter([121.350286, 30.716735], false, 500);
        this.map.setZoom(20, false, 500);
      });

      //#endregion

      // ------楼号 - 标注------
      //#region
      this.buildingNum.forEach((item) => {
        var text = new AMap.Text({
          text: item.title,
          anchor: "center", // 设置文本标记锚点
          cursor: "default",
          style: {
            "background-color": "transparent",
            "border-width": 0,
            "text-align": "center",
            "font-size": "40px",
            color: "#FFF",
          },
          position: item.lnglat,
          offset: [0, 8],
        });

        this.mapTextObjArr.push(text);
      });

      //#endregion

      // ------监控icon点位------
      //#region
      var map_icon_jiankong = new AMap.Icon({
        size: new AMap.Size(84, 109),
        // image: "https://pic.rmb.bdstatic.com/bjh/076f7226a453a740890c2f138eecd685.png",
        image: "https://s1.ax1x.com/2022/10/17/xDdXZt.png",
        imageSize: new AMap.Size(84, 109),
        imageOffset: new AMap.Pixel(0, 0),
      });

      this.controlData.forEach((item, index) => {
        var control = new AMap.Marker({
          position: item.controlLnglat,
          icon: map_icon_jiankong,
          offset: new AMap.Pixel(-41, -41),
        });

        control.on("click", (ev) => {
          this.windowLeave();
          if (!this.infoWindow) {
            this.monitorData = this.controlData[index];
            this.isMonitorShow = true;
            this.$nextTick(() => {
              this.infoWindow = new AMap.InfoWindow({
                isCustom: true, // 使用自定义窗体
                anchor: "top-left",
                content: this.$refs.monitorWindow.$el, // 传入 dom 对象，或者 html 字符串
                offset: new AMap.Pixel(100, -55),
              });
              this.infoWindow.open(this.map, [ev.lnglat.lng, ev.lnglat.lat]);
            });
          }
        });

        this.controlArr.push(control);

        control.setLabel({
          direction: "top",
          offset: new AMap.Pixel(15, -25), //设置文本标注偏移量
          content: "<div>监控</div>", //设置文本标注内容
        });
      });

      //#endregion

      // ------管网数据库------
      // 雨水管网
      //#region
      // 一级管网
      let path = [
        [121.345535, 30.718625],
        [121.345752, 30.718667],
        [121.345897, 30.71803],
        [121.346412, 30.715816],
        [121.350141, 30.71528],
        [121.351545, 30.715405],
      ];

      let polyline = new AMap.Polyline({
        path: path,
        isOutline: true,
        borderWeight: 3,
        strokeColor: "#2bfbd9",
        strokeOpacity: 1,
        strokeWeight: 20,
        lineJoin: "round",
        lineCap: "round",
        cursor: "pointer",
        zIndex: 50,
      });
      polyline.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
      });
      this.rainPipeArr.push(polyline);

      // 二级管网
      let pathArr = [
        [
          [121.345873, 30.718105],
          [121.346222, 30.718159],
        ],
        [
          [121.346222, 30.718159],
          [121.34662, 30.718218],
        ],
        [
          [121.34662, 30.718218],
          [121.347091, 30.718287],
        ],
        [
          [121.347091, 30.718287],
          [121.347429, 30.718339],
        ],
        [
          [121.347429, 30.718339],
          [121.349611, 30.718689],
        ],
        [
          [121.347435, 30.718323],
          [121.347803, 30.716697],
          [121.348475, 30.716284],
        ],
        [
          [121.349016, 30.716388],
          [121.34942, 30.716407],
          [121.349858, 30.71652],
          [121.350137, 30.715444],
          [121.350142, 30.715277],
        ],
      ];
      pathArr.forEach((item) => {
        let polyline = new AMap.Polyline({
          path: item,
          isOutline: true,
          borderWeight: 3,
          strokeColor: "#2bfbd9",
          strokeOpacity: 1,
          strokeWeight: 15,
          lineJoin: "round",
          lineCap: "round",
          cursor: "pointer",
          zIndex: 50,
        });
        this.rainPipeArr.push(polyline);
      });

      // 三级管网
      let pipeArr = [
        [
          [121.345752, 30.718667],
          [121.345979, 30.718712],
        ],
        [
          [121.345752, 30.718667],
          [121.34562, 30.718548],
        ],
        [
          [121.346184, 30.718299],
          [121.346226, 30.718057],
        ],
        [
          [121.346076, 30.717964],
          [121.346226, 30.718057],
        ],
        [
          [121.346584, 30.718358],
          [121.346623, 30.718105],
        ],
        [
          [121.346931, 30.718403],
          [121.346942, 30.718335],
          [121.347078, 30.718289],
        ],
        [
          [121.347078, 30.718289],
          [121.347099, 30.718064],
        ],
        [
          [121.347366, 30.718195],
          [121.347338, 30.718105],
          [121.347608, 30.718181],
        ],
        [
          [121.347556, 30.717778],
          [121.347603, 30.717846],
        ],
        [
          [121.347556, 30.717778],
          [121.34747, 30.71785],
        ],
        [
          [121.347556, 30.717778],
          [121.347426, 30.717767],
        ],
        [
          [121.347627, 30.717461],
          [121.347712, 30.717506],
        ],
        [
          [121.347627, 30.717461],
          [121.347514, 30.717502],
        ],
        [
          [121.347627, 30.717461],
          [121.347549, 30.717447],
        ],
        [
          [121.347699, 30.717144],
          [121.347827, 30.717168],
        ],
        [
          [121.347699, 30.717144],
          [121.347602, 30.717191],
        ],
        [
          [121.347699, 30.717144],
          [121.347558, 30.717116],
        ],
        [
          [121.347699, 30.717144],
          [121.347569, 30.717054],
        ],
        [
          [121.347877, 30.716959],
          [121.347643, 30.716933],
        ],
        [
          [121.347917, 30.716758],
          [121.34769, 30.716655],
        ],
        [
          [121.348174, 30.716468],
          [121.34821, 30.716547],
        ],
        [
          [121.348174, 30.716468],
          [121.348076, 30.716442],
        ],
        [
          [121.348174, 30.716468],
          [121.348164, 30.716374],
        ],
        [
          [121.348486, 30.716279],
          [121.348579, 30.716419],
        ],
        [
          [121.348486, 30.716279],
          [121.348463, 30.716162],
        ],
        [
          [121.348486, 30.716279],
          [121.348534, 30.716196],
        ],
        [
          [121.346022, 30.717481],
          [121.346171, 30.717514],
        ],
        [
          [121.346022, 30.717481],
          [121.345704, 30.717436],
        ],
        [
          [121.346022, 30.717481],
          [121.345823, 30.717534],
        ],
        [
          [121.346085, 30.717206],
          [121.345952, 30.717337],
        ],
        [
          [121.346231, 30.717237],
          [121.345986, 30.717203],
        ],
        [
          [121.346085, 30.717206],
          [121.345897, 30.716903],
          [121.34575, 30.716873],
        ],
        [
          [121.345817, 30.716902],
          [121.345789, 30.717026],
        ],
        [
          [121.346257, 30.716823],
          [121.346061, 30.716797],
        ],
        [
          [121.346391, 30.716462],
          [121.346114, 30.716418],
        ],
        [
          [121.34645, 30.716174],
          [121.346172, 30.716126],
        ],
        [
          [121.346797, 30.715764],
          [121.346524, 30.715435],
        ],
        [
          [121.346797, 30.715764],
          [121.346762, 30.715556],
        ],
        [
          [121.347279, 30.715689],
          [121.34725, 30.715496],
        ],
        [
          [121.347681, 30.71563],
          [121.347653, 30.71544],
        ],
        [
          [121.347681, 30.71563],
          [121.347653, 30.71544],
        ],
        [
          [121.348103, 30.715573],
          [121.348054, 30.715403],
        ],
        [
          [121.348505, 30.715511],
          [121.348477, 30.715331],
        ],
        [
          [121.348915, 30.715455],
          [121.348879, 30.715275],
        ],
        [
          [121.349342, 30.715393],
          [121.349314, 30.715206],
        ],
        [
          [121.34978, 30.715324],
          [121.349744, 30.71513],
        ],
        [
          [121.350118, 30.715279],
          [121.350006, 30.714881],
        ],
        [
          [121.349958, 30.715805],
          [121.350148, 30.715844],
        ],
        [
          [121.349824, 30.716156],
          [121.350055, 30.716193],
          [121.350065, 30.716285],
        ],
        [
          [121.349958, 30.715805],
          [121.350148, 30.715844],
        ],
        [
          [121.349589, 30.716398],
          [121.349538, 30.71652],
        ],
        [
          [121.349251, 30.716453],
          [121.349259, 30.716349],
        ],
      ];

      pipeArr.forEach((item) => {
        let polyline = new AMap.Polyline({
          path: item,
          // isOutline: true,
          borderWeight: 3,
          strokeColor: "#2bfbd9",
          strokeOpacity: 1,
          strokeWeight: 10,
          lineJoin: "round",
          lineCap: "round",
          cursor: "pointer",
          zIndex: 50,
        });
        this.rainPipeArr.push(polyline);
      });

      // 管网的点击事件
      // this.rainPipeArr[0].on("click", (ev) => {
      //   console.log(ev.lnglat.lng, ev.lnglat.lat);
      // });

      this.rainPipeArr[2].on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.rainPipeInfo[0];
      });
      this.rainPipeArr[3].on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.rainPipeInfo[1];
      });
      this.rainPipeArr[4].on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.rainPipeInfo[2];
      });

      //#endregion

      // 污水管网
      //#region
      let sewagePipePath01 = new this.AMap.Polyline({
        path: [
          [121.346292, 30.718105],
          [121.345944, 30.718054],
        ],
      });
      let sewagePipePath02 = new this.AMap.Polyline({
        path: [
          [121.34676, 30.718173],
          [121.346292, 30.718105],
        ],
      });
      let sewagePipePath03 = new this.AMap.Polyline({
        path: [
          [121.347225, 30.718242],
          [121.34676, 30.718173],
        ],
      });
      let sewagePipePath04 = new this.AMap.Polyline({
        path: [
          [121.347634, 30.718308],
          [121.347225, 30.718242],
        ],
      });

      let sewagePipePath05 = new this.AMap.Polyline({
        path: [
          [121.349627, 30.718624],
          [121.347624, 30.718306],
        ],
      });

      let sewagePipePath06 = new this.AMap.Polyline({
        path: [
          [121.347201, 30.718237],
          [121.347228, 30.718089],
        ],
      });

      let sewagePipePath07 = new this.AMap.Polyline({
        path: [
          [121.348422, 30.718432],
          [121.348446, 30.718336],
        ],
      });

      let sewagePipePath08 = new this.AMap.Polyline({
        path: [
          [121.349214, 30.71856],
          [121.349238, 30.718443],
        ],
      });

      let sewagePipePath09 = new this.AMap.Polyline({
        path: [
          [121.345974, 30.718054],
          [121.346491, 30.715888],
          [121.34982, 30.715392],
          [121.350214, 30.715415],
          [121.34998, 30.716477],
          [121.349436, 30.716386],
          [121.349457, 30.716321],
        ],
      });

      let sewagePipePath10 = new this.AMap.Polyline({
        path: [
          [121.346368, 30.717427],
          [121.345741, 30.717315],
        ],
      });

      let sewagePipePath11 = new this.AMap.Polyline({
        path: [
          [121.34628, 30.716771],
          [121.346007, 30.716725],
        ],
      });

      let sewagePipePath12 = new this.AMap.Polyline({
        path: [
          [121.348895, 30.715585],
          [121.348843, 30.715333],
        ],
      });

      let sewagePipePath13 = new this.AMap.Polyline({
        path: [
          [121.34998, 30.715777],
          [121.350254, 30.715845],
        ],
      });

      let sewagePipePath14 = new this.AMap.Polyline({
        path: [
          [121.350047, 30.716166],
          [121.349829, 30.716117],
        ],
      });

      let sewagePipePath15 = new this.AMap.Polyline({
        path: [
          [121.347579, 30.718297],
          [121.347519, 30.71822],
          [121.347861, 30.716724],
          [121.347946, 30.716628],
          [121.348596, 30.716295],
          [121.348923, 30.71635],
          [121.348946, 30.716301],
        ],
      });

      let sewagePipePath16 = new this.AMap.Polyline({
        path: [
          [121.347516, 30.718219],
          [121.34739, 30.718195],
        ],
      });

      let sewagePipePath17 = new this.AMap.Polyline({
        path: [
          [121.347672, 30.71801],
          [121.347579, 30.717947],
          [121.347682, 30.717907],
        ],
      });

      let sewagePipePath18 = new this.AMap.Polyline({
        path: [
          [121.347719, 30.717743],
          [121.347479, 30.717703],
        ],
      });

      let sewagePipePath19 = new this.AMap.Polyline({
        path: [
          [121.347812, 30.717459],
          [121.347692, 30.717443],
        ],
      });

      let sewagePipePath20 = new this.AMap.Polyline({
        path: [
          [121.347816, 30.71736],
          [121.347713, 30.717362],
        ],
      });

      let sewagePipePath21 = new this.AMap.Polyline({
        path: [
          [121.3479, 30.716902],
          [121.347812, 30.716917],
        ],
      });

      let sewagePipePath22 = new this.AMap.Polyline({
        path: [
          [121.347981, 30.716814],
          [121.347733, 30.71665],
        ],
      });

      let sewagePipePath23 = new this.AMap.Polyline({
        path: [
          [121.348594, 30.716298],
          [121.348581, 30.716213],
        ],
      });
      //#endregion

      this.sewagePipe = new AMap.OverlayGroup([
        sewagePipePath01,
        sewagePipePath02,
        sewagePipePath03,
        sewagePipePath04,
        sewagePipePath05,
        sewagePipePath06,
        sewagePipePath07,
        sewagePipePath09,
        sewagePipePath10,
        sewagePipePath11,
        sewagePipePath12,
        sewagePipePath13,
        sewagePipePath14,
        sewagePipePath15,
        sewagePipePath16,
        sewagePipePath17,
        sewagePipePath18,
        sewagePipePath19,
        sewagePipePath20,
        sewagePipePath21,
        sewagePipePath22,
        sewagePipePath23,
      ]);

      this.sewagePipe.setOptions({
        borderWeight: 3,
        strokeColor: "#ffb12a",
        strokeOpacity: 1,
        strokeWeight: 15,
        lineJoin: "round",
        lineCap: "round",
        cursor: "pointer",
        zIndex: 50,
      });

      sewagePipePath01.on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.sewagePipeInfo[0];
      });

      sewagePipePath02.on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.sewagePipeInfo[1];
      });

      sewagePipePath03.on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.sewagePipeInfo[2];
      });

      sewagePipePath04.on("click", (ev) => {
        this.isPipeInfoShow = false;
        this.windowLeave();
        this.isPipeInfoShow = true;
        this.pipeData = this.sewagePipeInfo[3];
      });

      this.sewagePipe.on("click", (ev) => {
        console.log(ev.lnglat.lng, ev.lnglat.lat);
      });

      // this.rainPipeArr.push(this.sewagePipe);

      // ------管网井盖------
      let wellCoverPosition = [
        [121.346172, 30.718327],
        [121.346576, 30.71839],
        [121.346925, 30.718435],
        [121.347099, 30.718036],
        [121.346626, 30.718078],
        [121.346047, 30.717951],
      ];

      wellCoverPosition.forEach((item, index) => {
        var wellCover = new AMap.Circle({
          center: item,
          radius: 3,
          // borderWeight: 3,
          strokeColor: "#FFF",
          strokeOpacity: 1,
          strokeWeight: 4,
          fillColor: "#2bfbd9",
          fillOpacity: 1,
          cursor: "pointer",
          zIndex: 500,
        });

        this.rainPipeArr.push(wellCover);

        wellCover.on("click", (ev) => {
          console.log("点击了井盖");
          this.windowLeave();
          this.isWellCoverShow = true;
          this.wellCoverData = this.wellCoverInfo[index];
        });
      });

      // ------石化街道描边------
      let shiHuaPolyline = new AMap.Polyline({
        path: this.shiHuaStreet,
        // isOutline: true,
        borderWeight: 3,
        strokeColor: "#ffb222",
        strokeOpacity: 1,
        strokeWeight: 15,
        lineJoin: "round",
        lineCap: "round",
        cursor: "pointer",
        zIndex: 50,
      });

      this.map.add(shiHuaPolyline);

      // -----党建库-图标------
      //#region
      var partyBuildingIcon = new AMap.Icon({
        size: new AMap.Size(84, 109),
        image:
          "https://pic.rmb.bdstatic.com/bjh/4186192f079daa06dd3f9f57a2032648.png",
        imageSize: new AMap.Size(84, 109),
        imageOffset: new AMap.Pixel(0, 0),
      });

      this.partyInfo.forEach((item, index) => {
        var partyBuildingOffice = new AMap.Marker({
          position: item.position,
          icon: partyBuildingIcon,
          offset: new AMap.Pixel(-42, -109),
        });
        partyBuildingOffice.setLabel({
          direction: "top",
          offset: new AMap.Pixel(-60, -25), // 设置文本标注偏移量
          content: `<div>${item.title}</div>`, // 设置文本标注内容
        });
        // 鼠标点击创建-自定义信息弹窗
        partyBuildingOffice.on("click", (ev) => {
          this.isPartyBuildingShow = true;
          this.partyData = this.partyInfo[index];
          this.$nextTick(() => {
            this.infoWindow = new AMap.InfoWindow({
              isCustom: true,
              anchor: "top-right",
              content: this.$refs.partyBuildingWindow.$el,
              offset: new AMap.Pixel(-100, -300),
            });
            this.infoWindow.open(this.map, item.position);
          });
        });
        this.partyBuildingOfficeArr.push(partyBuildingOffice);
      });

      //#endregion

      //#region

      // ------宾旅馆 icon------
      var map_icon_hotel = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/17/xDdzi8.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 宾旅馆1
      var hotel01 = new AMap.Marker({
        position: new AMap.LngLat(121.344334, 30.717748),
        icon: map_icon_hotel,
        offset: new AMap.Pixel(-42, -109),
      });
      hotel01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), // 设置文本标注偏移量
        content: "<div>宾旅馆</div>", // 设置文本标注内容
      });
      hotel01.on("click", (ev) => {
        this.windowLeave();
      });
      // 宾旅馆2
      var hotel02 = new AMap.Marker({
        position: new AMap.LngLat(121.346382, 30.717836),
        icon: map_icon_hotel,
        offset: new AMap.Pixel(-42, -109),
      });
      hotel02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), // 设置文本标注偏移量
        content: "<div>宾旅馆</div>", // 设置文本标注内容
      });
      hotel02.on("click", (ev) => {
        this.windowLeave();
      });
      // 宾旅馆3
      var hotel03 = new AMap.Marker({
        position: new AMap.LngLat(121.3456, 30.716415),
        icon: map_icon_hotel,
        offset: new AMap.Pixel(-42, -109),
      });
      hotel03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(0, -25), // 设置文本标注偏移量
        content: "<div>宾旅馆</div>", // 设置文本标注内容
      });
      hotel03.on("click", (ev) => {
        this.windowLeave();
      });
      this.hotelArr.push(hotel01, hotel02, hotel03);

      // ------商业网点 icon------
      var map_icon_network = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/21/x6z7Pe.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 商业网点01
      var network01 = new AMap.Marker({
        position: new AMap.LngLat(121.347853, 30.716017),
        icon: map_icon_network,
        offset: new AMap.Pixel(-42, -109),
      });
      network01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>商业网点</div>", // 设置文本标注内容
      });
      network01.on("click", (ev) => {
        this.windowLeave();
      });
      // 商业网点02
      var network02 = new AMap.Marker({
        position: new AMap.LngLat(121.347398, 30.717197),
        icon: map_icon_network,
        offset: new AMap.Pixel(-42, -109),
      });
      network02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>商业网点</div>", // 设置文本标注内容
      });
      network02.on("click", (ev) => {
        this.windowLeave();
      });
      // 商业网点03
      var network03 = new AMap.Marker({
        position: new AMap.LngLat(121.35082, 30.717093),
        icon: map_icon_network,
        offset: new AMap.Pixel(-42, -109),
      });
      network03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>商业网点</div>", // 设置文本标注内容
      });
      network03.on("click", (ev) => {
        this.windowLeave();
      });
      // 商业网点04
      var network04 = new AMap.Marker({
        position: new AMap.LngLat(121.351616, 30.717817),
        icon: map_icon_network,
        offset: new AMap.Pixel(-42, -109),
      });
      network04.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>商业网点</div>", // 设置文本标注内容
      });
      network04.on("click", (ev) => {
        this.windowLeave();
      });
      // 商业网点05
      var network05 = new AMap.Marker({
        position: new AMap.LngLat(121.345979, 30.716702),
        icon: map_icon_network,
        offset: new AMap.Pixel(-42, -109),
      });
      network05.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>商业网点</div>", // 设置文本标注内容
      });
      network05.on("click", (ev) => {
        this.windowLeave();
      });

      this.networkArr.push(
        // network01,
        // network02,
        // network03,
        network04,
        network05,
      );

      // 121.347398, 30.717197
      // 121.35082,  30.717093
      // 121.351616, 30.717817
      // 121.345979, 30.716702


      // ------农贸市场 icon------
      var map_icon_farmMarket = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/17/xDdjdP.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 农贸市场01
      var farmMarket01 = new AMap.Marker({
        position: new AMap.LngLat(121.350069, 30.716076),
        icon: map_icon_farmMarket,
        offset: new AMap.Pixel(-42, -109),
      });
      farmMarket01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>农贸市场</div>", // 设置文本标注内容
      });
      farmMarket01.on("click", (ev) => {
        this.windowLeave();
      });

      // 农贸市场02
      var farmMarket02 = new AMap.Marker({
        position: new AMap.LngLat(121.35019, 30.715397),
        icon: map_icon_farmMarket,
        offset: new AMap.Pixel(-42, -109),
      });
      farmMarket02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>农贸市场</div>", // 设置文本标注内容
      });
      farmMarket02.on("click", (ev) => {
        this.windowLeave();
      });

      // 农贸市场03
      var farmMarket03 = new AMap.Marker({
        position: new AMap.LngLat(121.346897, 30.718117),
        icon: map_icon_farmMarket,
        offset: new AMap.Pixel(-42, -109),
      });
      farmMarket03.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>农贸市场</div>", // 设置文本标注内容
      });
      farmMarket03.on("click", (ev) => {
        this.windowLeave();
      });
      this.farmMarketArr.push(farmMarket03);

      // ------学校 icon------
      var map_icon_school = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/17/xDdLqI.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 学校01
      var school01 = new AMap.Marker({
        position: new AMap.LngLat(121.348445, 30.716839),
        icon: map_icon_school,
        offset: new AMap.Pixel(-42, -109),
      });
      school01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), // 设置文本标注偏移量
        content: "<div>学校</div>", // 设置文本标注内容
      });
      school01.on("click", (ev) => {
        this.windowLeave();
      });

      // 学校02
      var school02 = new AMap.Marker({
        position: new AMap.LngLat(121.347648, 30.71648),
        icon: map_icon_school,
        offset: new AMap.Pixel(-42, -109),
      });
      school02.setLabel({
        direction: "top",
        offset: new AMap.Pixel(15, -25), // 设置文本标注偏移量
        content: "<div>学校</div>", // 设置文本标注内容
      });
      school02.on("click", (ev) => {
        this.windowLeave();
      });
      this.schoolArr.push(school01, school02);

      // ------养老机构 icon------
      var map_icon_nursing = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/17/xDdqsA.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 养老机构
      var nursing01 = new AMap.Marker({
        position: new AMap.LngLat(121.345008, 30.717713),
        icon: map_icon_nursing,
        offset: new AMap.Pixel(-42, -109),
      });
      nursing01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>养老机构</div>", // 设置文本标注内容
      });
      nursing01.on("click", (ev) => {
        this.windowLeave();
      });
      this.nursingArr.push(nursing01);

      // ------宗教场所 icon------
      var map_icon_religion = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(84, 109),
        // 图标的取图地址
        image: "https://s1.ax1x.com/2022/10/17/xDwSJS.png",
        // 图标所用图片大小
        imageSize: new AMap.Size(84, 109),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(0, 0),
      });

      // 宗教场所
      var religion01 = new AMap.Marker({
        position: new AMap.LngLat(121.344826, 30.716806),
        icon: map_icon_religion,
        offset: new AMap.Pixel(-42, -109),
      });
      religion01.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25), // 设置文本标注偏移量
        content: "<div>宗教场所</div>", // 设置文本标注内容
      });
      religion01.on("click", (ev) => {
        this.windowLeave();
      });
      this.religionArr.push(religion01);

      // https://s1.ax1x.com/2022/10/17/xDdzi8.png 宾旅馆
      // https://s1.ax1x.com/2022/10/17/xDdXZt.png 监控
      // https://s1.ax1x.com/2022/10/17/xDdjdP.png 农贸市场
      // https://s1.ax1x.com/2022/10/17/xDdLqI.png 学校
      // https://s1.ax1x.com/2022/10/17/xDdqsA.png 养老机构
      // https://s1.ax1x.com/2022/10/17/xDwSJS.png 宗教场所

      // 临潮二村范围路径
      var linChaoErCommiteePath = [
        [121.349278, 30.71861],
        [121.349396, 30.718116],
        [121.349525, 30.718126],
        [121.349621, 30.717692],
        [121.349916, 30.71772],
        [121.350056, 30.717646],
        [121.350217, 30.716991],
        [121.350023, 30.716931],
        [121.350039, 30.716816],
        [121.349766, 30.716761],
        [121.349846, 30.71653],
        [121.351359, 30.716797],
        [121.350876, 30.718873],
      ];
      // 临潮左上角小范围路径
      var linChaoSmallAreaPath = [
        [121.34601, 30.718084],
        [121.347315, 30.718293],
        [121.347444, 30.717804],
        [121.347277, 30.717784],
        [121.347254, 30.717895],
        [121.346101, 30.717713],
      ];

      this.illnessControlAreaPath.push(
        linChaoSmallAreaPath,
        linChaoErCommiteePath,
        linSanCommiteePath,
        haiTangCommiteePath,
        meiZhouCommiteePath,
        siCunCommiteePath,
        qiCunCommiteePath,
        shiCunCommiteePath
      );

      this.illnessControlAreaPath.forEach((item, index) => {
        var illnessControl_Commitee = new AMap.Polygon({
          path: item,
          strokeColor: "#25ff68", // 线颜色
          strokeOpacity: 1, // 线透明度
          strokeWeight: 9, // 线宽
          strokeStyle: "dashed",
          strokeDasharray: [50, 50],
          fillColor: "#25ff68", // 填充色
          fillOpacity: 0.2, // 填充透明度
        });

        this.illnessControlArea.push(illnessControl_Commitee);
      });

      /* 
        linChaoErCommiteePath、linSanCommiteePath、
        haiTangCommiteePath、meiZhouCommiteePath、
        siCunCommiteePath、qiCunCommiteePath、shiCunCommiteePath
      */

      // ------沿街商铺-图标及事件------
      // 遍历沿街商铺的信息
      for (let currentUser in this.currentUserInfo) {
        // 得到某街道的商铺数组
        let shopTempArr = this.currentUserInfo[currentUser];
        // 遍历商铺数组
        shopTempArr.forEach((item, index) => {
          // 获取坐标
          let shopPos = [item.longitude, item.latitude];
          // 通过沿街商铺工具函数创建图标对象
          let shopTempObj = this.streetShopHandle(shopPos);
          this.streetShopArr.push(shopTempObj);
          // 需要设置展示信息的沿街商铺
          if (currentUser == "num02") {
            this.streetShopOtherArr.push(shopTempObj);
            return;
          }
        });
      }

      // 对部分沿街商铺设置展示信息
      this.streetShopOtherArr.forEach((item, index) => {
        // 点击事件
        item.on("click", () => {
          this.windowLeave();
          this.isCurrentUserShow = true; // 展示弹窗
          // 商铺图标对象和数据匹配
          this.currentUserInfo["num02"].forEach((shopItem, shopIndex) => {
            if (index == shopIndex) {
              this.streetShopData = shopItem;
            }
          });
        });
      });

      //#endregion

      // ------实例数组------
      // 道路红线
      // this.roadLineArr = [roadLine01, roadLine02, roadLine03];
      // 现状使用主体
      this.currentUserArr = [];
      this.gather_num_arr = [
        gather_num01,
        gather_num02,
        gather_num03,
        gather_num04,
        gather_num05,
      ];
      this.gether_big_num_arr = [gather_num_longAn, gather_num_linChao];
      // 房地调查数据库
      this.premisesSurveyArr = [
        shihuaPrimarySchool,
        linchaoKindergarten,
        enforceLawTeam,
        shihuaPrimarySchoolText,
        linchaoKindergartenText,
        enforceLawTeamText,
        floorInfo28,
      ];
      // 绿化主体
      this.greeningArr = [
        publicGreenPolygon01,
        // publicGreenPolygon02,
        afterSaleGreen,
        unitGreen,
        // this.eventTips,
      ];

      // 未读则添加图标
      if (this.isReaded) {
        this.greeningArr = [
          publicGreenPolygon01,
          // publicGreenPolygon02,
          afterSaleGreen,
          unitGreen,
          // this.eventTips,
        ];
      } else {
        this.greeningArr = [
          publicGreenPolygon01,
          // publicGreenPolygon02,
          afterSaleGreen,
          unitGreen,
          this.eventTips,
        ];
      }

      // 小区基础信息
      this.communityInfoArr = [
        // 消防通道
        emergencyAccessLine,
        // 出入口
        doorMarker01,
        doorMarker02,
        doorMarker03,
        // 转门
        revolvingDoorMarker01,
        revolvingDoorMarker02,
        revolvingDoorMarker03,
        revolvingDoorMarker04,
        // 垃圾房
        garbageRoom01,
        garbageRoom02,
        garbageRoom03,
        garbageRoom04,
        buildGarbage01,
        buildGarbage02,
        bulkyGarbage01,
        bulkyGarbage02,
        // 凉亭
        pavilion01,
        pavilion02,
        // 配电房
        powerRoom01,
        powerRoom02,
        // 健身点
        fitness,
        // 居委会
        residentCommittee,
        // 临潮幼儿园、石化第一小学
        shihuaPrimarySchoolText,
        linchaoKindergartenText,
      ];
      this.emergencyAccessLine.push(emergencyAccessLine);
      this.doorMarker.push(doorMarker01, doorMarker02, doorMarker03);
      this.revolvingDoorMarker.push(
        revolvingDoorMarker01,
        revolvingDoorMarker02,
        revolvingDoorMarker03,
        revolvingDoorMarker04
      );
      this.garbageRoom.push(
        garbageRoom01,
        garbageRoom02,
        garbageRoom03,
        garbageRoom04,
        buildGarbage01,
        buildGarbage02,
        bulkyGarbage01,
        bulkyGarbage02
      );
      this.pavilion.push(pavilion01, pavilion02);
      this.powerRoom.push(powerRoom01, powerRoom02);
      this.fitness.push(fitness);
      this.residentCommittee.push(residentCommittee);

      // this.map.add(this.partyBuildingOfficeArr);
      this.map.add(this.patternSpot);
    },
    // 居委会的点击事件
    committeeClick() {
      this.windowLeave();
      if (!this.infoWindow) {
        this.isBasicInfoShow = true;
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true,
            anchor: "middle-left",
            content: this.$refs.basicInfoWindow,
            offset: new AMap.Pixel(100, 0),
          });
          this.infoWindow.open(
            this.map,
            new AMap.LngLat(121.348532, 30.717847)
          );
        });
      }
    },
    // 获取按钮传来的数据
    getParentBtnTitle(title) {
      // 悬浮离开或者随意点击后 关闭弹窗
      this.windowLeave();
      // 通过title值来决定缩放等级事件的判断
      this.bottomBtnTitle = title;

      if (title == "全部数据") {
        this.getSelData();
      } else if (title == "现状使用主体") {
        if (!this.isCurrentUser) {
          this.currentUserDB();
        }
        // if (!this.isRoadLine) {
        //   this.roadLineDB();
        // }
      } else if (title == "房地调查数据库") {
        if (!this.isPremisesSurvey) {
          this.premisesSurveyDB();
        }
      } else if (title == "绿化主体") {
        if (!this.isGreening) {
          this.greeningDB();
        }
      } else if (title == "小区基础信息") {
        if (!this.isCommunityInfo) {
          this.communityBasicInfo();
        }
      } else if (title == "管网数据") {
        if (!this.isPipeNetwork) {
          this.pipeNetwork();
        }
      }
    },
    // 全部数据
    getSelData() {
      this.reset();
      // 添加多边形覆盖物
      // 临潮一村
      this.map.add(this.linChaoVillage);
      // 道路红线
      this.map.add(this.roadLineArr);
      // 石化第一小学 临潮幼儿园
      this.map.add(this.premisesSurveyArr);
      // 公共绿化 售后房屋绿化 单位绿化
      this.map.add(this.greeningArr);
      // 临蒙居委-镂空绿化
      this.map.add(this.greenPolygonArr);
      // 小区基础信息
      this.map.add(this.communityInfoArr);
      // 现状使用主体 - 沿街商铺
      this.map.add(this.streetShopArr);
      // this.map.add(this.gather_num_arr);
      // this.map.add(this.currentUserArr);
      // 添加楼号
      this.map.add(this.mapTextObjArr);
      // 添加监控
      this.map.add(this.controlArr);
      // 添加隔离酒店
      this.map.add(this.isolateHoteArr);
      // 添加雨水管网
      this.map.add(this.rainPipeArr);
      // 添加污水管网
      this.map.add(this.sewagePipe);
    },
    // 道路红线数据库
    roadLineDB() {
      this.reset();

      this.map.add(this.linChaoVillage);
      // 添加道路覆盖物
      this.map.add(this.roadLineArr);
      // 表示已点击按钮，防止重复点击重复触发
      this.isRoadLine = true;
    },
    // 现状使用主体
    currentUserDB() {
      this.reset();
      // 设置地图上显示的元素种类
      // this.map.setFeatures(["bg", "road", "point"]);
      // 地图样式改变了
      // this.isHideBuilding = true;
      // 显示icon选项按钮条
      this.currentUserSelBtnShow = true;
      // // 显示疫情防控按钮
      // this.illnessControlShow = true;

      // 设置中心点、缩放等级
      // this.map.setCenter([121.34855, 30.717661], false, 500);
      // this.map.setZoom(20, false, 500);
      this.map.setCenter([121.348263, 30.717106], false, 500);
      this.map.setZoom(19.5, false, 500);
      // this.map.add(this.linChaoVillage);
      // 去除临潮一村遮罩范围
      this.map.remove(this.linChaoVillage);
      // 创建卫星图
      // this.map.add(this.satelliteLayer);
      // this.isSatelliteLayer = true;

      // 添加覆盖物
      // this.map.add(this.gather_num_arr);
      // 添加监控
      // this.map.add(this.controlArr);
      // 添加隔离酒店
      // this.map.add(this.isolateHoteArr);

      // 表示已点击按钮，防止重复点击重复触发
      this.isCurrentUser = true;
    },
    // 房地调查数据库
    premisesSurveyDB() {
      this.reset();
      // 设置地图俯仰角
      this.map.setPitch(35);
      // 设置中心点
      this.map.setCenter([121.348348, 30.716949], false, 500);
      // 地图放大一级显示
      this.map.setZoom(20, false, 500);
      this.map.add(this.linChaoVillage);
      // 添加覆盖物
      this.map.add(this.premisesSurveyArr);
      // 添加楼号
      this.map.add(this.mapTextObjArr);
      // 表示已点击按钮，防止重复点击重复触发
      this.isPremisesSurvey = true;
    },
    // 绿化主体
    greeningDB() {
      this.reset();
      // 移除区域图斑
      this.map.remove(this.patternSpot);
      // 添加绿化图斑
      this.map.add(this.greeningSpotArr);
      // 添加绿化图斑文本
      this.map.add(this.greeningSpotTextArr);
      // 切换层级
      this.map.setZoom(16, false, 500);
      this.map.setCenter([121.314026, 30.716866], false, 500);

      this.map.remove(this.linChaoVillage);

      // this.reset();
      // // 公共绿化 售后房屋绿化 单位绿化
      // this.map.add(this.greeningArr);
      // // 临蒙居委-镂空绿化
      // this.map.add(this.greenPolygonArr);
      // // 表示已点击按钮，防止重复点击重复触发
      // this.isGreening = true;
    },
    // 点击绿化图斑
    greeningSpot() {
      this.reset();
      this.map.add(this.linChaoVillage);
      // 公共绿化 售后房屋绿化 单位绿化
      this.map.add(this.greeningArr);
      // 临蒙居委-镂空绿化
      this.map.add(this.greenPolygonArr);
      // 移除绿化图斑
      this.map.remove(this.greeningSpotArr);
    },
    // 小区基础信息
    communityBasicInfo() {
      this.reset();
      this.map.add(this.linChaoVillage);
      this.map.setCenter([121.348263, 30.717106], false, 500);
      // 添加覆盖物
      this.map.add(this.communityInfoArr);
      // 添加楼号
      this.map.add(this.mapTextObjArr);
      // 添加icon选项按钮
      this.iconSelBtnShow = true;

      // 表示已点击按钮，防止重复点击重复触发
      this.isCommunityInfo = true;
    },
    // 管网数据
    pipeNetwork() {
      this.reset();
      // 设置地图俯仰角
      this.map.setPitch(40);
      this.map.setZoom(19.8, false, 500);
      this.map.setCenter([121.348348, 30.716949], false, 500);
      this.map.add(this.linChaoVillage);
      // 添加雨水管网
      this.map.add(this.rainPipeArr);
      this.rainPipeShow = true;
      // 添加污水管网
      this.map.add(this.sewagePipe);
      this.sewagePipeShow = true;
      // 添加管网选项按钮
      this.pipeSelBtnShow = true;

      // 表示已点击按钮，防止重复点击重复触发
      this.isPipeNetwork = true;
    },
    // 点击下方按钮-重置选项
    reset(txt) {
      // 触发“还原”按钮，不设置地图层级
      if (txt != "restore") {
        // 设置地图初始俯仰角
        this.map.setPitch(0);
        // 地图缩小一级显示
        this.map.setZoomAndCenter(19.5, [121.348263, 30.717306], false, 500);
      }

      // 恢复地图样式
      if (this.isHideBuilding) {
        this.map.setFeatures(["bg", "road", "point", "building"]);
      }
      this.isClickWin = false;

      // 还原临潮一村的色值
      this.linChaoVillage.setOptions({
        fillColor: "#70b2e2",
        strokeColor: "#70b2e2",
        strokeWeight: 18,
        fillOpacity: 0.3,
      });
      // 去除周边范围遮罩物
      this.map.remove(this.illnessControlArea);
      // 疫情防控按钮，标记为按钮未点击
      this.isIllnessControl = false;
      // 还原聚合点的颜色
      this.gather_num_arr.forEach((item) => {
        item.setStyle({
          "background-color": "rgba(255,174,17,0.5000)",
          "border-color": "#FFAE11",
        });
      });

      // 移除多边形覆盖物
      // 道路红线
      this.map.remove(this.roadLineArr);
      // 石化第一小学 临潮幼儿园
      this.map.remove(this.premisesSurveyArr);
      // 公共绿化 售后房屋绿化 单位绿化
      this.map.remove(this.greeningArr);
      // 临蒙居委-镂空绿化
      this.map.remove(this.greenPolygonArr);
      // 移除绿化图斑
      this.map.remove(this.greeningSpotArr);
      // 移除绿化图斑文本
      this.map.remove(this.greeningSpotTextArr);
      // 小区基础信息
      this.map.remove(this.communityInfoArr);
      // 现状使用主体 - 沿街商铺
      this.map.remove(this.streetShopArr);
      // this.map.remove(this.gather_num_arr);
      // this.map.remove(this.currentUserArr);
      // 移除楼号
      this.map.remove(this.mapTextObjArr);

      // 移除监控
      this.map.remove(this.controlArr);
      // 移除隔离酒店
      this.map.remove(this.isolateHoteArr);
      // 移除沿街商铺
      // this.map.remove(this.gather_num_arr);
      // this.map.remove(this.gether_big_num_arr);
      // 移除宾旅馆
      this.map.remove(this.hotelArr);
      // 移除商业网点
      this.map.remove(this.networkArr);
      // 移除农贸市场
      this.map.remove(this.farmMarketArr);
      // 移除学校
      this.map.remove(this.schoolArr);
      // 移除养老机构
      this.map.remove(this.nursingArr);
      // 移除宗教场所
      this.map.remove(this.religionArr);
      this.monitorIconShow = false; // 监控
      this.isolateHoteIconShow = false; // 隔离酒店
      this.streetShopIconShow = false; // 沿街商铺
      this.hotelIconShow = false; // 宾旅馆
      this.networkIconShow = false; // 商业网点
      this.farmMarketIconShow = false; // 农贸市场
      this.schoolIconShow = false; // 学校
      this.nursingIconShow = false; // 养老机构
      this.religionIconShow = false; // 宗教场所
      for (var i = 0; i < 9; i++) {
        this.$refs["currentUser0" + i][0].style.backgroundColor = "#c7c7c6";
      }

      // 移除雨水管网
      this.map.remove(this.rainPipeArr);
      this.rainPipeShow = false;
      // 移除污水管网
      this.map.remove(this.sewagePipe);
      this.sewagePipeShow = false;

      // 移除图斑reset
      this.map.remove(this.patternSpot);

      // 移除党建库-办事处
      this.map.remove(this.partyBuildingOfficeArr);

      // 移除小区基础信息icon选项按钮
      this.iconSelBtnShow = false;
      // 移除管网数据的管网选项按钮
      this.pipeSelBtnShow = false;
      // 移除现状使用主体的选项按钮
      this.currentUserSelBtnShow = false;
      // console.log('reset触发');

      // 隐藏搜索框
      this.isInputShow = false;

      this.ispatternSpotShow = false;

      if (this.satelliteLayer) {
        // 去除卫星图层
        this.map.remove(this.satelliteLayer);
      }
      // 未显示卫星图层
      this.isSatelliteLayer = false;

      // 没有添加遮罩物
      this.isAllData = false;
      this.isRoadLine = false;
      this.isPremisesSurvey = false;
      this.isGreening = false;
      this.isCommunityInfo = false;
      this.isPipeNetwork = false;
      this.isCurrentUser = false;
    },

    // -----用地信息创建自定义窗口处理函数-----
    landInfoHandle(index, anchor, offset, position) {
      this.windowLeave();
      if (!this.infoWindow) {
        this.landInfoData = this.landInfo[index];
        this.isLandInfoShow = true;
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            anchor: anchor,
            content: this.$refs.landInfoWindow.$el, // 传入 dom 对象，或者 html 字符串
            offset: offset,
          });
          this.infoWindow.open(this.map, position);
        });
      }
    },

    // -----垃圾房创建自定义窗口处理函数-----
    garbageRoomHandle(index, obj, anchor) {
      this.windowLeave();
      if (!this.infoWindow) {
        this.garbageData = this.garbageRoomInfo[index];
        this.isGarbageShow = true;
        this.$nextTick(() => {
          var lng = obj.getPosition().lng;
          var lat = obj.getPosition().lat;
          var position = new AMap.LngLat(lng, lat);
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            anchor: anchor,
            content: this.$refs.garbageRoomWindow.$el, // 传入 dom 对象，或者 html 字符串
            offset: new AMap.Pixel(60, -55),
          });
          this.infoWindow.open(this.map, position);
        });
      }
    },

    // -----建筑垃圾和大件垃圾-创建自定义窗口处理函数-----
    garbageOtherWindow(index, anchor, offset, position) {
      this.windowLeave();
      if (!this.infoWindow) {
        this.garbageOtherData = this.garbageOtherInfo[index];
        this.isGarbageOtherShow = true;
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            anchor: anchor,
            content: this.$refs.garbageOtherWindow.$el, // 传入 dom 对象，或者 html 字符串
            offset: offset,
          });
          this.infoWindow.open(this.map, position);
        });
      }
    },

    // -----出入口创建自定义窗口处理函数-----
    entryExitHandle(index, obj, anchor) {
      this.windowLeave();
      if (!this.infoWindow) {
        this.entryExitData = this.entryExitInfo[index];
        this.isEntryExitShow = true;

        this.$nextTick(() => {
          var lng = obj.getPosition().lng;
          var lat = obj.getPosition().lat;
          var position = new AMap.LngLat(lng, lat);
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            anchor: anchor,
            content: this.$refs.entryExitWindow.$el, // 传入 dom 对象，或者 html 字符串
            offset: new AMap.Pixel(60, 0),
          });
          this.infoWindow.open(this.map, position);
        });
      }
    },

    // -----绿化创建自定义窗口处理函数-----
    greeningHandle(index, anchor, offset, position) {
      this.windowLeave();
      if (!this.infoWindow) {
        this.greeningData = this.greeningInfo[index];
        this.isGreeningShow = true;
        this.$nextTick(() => {
          this.infoWindow = new AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            anchor: anchor,
            content: this.$refs.greeningWindow.$el, // 传入 dom 对象，或者 html 字符串
            offset: offset,
          });
          this.infoWindow.open(this.map, position);
        });
      }
    },

    // -----管网数据创建自定义窗口处理函数-----
    // pipeInfoHandle(index, anchor, offset, position) {
    //   this.windowLeave();
    //   if (!this.infoWindow) {
    //     this.pipeData = this.rainPipeInfo[index];
    //     this.isPipeInfoShow = true;
    //     this.$nextTick(() => {
    //       this.infoWindow = new AMap.InfoWindow({
    //         isCustom: true, // 使用自定义窗体
    //         anchor: anchor,
    //         content: this.$refs.PipeInfoWindow.$el, // 传入 dom 对象，或者 html 字符串
    //         offset: offset,
    //       });
    //       this.infoWindow.open(this.map, position);
    //     });
    //   }
    // },

    // ------沿街商铺-数据弹窗处理函数------
    streetShopHandle(position) {
      let map_icon_streetShop = new AMap.Icon({
        size: new AMap.Size(84, 109),
        image: "https://s1.ax1x.com/2022/10/17/xDhexI.png",
        imageSize: new AMap.Size(84, 109),
        imageOffset: new AMap.Pixel(0, 0),
      });

      let streetShop = new AMap.Marker({
        position: position,
        icon: map_icon_streetShop,
        offset: new AMap.Pixel(-42, -109),
      });
      streetShop.setLabel({
        direction: "top",
        offset: new AMap.Pixel(-8, -25),
        content: "<div>沿街商铺</div>",
      });
      return streetShop;
    },

    // 悬浮离开或者随意点击后 关闭弹窗
    windowLeave() {
      this.isGarbageShow = false;
      this.isEntryExitShow = false;
      this.isLandInfoShow = false;
      this.isGreeningShow = false;
      this.isPipeInfoShow = false;
      this.isWellCoverShow = false;

      if (this.infoWindow) {
        this.infoWindow.close();
      }
      this.infoWindow = null;
    },
    // 关闭“现状使用主体”弹窗
    closeCurrentUseWindow(value) {
      this.isCurrentUserShow = value;
    },

    // ------浮窗功能------
    // 还原
    winRestore() {
      // 关闭信息弹窗
      if (this.infoWindow) {
        this.infoWindow.close();
      }
      this.infoWindow = null;
      this.bottomBtnTitle = "还原";

      this.reset("restore");
      let lng = this.map.getCenter().lng;
      let lat = this.map.getCenter().lat;
      if (this.map.getZoom() == 16 && lng == 121.314026 && lat == 30.716866) {
        this.map.remove(this.greeningSpotArr);
        this.map.add(this.patternSpot);
        return;
      } else {
        this.$emit("getDefaultVal", -1);
        this.isClickWin = true;
        this.map.setPitch(0);
        this.map.setZoom(16, false, 500);
        this.map.setCenter([121.314026, 30.716866], false, 500);
        this.map.remove(this.linChaoVillage);
        this.map.add(this.patternSpot);
      }
    },
    // 网格-图斑
    winPatternSpot() {
      console.log("浮窗-图斑");
      this.reset();
      this.isClickWin = true;
      if (this.ispatternSpotShow) {
        this.map.remove(this.patternSpot);
        this.ispatternSpotShow = false;
      } else {
        this.windowLeave();
        this.map.add(this.patternSpot);
        // 设置地图俯仰角
        this.map.setPitch(40);
        this.map.setCenter([121.341658, 30.718101], false, 500);
        this.map.setZoom(17, false, 500);
        this.ispatternSpotShow = true;
      }
      // setTimeout(() => {
      //   this.map.remove(this.gether_big_num_arr);
      // }, 3000);
    },
    // 搜索
    winSearch() {
      // console.log(this.isInputShow);
      if (this.isInputShow) {
        this.isInputShow = false;
      } else {
        this.isInputShow = true;
        // this.$nextTick(() => {
        //   this.$refs.input_search.focus();
        // });
      }
    },
    // 切换视图
    winSwitch() {
      if (this.isSatelliteLayer) {
        // 去除卫星图
        this.map.remove(this.satelliteLayer);
        // 设置地图上显示的元素种类
        this.map.setFeatures(["bg", "road", "point", "building"]);
        // 未显示卫星图层
        this.isSatelliteLayer = false;
      } else {
        // 添加卫星图
        this.map.add(this.satelliteLayer);
        this.map.setFeatures(["bg", "road", "point"]);
        // 显示卫星图层
        this.isSatelliteLayer = true;
      }
    },
    // 标识指示
    winTips() {
      console.log("浮窗-标注");
      if (this.isTipsShow) {
        this.isTipsShow = false;
      } else {
        this.isTipsShow = true;
      }
    },
    // 疫情防控
    illnessControl() {
      // 判断按钮是否已点击
      if (this.isIllnessControl) {
        this.map.remove(this.linChaoVillage); // 去除临潮一村范围遮罩物
        this.map.remove(this.illnessControlArea); // 去除周边范围遮罩物
        this.isIllnessControl = false; // 标记为按钮未点击
        // 还原聚合点的颜色
        // this.gather_num_arr.forEach((item) => {
        //   item.setStyle({
        //     "background-color": "rgba(255,174,17,0.5000)",
        //     "border-color": "#FFAE11",
        //   });
        // });
      } else {
        // 修改遮罩物的色值
        this.linChaoVillage.setOptions({
          strokeWeight: 9,
          fillOpacity: 0.2,
          fillColor: "#ff7725",
          strokeColor: "#ff7725",
        });
        this.map.add(this.linChaoVillage); // 添加临潮一村范围遮罩物
        this.map.add(this.illnessControlArea); // 添加周边范围遮罩物
        this.isIllnessControl = true; // 标记为按钮已点击

        // 修改红色区域聚合点颜色
        // this.redPoint.setStyle({
        //   "background-color": "rgba(255,73,17,0.5000)",
        //   "border-color": "#ff4911",
        // });
        // // 修改绿色区域聚合点颜色
        // this.greenPointArr.forEach((item) => {
        //   item.setStyle({
        //     "background-color": "rgba(38,254,107,0.5000)",
        //     "border-color": "#26fe6b",
        //   });
        // });
      }
    },
    test() {
      console.log("离开了");
    },
    // 创建图斑文本
    createSpotText(text, position) {
      let txt = new AMap.Text({
        text: text,
        anchor: "center", // 设置文本标记锚点
        cursor: "default",
        style: {
          "background-color": "transparent",
          "border-width": 0,
          "text-align": "center",
          "font-size": "50px",
          color: "#FFF",
        },
        cursor: "pointer",
        position: position,
        // offset: [0, 8],
      });

      this.greeningSpotTextArr.push(txt);
    },
    // 小区基础信息的icon显示
    iconSelBtn(index) {
      // 判断是哪一个图标
      if (index == 0) {
        // 出入口
        // 判断是否已显示图标（---待封装---）
        if (this.doorMarkerShow) {
          // 已显示-移除图标
          this.map.remove(this.doorMarker);
          // 已显示-修改为未选中样式
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          // 修改为未选中状态
          this.doorMarkerShow = false;
        } else {
          // 未显示-添加图标
          this.map.add(this.doorMarker);
          // 未显示-修改为选中样式
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          // 修改为选中状态
          this.doorMarkerShow = true;
        }
      } else if (index == 1) {
        // 转门
        if (this.revolvingDoorMarkerShow) {
          this.map.remove(this.revolvingDoorMarker);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.revolvingDoorMarkerShow = false;
        } else {
          this.map.add(this.revolvingDoorMarker);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.revolvingDoorMarkerShow = true;
        }
      } else if (index == 2) {
        // 居委
        if (this.residentCommitteeShow) {
          this.map.remove(this.residentCommittee);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.residentCommitteeShow = false;
        } else {
          this.map.add(this.residentCommittee);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.residentCommitteeShow = true;
        }
      } else if (index == 3) {
        // 垃圾房
        if (this.garbageRoomShow) {
          this.map.remove(this.garbageRoom);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.garbageRoomShow = false;
        } else {
          this.map.add(this.garbageRoom);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.garbageRoomShow = true;
        }
      } else if (index == 4) {
        // 配电室
        if (this.powerRoomShow) {
          this.map.remove(this.powerRoom);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.powerRoomShow = false;
        } else {
          this.map.add(this.powerRoom);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.powerRoomShow = true;
        }
      } else if (index == 5) {
        // 凉亭
        if (this.pavilionShow) {
          this.map.remove(this.pavilion);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.pavilionShow = false;
        } else {
          this.map.add(this.pavilion);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.pavilionShow = true;
        }
      } else if (index == 6) {
        // 健身点
        if (this.fitnessShow) {
          this.map.remove(this.fitness);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.fitnessShow = false;
        } else {
          this.map.add(this.fitness);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.fitnessShow = true;
        }
      } else if (index == 7) {
        // 消防通道
        if (this.emergencyAccessLineShow) {
          this.map.remove(this.emergencyAccessLine);
          this.$refs["icon0" + index][0].style.backgroundColor = "#c7c7c6";
          this.emergencyAccessLineShow = false;
        } else {
          this.map.add(this.emergencyAccessLine);
          this.$refs["icon0" + index][0].style.backgroundColor = "#2bfbd9";
          this.emergencyAccessLineShow = true;
        }
      }
    },
    // 管网数据的管网显示
    pipeSelBtn(index) {
      console.log("pipe" + index);
      if (index == 0) {
        // 雨水管线
        if (this.rainPipeShow) {
          this.map.remove(this.rainPipeArr);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#c7c7c6";
          this.rainPipeShow = false;
        } else {
          this.map.add(this.rainPipeArr);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#2bfbd9";
          this.rainPipeShow = true;
        }
      } else if (index == 1) {
        // 污水管线
        if (this.sewagePipeShow) {
          this.map.remove(this.sewagePipe);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#c7c7c6";
          this.sewagePipeShow = false;
        } else {
          this.map.add(this.sewagePipe);
          this.$refs["pipe0" + index][0].style.backgroundColor = "#2bfbd9";
          this.sewagePipeShow = true;
        }
      }
    },
    // 现状使用主体的icon显示
    currentUserSelBtn(index) {
      if (index == 0) {
        // 监控
        // 判断是否已显示图标（---待封装---）
        if (this.monitorIconShow) {
          // 已显示-移除图标
          this.map.remove(this.controlArr);
          // 已显示-修改为未选中样式
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          // 修改为未选中状态
          this.monitorIconShow = false;
        } else {
          // 未显示-添加图标
          this.map.add(this.controlArr);
          // 未显示-修改为选中样式
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          // 修改为选中状态
          this.monitorIconShow = true;
        }
      } else if (index == 1) {
        // 沿街商铺
        if (this.streetShopIconShow) {
          this.map.remove(this.streetShopArr);
          // this.map.remove(this.gather_num_arr);
          // this.map.remove(this.gether_big_num_arr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.streetShopIconShow = false;
        } else {
          // if (this.map.getZoom() <= 18.5) {
          //   this.map.add(this.gether_big_num_arr);
          // } else {
          //   this.map.add(this.gather_num_arr);
          // }
          this.map.add(this.streetShopArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.streetShopIconShow = true;
        }
      } else if (index == 2) {
        // 宾旅馆
        if (this.hotelIconShow) {
          this.map.remove(this.hotelArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.hotelIconShow = false;
        } else {
          this.map.add(this.hotelArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.hotelIconShow = true;
        }
      } else if (index == 3) {
        // 商业网点
        if (this.networkIconShow) {
          this.map.remove(this.networkArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.networkIconShow = false;
        } else {
          this.map.add(this.networkArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.networkIconShow = true;
        }
      } else if (index == 4) {
        // 农贸市场
        if (this.farmMarketIconShow) {
          this.map.remove(this.farmMarketArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.farmMarketIconShow = false;
        } else {
          this.map.add(this.farmMarketArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.farmMarketIconShow = true;
        }
      } else if (index == 5) {
        // 学校
        if (this.schoolIconShow) {
          this.map.remove(this.schoolArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.schoolIconShow = false;
        } else {
          this.map.add(this.schoolArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.schoolIconShow = true;
        }
      } else if (index == 6) {
        // 养老机构
        if (this.nursingIconShow) {
          this.map.remove(this.nursingArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.nursingIconShow = false;
        } else {
          this.map.add(this.nursingArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.nursingIconShow = true;
        }
      } else if (index == 7) {
        // 宗教场所
        if (this.religionIconShow) {
          this.map.remove(this.religionArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.religionIconShow = false;
        } else {
          this.map.add(this.religionArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.religionIconShow = true;
        }
      } else if (index == 8) {
        // 隔离酒店
        if (this.isolateHoteIconShow) {
          this.map.remove(this.isolateHoteArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#c7c7c6";
          this.isolateHoteIconShow = false;
        } else {
          this.map.add(this.isolateHoteArr);
          this.$refs["currentUser0" + index][0].style.backgroundColor =
            "#2bfbd9";
          this.isolateHoteIconShow = true;
        }
      }
    },
    // "党建库"按钮事件
    partyBuildingClick() {
      // console.log("触发了");
      this.reset("restore");
      // this.winRestore();
      // this.$emit("getDefaultVal", -1);
      // this.isClickWin = true;
      this.map.setPitch(0);
      this.map.setZoom(16, false, 500);
      this.map.setCenter([121.314026, 30.712866], false, 500);
      this.map.remove(this.linChaoVillage);
      this.map.remove(this.patternSpot);
      // 地图缩放等级改变事件（移除大聚合）
      this.bottomBtnTitle = "党建库"; // 通过title来决定缩放等级事件的触发
      this.map.add(this.partyBuildingOfficeArr);
    },
  },
  mounted() {
    // DOM初始化完成进行地图初始化
    this.initMap();

    // console.log(this.streetShopData);
  },
  beforeDestroy() {
    this.map.destroy();
  },
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>