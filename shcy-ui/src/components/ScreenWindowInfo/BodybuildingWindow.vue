<!-- 隔离酒店信息展示弹窗 -->
<template>
  <div class="Bodybuilding-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">临潮一村-健身点</span>
    </div>
    <div class="monitor">
      <img src="https://pic.rmb.bdstatic.com/bjh/3216b5fde9c70e56140f32c04449b80f.jpeg" width="100%" alt="" />
    </div>
    <!-- <div class="open-time-box">
      <div class="open-time-title">地址</div>
      <div class="open-time">{{ isolateHoteData.address }}</div>
    </div> -->
  </div>
</template>

<script>
export default {
  // props: ["isolateHoteData"],
  data() {
    return {
      // srcList: [
      //   "https://pic.rmb.bdstatic.com/bjh/977b0ebde14bf6cef267006be3afdb28.jpeg",
      // ],
    };
  },
};
</script>
<style lang="scss" scoped>
.Bodybuilding-container {
  width: 725px;
  padding: 0 75px 75px 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 99900;

  // 标题
  .title-box {
    position: relative;
    margin-top: 45px;
    width: 575px;
    height: 60px;

    // 圆环
    .ring {
      position: absolute;
      left: 0;
      top: 10px;
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 60px;
      // margin-bottom: 20px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  .area-info-box,
  .position-info-box,
  .open-time-box {
    margin-top: 45px;
    width: 575px;
    height: 130px;
  }

  .monitor {
    margin-top: 45px;
    width: 575px;
    // border: 2px solid #949ca9;
  }

  // 所属小区 分类投放垃圾种类
  .area-info-box {
    display: flex;
    justify-content: space-between;

    // 所属小区
    .community-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .community-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .community {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 分类投放垃圾种类
    .refuseClassify-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .refuseClassify-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .refuseClassify {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 垃圾房地址
  .position-info-box {
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .position-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .position {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
  // 投放时间
  .open-time-box {
    margin-bottom: 75px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .open-time-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .open-time {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
}
</style>