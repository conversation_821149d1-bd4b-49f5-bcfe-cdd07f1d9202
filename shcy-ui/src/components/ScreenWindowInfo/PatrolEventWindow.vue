<!-- 林长制巡查事件上报记录 -->
<template>
  <div class="PatrolEvent-container" ref="currentUse">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">林长制巡查事件上报记录</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <!-- 下拉列表 -->
    <div class="sel-box">
      <!-- 居委会 -->
      <ScreenDisplaySelect
        class="sel sel-committee"
        :selStyle="selStyle"
        :seldata="committeeData"
        :selDefault="defaultCommittee"
      />
      <!-- 行业类别 -->
      <ScreenDisplaySelect
        class="sel sel-industry-category"
        :selStyle="selStyle"
        :seldata="industryCategory"
        :selDefault="defaultindustry"
      />
      <!-- 房权属性 -->
      <ScreenDisplaySelect
        class="sel sel-updateTime"
        :selStyle="selStyle"
        :seldata="updateTime"
        :selDefault="defaultUpdateTime"
        v-if="false"
      />
      <!-- 搜索框 -->
      <div class="search-box" v-if="false">
        <input class="input-search" type="search" placeholder="请输入商铺" />
        <span class="input-search-icon" @click="isSearch()">
          <i class="search-icon"></i>
        </span>
      </div>
    </div>
    <!-- 数据列表 -->
    <div class="shops-list-data">
      <!-- 表头 -->
      <div class="thead">
        <span class="order">序号</span>
        <span class="area">事件区域</span>
        <span class="reporter">上报人</span>
        <span class="reportTime">上报时间</span>
        <span class="tel">联系方式</span>
        <span class="describe">事件描述</span>
        <span class="handleUnit">处理单位</span>
        <span class="flowTo">流转至</span>
        <span class="handleTime">处理时间</span>
        <span class="handleResult">处理结果</span>
      </div>
      <!-- 表体 -->
      <div class="tbody">
        <div
          class="shops-item"
          v-for="(item, index) in patrolEvent"
          :key="index"
        >
          <span class="item-order">{{ index + 1 }}</span>
          <span class="item-area">{{ item.area }}</span>
          <span class="item-reporter">{{ item.reporter }}</span>
          <span class="item-reportTime">{{ item.reportTime }}</span>
          <span class="item-tel">{{ item.tel }}</span>
          <div class="item-describe">{{ item.describe }}</div>
          <span class="item-handleUnit">{{ item.handleUnit }}</span>
          <span class="item-flowTo">{{ item.flowTo }}</span>
          <span class="item-handleTime">{{ item.handleTime }}</span>
          <span class="item-handleResult">{{ item.handleResult }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenDisplaySelect from "../ScreenDisplaySelect/index.vue";
export default {
  // props: ["currentUseData"],
  data() {
    return {
      display: false,
      selStyle: {
        left: "-4px",
        top: "116px",
      },
      defaultCommittee: "请选择时间",
      defaultindustry: "请选择处理状态",
      defaultUpdateTime: "请选择房权属性",
      committeeData: [
        "全部时间",
        "2022年07月",
        "2022年06月",
        "2022年05月",
        "2022年04月",
      ],
      industryCategory: ["全部", "已处理", "待处理", "未处理"],
      updateTime: ["全部", "市场公司", "城建公司", "聚慧"],

      // 巡查事件上报记录
      patrolEvent: [
        {
          // 事件区域
          area: "临潮一村0001",
          // 上报人
          reporter: "李向海",
          // 上报时间
          reportTime: "2022/07/30 11:07:08",
          // 联系方式
          tel: "13784268510",
          // 事件描述
          describe: "临蒙街心花园沿街植物影响行人，需要修剪。",
          // 处理单位
          handleUnit: "临蒙居委",
          // 流转至
          flowTo: "园区林所",
          // 处理时间
          handleTime: "2022/07/30 16:41:29",
          // 处理状态
          handleResult: "已处理",
        },
        {
          area: "机非隔离绿化0001",
          reporter: "东世杰",
          reportTime: "2022/08/01 10:27:48",
          tel: "18856901297",
          describe: "因交通事故导致绿化区域受损，需及时修复绿化区域。",
          handleUnit: "临蒙居委",
          flowTo: "园区林所",
          handleTime: "2022/08/01 14:51:23",
          handleResult: "已处理",
        },
        {
          area: "街心花园0001",
          reporter: "崔永珍",
          reportTime: "2022/08/01 15:21:38",
          tel: "18156035635",
          describe: "绿植枯萎，需要更换绿植，并向该区域进行散水工作。",
          handleUnit: "临蒙居委",
          flowTo: "未流转",
          handleTime: "",
          handleResult: "未处理",
        },
        {
          area: "金山一小0001",
          reporter: "康健维",
          reportTime: "2022/08/03 8:23:18",
          tel: "17681174554",
          describe: "杂草及害虫影响其他植被生长，需要拔除及消杀。",
          handleUnit: "临蒙居委",
          flowTo: "园区林所",
          handleTime: "2022/08/03 15:35:43",
          handleResult: "已处理",
        },
        {
          area: "街心花园0001",
          reporter: "崔永珍",
          reportTime: "2022/08/05 10:11:18",
          tel: "18156035635",
          describe: "因游玩孩童顽皮，园区北部偏倒了几颗树苗，需重新种植。",
          handleUnit: "临蒙居委",
          flowTo: "园区林所",
          handleTime: "2022/08/01 16:25:23",
          handleResult: "已处理",
        },
      ],
    };
  },
  components: {
    ScreenDisplaySelect,
  },
  methods: {
    closeWindow() {
      // this.$emit("closeWindow", this.display);
      this.$store.dispatch("patrolEvent/closeWindow", false);
    },
    isSearch() {
      console.log("搜索");
    },
  },
};
</script>

<style lang="scss" scoped>
// 现状使用主体-容器
.PatrolEvent-container {
  z-index: 1000;
  position: relative;
  top: 700px;
  left: 1115px;
  padding: 75px;
  width: 4050px;
  // height: 200px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 60px;
    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 50px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
    }
  }

  // 下拉列表
  .sel-box {
    margin: 50px 0;
    width: 100%;
    display: flex;

    .sel {
      margin-right: 80px;
    }

    // 搜索框
    .search-box {
      display: flex;
      width: 764px;
      height: 124px;
      border: 4px solid #656594;

      .input-search {
        padding: 0 0 0 50px;
        width: 640px;
        // height: 124px;
        outline: none;
        border: none;
        border-right: 4px solid #656594;
        background-color: #181839;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #8f8fa7;
        line-height: 124px;
      }

      .input-search-icon {
        position: relative;
        display: inline-block;
        width: 124px;
        // height: 124px;
        background-color: #2d2d64;
        cursor: pointer;

        .search-icon {
          position: absolute;
          top: 41px;
          left: 41px;
          display: block;
          width: 38px;
          height: 44px;
          background: url("../../assets/screen_display_img/bottom_icon_search.png")
            no-repeat;
          background-size: contain;
        }
      }
    }
  }

  // 数据列表
  .shops-list-data {
    width: 3892px;
    // height: 100px;
    border: 4px solid #656594;

    // 表头
    .thead {
      width: 100%;
      height: 96px;
      border-bottom: 4px solid #656594;
      background-color: #2d2d64;

      span {
        display: inline-block;
        // margin-left: 50px;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
      }

      // 序号
      .order {
        margin-left: 20px;
        width: 170px;
      }
      // 事件区域
      .area {
        width: 380px;
      }
      // 上报人
      .reporter {
        width: 200px;
      }
      // 上报时间
      .reportTime {
        margin: 0 40px;
        width: 440px;
      }
      // 联系方式
      .tel {
        width: 300px;
      }
      // 事件描述
      .describe {
        width: 1000px;
      }
      // 处理单位
      .handleUnit {
        width: 280px;
      }
      // 流转至
      .flowTo {
        width: 280px;
      }
      // 处理时间
      .handleTime {
        width: 450px;
      }
      // 处理状态
      .handleResult {
        width: 250px;
      }
    }

    // 表体
    .tbody {
      width: 100%;
      height: 1350px;
      overflow: scroll;
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      // line-height: 105px;

      // 每一行
      .shops-item {
        width: 100%;
        height: 115px;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;

        span {
          display: inline-block;
          line-height: 115px;
        }
        // 序号
        .item-order {
          margin-left: 20px;
          width: 170px;
          text-align: center;
        }
        // 事件区域
        .item-area {
          width: 380px;
          text-align: center;
        }
        // 上报人
        .item-reporter {
          width: 200px;
          text-align: center;
        }
        // 上报时间
        .item-reportTime {
          display: inline-block;
          margin: -7px 40px 0 40px;
          width: 440px;
          vertical-align: middle;
          text-align: center;
        }
        // 联系方式
        .item-tel {
          width: 300px;
          text-align: center;
        }
        // 事件描述
        .item-describe {
          display: inline-block;
          width: 1000px;
          height: 115px;
          line-height: 115px;
          // vertical-align: middle;
          text-align: center;
        }
        // 处理单位
        .item-handleUnit {
          width: 280px;
          text-align: center;
        }
        // 流转至
        .item-flowTo {
          width: 280px;
          text-align: center;
        }
        // 处理时间
        .item-handleTime {
          width: 450px;
          text-align: center;
        }
        // 处理状态
        .item-handleResult {
          width: 250px;
          text-align: center;
        }
      }

      // 隔行换色
      .shops-item:nth-of-type(odd) {
        background-color: #181839;
      }
      .shops-item:nth-of-type(even) {
        background-color: #252545;
      }
    }

    // 导航条隐藏
    .tbody::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>