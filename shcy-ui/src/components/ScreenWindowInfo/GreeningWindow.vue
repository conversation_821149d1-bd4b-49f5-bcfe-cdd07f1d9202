<!-- 绿化信息展示弹窗 -->
<template>
  <div class="greening-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ greeningData.greeningNumber }}</span>
    </div>
    <!-- 类型 -->
    <div class="type">
      <div class="type-title">类型</div>
      <div class="type-txt">{{ greeningData.type }}</div>
    </div>
    <!-- 权属单位 养护单位 -->
    <div class="ownershipUnit-maintainUnit">
      <!-- 权属单位 -->
      <div class="ownershipUnit">
        <div class="ownershipUnit-title">权属单位</div>
        <div class="ownershipUnit-txt">{{ greeningData.ownershipUnit }}</div>
      </div>
      <!-- 养护单位 -->
      <div class="maintainUnit">
        <div class="maintainUnit-title">养护单位</div>
        <div class="maintainUnit-txt">{{ greeningData.maintainUnit }}</div>
      </div>
    </div>

    <!-- 养护责任人 联系方式 -->
    <div class="maintainPerson-phoneNumber">
      <!-- 养护责任人 -->
      <div class="maintainPerson">
        <div class="maintainPerson-title">养护责任人</div>
        <div class="maintainPerson-txt">{{ greeningData.maintainPerson }}</div>
      </div>
      <!-- 联系方式 -->
      <div class="phoneNumber">
        <div class="phoneNumber-title">联系方式</div>
        <div class="phoneNumber-txt">{{ greeningData.phoneNumber }}</div>
      </div>
    </div>

    <!-- 查看详细信息 -->
    <div class="moreInfo" @click="more()">查看事件上报记录</div>
  </div>
</template>

<script>
export default {
  props: ["greeningData"],
  methods: {
    more() {
      this.$store.dispatch('patrolEvent/closeWindow', true);
    },
  }
};
</script>

<style lang="scss" scoped>
.greening-container {
  width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }
  // 权属单位 养护单位
  .ownershipUnit-maintainUnit {
    margin: 45px 0;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 权属单位
    .ownershipUnit {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .ownershipUnit-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .ownershipUnit-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 养护单位
    .maintainUnit {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title
      .maintainUnit-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .maintainUnit-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 类型
  .type {
    margin-top: 45px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .type-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .type-txt {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
  // 养护责任人 联系方式
  .maintainPerson-phoneNumber {
    margin: 45px 0 45px;
    width: 575px;
    height: 130px;
    display: flex;
    // justify-content: space-between;
    background-color: #36365e;

    // 是否有门卫
    .maintainPerson {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;

      .maintainPerson-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .maintainPerson-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .phoneNumber {
      padding: 20px 0 0 38px;
      width: 315px;
      height: 130px;

      // title - 分类投放垃圾种类
      .phoneNumber-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .phoneNumber-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 查看详情
  .moreInfo {
    margin-bottom:75px;
    width: 575px;
    height: 100px;
    border-radius: 50px;
    cursor: pointer;
    text-align: center;
    line-height: 100px;
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    background-color: #00f4e8;
    color: #101534;
  }
}
</style>