<!-- 管网数据展示弹窗 -->
<template>
  <div class="pipeInfo-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">管网信息</span>
    </div>

    <!-- 管线类型  管线材质 -->
    <div class="type-material">
      <!-- 管线类型 -->
      <div class="type">
        <div class="type-title">管线类型</div>
        <div class="type-txt">{{ pipeData.pipeType }}</div>
      </div>
      <!-- 管线材质 -->
      <div class="material">
        <div class="material-title">管线材质</div>
        <div class="material-txt">{{ pipeData.pipeMaterial }}</div>
      </div>
    </div>

    <!-- 管线宽度  管线高度 -->
    <div class="type-material">
      <!-- 管线宽度 -->
      <div class="type">
        <div class="type-title">管线宽度</div>
        <div class="type-txt">{{ pipeData.pipeWidth }}</div>
      </div>
      <!-- 管线高度 -->
      <div class="material">
        <div class="material-title">管线高度</div>
        <div class="material-txt">{{ pipeData.pipeHeight }}</div>
      </div>
    </div>

    <!-- 管线形状  埋设方式 -->
    <div class="type-material">
      <!-- 管线形状 -->
      <div class="type">
        <div class="type-title">管线形状</div>
        <div class="type-txt">{{ pipeData.pipeShape }}</div>
      </div>
      <!-- 埋设方式 -->
      <div class="material">
        <div class="material-title">埋设方式</div>
        <div class="material-txt">{{ pipeData.buryMethod }}</div>
      </div>
    </div>

    <!-- 所在道路  管道长度 -->
    <div class="type-material">
      <!-- 所在道路 -->
      <div class="type">
        <div class="type-title">所在道路</div>
        <div class="type-txt">{{ pipeData.whereRoad }}</div>
      </div>
      <!-- 管道长度 -->
      <div class="material">
        <div class="material-title">管道长度</div>
        <div class="material-txt">{{ pipeData.pipeLength }}</div>
      </div>
    </div>

    <!-- 相邻道路 -->
    <div class="landNumber">
      <div class="landNumber-title">相邻道路</div>
      <div class="landNumber-txt">{{ pipeData.adjoinRoad }}</div>
    </div>

    <!-- 埋设年份  废弃年份 -->
    <div class="type-material">
      <!-- 埋设年份 -->
      <div class="type">
        <div class="type-title">埋设年份</div>
        <div class="type-txt">{{ pipeData.buryDate }}</div>
      </div>
      <!-- 废弃年份 -->
      <div class="material">
        <div class="material-title">废弃年份</div>
        <div class="material-txt">{{ pipeData.abandonDate }}</div>
      </div>
    </div>

    <!-- 探测性质  探测年代 -->
    <div class="type-material">
      <!-- 探测性质 -->
      <div class="type">
        <div class="type-title">探测性质</div>
        <div class="type-txt">{{ pipeData.detectProperty }}</div>
      </div>
      <!-- 探测年代 -->
      <div class="material">
        <div class="material-title">探测年代</div>
        <div class="material-txt">{{ pipeData.detectDate }}</div>
      </div>
    </div>

    <!-- 监理单位 -->
    <div class="landNumber">
      <div class="landNumber-title">监理单位</div>
      <div class="landNumber-txt">{{ pipeData.supervisor }}</div>
    </div>

    <!-- 探测单位 -->
    <div class="landUse-box">
      <div class="landUse">
        <div class="landUse-title">探测单位</div>
        <div class="landUse-txt">{{ pipeData.detectDepartment }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["pipeData"],
  data() {
    return {
      // pipeData: {
      //   pipeType: "雨水",
      //   pipeMaterial: "砼",
      //   pipeShape: "圆形",
      //   pipeWidth: "600(毫米)",
      //   pipeHeight: "600(毫米)",
      //   buryMethod: "直埋",
      //   whereRoad: "沪杭公路",
      //   adjoinRoad: "蒙山路-戚家墩路",
      //   pipeLength: "47.843(米)",
      //   buryDate: "201203",
      //   abandonDate: "",
      //   detectProperty: "普查",
      //   detectDepartment: "上海勘察设计研究院（集团）有限公司",
      //   detectDate: "201706",
      //   supervisor: "上海市地质调查研究院",
      // },
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.pipeInfo-container {
  position: absolute;
  top: 750px;
  left: 4500px;
  width: 725px;
  padding: 0 75px 75px 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 999;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 46px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  //  所在小区域  地块面积
  .type-material {
    margin-top: 45px;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 是否有门卫
    .type {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .type-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .type-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .material {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .material-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .material-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 宗地号
  .landNumber {
    margin-top: 45px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .landNumber-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .landNumber-txt {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }

  // 土地用途
  .landUse-box {
    margin-top: 45px;
    width: 575px;
    // height: 130px;
    display: flex;
    justify-content: space-between;

    // 土地用途
    .landUse {
      padding: 20px 0 20px 38px;
      width: 100%;
      // height: 130px;
      background-color: #36365e;

      .landUse-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .landUse-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }
}
</style>