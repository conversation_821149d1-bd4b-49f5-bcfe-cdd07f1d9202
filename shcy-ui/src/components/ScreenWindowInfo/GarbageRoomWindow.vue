<!-- 垃圾房信息展示弹窗 -->
<template>
  <div class="garbageRoom-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ garbageData.title }}</span>
    </div>
    <div class="area-info-box">
      <div class="community-box">
        <div class="community-title">所属小区</div>
        <div class="community">{{ garbageData.community }}</div>
      </div>
      <div class="refuseClassify-box">
        <div class="refuseClassify-title">分类投放垃圾种类</div>
        <div class="refuseClassify">{{ garbageData.refuseClassify }}</div>
      </div>
    </div>
    <div class="position-info-box">
      <div class="position-title">点位地址</div>
      <div class="position">{{ garbageData.position }}</div>
    </div>
    <div class="open-time-box">
      <div class="open-time-title">投放点开放时间</div>
      <div class="open-time">{{ garbageData.openTime }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["garbageData"],
  // mounted() {
  //   console.log(props);
  // },
};
</script>
<style lang="scss" scoped>
.garbageRoom-container {
  width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 500;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  .area-info-box,
  .position-info-box,
  .open-time-box {
    margin-top: 45px;
    width: 575px;
    height: 130px;
  }

  // 所属小区 分类投放垃圾种类
  .area-info-box {
    display: flex;
    justify-content: space-between;

    // 所属小区
    .community-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .community-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .community {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 分类投放垃圾种类
    .refuseClassify-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .refuseClassify-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .refuseClassify {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 垃圾房地址
  .position-info-box {
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .position-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .position {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
  // 投放时间
  .open-time-box {
    margin-bottom: 75px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .open-time-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .open-time {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
}
</style>