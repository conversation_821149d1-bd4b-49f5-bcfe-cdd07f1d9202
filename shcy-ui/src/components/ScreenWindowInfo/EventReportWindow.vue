<!-- 事件上报情况弹窗 -->
<template>
  <div class="eventReport-container" ref="eventReport">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">事件上报情况</span>
      <div class="close" @click="closeWindow()"></div>
    </div>

    <!-- 数据列表 -->
    <div class="content-data">
      <div class="content-top">
        <div class="content-item">
          <div class="title">案件阶段</div>
          <div class="content">{{ eventReportData.stage }}</div>
        </div>
        <div class="content-item">
          <div class="title">案件状态</div>
          <div class="content">{{ eventReportData.state }}</div>
        </div>
        <div class="content-item">
          <div class="title">案件编号</div>
          <div class="content">{{ eventReportData.num }}</div>
        </div>
        <div class="content-item">
          <div class="title">案件大类</div>
          <div class="content">{{ eventReportData.majorCategory }}</div>
        </div>
        <div class="content-item">
          <div class="title">案件小类</div>
          <div class="content">{{ eventReportData.minorCategory }}</div>
        </div>
        <div class="content-item">
          <div class="title">案件子类</div>
          <div class="content">{{ eventReportData.subCategory }}</div>
        </div>
        <div class="content-item">
          <div class="title">所属网格</div>
          <div class="content">{{ eventReportData.belongGrid }}</div>
        </div>
        <div class="content-item">
          <div class="title">发生地址</div>
          <div class="content">{{ eventReportData.address }}</div>
        </div>
        <div class="content-item">
          <div class="title">上报人</div>
          <div class="content">{{ eventReportData.reportedBy }}</div>
        </div>
        <div class="content-item">
          <div class="title">发现时间</div>
          <div class="content">{{ eventReportData.time }}</div>
        </div>
      </div>
      <div class="case-describe">
        <div class="title">案件描述</div>
        <div class="content">
          {{ eventReportData.description }}
        </div>
      </div>
      <div class="content-bottom">
        <div class="content-item">
          <div class="title">处置街道</div>
          <div class="content">{{ eventReportData.disposalStreet }}</div>
        </div>
        <div class="content-item">
          <div class="title">处置部门</div>
          <div class="content">{{ eventReportData.disposalDepartment }}</div>
        </div>
        <div class="content-item">
          <div class="title">处置人</div>
          <div class="content">{{ eventReportData.disposer }}</div>
        </div>
        <div class="content-item">
          <div class="title">处置时间</div>
          <div class="content">{{ eventReportData.disposalTime }}</div>
        </div>
        <div class="content-item">
          <div class="title">结案结果</div>
          <div class="content">{{ eventReportData.caseClosingResult }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["eventReportData"],
  data() {
    return {
      display: false,
      selStyle: {
        left: "-4px",
        top: "116px",
      },
    };
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
    },
    isSearch() {
      console.log("搜索");
    },
  },
};
</script>

<style lang="scss" scoped>
// 现状使用主体-容器
.eventReport-container {
  z-index: 1000;
  position: relative;
  top: 370px;
  left: 980px;
  padding: 75px;
  width: 4050px;
  // height: 200px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 60px;
    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 50px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
    }
  }

  // 数据列表
  .content-data {
    margin: 0 auto;
    margin-top: 75px;
    width: 3764px;
    // border: 4px solid #656594;

    .content-top {
      display: flex;
      flex-flow: wrap row;

      .content-item {
        line-height: 105px;
        margin-bottom: 50px;
        .title {
          width: 805px;
          height: 105px;
          font-size: 40px;
          color: #28dfae;
        }
        .content {
          padding-left: 45px;
          width: 805px;
          height: 105px;
          font-size: 42px;
          color: #fff;
          background: #2d2d64;
          border: 4px solid #656594;
        }
      }

      .content-item:nth-child(1),
      .content-item:nth-child(2),
      .content-item:nth-child(3),
      .content-item:nth-child(5),
      .content-item:nth-child(6),
      .content-item:nth-child(7),
      .content-item:nth-child(9),
      .content-item:nth-child(10) {
        margin-right: 178px;
      }
    }

    .case-describe {
      margin-bottom: 50px;
      .title {
        width: 805px;
        height: 105px;
        font-size: 40px;
        color: #28dfae;
      }

      .content {
        padding: 30px 45px;
        width: 100%;
        height: 304px;
        background: #2d2d64;
        border: 4px solid #656594;
        font-size: 42px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #ffffff;
      }
    }

    .content-bottom {
      display: flex;
      flex-flow: wrap row;
      .content-item {
        line-height: 105px;
        margin-bottom: 50px;
        .title {
          width: 805px;
          height: 105px;
          font-size: 40px;
          color: #28dfae;
        }
        .content {
          padding-left: 45px;
          width: 805px;
          height: 105px;
          font-size: 42px;
          color: #fff;
          background: #2d2d64;
          border: 4px solid #656594;
        }
      }

      .content-item:nth-child(1),
      .content-item:nth-child(2),
      .content-item:nth-child(3),
      .content-item:nth-child(5) {
        margin-right: 178px;
      }
    }
  }
}
</style>