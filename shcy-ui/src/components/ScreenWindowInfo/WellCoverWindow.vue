<!-- 井盖数据展示弹窗 -->
<template>
  <div class="WellCover-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">井盖信息</span>
    </div>

    <!-- 正式点号  管线类型 -->
    <div class="type-material">
      <!-- 正式点号 -->
      <div class="type">
        <div class="type-title">正式点号</div>
        <div class="type-txt">{{ wellCoverData.formalNum }}</div>
      </div>
      <!-- 管线类型 -->
      <div class="material">
        <div class="material-title">管线类型</div>
        <div class="material-txt">{{ wellCoverData.pipeType }}</div>
      </div>
    </div>

    <!-- 点的特征  附属物种类 -->
    <div class="type-material">
      <!-- 点的特征 -->
      <div class="type">
        <div class="type-title">点的特征</div>
        <div class="type-txt">{{ wellCoverData.pointFeature }}</div>
      </div>
      <!-- 附属物种类 -->
      <div class="material">
        <div class="material-title">附属物种类</div>
        <div class="material-txt">{{ wellCoverData.attachType }}</div>
      </div>
    </div>

    <!-- 附属物空间未知来源 -->
    <div class="landNumber">
      <div class="landNumber-title">附属物空间未知来源</div>
      <div class="landNumber-txt">{{ wellCoverData.attachNuknown }}</div>
    </div>

    <!-- 井的形状  井深 -->
    <div class="type-material">
      <!-- 井的形状 -->
      <div class="type">
        <div class="type-title">井的形状</div>
        <div class="type-txt">{{ wellCoverData.wellShape }}</div>
      </div>
      <!-- 井深 -->
      <div class="material">
        <div class="material-title">井深</div>
        <div class="material-txt">{{ wellCoverData.wellDepth }}</div>
      </div>
    </div>

    <!-- 所在道路 -->
    <div class="landNumber">
      <div class="landNumber-title">所在道路</div>
      <div class="landNumber-txt">{{ wellCoverData.whereRoad }}</div>
    </div>

    <!-- 相邻道路 -->
    <div class="landNumber">
      <div class="landNumber-title">相邻道路</div>
      <div class="landNumber-txt">{{ wellCoverData.adjoinRoad }}</div>
    </div>

    <!-- 探测年月  建设年月 -->
    <div class="type-material">
      <!-- 探测年月 -->
      <div class="type">
        <div class="type-title">探测年月</div>
        <div class="type-txt">{{ wellCoverData.detectDate }}</div>
      </div>
      <!-- 建设年月 -->
      <div class="material">
        <div class="material-title">建设年月</div>
        <div class="material-txt">{{ wellCoverData.buildDate }}</div>
      </div>
    </div>

    <!-- 监理单位 -->
    <div class="landNumber">
      <div class="landNumber-title">监理单位</div>
      <div class="landNumber-txt">{{ wellCoverData.supervisor }}</div>
    </div>

    <!-- 探测单位 -->
    <div class="landUse-box">
      <div class="landUse">
        <div class="landUse-title">探测单位</div>
        <div class="landUse-txt">{{ wellCoverData.detectDepartment }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["wellCoverData"],
  data() {
    return {
      // wellCoverData: {
      //   formalNum: "YS90",
      //   pipeType: "雨水",
      //   pointFeature: "直线点",
      //   attachType: "雨水簏",
      //   attachNuknown: "使用探测成果",
      //   wellShape: "",
      //   wellDepth: "0.0",
      //   whereRoad: "沪杭公路",
      //   adjoinRoad: "蒙山路-戚家墩路",
      //   buildDate: "201203",
      //   detectDepartment: "上海勘察设计研究院（集团）有限公司",
      //   detectDate: "201706",
      //   supervisor: "上海市地质调查研究院",
      // },
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.WellCover-container {
  position: absolute;
  top: 750px;
  left: 4500px;
  width: 725px;
  padding: 0 75px 75px 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 999;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 46px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  //  所在小区域  地块面积
  .type-material {
    margin-top: 45px;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 是否有门卫
    .type {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .type-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .type-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .material {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .material-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .material-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 宗地号
  .landNumber {
    margin-top: 45px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .landNumber-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .landNumber-txt {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }

  // 土地用途
  .landUse-box {
    margin-top: 45px;
    width: 575px;
    // height: 130px;
    display: flex;
    justify-content: space-between;

    // 土地用途
    .landUse {
      padding: 20px 0 20px 38px;
      width: 100%;
      // height: 130px;
      background-color: #36365e;

      .landUse-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .landUse-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }
}
</style>