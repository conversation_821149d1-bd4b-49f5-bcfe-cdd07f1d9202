<!-- 应急通道信息框 -->
<template>
  <div class="emergency-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">消防通道</span>
    </div>
    <div class="emergency-info-box">
      <div class="openTime-box">
        <div class="openTime-title">开放时间</div>
        <div class="openTime">24小时</div>
      </div>
      <div class="distance-box">
        <div class="distance-title">距离</div>
        <div class="distance">872m</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {};
</script>
<style lang="scss" scoped>
.emergency-container {
  width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 50;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  .emergency-info-box,
  .position-info-box,
  .open-time-box {
    margin-top: 45px;
    width: 575px;
    height: 130px;
  }

  // 应急通道
  .emergency-info-box {
    margin-bottom: 75px;
    display: flex;
    justify-content: space-between;

    // 开放时间
    .openTime-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .openTime-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .openTime {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 距离
    .distance-box {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 距离
      .distance-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .distance {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }
}
</style>