<!-- 现状使用主体-沿街商铺 -->
<template>
  <div class="currentUse-container" ref="currentUse">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">临蒙居委会沿街商铺情况</span>
      <div class="close" @click="closeWindow()"></div>
    </div>
    <!-- 下拉列表 -->
    <div class="sel-box">
      <!-- 居委会 -->
      <ScreenDisplaySelect
        class="sel sel-committee"
        :selStyle="selStyle"
        :seldata="committeeData"
        :selDefault="defaultCommittee"
      />
      <!-- 行业类别 -->
      <ScreenDisplaySelect
        class="sel sel-industry-category"
        :selStyle="selStyle"
        :seldata="industryCategory"
        :selDefault="defaultindustry"
      />
      <!-- 房权属性 -->
      <ScreenDisplaySelect
        class="sel sel-updateTime"
        :selStyle="selStyle"
        :seldata="updateTime"
        :selDefault="defaultUpdateTime"
      />
      <!-- 搜索框 -->
      <div class="search-box">
        <input class="input-search" type="search" placeholder="请输入商铺" />
        <span class="input-search-icon" @click="isSearch()">
          <i class="search-icon"></i>
        </span>
      </div>
    </div>
    <!-- 数据列表 -->
    <div class="shops-list-data" v-show="!itemInfoShow">
      <!-- 表头 -->
      <div class="thead">
        <span class="order">序号</span>
        <span class="committee">管辖居委会</span>
        <span class="shopName">店招名称</span>
        <span class="license">营业执照名称</span>
        <span class="address">地址</span>
        <span class="category">行业类别</span>
        <span class="generalCategory">大类</span>
        <span class="subclass">小类</span>
        <span class="contacts">联系人</span>
        <span class="tel">电话</span>
        <span class="ownership">房屋权属</span>
        <span class="employNum">从业人员数</span>
        <span class="placeCode">状态</span>
      </div>
      <!-- 表体 -->
      <div class="tbody">
        <div
          class="shops-item"
          v-for="(item, index) in currentUserData"
          :key="index"
          @click="itemInfoCheck()"
        >
          <span class="item-order">{{ index + 1 }}</span>
          <span class="item-committee">{{ item.committee }}</span>
          <span class="item-shopName">{{ item.shopName }}</span>
          <div class="item-license">{{ item.license }}</div>
          <span class="item-address">{{ item.address }}</span>
          <span class="item-category">{{ item.category }}</span>
          <span class="item-generalCategory">{{ item.generalCategory }}</span>
          <span class="item-subclass">{{ item.subclass }}</span>
          <span class="item-contacts">{{ item.contacts }}</span>
          <span class="item-tel">{{ item.tel }}</span>
          <span class="item-ownership">{{ item.ownership }}</span>
          <span class="item-employNum">{{ item.employNum }}</span>
          <div class="item-placeCode error-active" v-if="item.placeCode == '异常'">{{ item.placeCode }}</div>
          <div class="item-placeCode" v-else-if="item.placeCode == '阴性'">{{ item.placeCode }}</div>
          
        </div>
      </div>
    </div>
    <!-- 详细数据 -->
    <div class="itemInfoCheck" v-show="itemInfoShow">
      <img src="https://s1.ax1x.com/2022/10/18/xryHPI.png" width="100%" alt="">
    </div>
  </div>
</template>

<script>
import ScreenDisplaySelect from "../ScreenDisplaySelect/index.vue";
export default {
  props: ["currentUserData"],
  data() {
    return {
      display: false,
      itemInfoShow: false,
      selStyle: {
        left: "-4px",
        top: "116px",
      },
      defaultCommittee: "临蒙居委会",
      defaultindustry: "请选择行业类别",
      defaultUpdateTime: "请选择房权属性",
      committeeData: [
        "全部",
        "临蒙居委会",
        "蒙山居委会",
        "紫卫社区居委会",
        "潮河居委会",
      ],
      industryCategory: [
        "全部",
        "服务业",
        "零售业",
        "公用事业",
        "医疗卫生",
        "文体类",
        "其他商铺",
      ],
      updateTime: ["全部", "市场公司", "城建公司", "聚慧"],
      // 其他商铺数据
      otherShops: [
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
        {
          committee: "临蒙居委会",
          shopName: "上海翔峰房地产经纪事务所",
          license: "金源综合市场经营管理有限公司石化隆安市场分公司",
          address: "临潮街216号",
          category: "其他商铺",
          generalCategory: "水电营业厅",
          subclass: "非机动车维修店",
          contacts: "朱慧珍",
          tel: "18854679647",
          ownership: "城建公司",
          employNum: 24,
          placeCode: "是",
        },
      ],
    };
  },
  components: {
    ScreenDisplaySelect,
  },
  methods: {
    closeWindow() {
      this.$emit("closeWindow", this.display);
      this.itemInfoShow = false;
    },
    isSearch() {
      console.log("搜索");
    },
    itemInfoCheck() {
      this.itemInfoShow = true;
    }
  },
};
</script>

<style lang="scss" scoped>
// 现状使用主体-容器
.currentUse-container {
  z-index: 1000;
  position: relative;
  top: 700px;
  left: 1115px;
  padding: 75px;
  width: 4050px;
  // height: 200px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    position: relative;
    width: 100%;
    height: 60px;
    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 50px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
    }
  }

  // 下拉列表
  .sel-box {
    margin: 50px 0;
    width: 100%;
    display: flex;

    .sel {
      margin-right: 80px;
    }

    // 搜索框
    .search-box {
      display: flex;
      width: 764px;
      height: 124px;
      border: 4px solid #656594;

      .input-search {
        padding: 0 0 0 50px;
        width: 640px;
        // height: 124px;
        outline: none;
        border: none;
        border-right: 4px solid #656594;
        background-color: #181839;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #8f8fa7;
        line-height: 124px;
      }

      .input-search-icon {
        position: relative;
        display: inline-block;
        width: 124px;
        // height: 124px;
        background-color: #2d2d64;
        cursor: pointer;

        .search-icon {
          position: absolute;
          top: 41px;
          left: 41px;
          display: block;
          width: 38px;
          height: 44px;
          background: url("../../assets/screen_display_img/bottom_icon_search.png")
            no-repeat;
          background-size: contain;
        }
      }
    }
  }

  // 数据列表
  .shops-list-data {
    width: 3892px;
    // height: 100px;
    border: 4px solid #656594;

    // 表头
    .thead {
      width: 100%;
      height: 96px;
      border-bottom: 4px solid #656594;
      background-color: #2d2d64;

      span {
        display: inline-block;
        // margin-left: 50px;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
      }

      // 序号
      .order {
        margin-left: 20px;
        width: 170px;
      }
      // 管辖居委会
      .committee {
        width: 280px;
      }
      // 店招名称
      .shopName {
        width: 520px;
      }
      // 营业执照名称
      .license {
        margin: 0 40px;
        width: 440px;
      }
      // 地址
      .address {
        width: 300px;
      }
      // 行业类别
      .category {
        width: 220px;
      }
      // 大类
      .generalCategory {
        width: 300px;
      }
      // 小类
      .subclass {
        width: 330px;
      }
      // 联系人
      .contacts {
        width: 200px;
      }
      // 电话
      .tel {
        width: 300px;
      }
      // 房屋权属
      .ownership {
        width: 240px;
      }
      // 从业人员数
      .employNum {
        width: 200px;
      }
      // 是否有场所码
      .placeCode {
        width: 280px;
      }
    }

    // 表体
    .tbody {
      width: 100%;
      height: 1350px;
      overflow: scroll;
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      // line-height: 105px;

      // 每一行
      .shops-item {
        width: 100%;
        height: 115px;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        cursor: pointer;

        span {
          display: inline-block;
          line-height: 115px;
        }
        // 序号
        .item-order {
          margin-left: 20px;
          width: 170px;
          text-align: center;
        }
        // 管辖居委会
        .item-committee {
          width: 280px;
          text-align: center;
        }
        // 店招名称
        .item-shopName {
          width: 520px;
          text-align: center;
        }
        // 营业执照名称
        .item-license {
          display: inline-block;
          margin: -7px 40px 0 40px;
          width: 440px;
          vertical-align: middle;
          text-align: center;
        }
        // 地址
        .item-address {
          width: 300px;
          text-align: center;
        }
        // 行业类别
        .item-category {
          width: 220px;
          text-align: center;
        }
        // 大类
        .item-generalCategory {
          width: 300px;
          text-align: center;
        }
        // 小类
        .item-subclass {
          width: 330px;
          text-align: center;
        }
        // 联系人
        .item-contacts {
          width: 200px;
          text-align: center;
        }
        // 电话
        .item-tel {
          width: 300px;
          text-align: center;
        }
        // 房屋权属
        .item-ownership {
          width: 240px;
          text-align: center;
        }
        // 从业人员数
        .item-employNum {
          width: 200px;
          text-align: center;
        }
        // 是否有场所码
        .item-placeCode {
          margin-left: 76px;
          width: 125px;
          height: 62px;
          // line-height: 62px;
          display: inline-block;
          color: #22ff46;
          border: 4px solid #22ff46;
          background-color: rgba(34,255,70, .3);
          text-align: center;
        }

        .error-active {
          color: #ffa02f;
          border: 4px solid #ffa02f;
          background-color: rgba(255,160,47, .3);
        }
      }

      // 隔行换色
      .shops-item:nth-of-type(odd) {
        background-color: #181839;
      }
      .shops-item:nth-of-type(even) {
        background-color: #252545;
      }

      .shops-item:hover {
        background-color: rgba(42, 252, 220, 0.3);
      }
    }

    // 导航条隐藏
    .tbody::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>