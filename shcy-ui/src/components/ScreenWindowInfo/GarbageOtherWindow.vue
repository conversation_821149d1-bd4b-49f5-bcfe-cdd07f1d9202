<!-- 大件垃圾和建筑垃圾-展示弹窗 -->
<template>
  <div class="garbageOther-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ garbageOtherData.title }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: ["garbageOtherData"],
  // mounted() {
  //   console.log(props);
  // },
};
</script>
<style lang="scss" scoped>
.garbageOther-container {
  // width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    margin: 45px 0;
    // width: 575px;
    height: 60px;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }
}
</style>