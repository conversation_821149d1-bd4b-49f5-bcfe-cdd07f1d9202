<!-- 井盖数据展示弹窗 -->
<template>
  <div class="greeningSpot-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ greeningSpotData.packageArea }}</span>
    </div>

    <!-- 名称  资源类型 -->
    <div class="type-material">
      <!-- 名称 -->
      <div class="type">
        <div class="type-title">名称</div>
        <div class="type-txt">{{ greeningSpotData.name }}</div>
      </div>
      <!-- 资源类型 -->
      <div class="material">
        <div class="material-title">资源类型</div>
        <div class="material-txt">{{ greeningSpotData.resourceType }}</div>
      </div>
    </div>

    <!-- 林长  副林长 -->
    <div class="type-material">
      <!-- 林长 -->
      <div class="type">
        <div class="type-title">林长</div>
        <div class="type-txt">{{ greeningSpotData.forestHead }}</div>
      </div>
      <!-- 副林长 -->
      <div class="material">
        <div class="material-title">副林长</div>
        <div class="material-txt">{{ greeningSpotData.deputyForestHead }}</div>
      </div>
    </div>

    <!-- 面积  管理方式 -->
    <div class="type-material">
      <!-- 面积 -->
      <div class="type">
        <div class="type-title">面积</div>
        <div class="type-txt">{{ greeningSpotData.area }}</div>
      </div>
      <!-- 管理方式 -->
      <div class="material">
        <div class="material-title">管理方式</div>
        <div class="material-txt">{{ greeningSpotData.managementStyle }}</div>
      </div>
    </div>

    <!-- 管理单位 -->
    <div class="landNumber">
      <div class="landNumber-title">管理单位</div>
      <div class="landNumber-txt">{{ greeningSpotData.managementUnit }}</div>
    </div>

    <!-- 管理责任人 联系方式 -->
    <div class="maintainPerson-phoneNumber">
      <!-- 管理责任人 -->
      <div class="maintainPerson">
        <div class="maintainPerson-title">管理责任人</div>
        <div class="maintainPerson-txt">{{ greeningSpotData.managementUnitPerson }}</div>
      </div>
      <!-- 联系方式 -->
      <div class="phoneNumber">
        <div class="phoneNumber-title">联系方式</div>
        <div class="phoneNumber-txt">{{ greeningSpotData.managementUnitTelphone }}</div>
      </div>
    </div>

    <!-- 养护单位 -->
    <div class="landNumber">
      <div class="landNumber-title">养护单位</div>
      <div class="landNumber-txt">{{ greeningSpotData.conservationUnit }}</div>
    </div>

    <!-- 养护责任人 联系方式 -->
    <div class="maintainPerson-phoneNumber">
      <!-- 养护责任人 -->
      <div class="maintainPerson">
        <div class="maintainPerson-title">养护责任人</div>
        <div class="maintainPerson-txt">{{ greeningSpotData.conservationUnitPerson }}</div>
      </div>
      <!-- 联系方式 -->
      <div class="phoneNumber">
        <div class="phoneNumber-title">联系方式</div>
        <div class="phoneNumber-txt">{{ greeningSpotData.conservationUnitTelphone }}</div>
      </div>
    </div>

    <!-- 工作职责 -->
    <div class="landNumber">
      <div class="landNumber-title">工作职责</div>
      <div class="landNumber-txt">{{ greeningSpotData.jobDuty }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["greeningSpotData"],
  data() {
    return {
      // greeningSpotData: {
      //   id: 3,
      //   forestHead: "姚德磊",
      //   deputyForestHead: "钱军",
      //   packageArea: "鹦鹉洲生态湿地",
      //   resourceType: "生态廊道",
      //   name: "湿地",
      //   area: "145842㎡",
      //   managementUnit: "上海金山文旅产业有限公司",
      //   managementUnitPerson: null,
      //   managementUnitTelphone: null,
      //   conservationUnit: "上海金山文旅产业有限公司",
      //   conservationUnitPerson: null,
      //   conservationUnitTelphone: null,
      //   managementStyle: "督促",
      //   jobDuty: null,
      // },
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.greeningSpot-container {
  position: absolute;
  top: 750px;
  left: 1080px;
  width: 725px;
  padding: 0 75px 75px 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 999;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 46px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  //  所在小区域  地块面积
  .type-material {
    margin-top: 45px;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 是否有门卫
    .type {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .type-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .type-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .material {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .material-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .material-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 宗地号
  .landNumber {
    margin-top: 45px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .landNumber-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .landNumber-txt {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }

  // 养护责任人 联系方式
  .maintainPerson-phoneNumber {
    margin: 45px 0 45px;
    width: 575px;
    height: 130px;
    display: flex;
    // justify-content: space-between;
    background-color: #36365e;

    // 是否有门卫
    .maintainPerson {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;

      .maintainPerson-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .maintainPerson-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .phoneNumber {
      padding: 20px 0 0 38px;
      width: 315px;
      height: 130px;

      // title - 分类投放垃圾种类
      .phoneNumber-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .phoneNumber-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 土地用途
  .landUse-box {
    margin-top: 45px;
    width: 575px;
    // height: 130px;
    display: flex;
    justify-content: space-between;

    // 土地用途
    .landUse {
      padding: 20px 0 20px 38px;
      width: 100%;
      // height: 130px;
      background-color: #36365e;

      .landUse-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .landUse-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }
}
</style>