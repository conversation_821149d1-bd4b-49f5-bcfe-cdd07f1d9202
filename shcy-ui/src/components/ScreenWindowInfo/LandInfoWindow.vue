<!-- 用地信息展示弹窗 -->
<template>
  <div class="landInfo-container">
    <!-- 标题 -->
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ landInfoData.title }}</span>
    </div>

    <!-- 所在小区域  地块面积 -->
    <div class="area-plotArea">
      <!-- 所在小区域 -->
      <div class="area">
        <div class="area-title">所在小区域</div>
        <div class="area-txt">{{ landInfoData.area }}</div>
      </div>
      <!-- 地块面积 -->
      <div class="plotArea">
        <div class="plotArea-title">地块面积</div>
        <div class="plotArea-txt">{{ landInfoData.plotArea }} m²</div>
      </div>
    </div>

    <!-- 宗地号 -->
    <div class="landNumber">
      <div class="landNumber-title">宗地号</div>
      <div class="landNumber-txt">{{ landInfoData.landNumber }}</div>
    </div>
    <!-- 土地用途 -->
    <div class="landUse-box">
      <div class="landUse">
        <div class="landUse-title">土地用途</div>
        <div class="landUse-txt">{{ landInfoData.landUse }}</div>
      </div>
    </div>

    <!-- 查看详细信息 -->
    <div class="moreInfo" @click="more(landInfoData.index)">查看详细信息</div>
  </div>
</template>

<script>
export default {
  props: ["landInfoData"],
  methods: {
    more(index) {
      if (index == 3) {
        this.$router.push("/floorInfo");
      } else {
        this.$store.dispatch("landSurvey/getRealEstateData", index);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.landInfo-container {
  width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 46px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  //  所在小区域  地块面积
  .area-plotArea {
    margin-top: 45px;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 是否有门卫
    .area {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      .area-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .area-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }

    // 非机动车通行
    .plotArea {
      padding: 20px 0 0 38px;
      width: 260px;
      height: 130px;
      background-color: #36365e;

      // title - 分类投放垃圾种类
      .plotArea-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .plotArea-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 宗地号
  .landNumber {
    margin-top: 45px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .landNumber-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .landNumber-txt {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }

  // 土地用途
  .landUse-box {
    margin-top: 45px;
    width: 575px;
    height: 130px;
    display: flex;
    justify-content: space-between;

    // 土地用途
    .landUse {
      padding: 20px 0 0 38px;
      width: 100%;
      height: 130px;
      background-color: #36365e;

      .landUse-title {
        margin-bottom: 10px;
        font-size: 26px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
      }

      .landUse-txt {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #00f3e7;
      }
    }
  }

  // 查看详细信息
  .moreInfo {
    margin: 45px 0 75px;
    width: 575px;
    height: 100px;
    border-radius: 50px;
    cursor: pointer;
    text-align: center;
    line-height: 100px;
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    background-color: #00f4e8;
    color: #101534;
  }
}
</style>