<!-- 房地调查数据库 - 房地详情弹窗 -->
<template>
  <transition name="el-zoom-in-center">
    <div
      class="LandSurvey-container"
      v-show="this.$store.state.landSurvey.display"
    >
      <!-- 标题 -->
      <div class="title-box">
        <span class="ring"></span>
        <span class="window-title">{{ itemData.title }}</span>
        <div class="close" @click="closeWindow()"></div>
      </div>
      <!-- 数据列表 -->
      <div class="shops-list-data">
        <!-- 土地信息 -->
        <div class="landInfo">
          <div class="title">土地信息</div>
          <div class="thead">
            <span class="order">序号</span>
            <span class="committee">管辖居委会</span>
            <span class="village">所在小区域</span>
            <span class="landNumber">宗地号</span>
            <span class="plotArea">地块面积（㎡）</span>
            <span class="issuedArea">发证面积（㎡）</span>
            <span class="landUse">土地用途（试行）</span>
          </div>
          <div class="tbody">
            <div class="lands-item">
              <span class="item-order">1</span>
              <span class="item-committee">{{ itemData.committee }}</span>
              <span class="item-village">{{ itemData.village }}</span>
              <span class="item-landNumber">{{ itemData.landNumber }}</span>
              <span class="item-plotArea">{{ itemData.plotArea }}</span>
              <span class="item-issuedArea">{{ itemData.issuedArea }}</span>
              <span class="item-landUse">{{ itemData.landUse }}</span>
            </div>
          </div>
        </div>
        <!-- 房屋信息 -->
        <div class="houseInfo">
          <div class="title">房屋信息</div>
          <div class="thead">
            <span class="propertySubject">产权主体</span>
            <span class="propertyLicenseNumber">产证门牌号</span>
            <span class="registeredArea">登记土地面积（㎡）</span>
            <span class="buildingArea">建筑面积（㎡）</span>
            <span class="landApprovedUse">土地批准用途</span>
            <span class="currentUser">现状使用主体</span>
          </div>
          <div class="tbody">
            <div class="lands-item">
              <div class="item-propertySubject">
                {{ itemData.propertySubject }}
              </div>
              <span class="item-propertyLicenseNumber">{{
                itemData.propertyLicenseNumber
              }}</span>
              <span class="item-registeredArea">{{
                itemData.registeredArea
              }}</span>
              <span class="item-buildingArea">{{ itemData.buildingArea }}</span>
              <span class="item-landApprovedUse">{{
                itemData.landApprovedUse
              }}</span>
              <div class="item-currentUser">{{ itemData.currentUser }}</div>
            </div>
          </div>
        </div>
        <!-- 供地信息 -->
        <div class="landSupplyInfo">
          <div class="title">供地信息</div>
          <div class="thead">
            <span class="landSupplyNo">供地编号</span>
            <span class="projectName">项目名称</span>
            <span class="landUser">用地单位</span>
            <span class="approvalNo">用地批文号</span>
            <span class="landPurpose">土地用途</span>
            <span class="approvalTime">批准时间</span>
            <span class="approvedTotalArea">批准总面积（㎡）</span>
          </div>
          <div class="tbody">
            <div class="lands-item">
              <div class="item-landSupplyNo">{{ itemData.landSupplyNo }}</div>
              <div class="item-projectName">{{ itemData.projectName }}</div>
              <div class="item-landUser">{{ itemData.landUser }}</div>
              <div class="item-approvalNo">{{ itemData.approvalNo }}</div>
              <div class="item-landPurpose">{{ itemData.landPurpose }}</div>
              <span class="item-approvalTime">{{ itemData.approvalTime }}</span>
              <span class="item-approvedTotalArea">{{
                itemData.approvedTotalArea
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: ["currentUseData"],
  data() {
    return {
      data: null,
    };
  },
  computed: {
    itemData() {
      return this.$store.state.landSurvey.realEstateData;
    },
  },
  watch: {
    itemData(newVal, oldVal) {
      console.log(newVal, oldVal);
    },
  },
  methods: {
    closeWindow() {
      this.$store.dispatch("landSurvey/closeWindow", false);
    },
  },
};
</script>

<style lang="scss" scoped>
// 现状使用主体-容器
.LandSurvey-container {
  z-index: 1000;
  position: absolute;
  top: 700px;
  left: -50px;
  padding: 75px;
  // width: 6124px;
  // height: 200px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;

  // 标题
  .title-box {
    position: relative;
    margin-bottom: 75px;
    width: 100%;
    height: 60px;
    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }
    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }

    // 关闭按钮
    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
      height: 50px;
      cursor: pointer;
      background-image: url("../../assets/screen_display_img/icon_close.png");
    }
  }

  // 数据列表
  .shops-list-data {
    display: flex;
    // width: 6000px;
    border: 4px solid #656594;
    font-size: 36px;
    color: #fff;

    .title {
      width: 100%;
      height: 100px;
      text-align: center;
      font-size: 40px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 100px;
      background: #2d2d64;
      border-bottom: 4px solid #656594;
    }

    .thead {
      width: 100%;
      border-bottom: 4px solid #656594;
      background-color: #2d2d64;

      span {
        height: 96px;
        display: inline-block;
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 96px;
        text-align: center;
      }
    }

    // 表体
    .tbody {
      width: 100%;
      height: 1350px;
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;

      span {
        display: inline-block;
        line-height: 115px;
        text-align: center;
      }
    }

    // 土地信息
    .landInfo {
      width: 2000px;

      // 表头
      .thead {
        .order {
          margin: 0 50px;
          width: 100px;
        }
        .committee {
          width: 250px;
        }
        .village {
          width: 250px;
        }
        .landNumber {
          width: 350px;
        }
        .plotArea,
        .issuedArea {
          width: 300px;
        }
        .landUse {
          width: 350px;
        }
      }
      // 表体
      .tbody {
        .item-order {
          margin: 0 50px;
          width: 100px;
          text-align: center;
        }
        .item-committee {
          width: 250px;
        }
        .item-village {
          width: 250px;
        }
        .item-landNumber {
          width: 350px;
        }
        .item-plotArea,
        .item-issuedArea {
          width: 300px;
        }
        .item-landUse {
          width: 350px;
        }
      }
    }

    // 房屋信息
    .houseInfo {
      width: 2000px;

      .title {
        border-left: 4px solid #656594;
      }

      // 表头
      .thead {
        border-left: 4px solid #656594;

        .propertySubject {
          margin: 0 20px;
          width: 300px;
        }
        .propertyLicenseNumber {
          width: 250px;
        }
        .registeredArea {
          width: 350px;
        }
        .buildingArea {
          width: 300px;
        }
        .landApprovedUse {
          width: 320px;
        }
        .currentUser {
          width: 410px;
        }
      }
      // 表体
      .tbody {
        border-left: 4px solid #656594;

        .item-propertySubject {
          margin: 0 20px;
          display: inline-block;
          width: 300px;
          height: 115px;
          line-height: 55px;
          vertical-align: middle;
        }
        .item-propertyLicenseNumber {
          width: 250px;
        }
        .item-registeredArea {
          width: 350px;
        }
        .item-buildingArea {
          width: 300px;
        }
        .item-landApprovedUse {
          width: 320px;
        }
        .item-currentUser {
          display: inline-block;
          width: 410px;
          height: 115px;
          line-height: 115px;
          text-align: center;
          vertical-align: middle;
        }
      }
    }

    // 供地信息
    .landSupplyInfo {
      width: 2000px;

      .title {
        border-left: 4px solid #656594;
      }
      // 表头
      .thead {
        border-left: 4px solid #656594;

        .landSupplyNo {
          margin: 0 50px;
          width: 250px;
        }
        .projectName,
        .landUser {
          margin-left: 20px;
          width: 320px;
        }
        .approvalNo {
          width: 260px;
        }
        .landPurpose {
          width: 160px;
        }
        .approvalTime {
          width: 240px;
        }
        .approvedTotalArea {
          width: 300px;
        }
      }
      // 表体
      .tbody {
        border-left: 4px solid #656594;

        .lands-item {
          div {
            height: 115px;
            line-height: 55px;
          }

          .item-landSupplyNo {
            display: inline-block;
            margin: 0 50px;
            width: 250px;
            height: 96px;
            vertical-align: middle;
            word-break: break-all;
          }
          .item-projectName,
          .item-landUser {
            margin-left: 20px;
            display: inline-block;
            width: 320px;
            vertical-align: middle;
          }

          .item-approvalNo {
            display: inline-block;
            width: 260px;
            vertical-align: middle;
            text-align: center;
          }
          .item-landPurpose {
            display: inline-block;
            width: 160px;
            line-height: 115px;
            vertical-align: middle;
            text-align: center;
          }
          .item-approvalTime {
            width: 240px;
          }
          .item-approvedTotalArea {
            width: 300px;
          }
        }
      }
    }
  }
}
</style>