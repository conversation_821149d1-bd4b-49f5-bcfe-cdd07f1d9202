<!-- 党建库-办事处信息展示弹窗 -->
<template>
  <div class="partyBuilding-container">
    <div class="title-box">
      <span class="ring"></span>
      <span class="window-title">{{ partyData.title }}</span>
    </div>
    <div class="position-info-box">
      <div class="position-title">地址</div>
      <div class="position">{{ partyData.address }}</div>
    </div>
    <div class="position-info-box">
      <div class="position-title">办公时间</div>
      <div class="position">{{ partyData.openTime }}</div>
    </div>
    <div class="open-time-box">
      <div class="open-time-title">联系方式</div>
      <div class="open-time">{{ partyData.tel }}</div>
    </div>
    <div class="introduce">
      <div class="title">介绍概述</div>
      <p class="content" v-for="(item, index) in introduce" :key="index">
        {{ item }}
      </p>
    </div>
    <div class="moreInfo">查看详细信息</div>
  </div>
</template>

<script>
export default {
  props: ["partyData"],
  computed: {
    introduce() {
      return this.partyData.introduce.split("<br/>");
    },
  },
};
</script>
<style lang="scss" scoped>
.partyBuilding-container {
  // position: absolute;
  // top: 1000px;
  // left: 1500px;
  width: 725px;
  padding: 0 75px;
  background-image: linear-gradient(209deg, #2d2d64, #030315);
  border: 4px solid #ffffff;
  border-radius: 10px;
  z-index: 500;

  // 标题
  .title-box {
    margin-top: 45px;
    width: 575px;
    height: 60px;

    // 圆环
    .ring {
      display: inline-block;
      width: 36px;
      height: 36px;
      border: 8px solid #28dfae;
      border-radius: 50%;
      margin: auto;
    }

    // 标题-字样
    .window-title {
      margin-left: 30px;
      font-size: 45px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
    }
  }

  .position-info-box,
  .open-time-box {
    margin-top: 45px;
    width: 575px;
    height: 130px;
  }

  // 垃圾房地址
  .position-info-box {
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .position-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .position {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }
  // 投放时间
  .open-time-box {
    // margin-bottom: 75px;
    padding: 20px 0 0 38px;
    width: 575px;
    height: 130px;
    background-color: #36365e;

    .open-time-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .open-time {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }
  }

  // 介绍概述
  .introduce {
    margin-top: 45px;
    width: 575px;

    .title {
      font-size: 36px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #00f3e7;
    }

    .content {
      text-indent: 2em;
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }

  // 查看详细信息
  .moreInfo {
    margin: 45px 0 45px;
    width: 575px;
    height: 100px;
    border-radius: 50px;
    cursor: pointer;
    text-align: center;
    line-height: 100px;
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    background-color: #00f4e8;
    color: #101534;
  }
}
</style>