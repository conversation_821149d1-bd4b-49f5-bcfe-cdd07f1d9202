<template>
  <div class="container">
    <div class="sel-box" tabindex="1" @blur="flag = false">
      <div class="selected-item" @click="flag = !flag">{{ selTxt }}</div>
      <ul class="ul-box" v-show="flag" :style="selStyle">
        <li
          class="list-item"
          v-for="(item, index) in seldata"
          :key="index"
          @click="handle(item)"
        >
          {{ item }}
        </li>
      </ul>
      <i @click="flag = !flag" class="el-icon-caret-bottom"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: ["selStyle", "seldata", "selDefault"],
  data() {
    return {
      selTxt: this.selDefault,
      flag: false,
    };
  },
  methods: {
    handle(value) {
      this.selTxt = value;
      this.flag = false;
      // console.log("当前选择的是---" + value);
      this.$emit("handleSelect", value);
    },
  },
};
</script>

<style lang="scss" scoped>
.sel-box {
  position: relative;
  display: inline-block;
  border: 2px solid #656594;
  cursor: pointer;

  .selected-item {
    padding-left: 20px;
    width: 254px;
    height: 38px;
    font-size: 12px;
    line-height: 41px;
    color: #fff;
    background-color: #181839;
  }

  .ul-box {
    padding: 0;
    margin: 0;
    position: absolute;
    // left: -4px;
    // bottom: 120px;
    width: 257px;
    max-height: 400px;
    overflow-y: scroll;
    border: 2px solid #656594;

    .list-item {
      padding-left: 20px;
      width: 100%;
      height: 41px;
      line-height: 41px;
      list-style: none;
      font-size: 12px;
      color: #fff;
      background-color: #181839;
    }

    .list-item:hover {
      background-color: #36365e;
    }
  }

  .el-icon-caret-bottom {
    position: absolute;
    right: 10px;
    top: 13px;
    display: block;
    font-size: 13px;
    // margin-bottom: 15px;
    color: #fff;
    transition: color 0.15s linear;
  }
}

/*定义整个滚动条高宽及背景：高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width:2px;
  background-color:#F5F5F5;
}
/*定义滚动条轨道：内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color:#F5F5F5;
}
/*定义滑块：内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius:10px;
  background-color:#555;
}

</style>
